{"name": "v", "version": "1.0.0", "description": "", "main": "", "scripts": {"start": "webpack-dev-server --config config/webpack.dev.config.js --hot --inline", "dev": "webpack --config config/webpack.dev.config.js --watch --progress", "build": "NODE_OPTIONS='--max_old_space_size=8192' webpack --config config/webpack.prod.config.js", "build-release": "webpack --config config/webpack.prod.config.js", "icon": "node ./tools/update-icon", "analyz": "webpack --config config/webpack.prod.config.js --analyz", "debugger": "node --inspect-brk ./node_modules/webpack/bin/webpack.js --config config/webpack.prod.config.js", "i18n": "fx-cli check-i18n", "i18n-check-key": "python3  ./i18n/i18n_check_key.py", "i18n-scan-install": "pip3 install -r ./i18n_scan/requirements.txt", "i18n-scan": "python3 ./i18n_scan/main.py"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.18.5", "@babel/preset-env": "^7.18.2", "@beecraft/core": "^1.4.11", "@beecraft/engine": "^1.4.11", "@csstools/postcss-global-data": "^1.0.3", "@types/vue2": "^2.6.12", "autoprefixer": "^9.8.8", "axios": "0.18.0", "babel-helper-vue-jsx-merge-props": "^2.0.0", "babel-loader": "8.2.5", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^4.0.1", "clean-webpack-plugin": "^0.1.19", "copy-webpack-plugin": "^4.6.0", "css-loader": "^1.0.0", "file-loader": "^1.1.11", "less": "^3.13.1", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^0.4.2", "optimize-css-assets-webpack-plugin": "^5.0.8", "postcss": "^8.4.21", "postcss-loader": "^4.3.0", "postcss-preset-env": "^7.8.3", "request": "^2.88.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "thread-loader": "^3.0.4", "ts-loader": "^8.4.0", "tslib": "^2.4.0", "typescript": "^4.7.3", "uglifyjs-webpack-plugin": "^2.0.1", "url-loader": "^1.0.1", "vue-drag-resize": "^1.5.4", "vue-loader": "^15.9.8", "vue-template-compiler": "2.6.12", "vuex": "3.0.1", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.3", "webpack-manifest-plugin": "^2.0.3", "webpack-merge": "^4.1.3", "webpack-parallel-uglify-plugin": "^2.0.0", "webpack-runtime-public-path-plugin": "^1.1.2", "webpack-tplconfig-plugin": "^1.0.2", "webpack-transfer-cmd-plugin": "^1.0.9"}, "dependencies": {"@antv/g6": "^4.8.22", "@aws-sdk/eventstream-marshaller": "^3.120.0", "@aws-sdk/util-utf8-node": "^3.259.0", "@babel/helper-module-imports": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@gamma/common": "^2.2.1", "@stock-plugin/web": "965.0.2", "accountaddrobj": "^1.0.9", "accountfininfoobj": "^1.0.2", "ai-helper-web": "2.0.33", "async-validator": "^1.10.1", "attribute": "960.0.4", "backcalculation": "950.0.0", "bom": "965.0.2", "countup.js": "^2.9.0", "crm-biz-gantt": "960.0.1", "crm-biz-resource": "960.0.2", "mc_currency": "960.0.5", "message": "880.0.1", "multi-unit-plugin": "950.0.4", "paas_plugin_manager": "965.0.1", "plugin_base": "960.0.1", "plugin_log": "870.0.2", "plugin_public_methods": "950.0.1", "priceservice": "960.0.7", "price_policy": "960.0.6", "quoter_advanced_formula": "960.0.3", "rebate": "965.0.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "vditor": "^3.11.0", "vue": "2.6.12", "vue-class-component": "^7.2.6", "vue-hot-img": "0.3.1", "vue-property-decorator": "^9.1.2"}}