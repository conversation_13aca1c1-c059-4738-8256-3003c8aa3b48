
# SFA New AI Chat 组件

## 外部组件支持

### 通过 Slot 传入自定义组件

sfa_new_ai_chat 组件支持通过 slot 方式传入自定义组件，**外部 slot 优先于内部组件**。

#### 支持的 Slot 类型

- **mask**: 遮罩层组件
- **welcome**: 欢迎卡片组件
- **displayData**: 数据展示组件

#### 使用示例

```vue
<SfaNewAiChat>
  <!-- 自定义遮罩组件 -->
  <template #mask="{ agentData, message }">
    <MyCustomMask
      :agentData="agentData"
      :message="message"
      @send-message="handleMessage"
      @close-mask="handleClose"
      @action="handleAction"
    />
  </template>

  <!-- 自定义欢迎组件 -->
  <template #welcome="{ agentData, message }">
    <MyCustomWelcome
      :agentData="agentData"
      :message="message"
      @send-message="handleMessage"
    />
  </template>

  <!-- 自定义数据展示组件 -->
  <template #displayData="{ agentData, message, type }">
    <MyCustomDisplay
      :agentData="agentData"
      :message="message"
      :type="type"
      @send-message="handleMessage"
    />
  </template>
</SfaNewAiChat>
```

#### Slot 参数说明

##### mask slot
- `agentData`: Agent 数据对象
- `message`: 消息对象
- 支持事件：`@send-message`、`@close-mask`、`@action`

##### welcome slot
- `agentData`: Agent 数据对象
- `message`: 消息对象
- 支持事件：`@send-message`

##### displayData slot
- `agentData`: Agent 数据对象
- `message`: 消息对象
- `type`: 数据类型标识
- 支持事件：`@send-message`

#### 优先级规则

1. **外部 slot 优先**：如果调用方传入了对应的 slot，优先使用外部组件
2. **内部组件兜底**：如果外部没有传入 slot，使用内部组件
3. **完全兼容**：现有的 `maskType`、`welcomeType` 等 props 继续有效

## 内部组件开发规范

agent-chat组件支持welcome和displayData两个插槽，分别用于显示欢迎卡片和显示数据。

统一在components目录下开发导出。

- displayData插槽命名规范，驼峰命名并以Display开头，组件通过消息type来匹配
例如：消息的type = sfa_custome_type，则组件名称为DisplaySfaCustomeType。
- welcome插槽命名规范，驼峰命名并以Welcome开头，组件通过传入的welcomeType来匹配
例如：WelcomeTest

## paas文档

- [agent-chat组件](https://fe.firstshare.cn/paas-fe-biz-doc/components/ai/agent/AgentChat.html)
- [agent聊天server接口](https://wiki.firstshare.cn/pages/viewpage.action?pageId=537495356)
