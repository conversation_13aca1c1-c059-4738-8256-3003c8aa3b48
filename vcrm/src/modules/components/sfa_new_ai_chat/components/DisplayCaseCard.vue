<template>
    <div class="display-sfa-business-info">
        <MarkdownRender :content="content" />
    </div>
</template>

<script>

export default {
    name: 'DisplayCaseCard',
    components: { 
        MarkdownRender: async () => (await Fx.getBizComponent('paasbiz', 'MarkdownRender'))(),
    },
    props: {
        agentData: {
            type: Object,
            default: () => ({})
        },
        message: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        content() {
            return this.message.content;
        }
    }
}
</script>
<style lang="less" scoped>
.display-sfa-business-info {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    max-height: 152px;
    overflow: hidden;
    padding: 12px;
    border-radius: var(--8, 8px);
    border: 2px solid var(--color-special01);
    background: var(--color-neutrals01);
}
</style>