
/**
 *  统一动作常量
 */
export const ACTIONS = Object.freeze({
  // 流程控制
  NEXT_STEP   : 'wizard:next-step',
  PREV_STEP   : 'wizard:prev-step',
  CREATE_NEW  : 'wizard:create-new',
  GOTO_STEP   : 'wizard:goto-step',
  SEND_AIMessage: 'SendAIMessage',
});

/**
 * 流式消息状态常量
 */
export const STREAM_STATUS = Object.freeze({
  START: 'start',
  DATA: 'data',
  FINISH: 'finish',
  ERROR: 'error'
});

/**
 * 获取当前步骤
 */
export function getCurrentStep(state) {
  return state?.mode?.steps?.[state?.stepIndex] || null;
}

/**
 * 合并/设置步骤状态
 */
export function setStepStatus(state, stepId, patch) {
  const prev = state.stepStatus[stepId] || {};
  Vue.set(state.stepStatus, stepId, {
    ...prev,
    ...patch
  });
}

/**
 * 确保target[key]是响应式数组
 */
export function ensureArrayReactive(target, key) {
  if (!Array.isArray(target[key])) {
    Vue.set(target, key, []);
  }
  return target[key];
}

/**
 * 把分段内容以响应式方式追加到stepOutput
 */
export function appendPartsReactive(state, stepId, parts) {
  if (!parts || !parts.length) return;
  const arr = ensureArrayReactive(state.stepOutput, stepId);
  arr.push(...parts);
}


/**
 * 创建状态对象
 * 用 Vue.observable 做一个迷你 "store"
 */
export function createWizardState() {
  return Vue.observable({
    mode: null,        // 当前所选模式
    stepIndex: 0,      // 当前步骤索引
    stepOutput: {},    // 当前步骤输出
    stepStatus: {},    // 当前步骤状态
    data: {},          // 过程产生的业务数据
  });
}

/**
 * 重置状态
 */
export function resetWizard(state) {
  if (!state) return;
  state.mode = null;
  state.stepIndex = 0;  
  state.stepOutput = {};
  state.stepStatus = {};
  state.data = {};
}

/**
 * ====================
 * 5⃣ 统一导出对象
 * ====================
 */
export default {
  // 常量
  ACTIONS,
  STREAM_STATUS,
  
  // 状态管理
  createWizardState,
  resetWizard,
  getCurrentStep,
  setStepStatus,
  
  // 响应式工具
  ensureArrayReactive,
  appendPartsReactive,
};

