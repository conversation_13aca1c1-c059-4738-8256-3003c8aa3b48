<template>
    <div class="sfa-ai-wizard-wrap">
        <!-- ====== 模式卡片 ====== -->
        <template v-if="!currMode">
            <div class="wizard-mode-wrapper">
                <h2 class="wizard-title">{{ title }}</h2>
                <div class="mode-list">
                    <div
                        v-for="(m, i) in modes"
                        :key="m.id"
                        class="mode-card"
                        :style="cardStyle(i, m)"
                        @click="selectMode(m)"
                    >
                        <div class="mode-card_body">
                            <h3 class="mode-card_name">{{ m.title }}</h3>
                            <p class="mode-card_desc">{{ m.label }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <!-- ====== 步骤向导 ====== -->
        <template v-else>
            <div class="wizard-steps-wrapper">
                <StepItem
                    v-for="(step, idx) in currSteps"
                    :key="step.apiName"
                    :comp="resolve(step.comp)"
                    :compProps="{
                        ...step.props,
                        apiName: step.apiName,
                    }"
                    :shellProps="{
                        ...step
                    }"
                    :active="idx === currIndex"
                    :done="idx < currIndex"
                    :last="idx === currSteps.length - 1"
                    :disabled ="idx != currIndex"
                    v-show="idx <= currIndex"
                    @action="handleAction"
                    @valueChange="handleValueChange"
                />
            </div>
        </template>
    </div>
</template>
<script>
import WizardCore from "./core";
import components from "./comps";

export default {
    name: "Wizard",
    components,
    props: {
        schema: { type: Object, required: true },
        componentsMap: {
            // 业务方自定义组件优先查找
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            wizardState: WizardCore.createWizardState(),
        };
    },
    provide() {
        return {
            wizardState: this.wizardState,
        };
    },

    computed: {
        title() {
            return this.schema?.wizard?.title || "";
        },
        modes() {
            return this.schema?.wizard?.modes || [];
        },
        currSteps() {
            return this.currMode?.steps || [];
        },
        currMode() {
            return this.wizardState.mode;
        },
        currIndex() {
            return this.wizardState.stepIndex;
        },
        curStep() {
            return this.currSteps[this.wizardState.stepIndex] || null;
        },
    },
    watch: {
        schema: {
            handler() {
                // 当 schema 发生变化时，重置向导，避免索引越界或旧数据残留
                WizardCore.resetWizard(this.wizardState);
            },
            deep: false,
        },
    },
    methods: {
        /** ---------- 对外可用方法 ---------- */
        reset() {
            WizardCore.resetWizard(this.wizardState);
        },

        handleSendStatus(status, data) {
            const step = this.curStep;
            if (!step) {
                return;
            }
            //合并状态,后产生的信息覆盖之前的
            WizardCore.setStepStatus(this.wizardState, step.id, {
                status,
                data,
            });

            //流式分段数据，每次把 stream 分段追加到对应 stepOutput
            if (status === WizardCore.STREAM_STATUS.DATA) {
                const parts = data?.artifact?.parts || [];
                WizardCore.appendPartsReactive(
                    this.wizardState,
                    step.id,
                    parts
                );
            }
        },
        /** ---------- 内部 ---------- */
        cardStyle(idx, mode) {
            const url = require("@assets/images/aishell/ai-shell-mode-card-" +
                (idx % 3) +
                ".png");
            return {
                backgroundImage: `url(${url})`,
                backgroundPosition: "right center",
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
            };
        },

        resolve(name) {
            return (
                this.componentsMap[name] || // ★ 业务侧
                this.$options.components[name] || // 本文件 local 注册
                this.$root.$options.components[name] || // 全局
                name // 兜底：字符串名由 Vue 自行解析
            );
        },

        selectMode(mode) {
            this.wizardState.mode = mode;
            this.wizardState.stepIndex = 0;
            this.$emit("action", {
                meta: {
                    mode: mode.id,
                },
                action: "mode-change",
            });
        },

        /** 把子组件上浮的事件转成 dispatch */
        handleAction(evt) {
            this.dispatch(evt);
            this.$emit("action", evt);
        },

        handleValueChange(evt) {
            this.$emit("valueChange", evt);
        },

        dispatch(evt) {
            const { meta, btn = {} } = evt;
            const action = evt.action || btn.action;

            switch (action) {
                case WizardCore.ACTIONS.NEXT_STEP:
                    this.goToNextStep();
                    break;
                case WizardCore.ACTIONS.PREV_STEP:
                    this.wizardState.stepIndex = Math.max(
                        0,
                        this.wizardState.stepIndex - 1
                    );
                    break;
                case WizardCore.ACTIONS.GOTO_STEP:
                    this.wizardState.stepIndex = +meta.index || 0;
                    break;
                case WizardCore.ACTIONS.CREATE_NEW:
                    this.reset();
                    break;
                case WizardCore.ACTIONS.SEND_AIMessage:
                    this.setMessage(evt);
                    break;
                default:
                    console.log("[wizard-shell] unknown action", action);
            }
        },

        goToNextStep() {
            if (!this.isLastStep()) {
                this.wizardState.stepIndex++;
            } else {
                // 如果是最后步骤，直接关闭引导组件，走chat组件
                this.$emit("close-mask");
                return;
            }
        },

        /** 使用shareGpt发送消息 */
        async setMessage(action) {
            const bizData = await new Promise((resolve) => {
                this.$emit("action", {
                    ...action,
                    action: "beforeSendMessage",
                    done: resolve, // 业务侧调用 done() 回传
                });
            });
            if (bizData) {
                const content = [
                    {
                        type: "text",
                        text: bizData,
                    },
                ];
                this.$emit("send-message", { content });
                this.goToNextStep();
            }
        },

        isLastStep() {
            return this.wizardState.stepIndex === this.currSteps.length - 1;
        },
    },
};
</script>
<style lang="less" scoped>
.sfa-ai-wizard-wrap {
    height: 100%;
    padding: 12px;

    .wizard-mode-wrapper {
        width: 100%;
        height: 100%;
        padding-top: 88px;
    }

    .wizard-title {
        font-size: 22px;
        color: var(--color-neutrals19);
        font-weight: 500;
        text-align: center;
        margin-bottom: 36px;
    }

    .mode-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }

    .mode-card {
        position: relative;
        flex: 1 1 calc((100% - 32px) / 3);
        box-sizing: border-box;
        min-width: 345px;
        height: 120px;
        padding: 20px 90px 20px 12px;
        line-height: 18px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.2s ease;
        background: #fff;
    }

    .mode-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .mode-card_name {
        font-size: 18px;
        font-weight: 700;
        line-height: 28px;
        margin-bottom: 8px;
        color: var(--color-neutrals19);
    }

    .mode-card__desc {
        font-size: 13px;
        line-height: 18;
        color: var(--color-neutrals19);
    }

    .wizard-steps-wrapper {
        max-width: 836px;
        height: auto;
        padding: 16px;
        margin: 0 auto;
        background: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: var(--border-radius, 8px);
        border: 1px solid var(--color-neutrals05);
        background: #fff;
    }
}
</style>
