<template>
    <StepShell 
        :active="active" 
        :done="done" 
        :last="last" 
        :status="wizardState.stepStatus[shellProps.id]"
        :isStatusComp="shellProps.isStatusComp"
        :defaultAction="shellProps.defaultAction"
        v-bind="shellProps"
        @action="handleAction"
        >
        <template #content>
            <component
                :is="comp"
                v-bind="compProps"
                :disabled="disabled"
                @action="$emit('action', $event)"
                @valueChange="$emit('valueChange', $event)"
                @next="$emit('next')"
            />
        </template>
    </StepShell>
</template>
  
<script>
import StepShell from "./stepshell.vue";

export default {
    name: "StepItem",
    inject: ['wizardState'],
    components: { StepShell },
    props: {
        comp: [String, Object],
        compProps: { type: Object, default: () => ({}) },
        shellProps: { type: Object, default: () => ({}) },
        active: <PERSON><PERSON><PERSON>,
        done: <PERSON><PERSON><PERSON>,
        last: <PERSON><PERSON><PERSON>,
        disabled:<PERSON><PERSON><PERSON>,
    },
    methods: {
        handleAction(evt) {
            this.$emit('action', evt);
        }
    }
};
</script>