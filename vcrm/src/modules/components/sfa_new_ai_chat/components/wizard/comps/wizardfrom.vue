<template>
    <div class="wizard-form">
        <p class="wizard-form__title">{{ title }}</p>
        <div class="wizard-form__section">
            <div v-for="(field, idx) in fields" :key="field.apiName || idx">
                <component
                    :is="getFieldComp(field)"
                    v-model="form[field.apiName]"
                    :field="field"
                    :disabled="disabled"
                    @change="onFieldChange(field, $event)"
                    ref="fieldRefs"
                    :ref-in-for="true"
                />
            </div>
        </div>

        <div class="wizard-form__btns">
            <fx-button
                v-for="btn in buttons"
                :key="btn.id"
                :disabled="disabled"
                plain
                :class="`wizard-btn wizard-btn__${btn.style}`"
                @click="handleClick(btn)"
            >
                <i :class="btn.icon"></i>
                <span class="wizard-btn__label">{{ btn.label }}</span>
            </fx-button>
        </div>
    </div>
</template>
<script>
import { registry as fieldRegistry } from "./fields";
export default {
    name: "WizardForm",
    inject: ["wizardState"],
    props: {
        apiName: String,
        title: String,
        fields: { type: Array, default: () => [] },
        buttons: { type: Array, default: () => [] },
        disabled: Boolean,
    },

    computed: {
        form: {
            get() {
                if (!this.wizardState.data) {
                    this.$set(this.wizardState, "data", {});
                }
                if (!this.wizardState.data[this.apiName]) {
                    this.$set(this.wizardState.data, this.apiName, {});
                }
                return this.wizardState.data[this.apiName];
            },
            set(val) {
                if (!this.wizardState.data) {
                    this.$set(this.wizardState, "data", {});
                }
                if (!this.wizardState.data[this.apiName]) {
                    this.$set(this.wizardState.data, this.apiName, {});
                }
                this.$set(this.wizardState.data, this.apiName, val || {});
            },
        },
    },
    created() {
        this.ensureFieldKeys();
    },
    methods: {
        ensureFieldKeys() {
            const data = this.form; // proxies wizardState.data
            (this.fields || []).forEach((f) => {
                if (!Object.prototype.hasOwnProperty.call(data, f.apiName)) {
                    const def =
                        f.default !== undefined
                            ? f.default
                            : f.type === "upload" || f.multiple
                            ? []
                            : "";
                    this.$set(data, f.apiName, def);
                }
            });
        },

        getFieldComp(field) {
            return fieldRegistry[field.type] || this.UnknownField;
        },

        UnknownField: {
            functional: true,
            render(h, ctx) {
                return h("div", { style: { color: "#f56c6c" } }, [
                    `未识别的字段类型: ${
                        ctx.props.field && ctx.props.field.type
                    }`,
                ]);
            },
        },

        onFieldChange(field, value) {
            if (field?.apiName) {
                this.$set(this.form, field.apiName, value);
            }
        },

        async validateAll() {
            const comps = Array.isArray(this.$refs.fieldRefs)
                ? this.$refs.fieldRefs
                : [];
            const errors = [];
            for (const comp of comps) {
                if (!comp) continue;

                let result;
                try {
                    if (typeof comp.validate === "function") {
                        // 允许字段组件实现自定义（可异步）校验
                        result = await comp.validate();
                    } else {
                        // 兜底：根据字段 required 做同步必填校验
                        const fld =
                            comp.field || (comp.$props && comp.$props.field);
                        if (fld && fld.required) {
                            const v = this.form[fld.apiName];
                            const empty = Array.isArray(v)
                                ? v.length === 0
                                : v === "" || v === null || v === undefined;
                            result = {
                                valid: !empty,
                                message: `${fld.title || fld.apiName}必填`,
                            };
                        } else {
                            result = { valid: true };
                        }
                    }
                } catch (e) {
                    result = {
                        valid: false,
                        message: e && e.message ? e.message : "校验失败",
                    };
                }

                if (!result || result.valid === false) {
                    errors.push({
                        comp,
                        message: (result && result.message) || "校验失败",
                    });
                }
            }

            if (errors.length) {
                const first = errors[0];
                if (
                    this.$message &&
                    typeof this.$message.warning === "function"
                ) {
                    this.$message.warning(first.message);
                }
                this.$nextTick(() => {
                    if (first.comp) {
                        if (typeof first.comp.focus === "function") {
                            first.comp.focus();
                        }
                        const el = first.comp.$el;
                        if (el && el.scrollIntoView) {
                            el.scrollIntoView({
                                behavior: "smooth",
                                block: "center",
                            });
                        }
                    }
                });
                return { valid: false, errors };
            }
            return { valid: true };
        },

        async handleClick(btn) {
            if (this.disabled) return;
            const res = await this.validateAll();
            if (!res || res.valid === false) return;

            this.$emit("action", {
                meta: {
                    comp: "wizard-form",
                    apiName: this.apiName,
                    value: { ...this.form },
                },
                btn,
            });
        },
    },
};
</script>
<style lang="less" scoped>
.wizard-form {
    .wizard-form__title {
        margin-bottom: 8px;
        color: var(--color-neutrals19);
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;
    }
    .wizard-form__btns {
        margin-top: 8px;

        .wizard-btn {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background: none;
            padding: 0px 12px;
            height: 32px;
            line-height: 32px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            .wizard-btn__label {
                position: relative;
                z-index: 1;
                display: inline-block;
            }
            i {
                display: inline-block;
                font-size: 16px;
                line-height: 16px;
                position: relative;
                z-index: 1;
            }
            &[disabled],
            &.is-disabled {
                cursor: not-allowed;
            }
        }

        .wizard-btn__light {
            border: 1px solid var(--color-neutrals05);
            transition: background-color 0.2s ease, box-shadow 0.2s ease,
                border-color 0.2s ease;
            &:hover {
                background-color: #f4f4fd;
            }
            &:active {
                background-color: #e9e7fb;
            }

            .wizard-btn__label {
                background: linear-gradient(
                    90deg,
                    #0099ff,
                    #a033ff,
                    #ff5280,
                    #ff7061
                );
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            i {
                background: linear-gradient(
                    90deg,
                    #0099ff,
                    #a033ff,
                    #ff5280,
                    #ff7061
                );
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            &[disabled],
            &.is-disabled {
                border: 1px solid var(--color-neutrals05);
                background: #f4f4fd;

                .wizard-btn__label, i {
                    background: none !important;
                    -webkit-background-clip: initial;
                    background-clip: border-box;
                    -webkit-text-fill-color: currentColor;
                    color: var(--color-neutrals07) !important;
                }
            }
        }

        .wizard-btn__dark {
            border: none;
            background: var(
                --Liner2-,
                linear-gradient(90deg, #4f68ff -0.11%, #8048ff 100.09%)
            );
            color: #fff !important;
            transition: background-color 0.2s ease, box-shadow 0.2s ease,
                border-color 0.2s ease;
                i::before,
  [class^="fx-icon-"]::before,
  [class*=" fx-icon-"]::before {
    color: #fff !important;
  }

            &:hover {
                background: linear-gradient(
                    90deg,
                    rgba(79, 104, 255, 0.8) -0.11%,
                    rgba(128, 72, 255, 0.8) 100.09%
                );   
            }

            &[disabled], &.is-disabled {
                background: linear-gradient(
                    90deg,
                    rgba(79, 104, 255, 0.5) -0.11%,
                    rgba(128, 72, 255, 0.5) 100.09%
                );

                
            }
            &:hover, &[disabled], &.is-disabled {
                .wizard-btn__label,i {
                    color: var(--color-neutrals01);
                }
            }
        }
    }
}
</style>