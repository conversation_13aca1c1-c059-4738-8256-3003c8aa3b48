<!-- 基于业务信息生成 -->
<template>
    <div class="wizard-textarea">
        <div class="wizard-textarea__content">
            <fx-input
                class="wizard-textarea__input"
                type="textarea"
                size="small"
                :rows="2"
                placeholder="请输入内容"
                v-model="inner"
                @change="onChange"
            >
            </fx-input>
        </div>
    </div>
</template>
<script>
export default {
    name: "Generate",
    props: {
        field: { type: Object, required: true },
        value: String,
    },
    data() {
        return {
            inner: this.value,
        };
    },
    watch: {
        value(v) {
            this.inner = v;
        },
    },
    methods: {
        validate() {
            if (!this.field.required) return { valid: true };
            return {
                valid: this.inner !== "" && this.inner != null,
                message: `${this.field.title}必填`,
            };
        },
        onChange(value) {
            this.inner = value;
            this.$emit("change", this.inner);
        },

    },
};
</script>
<style lang="less" scoped>
.wizard-textarea__input /deep/ .el-textarea__inner{
        //background: linear-gradient(100deg, #09f, #a033ff 50%, #ff5280 75%, #ff7061);
        background:
      /* inside area */        linear-gradient(#fff, #fff) padding-box,
      /* border gradient */    linear-gradient(
                                100deg,
                                #0099ff   0%,
                                #a033ff  50%,
                                #ff5280  75%,
                                #ff7061 100%
                              ) border-box;
                              border: 2px solid transparent;
                              background-clip: padding-box, border-box;

        transition: box-shadow 0.2s ease;
}
</style>
