<template>
    <div class="wizard-select">
        <p class="title">{{ title }}</p>
        <fx-select
            ref="select"
            class="wizard-select__selectInput"
            v-model="inner"
            :options="options"
            :dropdownMaxWidth="500"
            @change="onChange"
            filterable
        >
        </fx-select>
    </div>
</template>
<script>
export default {
    name: "Select",
    props: {
        field: { type: Object, required: true },
        value: null,
    },
    data() {
        return {
            inner: this.value,
            options: [],
        };
    },
    watch: {
        value(v) {
            this.inner = v;
        }
    },
    created() {
        if (Array.isArray(this.field.options) && this.field.options.length) {
            this.options = this.field.options;
            return;
        }
        if (this.field.dataUrl) this.fetchOptions();
    },
    methods: {
        validate() {
            if (!this.field.required) return { valid: true };
            return {
                valid: this.inner !== "" && this.inner != null,
                message: `${this.field.title}必填`,
            };
        },
        onChange(value) {
            this.inner = value;
            this.$emit("change", this.inner);
        },

        async fetchOptions() {
            const res = await this.requestData();
            const { value = "value", label = "label" } = this.field.optionFieldMap;
            this.options = (res.data || []).map((item) => {
                return {
                    value: item[value],
                    label: item[label],
                };
            });
        },
        requestData() {
            const me = this;
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: me.field.dataUrl,
                    success: function (res) {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            reject(
                                new Error(
                                    (res.Result && res.Result.FailureMessage) ||
                                        "API returned error"
                                )
                            );
                        }
                    },
                    error: function (err) {
                        reject(err);
                    },
                });
            });
        },
    },
};
</script>
<style lang="less" scoped>
.wizard-select {
    .wizard-select__title {
        color: var(--color-neutrals19);
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
    }

    .wizard-select__selectInput {
        width: 454px;
    }

}
</style>
