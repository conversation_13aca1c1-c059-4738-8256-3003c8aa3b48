<!-- 基于业务信息生成 -->
<template>
    <div class="wizard-text">
        <!-- 说明文字 / 列表 -->
        <div class="wizard-text__paragraph" v-for="(item, index) in field.content||[]" :key="index">
            <div v-if="item.title" class="wizard-info__paragraph-title">
                {{ item.title }}
            </div>
            <ul v-if="item.children" class="wizard-text__list">
                <li v-for="child in item.children" :key="child">{{ child }}</li>
            </ul>
            <p v-else class="wizard-text__text">{{ item }}</p>
        </div>
    </div>
</template>
<script>
export default {
    name: "Text",
    props: {
        field: { type: Object, required: true },
    },
  
};
</script>
<style scoped lang="less">
.wizard-text {

    // 正文
    &__paragraph {
        margin-top: 8px;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        color: var(--color-neutrals19);
    }

    &__list {
        padding-left: 16px;

        li {
            list-style: disc;
            line-height: 22px;
        }
    }

    
}
</style>