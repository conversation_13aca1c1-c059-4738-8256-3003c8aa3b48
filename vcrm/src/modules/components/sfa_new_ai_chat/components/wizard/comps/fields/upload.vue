<template>
    <div class="wizard-upload">
        <fx-upload
            :list-type="(field.props && field.props.listType) || 'picture-card'"
            :file-list="inner"
            :disabled="disabled"
            :on-success="handleSuccess"
        >
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }">
                <p data-title="name">{{ file.name }}</p>
                <!-- <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" /> -->
                <span class="el-upload-list__item-actions">
                    <span
                        class="el-upload-list__item-delete"
                        @click="remove(file)"
                    >
                        <i class="el-icon-delete"></i>
                    </span>
                </span>
            </div>
        </fx-upload>
        <fx-dialog :visible.sync="dlgVisible">
            <img width="100%" :src="dlgUrl" alt="" />
        </fx-dialog>
    </div>
</template>
<script>
export default {
    name: "Upload",
    props: {
        field: { type: Object, required: true },
        value: { default: () => [] },
        disabled: Boolean,
    },

    data() {
        return {
            inner: Array.isArray(this.value) ? [...this.value] : [],
            dlgVisible: false,
            dlgUrl: "",
        };
    },
    watch: {
        value(v) {
            this.inner = Array.isArray(v) ? [...v] : [];
        },
    },
    methods: {
        handleSuccess(response, file, fileList) {
            const item = {
                path: response.TempFileName,
                filename: file.name,
                size: file.size,
                ext: response.FileExtension,
                url: Fx.file.getFilePath(response.TempFileName),
                status: file.status,
                uid: file.uid,
            };

            this.inner.push(item);
            this.$emit("change", this.inner);
        },

        remove(file) {
            const idx = this.inner.findIndex(x => x.uid === file.uid);
            if (idx > -1) this.inner.splice(idx, 1);
            this.$emit("change", this.inner);
        },

        focus() {
            // 尝试把焦点放到上传区域，便于用户修正
            const root = this.$el;
            if (root) {
                const el = root.querySelector('.el-upload, .el-upload__input, button, input');
                if (el && typeof el.focus === 'function') {
                    el.focus();
                }
            }
        },

        getValue() {
            return this.inner;
        },

        validate(){
            if (!this.field.required) return { valid: true };
            return {
                valid: Array.isArray(this.inner) && this.inner.length > 0,
                message: `${this.field.title}必传`,
            };
        },
    },
};
</script>
<style lang="less" scoped>

</style>