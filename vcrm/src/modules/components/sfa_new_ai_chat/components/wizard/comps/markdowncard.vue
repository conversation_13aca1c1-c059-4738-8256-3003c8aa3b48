<template>
    <div class="markdown-card" @click="handleClick">
        <MarkdownRender :content="content" />
    </div>
</template>

<script>
import util from '@/modules/common/util';
export default {
    name: 'MarkDownCard',
    inject: ['wizardState'],
    components: {
        MarkdownRender: async () => (await Fx.getBizComponent('paasbiz', 'MarkdownRender'))(),
    },
    props: {
        id: String,
    },
    data() {
        return {
            content: '',
            dataId: null
        }
    },

    computed: {
        stepStatus() {
            return this.wizardState.stepStatus?.[this.id];
        }
    },
    watch: {
        stepStatus: {
            deep: true,
            immediate: true,      
            handler(val) {
                if (val.status === 'finish') {
                    this.updateFromOutput();
                }
            }
        }
    },
    methods: {
        updateFromOutput() {
            const parts = this.wizardState.stepOutput?.[this.id] || [];
            const mark = parts.find(p => p.type === 'case_card');
            const info = parts.find(p => p.type === 'data_info');
            this.content = mark?.data?.content || '';
            this.dataId = info?.data?.dataId || null;
        },
        handleClick() {
            if (this.dataId) {
                util.showMarkDownDetail({
                    content: this.content,
                    title: "业务前情",
                    dataId: this.dataId
                });
            }
        }
    },


}
</script>
<style lang="less" scoped>
.markdown-card {
    width: 100%;
    height: auto;
    box-sizing: border-box;
    max-height: 152px;
    overflow: hidden;
    padding: 12px;
    border-radius: var(--8, 8px);
    border: 2px solid var(--color-special01);
    background: var(--color-neutrals01);
}
</style>