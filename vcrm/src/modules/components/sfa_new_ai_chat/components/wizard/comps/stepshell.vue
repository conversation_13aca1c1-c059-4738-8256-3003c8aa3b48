<template>
    <div class="step-shell" :class="{ active, done }">
        <!-- 时间轴圆点 -->
        <span class="dot" :style="dotStyle">
            <i v-if="icon" :class="icon"></i>
            <i v-else-if="isLoading" class="el-icon-loading"></i>
            <i v-else-if="isFinish" class="el-icon-check"></i>
            <img v-else :src="dotSrc" alt="">
        </span>
        <!-- 竖线 -->
        <span class="line" v-if="!last"></span>

        <!-- 标题栏 -->
        <div class="head">
            <slot name="title">
                <p class="title">{{ title }}<span v-if="isStatusComp">{{ getStatusStr() }}</span></p>
            </slot>
            <span class="time" v-if="time">{{ time }}</span>
        </div>

        <!-- 真正业务内容 -->
        <slot v-if="canInit" name="content" />
    </div>
</template>

<script>
export default {
    name: "StepShell",
    inject: ['wizardState'],
    props: {
        status: Object,
        active: Boolean,
        done: <PERSON><PERSON><PERSON>,
        last: <PERSON><PERSON>an,
        title: String,
        icon: String,
        defaultAction: String,
        isStatusComp: Boolean,
        time: {
            type: String,
            default: () => {
                const d = new Date();
                return `${d.getHours()}:${d
                        .getMinutes()
                        .toString()
                        .padStart(2, "0")}`;
            },
        },
    },
    computed: {
        dotSrc() {
            const icon = this.icon || "sfai/fx_AI.svg";
            const url = require("@assets/images/" + icon);
            return url;
        },
        canInit() {
            return this.isStatusComp && this.status?.status === 'finish' || !this.isStatusComp;
        },
        isLoading() {
            return this.isStatusComp && this.status?.status !== 'finish';
        },
        isFinish() {
            return this.isStatusComp && this.status?.status === 'finish';
        }
    },
    watch: {
        status(val) {
            const isInit = this.isStatusComp && val.status === 'finish' || !this.isStatusComp;
            if (isInit && this.defaultAction) {
                this.$emit('action', { action: this.defaultAction });
            }
        }
    },
    methods: {
        getStatusStr() {
            if (this.status?.data?.content) {
                return this.status.data.content + "...";
            } else {
                return this.status?.status == "finish" ? "已生成" : "生成中...";
            }

        }
    }
};
</script>

<style scoped lang="less">
.step-shell {
    width: 100%;
    box-sizing: border-box;
    position: relative;
    padding: 12px 0 12px 32px;
    /* 左侧预留给时间轴 */
    margin-bottom: 12px;
    transition: border-color 0.2s ease;

    /* 圆点 */
    .dot {
        position: absolute;
        top: 10px;
        left: 0;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #f8f9ff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 10px;
        z-index: 1;
        text-align: center;

        i {
            font-size: 16px;
            background: linear-gradient(90deg, #4f68ff 0%, #8048ff 100%);
            -webkit-background-clip: text;
            /* 只保留文字部分的背景 */
            -webkit-text-fill-color: transparent;
            /* 文字本色设为透明 */
            /* 如果在 Firefox 需同时加上： */
            background-clip: text;
            color: transparent;
        }
    }

    /* 竖线：到下一卡片底部 − 24px */
    .line {
        position: absolute;
        top: 34px;
        /* 12px 下移 = dot.height + 间距 */
        left: 9px;
        /* dot.left + dot.width/2 − 1px */
        bottom: -24px;
        /* 留出卡片间距 */
        width: 2px;
        background: var(--color-special01);
    }

    /* 标题栏 */
    .head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .title {
            color: var(--color-neutrals19);
            font-size: 13px;
            font-weight: 500;
            line-height: 18px;
        }

        .time {
            color: var(--color-neutrals11);
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            /* 150% */
        }
    }
}
</style>
