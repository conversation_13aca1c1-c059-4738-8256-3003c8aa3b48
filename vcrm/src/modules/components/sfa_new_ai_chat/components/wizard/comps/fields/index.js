// WizardDynamicForm/fields/index.js

// 内置类型的懒加载（会被 webpack 拆分为独立 chunk）
export const registry = {
    text:   () => import( './text.vue'),
    select: () => import( './select.vue'),
    upload: () => import( './upload.vue'),
    textarea: () => import( './textarea.vue'),
  };
  
  // 允许业务侧动态注入新的字段类型：WizardDynamicForm.register('xxx', CompOrLoader)
  export function register(type, loaderOrComponent) {
    registry[type] = loaderOrComponent;
  }
