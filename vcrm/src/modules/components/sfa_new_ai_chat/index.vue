<template>
  <div class="sfa-new-ai-chat-container" :style="{ height: computedHeight + 'px' }">
    <AgentChat
        class="agent-chat"
        ref="agentChat"
        :api-name="agentApiName"
        :session-id="getBizSessionId()"
        :variables="cVariables"
        :showMask="showMask"
        :key="getBizSessionId()"
        :businessName="businessName"
        v-bind="$attrs"
        v-on="$listeners"
        @sendStatus="handleSendStatus"
    >
        <template #mask="{ agentData, message }">
            <slot name="mask" :agentData="agentData" :message="message">
                <component
                    :is="maskType"
                    :schema="schema"
                    :agentData="agentData"
                    ref="maskRef"
                    @send-message="setMessage"
                    @close-mask="handleCloseMask"
                    @action="forwardAction"/>
            </slot>
        </template>
        <template #welcome="{ agentData, message }">
            <slot name="welcome" :agentData="agentData" :message="message">
                <component :is="welcomeType" :agentData="agentData" @send-message="setMessage" />
            </slot>
        </template>
        <template #displayData="{ agentData, message, type }">
            <slot name="displayData" :agentData="agentData" :message="message" :type="type">
                <component :is="formatDisplayComponentName(type)" :agentData="agentData" :message="message"  @send-message="setMessage" />
            </slot>
        </template>
    </AgentChat>
  </div>
</template>

<script>
import components from './components';

// 业务开发的卡片组件需要同时提供给全局gpt注册，这里统一导出
export const getComponents = (type) => {
    return components[type];
}

export default {
    name: 'SfaNewAiChat',
    props: {
        showMask: {
            type: Boolean,
            default: false
        },
        maskType: {
            type: String,
            default: ''
        },
        welcomeType: {
            type: String,
            default: ''
        },
        // agent业务埋点
        businessName: {
            type: String,
            default: 'sfa_ai'
        },
        agentApiName: {
            type: String,
            default: ''
        },
        sessionId: {
            type: String,
            default: ''
        },
        sessionIdVariables: {
            type: Array,
            default: () => ([])
        },
        variables: {
            type: Object,
            default: () => ({})
        },
        // 引用来源，用于区分不同的引用来源：详情页、其他AI应用等，方便后期埋点等
        from: {
            type: String,
            required: true
        },
        schema: {
            type: Object,
            default: () => ({})
        },
        computedHeight: {
            type: Number,
            default: 800
        }
    },
    components: {
        ...components,
        AgentChat: () =>
            Fx.getBizComponent("paasdev", "AgentChat").then((res) => res())
    },
    data() {
        return {
        };
    },
    computed: {
        cVariables() {
            return Object.entries(this.variables)
                .map(([name, value]) => ({
                    name,
                    value
                }));
        },
    },
    created() {
        console.log('SfaNewAiChat created');
    },
    methods: {
        setMessage(msg) {
            this.$refs.agentChat.sendMessage(msg);
        },
        getBizSessionId() {
            if (this.sessionId) {
                return this.sessionId;
            }
            const { enterpriseAccount, employeeID } = FS.contacts.getCurrentEmployee() || {}
            const isCross = this.isCross();
            // 下游取ERUpstreamEa
            const ea = isCross ? $.cookie('ERUpstreamEa') : enterpriseAccount;
            // 下游取EROuterUid
            const empId = isCross ? $.cookie('EROuterUid') : employeeID;
            const _sessionId = [ea, empId, this.agentApiName, this.from, ...this.sessionIdVariables].filter(Boolean).join('_');
            console.log('_sessionId', _sessionId);
            return _sessionId;
        },
        // 是否下游
        isCross() {
            return window?.PAAS?.app?.isCross();
        },
        // 根据type转换为相应组件名，type为sfa_custome_type，则组件名称为DisplaySfaCustomeType
        formatDisplayComponentName(type) {
            if (!type) return '';
            
            return 'Display' + type
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join('');
        },
        //处理流式数据
        handleSendStatus(status,data) {
            if(this.$refs.maskRef){
                this.$refs.maskRef.handleSendStatus(status,data);
            }
            this.$emit('valueChange',{
                status,
                data
            })
        },
        /*   欢迎卡片组件抛出事件   */
        forwardAction(evt) {
            this.$emit('action', evt);
        },
        handleCloseMask() {
            this.showMask = false;
            this.$emit('valueChange', {
                name: 'SfaNewAiChat',
                key: 'showMask',
                value: false
            });   
            
        },
        getComponent(type) {
            return components.getComponent(type);
        }
    }
};
</script>

<style lang="less">
.sfa-new-ai-chat-container {
    width: 100%;
    height: 800px;

    .agent-chat {
        width: 100%;
        height: 100%;
    }
}
</style>
