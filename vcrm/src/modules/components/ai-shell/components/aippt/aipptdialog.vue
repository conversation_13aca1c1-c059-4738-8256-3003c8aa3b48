<template>
    <fx-dialog :visible.sync="dialogVisible" fullscreen appendToBody hasScroll @closed="onClosed">
        <div ref="wrap" class="ppt-wrap"></div>
    </fx-dialog>
</template>
<script>
import { loadAipptSdk } from '@common/loadaipptsdk.js';
export default {
    name: 'AipptDialog',
    inject: ['bizState'],
    data() {
        return {
            dialogVisible: false,
            log: {},
            pptId:null
        }
    },
    methods: {
        /** 供父组件调用 */
        createPpt(content, cb) {
            this.dialogVisible = true;
            this.handleCreate(this.$refs.wrap, content, (evt, data) => {
                cb && cb(evt, data);
                this.$emit('aippt-msg', { evt, data })
            });
        },

        editPpt(cb) {
            this.dialogVisible = true;
            this.handleEdit(this.$refs.wrap, (evt, data) => {
                cb && cb(evt, data);
                this.$emit('aippt-msg', { evt, data })
            });
        },

        close() {
            this.dialogVisible = false
            this.closePpt()
        },

        /************** AiPPt相关内部事件 **************/
        async handleCreate(containerEl, content, onMessageCb = () => { }) {
            await loadAipptSdk();
            const codeResult = await this.requestAiPptAuth(); //{ "code": 0, "data": { "api_key": "682acdd82f289", "uid": "fs", "code": "10e9637ab1b6835120d5f95efae7f3f6", "time_expire": 86400 }, "msg": "ok" }
            const { apiKey, token: code, channel } = codeResult?.data ?? {};
            if (!apiKey || !channel || !code) {
                throw new Error('AI-PPT 响应缺失关键信息');
            }
            this.log = this.generateBaseLogInfo();
            const me = this;
            try {
                await window.AipptIframe.show({
                    appkey: apiKey,
                    channel,
                    code,
                    container: containerEl,
                    editorModel: true,
                    options: {
                        fc_plate: ['20054', '20055', '20056', '20058', '2011', '2014', '2015', '2016', '2024'],
                        download_mode: 2,
                        scene_auth: false,
                        custom_generate: {
                            type: 7,
                            content: content,
                            step: 2
                        }
                    },
                    onMessage(evt, data) {
                        try {
                            console.log('[AiPPT]', evt, data)
                            me.generateLog(evt, data)
                            onMessageCb(evt, data)         // 将事件透传给调用方
                        } catch (e) {
                            // 保证外部回调异常不影响核心流程
                            console.log('AipPT onMessage callback error', e)
                        }
                    }
                })
            } catch (e) {
                CRM.util.alert(e && e.message || 'AipPT onMessage callback error')
                console.log('AipPT onMessage callback error', e)
            }
        },

        async handleEdit(containerEl, onMessageCb) {
            await loadAipptSdk();
            const codeResult = await this.requestAiPptAuth();
            const { apiKey, token: code, channel } = codeResult?.data ?? {};
            if (!apiKey || !channel || !code) {
                throw new Error('AI-PPT 响应缺失关键信息');
            }
            const me = this;
            try {
                await window.AipptIframe.show({
                    appkey: apiKey,
                    channel,
                    code,
                    container: containerEl,
                    editorModel: true,
                    routerOptions: {
                        list: ['generate', 'editor'],
                        editor: {
                            id: me.pptId
                        },
                    },
                    onMessage(evt, data) {
                        try {
                            console.log('[AiPPT]', evt, data)
                            onMessageCb(evt, data)         // 将事件透传给调用方
                        } catch (e) {
                            // 保证外部回调异常不影响核心流程
                            console.log('AipPT onMessage callback error', e)
                        }
                    }
                })
            } catch (e) {
                CRM.util.alert(e && e.message || 'AipPT onMessage callback error')
                console.log('AipPT onMessage callback error', e)
            }
        },

        // 生成基础日志信息
        generateBaseLogInfo() {
            this.pptId = null;
            let item = {
                requestId: CRM.util.uniqueCode(),
                platform: "AiPPT",
                apiEndpoint: "https://co.aippt.cn/jsapi/design/save",
                method: "POST",
                requestTime: new Date().getTime(),
            };
            if (this.bizState?.pptSourceInfo) {
                item = {
                    ...item,
                    ...this.bizState.pptSourceInfo,
                    sourceId: this.bizState[this.bizState.pptSourceInfo.sourceId]
                }
            }
            return item;
        },

        generateLog(evt, data) {
            switch (evt) {
                case "TEMPLATE_SELECTED":
                    this.log.requestParams = {
                        templateId: data.templateId,
                        taskId:data.taskId
                    };
                    break;
                case "GENERATE_PPT_SUCCESS":
                    this.pptId = data.id;
                    // 记录状态码
                    this.log.responseTime = new Date().getTime();
                    this.log.statusCode = 0;
                    this.log.success = true;
                    this.log.responseBody = data;//JSON.stringify(data);
                    this.log.responseMessage = "生成成功";
                    this.log.hasArtifact = true;
                    this.log.artifactType = "ppt_id";
                    this.log.artifactPath = data.id;
                    this.sendAiPptLog();
                    break;
                default:
                    break;
            }
        },

        requestAiPptAuth() {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/ai-ppt-auth/service/generate",
                    success: function (res) {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            reject(new Error(
                                (res.Result && res.Result.FailureMessage) || 'API returned error'
                            ));
                        }
                    },
                    error: function (err) {
                        reject(err);
                    }
                });
            });
        },

        sendAiPptLog() {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/api-log/service/call",
                    data: this.log,
                    success: function (res) {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            reject(new Error(
                                (res.Result && res.Result.FailureMessage) || 'API returned error'
                            ));
                        }
                    },
                    error: function (err) {
                        reject(err);
                    }
                });
            });
        },

        /** Dialog 内部关闭时，同步销毁 iframe */
        onClosed() {
            this.closePpt()                       // 销毁 iframe
        },


        closePpt() {
            window.AipptIframe?.deleteIframe()
        }
    }
}
</script>
<style scoped>
.ppt-wrap {
    width: 100%;
    height: 90vh;
}
</style>