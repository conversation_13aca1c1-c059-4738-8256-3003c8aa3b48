<template>
    <div class="sfa-ai-editor">
        <div class="editor-wrap">
            <Vditor ref="vditor" :height="editorHeight" :content="innerContent" @ready="onVditorReady" :vditorConfig="editorConfig"/>
            <div class="editor-operate">
                <fx-button type="primary" size="small" class="op-btn" @click="submit">保存</fx-button>
                <fx-button size="small" class="op-btn" @click="editPpt" v-if="pptId">编辑ppt</fx-button>
                <fx-button type="primary" size="small" class="op-btn gradient-text" @click="createPpt">
                    <img src="@assets/images/aishell/ai-magic-stick.svg">
                    生成ppt</fx-button>
            </div>
        </div>
        <AipptDialog ref="aipptDialog" @aippt-msg="handleAipptMsg" />
    </div>
</template>
<script>
import Vditor from "@components/vditor/index.vue";
import AipptDialog from '@components/ai-shell/components/aippt/aipptdialog.vue';
export default {
    name: 'EditorWithPpt',
    props: {
        apiName: String,
        height: { type: Number, default: 500 },
        content: { type: String, default: '' },
        watchField: { type: String, default: '' },
        contentSource: { type: String, default: '' },
        fetchApi: { type: Object, default: () => ({}) },
    },
    inject: ['bizState'],
    components: {
        AipptDialog,
        Vditor
    },
    data() {
        return {
            innerContent: this.content || "",
            editorConfig:{
                toolbar: ["headings", "bold", "italic", "list", "ordered-list", "outdent", "indent", "table", "undo", "redo", "export", "outline"],
            },
            editorReady: false,
            dataId: null,
            pptId: null,

        }
    },
    computed: {
        editorHeight() {
            return window.innerHeight - 144;
            // 业务期望：父容器高度 - 54px；此处可按需改造
            // this.$nextTick(() => {
            //     const wrap = this.$el.querySelector('.editor-wrap');
            //     if (wrap) {
            //         return wrap.clientHeight - 54;
            //     }else{
            //         return 500;
            //     }
            // });
        }
    },
    created() {
        /* 动态监听 → 引用变就触发 */
        this.$watch(
            () => this.bizState[this.watchField],
            id => {
                if (id) this.setContentById();
            },
            { immediate: true }
        );
    },
    mounted() {
        this.calcHeight();
        // 监听窗口尺寸变化，动态更新高度
        this._onResize = this.calcHeight.bind(this);
        window.addEventListener('resize', this._onResize);
        // 若外部直接传了 content，优先使用
        if (this.content) {
            this.setValue(this.content);
        } else if (this.contentSource && this.bizState && this.bizState[this.contentSource]) {
            this.setValue(this.bizState[this.contentSource]);
        }
    },
    beforeDestroy() {
        if (this._onResize) window.removeEventListener('resize', this._onResize);
        // if (this.$refs.vditor && typeof this.$refs.vditor.destroy === 'function') {
        //     this.$refs.vditor.destroy();
        // }
    },
    methods: {
        /************ 编辑器相关 ************/
        onVditorReady(){
            this.editorReady = true;
        },
        setValue(content) {
            this.innerContent = content || "";
            this.applyToVditor();
        },
        applyToVditor() {
            if (!this.editorReady) return;
            const ref = this.$refs.vditor;
            if (typeof ref.setValue === 'function') {
                ref.setValue(this.innerContent || '');
            }
        },
        getValue() {
            const content = this.$refs.vditor.getValue();
            return {
                markdownContent: content,
                pptId: this.pptId
            }
        },
        submit() {
            const { markdownContent } = this.getValue();
            this.$emit('action', {
                meta: {
                    comp: "EditorWithPpt",
                    apiName: this.apiName,
                    content: markdownContent
                },
                action: "submit"
            });
        },

        async setContentById() {
            // 1) 有缓存内容优先
            if (this.contentSource && this.bizState && this.bizState[this.contentSource]) {
                this.setValue(this.bizState[this.contentSource]);
                return;
            }
            // 2) 请求接口
            if (this.fetchApi && this.fetchApi.url && Array.isArray(this.fetchApi.argInfo)) {
                const { url, argInfo } = this.fetchApi;
                const args = argInfo.reduce((acc, item) => {
                    acc[item.key] = this.bizState[item.value];
                    return acc;
                }, {});
                try {
                    const result = await this.fetchContent(url, args);
                    const content = result?.data?.content || '';
                    this.setValue(content);
                    if (this.contentSource && this.bizState) this.$set(this.bizState, this.contentSource, content);
                } catch (e) {
                    console.error('[EditorWithPpt] fetch content error:', e);
                    this.setMarkdown('');
                }
            }
        },
        fetchContent(url, args) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url,
                    data: args,
                    success: (res) => {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            reject(res.Result.FailureMessage);
                        }
                    },
                    fail: (err) => {
                        reject(err);
                    }
                })
            })
        },
        /************ ppt相关 ************/
        handleAipptMsg({ evt, data }) {
            if (evt == "GENERATE_PPT_SUCCESS") {
                this.$emit('action', {
                    meta: {
                        comp: "EditorWithPpt",
                        apiName: this.apiName,
                        appId: data.id
                    },
                    action: "createPpt"
                });
            }
        },
        createPpt() {
            const me = this;
            const content = this.$refs.vditor.getValue();
            this.$refs.aipptDialog.createPpt(content, function (evt, data) {
                if (evt === "GENERATE_PPT_SUCCESS") {
                    me.pptId = data.id;
                }
            });

        },
        editPpt() {
            this.$refs.aipptDialog.editPpt();
        },
        calcHeight() {
            // 容器高度 - 54px（操作栏）
            this.$nextTick(() => {
                const wrap = this.$el.querySelector('.editor-wrap');
                if (wrap) {
                    this.editorHeight = wrap.clientHeight - 54;
                }
            });
        },
    }
}
</script>
<style lang="less" scoped>
.sfa-ai-editor {
    height: 100%;
    position: relative;
    background-color: #fff;

    .editor-wrap {
        height: 100%;
    }

    .editor-operate {
        width: 100%;
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 1000;
        background-color: #fff;
        padding: 10px;
        border-top: 1px solid #e0e0e0;
        text-align: right;

        .op-btn {
            display: inline-block
        }

        .gradient-text,
        .gradient-text:hover {
            align-items: center;
            padding: 0px 12px;
            height: 32px;
            line-height: 32px;
            color: #fff;
            transition: all 0.2s;
            border-radius: 8px;
            gap: 4px;
            cursor: pointer;
            background: #fff;
            border: 1px solid var(--color-neutrals05);
            background-image: linear-gradient(90deg, #0099ff, #a033ff, #ff5280, #ff7061);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            img {
                margin: auto;
                vertical-align: middle;
            }
        }

        .gradient-text:hover {
            background: #F4F4FD;
            border: 1px solid var(--color-neutrals05);
            background-image: linear-gradient(90deg, #0099ff, #a033ff, #ff5280, #ff7061);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
</style>