<template>
    <fx-dialog :visible.sync="dialogVisible" width="50%" appendToBody hasScroll sliderPanel :title="title"
        scrollbarScroll="true" @closed="onClosed">
        <div class="editor-detail-container">
            <Vditor
                ref="vditor"
                :height="editorHeight"
                :content="innerContent"
                :vditorConfig="vditorConfig"
                @ready="onVditorReady"
            />
        </div>
    </fx-dialog>
</template>
<script>
import Vditor from "@components/vditor/index.vue";
export default {
    name: "EditorDetail",
    components: {
        Vditor
    },
    props: {
        title: {
            type: String,
            default: () => ""
        },
        dataId:String,
        fetchApi:{
            url:String,
            argInfo:Array
        },
        content:{
            type: String,
            default: () => ""
        }
    },
    data() {
        return {
            dialogVisible: false,
            innerContent: this.content || "",
            expandable: true,
            isVditorReady: false,
            vditorConfig:{
                toolbar: ["headings", "bold", "italic", "list", "ordered-list", "outdent", "indent", "table", "undo", "redo", "export", "outline"],
            },
        }
    },
    computed: {
        editorHeight() {
            // 业务期望：父容器高度 - 54px；此处可按需改造
            return window.innerHeight - 144;
        },
        hasExternalContent() {
            return !!this.content;
        }
    },
    watch: {
        content: {
            immediate: true,
            handler(val) {
                if (val) {
                    this.innerContent = val;
                    this.applyToVditor();
                }
            }
        },
        dataId(val) {
            if (!this.hasExternalContent && val) {
                this.loadContentById();
            }
        }
    },
    methods: {
        open() {
            this.dialogVisible = true;
            if (this.dataId && !this.hasExternalContent) {
                this.loadContentById();
            }
        },

        onClosed() {
            this.$emit('closed')
            this.$destroy()
            this.$el.parentNode && this.$el.parentNode.removeChild(this.$el)
        },

        onVditorReady() {
            this.isVditorReady = true;
        },

        applyToVditor() {
            if (!this.isVditorReady) return;
            if (this.$refs.vditor && typeof this.$refs.vditor.setValue === 'function') {
                this.$refs.vditor.setValue(this.innerContent || '');
            }
        },

        async loadContentById() {
            try {
                const result = await this.fetchContent();
                this.innerContent = result?.data?.content || '';
                this.applyToVditor();
            } catch (e) {
                console.error('[EditorDetail] loadContentById error:', e);
                this.innerContent = '';
            }
        },

        fetchContent() {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/case-overview/service/query",
                    data: {
                        id: this.dataId
                    },
                    success: (res) => {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            reject(res.Result.FailureMessage);
                        }
                    },
                    fail: (err) => {
                        reject(err);
                    }
                })
            })
        }
    }
}
</script>