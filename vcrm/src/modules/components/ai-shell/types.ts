
//schema数据结构
export interface ShellSchema{

  sidebar: SidebarSchema
     /** 模式列表（左侧 + ModeSelect 卡片） */
  modes: ModeSchema[]

  /** 编辑页定义（EditorSuite） */
  editor: {
    component: string        // 组件名（在 componentsMap 找）
    props?:   Record<string, any>
  }

  /** 是否让框架自动帮你维护全局数据（推荐 true） */
  useSharedState?: boolean
}


interface SidebarSchema{
  buttons: Array<{
    id: string,
    label: string,
    icon: string,
    action: string,
  }>
  data: Array<{
    id: string,
    title: string,
  }>
  dataApi: {
    url: string,
    method: string,
    mapResponse: string,
  }
}


interface ModeSchema {
  key  : string        // 唯一 ID
  title: string        // 展示标题
  label: string        // 展示文案
  icon?: string        // 可选图标

  /** 步骤数组：按顺序渲染 */
  steps: StepSchema[]

  /** 进入模式时是否先清空 sharedState（默认 true） */
  resetState?: boolean
}

interface StepSchema {
  /** 必填：唯一 id */
  id: string
  /** 必填：要渲染的 Vue 组件 (注册名或 import 引用) */
  comp: string | any
  /** 可选：传给组件的初始属性 */
  props?: Record<string, any>

  /* ↓——以下是可选的扩展字段 ——↓ */
  /** 按钮 / 操作区（数组渲染顺序） */
  buttons?: WizardBtn[]
  /** 是否在进入向导时就跳过该步（表达式字符串或布尔值） */
  skipWhen?: string | boolean
  /** 步骤标题（若内部需要显示，可放 props）*/
  title?: string
}

interface WizardBtn {
  id: string
  label: string
  /** 统一派发到 Wizard.dispatch(action) */
  action: string
  /** 是否禁用：表达式字符串 / 布尔 */
  disabled?: string | boolean
  /** 是否隐藏：表达式字符串 / 布尔 */
  hidden?: string | boolean
}