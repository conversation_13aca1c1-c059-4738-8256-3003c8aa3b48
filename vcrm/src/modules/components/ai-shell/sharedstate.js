
//统一动作常量
export const ACTIONS = {
  CREATE_NEW:      'shell:create-new',
  SELECT_HISTORY:  'shell:select-history',
  OPEN_EDITOR:     'shell:open-editor', // payload: { doc }
  BACK_TO_WIZARD:  'shell:back-to-wizard',
  GENERATE:        'shell:generate'
} 

/**
 * 工厂: 创建一份全新的 ShellState
 * @param {Object} seed  可选初始值
 */
export function createShellState(seed = {}) {
  return Vue.observable({
    currentSidebarId: '',             // 当前选中项
    payload: {},                      // 业务数据
    ...seed,
  });
}

/** 重置指定 state 对象 */
export function resetShell(state) {
  if (!state) return;
  state.currentSidebarId  = '';
  state.payload           = {};
}

/** 事件总线 */
export function createShellEventBus() {
  const bucket  = new Map();
  function on(evt,fn){
    if(!bucket.has(evt)) bucket.set(evt,new Set());
    bucket.get(evt).add(fn);
  }
  function off(evt,fn){
    if(bucket.has(evt)) bucket.get(evt).delete(fn);
  }
  function emit(evt,payload){
    if(bucket.has(evt)) bucket.get(evt).forEach(fn=>fn(payload));
  }
  return {on,off,emit};
}