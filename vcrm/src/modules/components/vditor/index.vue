<template>
	<div ref="vditorWrap" class="vditor-wrap" />
</template>
<script>
import Vditor from 'vditor';
import 'vditor/dist/index.css';

export default {
	name: 'Vditor',
	props: {
		content: { type: String, default: '' },
		height: { type: Number, default: 500 },
        vditorConfig: { type: Object, default: () => ({}) },
	},

	data() {
		return {
			editor: null
		}
	},

	mounted() {
		const vm = this;
		this.editor = new Vditor(this.$refs.vditorWrap, {
			height: vm.height,
			cache: { enable: false },
			toolbarConfig: { pin: true },
			outline: {
				enable: true
			},
			preview: {
				markdown: {
					toc: true
				},
				theme: {
					current: 'light'
				}
			},
			after: () => {
				vm.editor.setValue(vm.content)        // 设置初始内容
				vm.$emit('ready', this)        // 通知父组件
			},
            ...this.vditorConfig
		})
	},
	beforeDestroy() {
		this.destroy()
	},
	methods: {
		/** 获取 Markdown */
		getValue() {
			return this.editor?.getValue() || ''
		},
		/** 设置 Markdown */
		setValue(md) {
			this.editor?.setValue(md)
		},
		/** 手动刷新尺寸 */
		resize() {
			this.editor?.resize()
		},
		/** 销毁并释放资源 */
		destroy() {
			if (this.editor) {
				this.editor.destroy()
				this.editor = null
			}
		}
	}
}
</script>
<style scoped lang="less">
.vditor-wrap {
	border: none;

	/deep/ .vditor-toolbar {
		border-bottom: none;
		padding-left: 0 !important;
	}

	/deep/ .vditor-ir pre.vditor-reset {
		background-color: #fff;
	}

	/deep/ .vditor-preview {
		background-color: #fff;
	}

	/deep/ .vditor-outline {
		border-right: none;

		.vditor-outline__title {
			border-bottom: none;
		}
	}
}
</style>