<template>
    <!-- 仅包一层，传配置/插槽给基础助手 -->
    <AiShell 
        ref="shell" 
        :schema="schema" 
        :bizState="bizState" 
        :components-map="bizComponents"
        @resetSchema="resetSchema" 
        @action="handleShellAction"
        @valueChange="handleValueChange" @closed="onClosed">
    </AiShell>
</template>

<script>
import AiShell from "@components/ai-shell/index.vue";
import createSolutionSchema from "./schema";

export default {
    name: "AiSolutionMaker",
    components: {
        AiShell,
    },
    props: {
        relatedObjectDescribe: {
            type: String,
            required: true
        },
        relatedDataId: {
            type: String,
            required: true
        }
    },
    data() {
        const schema = createSolutionSchema();
        const bizState = schema.shared();
        return {
            bizComponents: {},
            schema: {
                ...schema,
                meta: {
                    relatedObjectDescribe: this.relatedObjectDescribe,
                    relatedDataId: this.relatedDataId
                }
            },
            bizState: {
                ...bizState,
                relatedObjectDescribe: this.relatedObjectDescribe,
                relatedDataId: this.relatedDataId
            },
        };
    },
    provide() {
        return { bizState: this.bizState };
    },
    mounted() {
        // 每次重新生成 schema 时都得重新 watch → 写在 mounted 里
        this.$watch(
            () => this.bizState.proposalId,   // 监听 getter，确保响应式
            (val) => {
                if (val) {                           // 有内容才触发
                    // ② 通过 $refs 拿到 AiShell 实例里的 bus
                    this.$refs.shell.shellEventBus.emit(
                        'show-component',        // 与按钮 action 一致
                        { component: 'EditorWithPpt' }              // meta 可选
                    );
                }
            },
            { immediate: false }
        );
    },
    methods: {
        /** 外层通过 instance.open() 触发 */
        open() {
            this.$nextTick(() => {
                this.$refs.shell.open();
            });
        },
        /** 关闭动画结束 —— 做彻底销毁 */
        onClosed() {
            this.$emit('closed')
            this.$destroy()
            this.$el.parentNode && this.$el.parentNode.removeChild(this.$el)
        },

        /** 重新生成 schema + bizState（相当于刷新） */
        resetSchema(param) {
            const newSchema = createSolutionSchema(param);
            Object.assign(this.schema,newSchema);

            const newBizState = newSchema.shared();
            Object.assign(this.bizState, newBizState)
        },


        handleShellAction(payload) {
            const { btn, action } = payload;
            switch (action) {
                case "beforeSendMessage":
                    if (btn.id === 'generate_case_overview') {
                        this.generateCaseOverview(payload);
                    } else if (btn.id === 'generate_solution') {
                        this.generateSolution(payload);
                    }
                    break;
                case "submit":
                    this.saveSolution(payload);
                    break;
                case "createPpt":
                    this.savePptId(payload);
                    break;
                case "shell:select-history":
                    this.openHistory(payload);
                    break;
                default:
                    console.log("[action from aiSolutionMaker]", payload);
            }
        },

        // 生成业务前情
        generateCaseOverview(payload) {
            debugger
            console.log("generateCaseOverview", payload);
            const { meta } = payload;
            let extra = {};
            let text = "";
            switch (meta.apiName) {
                case "uploadFile":
                    extra = {
                        relatedDataId: this.relatedDataId,
                        relatedObjectDescribe: this.relatedObjectDescribe,
                        inputMode: "FILE",
                        promptAttachment: (meta?.value?.promptAttachment || []).map(i => {
                            return {
                                "create_time": 1753802758403,
                                "size": i.size,
                                "filename": i.filename,
                                "ext": i.ext,
                                "path": i.path
                            }
                        })
                    }
                    text = "清空上下文，上传文件生成业务前情" + JSON.stringify(extra);
                    break;
                default:
                    extra = {
                        agentName: "caseOverview",
                        input: {
                            relatedDataId: this.relatedDataId,
                            relatedObjectDescribe: this.relatedObjectDescribe,
                            inputMode: "OBJECT"
                        }
                    };
                    text= "清空上下文，业务场景生成业务前情" + JSON.stringify(extra);
                    break;
            }
        
            payload.done && payload.done(text);
        },

        // 生成方案
        generateSolution(payload) {
            console.log("generateSolution", payload);
            const { meta } = payload;
            let extra = {
                relatedDataId: this.relatedDataId,
                relatedObjectDescribe: this.relatedObjectDescribe
            };
            let text ="";
            switch (meta.apiName) {
                case "selectFlow":
                    extra.flowId = meta.value?.flowId
                    text = "清空上下文，通过模板生成方案" + JSON.stringify(extra);
                    break;
                default:
                    extra.caseOverviewId = this.bizState.caseOverviewId;
                    extra.customPrompt = meta.value?.generatePrompt;
                    text = "清空上下文，生成方案" + JSON.stringify(extra);
                    break;
            }
            payload.done && payload.done(text);
        },

        async handleValueChange(payload) {
            const { status, data } = payload;
            if (status === 'data') {
                const dataInfo = (data?.artifact?.parts || []).find(p => p.type == "data_info")
                if (dataInfo) {
                    const{bizSource,dataId} = dataInfo.data;
                    const key = `${bizSource}Id`;
                    this.bizState[key] = dataId;
                    
                    console.log("bizState", this.bizState);
                    console.log("生成了id", key, dataInfo.data.dataId);

                    if(key === "proposalId"){
                        this.initSaveSolutionAndSessionId() ;
                    }
                }
            }
        },

        // 初始化保存方案和sessingId
        async initSaveSolutionAndSessionId(){
            const url = "/EM1HNCRM/API/v1/object/proposal/service/query";
            const args={
                id:this.bizState.proposalId
            }
            const result = await this.requestHandle(url, args);
            this.bizState.proposalContent = result?.data?.content || "";
            this.saveSolution();
        },

        // 保存方案
        saveSolution(payload) {
            if(payload){
                const { content = "" } = payload?.meta || {};
                this.bizState.proposalContent = content;
            }
            const url = "/EM1HNCRM/API/v1/object/proposal/service/update";
            const args = {
                id: this.bizState.proposalId,
                content: this.bizState.proposalContent,
                sessionId: this.bizState.sessionId
            }
            this.requestHandle(url, args);
        },

        savePptId(payload) {
            const { meta } = payload;
            this.bizState.pptId = meta.appId;
            const url = "/EM1HNCRM/API/v1/object/proposal/service/set-ppt";
            const args = {
                id: this.bizState.proposalId,
                ppt_id: this.bizState.pptId
            }
            this.requestHandle(url, args);
        },

        requestHandle(url, args,msg) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: url,
                    data: args,
                    success: function (res) {
                        if (res.Result && res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            CRM.util.remind(1, msg || "保存成功");
                        } else {
                            reject(new Error(
                                (res.Result && res.Result.FailureMessage) || 'API returned error'
                            ));
                        }
                    },
                });
            })
        },

        // 打开历史记录
        async openHistory(payload) {
            const { meta } = payload;

            const result = await this.getProposalData(meta.data.id);
            const proposalData = result.data;

            const newBizState = this.schema.shared();
            Object.assign(this.bizState, newBizState,{
                proposalContent: proposalData.content,
                pptId: proposalData.pptId,
                sessionId: proposalData.sessionId,
                name: proposalData.name,
                proposalId : proposalData.id,
                caseOverviewId: proposalData.caseOverviewId
            });

            let aiChat = this.schema.workarea.components.find(c => c.name === "SfaNewAiChat");
            aiChat.props.sessionId = proposalData.sessionId;
            aiChat.props.showMask = false;
        },

        async getProposalData(id){
            const url = "/EM1HNCRM/API/v1/object/proposal/service/query";
            const args = {
                id: id
            }
            return this.requestHandle(url, args,"获取方案数据成功");
        }
    },
};
</script>
