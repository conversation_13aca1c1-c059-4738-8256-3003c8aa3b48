import Vue from 'vue'
import Component from './index.vue'

let instance = null

function createInstance (opts) {
  const Ctor = Vue.extend(Component)
  instance = new Ctor({ propsData: opts })
  instance.$mount()
  document.body.appendChild(instance.$el)

  // 组件内部 emit('closed') 时重置单例
  instance.$once('closed', () => { instance = null })
}

export function openSolutionMaker (opts) {
  if (!instance) createInstance(opts)
  instance.open()
}

// export function closeSolutionMaker () {
//   instance?.close()
// }