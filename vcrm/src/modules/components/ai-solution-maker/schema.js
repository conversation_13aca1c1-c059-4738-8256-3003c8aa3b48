import Vue from 'vue'

/**
 * 方案生成器 Schema 2.0
 * 支持 sharedState / 按钮隐藏 / 自定义跳转
 */
export default function createSolutionSchema() {
    const sessionId = "session-"+CRM.util.uniqueCode();
    return {
        useSharedState: true,
        /** 头部标题 & 按钮 */
        header: {
            title: 'AI方案生成器',
            titleDesc: '开启智能创作，让方案设计事半功倍！',
            titleBtns: [{
                id: 'openBusinessInfo',
                label: '业务前情',
                icon:"fx-icon-AIwenzhang",
                action: 'show-detail',
                showByData:"caseOverviewId",
                meta: { 
                    title:"业务前情",
                    apiName: "caseInfoContent" ,
                    fetchApi: {
                        url: '/EM1HNCRM/API/v1/object/case-overview/service/query',
                        argInfo:[{
                            key:"id",
                            value:"caseOverviewId"
                        }]
                    }
                }
            },{
                id: 'openEditor',
                label: '查看方案',
                icon:"fx-icon-AIxiangfa",
                action: 'toggle-component',
                showByData:"proposalId",
                meta: { component: 'EditorWithPpt' }
            }],
        },

        /** 左栏按钮 &历史数据*/
        sidebar: {
            buttons: [
                { id: 'create', icon: 'plus', label: '创建新方案', action: 'shell:create-new' },
            ],
            title: '历史方案',
            /** 声明接口地址 */
            dataApi: {
                url: '/EM1HNCRM/API/v1/object/proposal/service/list',
                args:{
                    "relatedObjectDescribe":"relatedObjectDescribe",
                    "relatedDataId":"relatedDataId"
                },
                /** 如需定制响应字段，可提供提取函数 */
                mapResponse: `(resp) => resp.dataList.map(i => ({ id:i._id, title:i.name }))`
            },
            itemOperate:[
                {
                    action:"delete",
                    label:"删除",
                    url:"/EM1HNCRM/API/v1/object/proposal/service/delete"
                },{
                    action:"rename",
                    label:"重命名",
                    url:"/EM1HNCRM/API/v1/object/proposal/service/rename"
                }
            ],
            // 当用户点击列表项时要做什么
            onSelect: 'shell:select-history'
        },
        /** 编辑器相关配置 */
        workarea: {
            layout: "row",          // "row" | "column"
            /** 需要渲染的子组件 */
            components: [
                {
                    name: "SfaNewAiChat",
                    props: {
                        agentApiName: "Proposal_generator",
                        sessionId: sessionId,
                        debug: true,
                        showMask: true,
                        maskType: "Wizard"  //有引导页时必须有对应的wizard数据
                    },
                    flex: "0 0 366px",
                    show: true
                },
                {
                    name: "EditorWithPpt",
                    weight: 6,
                    flex: "1 1 0%",
                    show: false,
                    props:{
                        apiName:"proposalContent",
                        watchField: 'proposalId',
                        contentSource: 'proposalContent',
                        fetchApi: {
                            url: '/EM1HNCRM/API/v1/object/proposal/service/query',
                            argInfo:[{
                                key:"id",
                                value:"proposalId"
                            }]
                        }
                    }
                }
            ]
        },
        /** AI Agent 引导页配置 */
        wizard: {
            title: '请选择您生成方案方式',
            modes: [
                {
                    key: 'FLOW',
                    title: '基于定制模版生成',
                    label: '选择专业的定制模板，按照模板设定快速生成高质量方案内容。适合标准化、规范化的方案需求',
                    steps: [
                        {
                            apiName: 'selectFlow',
                            comp: 'WizardForm',
                            title: '基于定制模版生成 ',
                            props: {
                                fields:[{
                                    type:"select",
                                    apiName:"flowId",
                                    title:"选择模板",
                                    required:true,
                                    dataUrl: '/EM1HNCRM/API/v1/object/proposal-generator/service/flow-list',
                                    optionFieldMap: {
                                        value: 'flowId',
                                        label: 'name'
                                    }
                                }],
                                buttons: [{ 
                                    id: 'generate_solution', 
                                    action: 'SendAIMessage' ,
                                    label: '生成方案', 
                                    icon:"fx-icon-AIgongju",
                                    style:"dark"
                                }]
                            },
                        }
                    ]
                },
                {
                    key: 'BUSINESSINFO',
                    title: '基于业务信息生成',
                    label: '智能分析和整理现有业务信息，自动生成符合需求的专业方案。适合个性化、定制化的方案需求',
                    steps: [
                        {
                            apiName: 'wizardInfo',
                            comp: 'WizardForm',
                            title: '基于业务信息生成',
                            props: {
                                fields:[{
                                    type:"text",
                                    apiName:"caseOverviewInfo",
                                    content: [{
                                        title: "自动整理您在系统中配置的业务规则、历史数据、客户画像等信息，帮助Al更精准地：",
                                        children: ["理解业务现状", "识别关键需求", "定位方案发力点"]
                                    }]
                                }],
                                buttons: [{
                                    id: 'generate_case_overview',
                                    action: 'SendAIMessage',
                                    label: '生成业务前情',
                                    icon:"fx-icon-AIgongju",
                                    style:"light"
                                }]
                            }
                        },
                        {
                            apiName: 'caseOverview',
                            comp: 'MarkDownCard',
                            title: '业务前情',  
                            isStatusComp:true,
                            defaultAction:'wizard:next-step' 
                        },
                        {
                            apiName: 'generateSolution',
                            comp: 'WizardForm',
                            title: '生成方案', 
                            icon: 'AIbianji',
                            props: {
                                fields:[{
                                    type:"textarea",
                                    apiName:"generatePrompt",
                                    required:false
                                }],
                                buttons: [{ 
                                    id: 'generate_solution', 
                                    action: 'SendAIMessage' ,
                                    label: '简单描述您要生成的内容', 
                                    icon:"fx-icon-AIgongju",
                                    style:"dark"
                                }]
                            }
                        }
                    ]
                }, {
                    key: 'FILE',
                    title: '基于上传文件生成',
                    label: '上传已有文件资料，智能提取关键信息并转化为专业方案。适合已有资料改写或升级的需求',
                    steps: [
                        {
                            apiName: 'uploadFile',
                            comp: 'WizardForm',
                            title: "上传文件生成",
                            props: {
                                title: "上传已有文件资料，智能提取关键信息并转化为专业方案。适合已有资料改写或升级的需求支持PDF、DOC、JEAP、PNG格式",
                                fields:[
                                    {
                                        type:"upload",
                                        apiName:"promptAttachment",
                                        title:"上传文件",
                                        required:true
                                    }
                                ],
                                buttons: [{
                                    id: 'generate_case_overview',
                                    action: 'SendAIMessage',
                                    label: '生成业务前情',
                                    icon:"fx-icon-AIgongju",
                                    style:"light"
                                }]
                            }
                        },
                        {
                            apiName: 'caseOverview',
                            comp: 'MarkDownCard',
                            title: '业务前情',  
                            isStatusComp:true,
                            defaultAction:'wizard:next-step' 
                        },
                        {
                            apiName: 'generateSolution',
                            comp: 'WizardForm',
                            title: '简单描述您要生成的内容',  
                            props: {
                                fields:[{
                                    type:"textarea",
                                    apiName:"generatePrompt",
                                    required:false
                                }],
                                buttons: [{ 
                                    id: 'generate_solution', 
                                    action: 'SendAIMessage' ,
                                    label: '生成方案', 
                                    icon:"fx-icon-AIgongju",
                                    style:"dark"
                                }]
                            }
                        }
                    ]
                }
            ],
        },

        /** 业务共享状态工厂函数 */
        shared: () => Vue.observable({
            apiName:"proposal",
            id:null,
            name:"",
            sessionId: sessionId,
            /** 业务前情id */
            caseOverviewId: null,
            /** 方案id */
            proposalId:null,
            /** 方案内容 */
            proposalContent:"",
            /** PPT id */
            pptId: null,
            pptSourceInfo:{
                bizSource:"ProposalGenerator",
                sourceType:"ProposalObj",
                sourceId:"proposalId"
            }
        })
    }
}       