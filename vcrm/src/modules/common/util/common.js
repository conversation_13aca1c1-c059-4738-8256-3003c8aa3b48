export default {
    isResponseSuccess(res = {}) {
        return res.Result && res.Result.StatusCode == 0;
    },

    /**
     * 在任意位置以弹窗方式预览 Markdown
     * @param {Object} opts
     * @param {String} opts.content   - markdown 字符串
     * @param {String} [opts.title]   - 弹窗标题
     * @returns {Promise<void>}
     */
    async showMarkDownDetail(opts) {
        // 1️⃣ 懒加载组件（webpack code‑split）
        const EditorDetail = (await import(
            /* webpackChunkName: "editor-detail" */
            "@components/ai-shell/components/editor_detail"
        )).default;
        // 2️⃣ 创建临时 Vue 实例 + Dialog
        const DialogCtor = Vue.extend(EditorDetail);
        let instance = new DialogCtor({ propsData: opts });
        instance.$mount();
        document.body.appendChild(instance.$el);
        instance.open()
        // 组件内部 emit('closed') 时重置单例
        instance.$once('closed', () => { instance = null })
    }
}
