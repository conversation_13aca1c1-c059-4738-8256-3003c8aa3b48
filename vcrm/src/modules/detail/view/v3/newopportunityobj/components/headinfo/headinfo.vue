  
<script>
export default {
    props: ['compInfo', 'apiName', 'dataId'],
    
    data() {
        return {
            detailData: this.$context.getData(),
        }
    },
    methods: {

        handleCreateCheckins: function ( btn ) {// 新建高级外勤计划 按钮
            let { _id: dataId, name, object_describe_api_name: apiName } = this.detailData;
            let objName = (CRM.util.getObjByApiName(apiName) || {}).displayName || $t("商机2.0");
            const isMeifu = FS.util.getUserAttribute('meifuAddCheckin');
            const event = isMeifu ? top.FS.MEDIATOR : FS.MEDIATOR;
            const { account_id = '', account_id__r = '', customer_address__c = '' } = this.detailData;
			event.trigger("appCheckin.addPlan", {
				saveSuccess: () => {
					setTimeout(() => {
						this.$context.$emit('root.refresh');
					}, 300); // 外勤计划创建是异步的，所以延迟300ms
				},
				zIndex: FS.util.getCurZIndex(),  //改成700  防止编辑时遮挡pickObject弹框
				data: isMeifu ? {
					// checkinId: check_id,
                    mainObjList: {
                        apiName: 'AccountObj',
                        dataId: account_id,
                        info: customer_address__c,
                        name: account_id__r,
                        objName: $t('crm.客户'),
                        source: 102
                    },
                    otherObjList: {
                        apiName,
                        dataId,
                        info: "",
                        name,
                        objName,
                        source: 108
                    }
				}: {
                    mainObjList: {
                        apiName,
                        dataId,
                        info: "",
                        name,
                        objName,
                        source: 108
                    }
                },
			});

            // 点击 新建高级外勤计划 埋点
            FS.util.pLog("createcheckins", {
                biz: 'FS-FieldWork',
                module: 'NewOpportunityObj',
                subModule: 'objdetail',
                eventId: "createcheckins",
                eventType: "cl",
            });
        },
    }
}
</script>