<script>
    let util = CRM.util;
    let api = CRM.api;
    import iconConfig from './iconconfig';
    import iconHandle from './iconhandle';
    import iconAuth from './iconauth';
    import createMarketingTag from "../../common/createMarketingTag";
    import CallOutPanel from '@/modules/components/call-out-panel/call-out-panel'
    import { openSolutionMaker } from '@components/ai-solution-maker/index.js'
    // todo: 这里边的事件名具体还需要和接口下发的action核对下，规则：handle + action；比如 handleChangeConfirmor；
    export default {
        props: ['compInfo', 'apiName', 'dataId'],
        data() {
            return {
                data: this.$context.getData(),
            }
        },
        beforeCreate(){
            /**
             * 营销通营销标签注入逻辑
             */
            const HeadInfoTag = createMarketingTag.apply(this);
            if(HeadInfoTag){
                this.$options.components["HeadInfoTag"] = HeadInfoTag
            }
        },
        methods: {
            async beforeInitDataAsync(next, data, context) {
                next(data);
                const me = this;
                if (!iconConfig.config[this.apiName]) {
                    return;
                }
                let _config = _.clone(iconConfig.config[this.apiName]);
                if (this.parseConfig) {
                    _config = await this.parseConfig(_config);
                }
                let helper_func = [];
                const _func = [];
                const checkFns = [];
                let helper_tag_func = [];

                _config.forEach((key) => {
                    let item = iconConfig.map[key];
                    if (iconHandle[`handle${key}`]) {
                        item = _.extend({}, item, {
                            on: {
                                click(e) {
                                    me.doLog(item.action);
                                    iconHandle[`handle${key}`](e, item, context);
                                }
                            }
                        })
                    }
                    checkFns.push(me.getCheckFn(item, context));
                    _func.push(item);
                });
                let len = checkFns.length;
                let count = 0;
                checkFns.forEach((fn, index) => {
                    if (fn instanceof Promise) {
                        fn.then((re) => {
                            if (re) {
                                !_.isBoolean(re) && (_func[index].data = re);
                                if(_func[index].isTag){
                                  helper_tag_func.splice(index, 0, _func[index]);
                                }else{
                                  helper_func.splice(index, 0, _func[index]);
                                }
                            }
                        }).finally(() => {
                            ++count;
                            if (count == len) {
                                Vue.set(context.dCompInfo, 'helper_func', helper_func);
                                Vue.set(context.dCompInfo, 'helper_tag_func', helper_tag_func);
                            }
                        })
                    } else {
                        if (fn) {
                            !_.isBoolean(fn) && (_func[index].data = fn);
                            if(_func[index].isTag){
                                helper_tag_func.splice(index, 0, _func[index]);
                              }else{
                                helper_func.splice(index, 0, _func[index]);
                              }
                        }
                        ++count;
                        if (count == len) {
                            Vue.set(context.dCompInfo, 'helper_func', helper_func);
                            Vue.set(context.dCompInfo, 'helper_tag_func', helper_tag_func);
                        }
                    }
                })
                // 使用Promise.all 若其中有一个失败则直接失败处理，正常的icon也无法展示
                // Promise.all(checkFns).then((res) => {
                //     console.log(res);
                //     res.forEach((re, index) => {
                //         if (re) {
                //             !_.isBoolean(re) && (_func[index].data = re);
                //             helper_func.push(_func[index]);
                //         }
                //     })
                // }).finally(() => {
                //     Vue.set(context.dCompInfo, 'helper_func', helper_func);
                // })
            },

            doLog(operationId, eventType) {
                var apiName = this.apiName;
                CRM.util.sendLog('crm', 'detail', { // 统计代码
                    operationId: operationId || 'view',
                    eventType: eventType || 'cl',
                    pageData: {
                        object_id: this.dataId,
                        object_api_name: apiName,
                    },
                });
            },

            ...iconAuth,

            // 外呼对象的外呼按钮
            handlebutton_e_call_out__c: function() {
                CallOutPanel.$show({
                    detailData: this.data,
                    apiName: this.apiName
                })
            },
            handleStartMeeting: function() {
                debugger
                const me = this
                seajs.use('app-standalone/core/workorder/tencent-meeting-adapter.js', function({handleLaunchTencentMeeting, destroyed}) {
                    handleLaunchTencentMeeting({
                        detailData: me.data,
                        apiName: me.apiName
                    });
                    me.$once('hook:beforeDestroy', () => {
                      destroyed && destroyed()
                    });
                });
            },
            handlebutton_e_call_out_1__c: function() {
                CallOutPanel.$show({
                    detailData: this.data,
                    apiName: this.apiName
                })
            },
            // 工作手机外呼
            handlebutton_e_work_phone_call_out__c: function() {
                CallOutPanel.$show({
                    detailData: this.data,
                    apiName: this.apiName,
                    dialoutType: 'workPhone'
                })
            },
            // 启用签章认证 - 客户签章认证/内部签章认证
            handleEnable: function(useStatus) {
                this._toggleStatus(2);
            },

            // 停用签章认证 - 客户签章认证/内部签章认证
            handleDisable: function() {
                this._toggleStatus(3);
            },

            _toggleStatus: function(status) {
                var me = this;
                // var data = this.get('pData').data || {};
                var data = me.$context.getData() || {};
                var _id = data._id;
                var obj = {
                    AccountSignCertifyObj: {
                        url: '/EM1HNCRM/API/v1/object/elec_sign_account_sign_certify/service/enable_or_disable',
                        accountSignCertifyId: _id,
                        useStatus: status
                    },
                    InternalSignCertifyObj: {
                        url: '/EM1HNCRM/API/v1/object/elec_sign_internal_sign_certify/service/enable_or_disable',
                        internalSignCertifyId: _id,
                        useStatus: status
                    }
                }
                var param = obj[me.apiName];

                util.FHHApi({
                    url: param.url,
                    data: _.omit(param, 'url'),
                    success: function(res) {
                        if (res.Result.FailureCode === 0) {
                            var adaptor = status === 2 ? $t("启用") : $t("停用");
                            var tips = adaptor + $t("成功");
                            var tipsType = 1;
                            if (res.Value.status == 2) {
                                tips = adaptor + $t("失败") + res.Value.message;
                                tipsType = 3;
                            } else {
                                me.$context.emit('root.refresh');
                            }
                            util.remind(tipsType, tips, null, 2500);
                        }
                    },
                }, {
                    errorAlertModel: 2
                });
            },

            // 工商查询 - 客户/合作伙伴
            handleInbusinessInquire: function (e) {
                var oData = me.$context.getData();

                api.business_detail({
                    apiname: this.apiName,
                    id: this.get('businessNo'),
                    name: oData.name,
                    zIndex: this.get('zIndex') + 10,
                });
            },

            /**
             *@desc 更换确认人 - 销售订单/退货单
             */
            handleChangeConfirmor: function() {
                var me = this;
                var oData = me.$context.getData();

                CRM.api.change_confirmer({
                    apiname: me.apiName,
                    id: me.dataId,
                    work_flow_id: oData.work_flow_id || '',
                    oData:oData,
                    success: function() {
                        // me.trigger('refresh');
                        // me.refresh();
                        me.$context.emit('root.refresh')

                    }
                })
            },

            // 新建采购入库单 - 采购订单
            handleAddpurchasegoodsReceivednote: function() {
                api.add({
                    apiname: 'GoodsReceivedNoteObj',
                    nonEditable: true,
                    record_type: 'purchase_order__c',
                    data: {
                        purchase_order_id: this.get('dataId'),
                        purchase_order_id__r: this.model.getMainName()
                    },
                })
            },

            /**
             * @desc 取消入账 - 账户收支流水/回款/自定义对象
             */
            handleCancelEntry: function(params) {
                var me = this;
                CRM.api.cancelentry_button_default(_.extend(params, {
                    source: 'detail',
                    dataId: me.dataId,
                    fields: params.param_form,
                    title: params.label,
                    button_apiname: params.api_name,
                    data: me.data,
                    apiname: me.apiName,
                    button_action: params.action,
                    success: function() {
                        me.$context.emit('root.refresh')
                    }
                }));
            },

            //禁用
            handleDisEnable: function () {
            this._updateAttribute("DisEnable");
            },

            //启用
            handleEnable: function () {
                this._updateAttribute("Enable");
            },

            // 客户树-成为根节点
            handleSetRoot(params) {
                params = this.parseParams(params);
                CRM.api.acctree_setroot(params);
            },

            // 客户树-节点变更
            handleNodeChange(params) {
                params = this.parseParams(params);
                CRM.api.acctree_nodechange(params);
            },

            // 校验主数据 && 股权树-获取股权关系
            handleQueryEquityRelationship(params) {
                params = this.parseParams(params);
                if (!params.fields || !params.fields.length) {
                    this.isIndustryRegister(params)
                }
            },
            //  股权树-获取股权关系
            getQueryEquityRelationship(params){
                    var confirm = CRM.util.confirm($t('crm.equitytree.query.confirm'), $t('提示'), function () {
                        confirm.hide();
                        CRM.api.after_action(params);
                    }, {
                        hideFn: function() {
                            confirm = null;
                        },
                        stopPropagation: false
                    })
            },

            // 校验主数据是否为工商注册..
            isIndustryRegister(param){
                const me  = this
                CRM.util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + (param.button_action || param.button_apiname),
				data: {
					objectDataId: param.dataId,
					skipPreFunction: param.skipPreFunction,
					queryParam: param._from === 'list' ? param.queryParam : void 0,
					trigger_info: param.trigger_info || {
						trigger_page: {
							list: 'List',
							relatedList: 'RelatedList'
						}[param._from] || 'Detail'
					}
				},
				success: function (res) {
					if (res.Result.StatusCode === 0) {
                        if(res.Value.notCheckRemind){
                            param.args = { notCheckRemind : true}
                            me.getQueryEquityRelationship(param)
                            return
                        }
                        CRM.util.alert(res.Value.message || $t("操作失败"));
                        return
					}
                        CRM.util.alert(res.Result.FailureMessage || $t("操作失败"));
				},
			}, {
				errorAlertModel: 1
			})
            },

            parseParams(obj) {
                const vm = this;
                return _.extend({
                    apiname: this.apiName,
                    dataId: this.dataId,
                    fields: obj.param_form,
                    title: obj.label,
                    button_apiname: obj.api_name,
                    data: vm.$context.getData(true),
                    button_type: obj.button_type,
                    button_action: obj.action,
                    redirect_type: obj.redirect_type,
                    __after_action: obj.__after_action,
                    success: function(data = {}) {
                        if (typeof data === 'object' && data.type === 'events') {
                            vm.$context.emitSystem(data.action, data.params)
                        }else {
                            vm.$context.emit('root.refresh', {
                                isRefreshList: true
                            })
                        }
                    }
                }, obj.customParam);
            },

            /**
             *@desc 修改属性启用禁用状态
            */
            _updateAttribute: function (url) {
                var me = this;
                util.FHHApi(
                    {
                        url: "/EM1HNCRM/API/v1/object/"+me.apiName+"/action/"+url,
                        data:{"data_id":me.dataId},
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                util.remind($t("操作成功"));
                                me.$context.emit('root.refresh',{
                                    isRefreshList:true
                                })
                                return;
                            }
                            util.alert(res.Result.FailureMessage);
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            },
            handleProposalAiGenerate(){
                const {object_describe_api_name,_id} = this.data;//this.detailData;
                openSolutionMaker({
                    relatedObjectDescribe:object_describe_api_name,
                    relatedDataId:_id
                });
            }
        },
        beforeDestroy() {
            $(window).off('message.enterprisemap');
        },
        // destroyed() {
        //   iconHandle.prosearchTitle && iconHandle.prosearchTitle.$destroy()
        //   iconHandle.prosearchTitle = null
        // },
    }
</script>
<style lang="less">
    .faci-detail_preset .faci-detail .d-headinfo_func{
        margin-right: 0;
    }
    .d-headinfo_icon-ui01,
    .d-headinfo_icon-ui02{
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        margin-right: 4px;
        color:var(--color-neutrals01);
        padding: 0 3px;
        cursor: pointer;
    }
    .d-headinfo_icon-ui01{
        border-radius: 2px;
        background-color: #fff5f0;
        color: #ff522a;
    }
    .d-headinfo_icon-ui02{
        height: 18px;
        line-height: 18px;
        border-radius: 9px;
        background-color: #ff9326;

    }
    .d-headinfo_icon-ui03.fx-icon-f-huobanyingxiao::before, .d-headinfo_icon-ui03.fx-icon-f-wanzhuanfuwuhao::before, .d-headinfo_icon-ui03.fx-icon-f-obj-app260::before{
      color: #ff9326;
    }
    .d-headinfo_icon-ui03{
        color: #ff9326;
        margin-right:4px;
        font-size:14px;
        box-sizing: border-box;
        cursor: pointer;
        width: 20px;
        height: 20px;
        text-align: center;
        padding: 3px;
        background-color: #FFF5F0;
        border-radius: 2px;
    }
    .d-headinfo_icon-ui03.p-unicode{
      width: 20px;
      height: 20px;
      padding: 3px;
      box-sizing: border-box;
      overflow: hidden;
      background-color: #fff5f0;
      font-size: 14px !important;
      border-radius: 2px;
  }
    .d-headinfo_icon-ui04{
      background-color: #fff5e6;
      color: #ff7c19;
    }
     .d-headinfo_icon-pro-search{
        position: relative;
        font-size: 12px;
        margin-right: 4px;
        color:var(--color-neutrals01);
        padding: 0 3px;
        cursor: pointer;
        height: 18px;
        line-height: 18px;
        border-radius: 9px;
        background-color: #ff9326;
        .to-search-procrument{
            position: absolute;
            z-index: 1;
            padding: 10px;
            border-radius: 4px;
            background: #FFF;
            width: 220px;
            color: black;
            list-style: none;
            li{
                font-size: 13px;
                height: 30px;
                line-height: 30px;
            }
            :first-child{
                color: #91959E;
            }

        }
    }
    .d-headinfo_icon-microconversation{
        background-image: url("https://a9.fspage.com/FSR/weex/avatar/object_detail//images/qiyeweixinlogo.svg");
        background-size: 100%;
        margin-right:4px;
        width: 18px;
        height: 18px;
        cursor: pointer;
    }
</style>
