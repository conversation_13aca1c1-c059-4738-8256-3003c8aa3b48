/*
 * @Author: hgl
 * @Date: 2022-01-14 11:33:23
 * @Description: 选择产品表格
 * @LastEditors: <PERSON> Jun
 */

define(function(require, exports, module) {
	let Table = require('crm-widget/table/table'),
		util = require('crm-modules/common/util'),
		tableItemTpl = require('./template/table-item-html'),
		PRODUCTNAME = 'product_name',
		CachePrefix = '_simpleorder_columns'
		layoutTpl = require('./template/table-layout-html');

	let TermBatchBase = function(prot){
		return {
			events: _.extend({}, prot.events, {
				'click .j-history': 'historyHandle',
				'click .j-fields': 'fieldsHandle'
			}),
			_renderMain(){
				let opts = this.options;
				opts.termBatchComplete && opts.termBatchComplete(this.$el);
				this._initFilter();
				// this._initMore();
				// if(!this._setColumn){
				// this._initSetColumns();
				// }
			},
			_initFilter(){
				prot._initFilter.apply(this,arguments);
			},
			_initMore(){
				// var me = this;
				// let data = (me.options.hasHistoryBtn ? [{
				// 	text: $t('添加历史订单产品'),
				// 	className: 'j-history'
				// }] : []).concat([{
				// 	text: $t('字段设置'),
				// 	className: 'j-fields'
				// }]);
				// require.async('crm-widget/dropdown/dropdown', function(DropDown){
				// 	me._morBatch = new DropDown({
				// 		$target:   me.$('.j-more'),
				// 		$parent:   me.$el,
				// 		trigger:   'click',
				// 		pos:       'bottom left',
				// 		width:     150,
				// 		space:     10,
				// 		data
				// 	});
				// 	me._morBatch.on('show', function() {
				// 		me.filter && me.filter.hide();
				// 	});
				// });
			},
			historyHandle(e){
				this.trigger('history');
				this._morBatch && this._morBatch.hide();
			},
			initColumns(){
				if(!this._setColumn){
					this._initSetColumns();
				}
				if(this._setColumn){
					this._setColumn.on('show', () => {
						this.filter && this.filter.hide();
					});
				}
			},
			fieldsHandle(e){
				// if(!this._setColumn){
				// 	this._initSetColumns();
				// }
				// this.$('.j-tb-setcolumn').trigger('click');
				// this._morBatch && this._morBatch.hide();
			},
			destroy(){
				prot.destroy.apply(this, arguments);
			}
		}
	}

	let SimpleTable = Table.extend({
		events: {
			'click .j-add-pd': 'addProduct',
			'mousedown .j-add-pd': 'mousedownAddCls',
			'mouseup .j-add-pd': 'mouseupRemoveCls',
			'click .j-select-spec': 'selectSku',
			'click .sku-item': 'addSkuHandle',
			'click .j-lookup': 'lookupHandle'
		},
		setup(opts){
			Table.prototype.setup.call(this,opts);
			let formatData = this.options.formatData;
			let apiNameKey = this.getMainNameKey();
			this.options.formatData = (res) => {
				let listLayouts = res.listLayouts || [];
				let defaultColumns = [],
					cacheValue = localStorage.getItem(this._getCacheKey()),
					showItemColumns;
				if(cacheValue){
					showItemColumns = JSON.parse(cacheValue);
				}else {
					if(listLayouts[0]){
						showItemColumns = listLayouts[0].components?.[0]?.include_fields || defaultColumns;
					}else {
						//整个默认值
						showItemColumns = defaultColumns;
					}
				}
				this.showItemColumns = _.filter(showItemColumns, (v) => v.api_name !== apiNameKey);
				if(this._termBatch && !this._termBatch.loadedColunms){
					this._termBatch.initColumns();
				}
				return formatData(res);
			}
			const oldBeforeRequestFHH = this.options.beforeRequestFHH, that = this;
			this.options.beforeRequestFHH = (...args) => {
				const promise = oldBeforeRequestFHH ? oldBeforeRequestFHH(...args) : Promise.resolve({});
				return promise.then(data => {
					data = data || {};
					data.parseParam = function(params) {
						const search_query_info = JSON.parse(params.search_query_info);
						let keyword = '';
						if(that._search && that._search.searchComp){
							keyword = that._search.searchComp.getIptValue()
						}
						if(keyword){
							search_query_info.filters = (search_query_info.filters || []).filter(item => {
								if(item.field_name == 'product_id.name' && item.field_values.includes(keyword)) return false;  //过滤掉产品名称
								return true;
							})
							const wheres = [];
							search_query_info.wheres = search_query_info.wheres || [];
							[{
								field_name: 'product_code', // 未开价目表，标准产品列表
								field_values: [keyword],
								operator: 'LIKE'
							}, {
								field_name: 'product_id.name',
								field_values: [keyword],
								operator: 'LIKE'
							},{
								field_name: 'product_code__c', // 价目表明细字段
								field_values: [keyword],
								operator: 'LIKE'
							}].forEach(filter => {
								search_query_info.wheres.forEach(where => {
									wheres.push({
										...where,
										filters: [...where.filters, filter]
									})
								})
							})
							search_query_info.wheres = wheres;
						}
						params.search_query_info = JSON.stringify(search_query_info);
						return params;
					}
					return data;
				});
			}
		},
		_initDtTermBatch(){
			// 初始化筛选，列设置
			if (!this.options.showTermBatch) {
				return
			}
			var me = this,
				opts = me.options;

			require.async('crm-widget/table/termbatch/termbatch', function(T){
				let TermBatch = T.extend(TermBatchBase(T.prototype));
				let termOpts = me.__getDtTermBatchOpts();
				me._termBatch = new TermBatch(_.extend({}, termOpts, {
					FilterClass: function (Filter) {
						return Filter.extend({
							_setPos(){
								return Filter.prototype.__proto__._setPos.apply(this, arguments)
							}
						})
					},
					parseSetColums(columns){
						let length = (me.showItemColumns || []).length;
						let nameKey = me.getMainNameKey();
						let picKey = me.options.isChooseSpu ? 'picture' : 'picture_path';
						columns = _.map(columns, function (c) {
							if(c.data === nameKey || c.data === 'product_id' || c.data === picKey){
								// 过滤掉产品名称
								return null;
							}
							let column = _.find(me.showItemColumns, (item) => c.data === item.api_name );
							let isHidden = true;
							let columnIndex = 0;
							if(column){
								columnIndex = _.indexOf(me.showItemColumns, column);
								isHidden = false;
							}
							return {
								...c,
								isHidden,
								fixed: false,
								__posorderindex: column ? columnIndex : length++
							}
						}).filter(v => !!v);
						return _.sortBy(columns, (v) => v.__posorderindex);
					},
					el: me.$('.table-header'),
					hasHistoryBtn: me.get('hasHistoryBtn'),
					isSimpleTable: true
				}));
				me._termBatch.on('term.advance', function (data, aloneValues, isInnerFilter) { // 筛选
					if (me.options.beforeTermAdvance && !me.options.beforeTermAdvance(data, aloneValues)) {
						return;
					} // 筛选前的逻辑
					Table.clearRule();
					me.filterStatus = true;
					me.setParamByKey('_aloneValuesKey', aloneValues);
					me._doFilterData(data, true, 'advance');
					me.trigger('term.advance', data, aloneValues, isInnerFilter);
					me._curFilterData = data;
				});
				me._termBatch.on('history', function(){
					me.trigger('history');
				});
				me._termBatch.on('columns.change', function(data){
					me._remberColumnsOrder(data);
					me._setShowItemColumnsAndSave(data);
					me.renderByColumns(data, null, true, true);
				})
			});
		},
		_setShowItemColumnsAndSave(data){
			this.showItemColumns = _.map(data, (c) => {
				if(!c.isHidden){
					return {
						api_name: c.api_name,
						field_name: c.field_name,
						label: c.label,
						render_type: c.render_type
					}
				}
				return null;
			}).filter(v => !!v);
			localStorage.setItem(this._getCacheKey(), JSON.stringify(this.showItemColumns));
		},
		_getCacheKey(){
			let curInfo = FS.contacts.getCurrentEmployee();
			let apiName = this.get('api_name');
			return `${CachePrefix}_${apiName}_${curInfo.enterpriseAccount}_${curInfo.employeeId}`
		},
		_initSearch(){
			// 初始化搜索
			var me = this;
			require.async('crm-widget/table/search/search', function(Search){
				var	opts = me.options,
					search = opts.search;
				me._searchkeydown = 0;
				if (search) {
					me._search = new Search(_.extend({
						$el: me.$el.find('.j-input-select'),
						// pos: '.j-input-select',
						dPreIcon: 'fx-icon-search',
						placeHolder: $t("请输入搜索文本"),
						filterColumns: null,
						objectApiName: opts.objectApiname,
						cacheSearchField: false,
						searchDefaultKeyword: opts.searchDefaultKeyword
					}, search, {
						placeHolder: opts.isChooseSpu ? $t('simplesalesorderobj.sp.placeholder') : $t("simplesalesorderobj.cp.placeholder")
					}));
					me._search.on('rendered', function(search) {
						// const old_handle = search.searchComp.handleKeyDown;
						search.searchComp.handleKeyDown = function(event) {
							const code = event.keyCode;
							if(code == 38 || code == 40){  //上键 下键
								let $current = me.$el.find('.table-item.is-selected'), current_item_index = $current.index();
								current_item_index = code == 38 ? Math.max(current_item_index - 1, 0) : Math.min(current_item_index + 1, me.curData.totalCount - 1);
								$current.removeClass('is-selected');
								$current = me.$el.find(`.table-item:eq(${current_item_index})`).addClass('is-selected');
								$current.length && me.__scrollSelectedItem($current);
								return;
							}else if(code == 13) {  //如果是回车
								let $current = me.$el.find('.table-item.is-selected');
								if($current){  //如果当前有选中的
									me.addProduct($current.data('id'), true);
									$current.removeClass('is-selected');
									return;
								}
							}

							me._keydownSearch()
						}
					});
					me._search.on('search', function (keyWord, field) {
						me._doSearch(keyWord, null);
					});
				}
			});
		},
		__scrollSelectedItem($item) {
			let $scroll = this.$el.find('.table-content');
			let scrollTop = $item.offset().top - $scroll.offset().top;
			let height = $item.outerHeight();
			let tableHeight = $scroll.height();

			if (scrollTop > tableHeight - 20) {
				$scroll.scrollTop($scroll.scrollTop() + height + scrollTop - tableHeight + 8);
			} else if (scrollTop < 0) {
				$scroll.scrollTop($scroll.scrollTop() - height + scrollTop);
			}
		},

		_keydownSearch: _.debounce(function() {
			const value = this._search.searchComp.getIptValue();
			this._doSearch(value, null);
		}, 500),
		renderMain(){
			// 反而没用了
		},
		_drawPage(data, totalCount, keepScroll){
			let me = this;
			if(data.length){
				me.addRow(data, false, 2);
			}else {
				me._showNodata(me._keyWord ? $t('没有找到') + '“' + _.escape(me._keyWord) + '”' : me.options.noDataTip);
			}
			me._renderPage(totalCount || 0);
			me.$el.toggleClass('crm-table-nodata', data.length === 0);
		},
		_showNodata(text){
			this.$('.empty-txt').html(text);
		},
		showLoading(){
			this.$('.dt-loading').show();
		},
		_renderPage(totalCount){
			var me = this,
				opts = me.options,
				param = me.getParam(),
				pageCount = 0;
			if (me.tableStatus == 2) {      // 表格已销毁，不再加载分页
				return;
			}
			if (!opts.showPage) {
				me.tableStatus = 3;
				return;
			}

			if (opts.noAlwaysShowPage && totalCount <= opts.page.pageSize) {
				me.$('.j-page').hide();
				return;
			}
			me.$('.j-page').show();
			pageCount = Math.ceil(totalCount / opts.page.pageSize) || 1;
			if (param.pageNumber > pageCount) { // 当请求页数大于当前页面
				param.pageNumber = 1;
			}
			me._initPage(totalCount, pageCount, opts.page.pageSize, param.pageNumber);
			me.tableStatus = 3;
			me._totalCount = totalCount;
			me._cacheSkus = {};
		},
		_initPage(totalCount, pageCount, pageSize, pageNumber){
			// 缺少当前的pageNumber
			let me = this;
			this._pagination && this._pagination.destroy();
			this._pagination = FxUI.create({
				wrapper: this.$('.j-page')[0],
				template: `<fx-pagination
							  layout="total, prev, next, jumper"
							  small
							  :total="total"
							  :page-size="pageSize"
							  :page-count="pageCount"
							  :current-page="currentPage"
							  @current-change="pageChange"
							  >
						</fx-pagination>`,
				data() {
					return {
						// small:true,
						pageCount,
						pageSize,
						total: totalCount,
						currentPage: pageNumber
					}
				},
				methods: {
					pageChange(data, node, tree) {
						me.setParam({
							pageNumber: data
						}, true);
						me.trigger('dt.page', data);
					},
					reset(){
						// 初始化当前的值
						this.currentPage = 1;
					}
				}
			})
		},
		_createTableColumn(data, index, columns){
			let isSpu = this.options.isChooseSpu,
				isPromotion = this.options.isPromotion,
				nameKey = this.getMainNameKey();
			return tableItemTpl({
				columns,
				data,
				index,
				nameKey,
				nameColumn: _.find(this.options.columns, (v) => {
					return v.data === nameKey
				}),
				isSpu,
				apiname: this.get('objectApiname'),
				isPromotion,
				showPromotionIcon: isPromotion && ((data.promotion && data.promotion.length) || data.have_promotion || data.hasPricePolicy),
				picture: this._parseImg(isSpu ? data.picture : ( CRM._cache.openPriceList ? (data.product_id__ro ? data.product_id__ro.picture_path : data.picture_path) : data.picture_path))
			})
		},
		_parseImg(pic){
			if(_.isArray(pic)){
				pic = pic[0];
			}
			if(!pic){
				return FS.CRM_MODULE.ASSETS_PATH + '/images/checkin/empty-img.svg';
			}
			if(pic.path){
				return util.getFscLinkByOpt({
					webp: true,
					id: pic.path + '3.' + pic.ext
				})
			}else {
				// 取#分割
				let pics = pic.split('#');
				let extName = pics[0].slice(pics[0].lastIndexOf('.'));
				return util.getFscLinkByOpt({
					webp: true,
					id: pics[1] + '3' + extName
				})
			}
		},
		addRow(data, extendDefault, proq, pos){
			let htmls = [],
				me = this,
				$table = me.$('.table-content'),
				rowData = me.getDefaultRowData();

			let _showItemColumns = _.map(this.showItemColumns, (item) => {
				return _.find(this.options.allColumns, c => c.data === item.api_name);
			}).filter(v => !!v);
			_.each(data, function(item, index){
				if(extendDefault){
					item = _.defaults(item, rowData);
				}
				htmls.push(me._createTableColumn(item, index, _showItemColumns));
				if(me.options.rowCallBack){
					me.options.rowCallBack('', item, '', '');
				}
			})
			if(_.isUndefined(pos) && proq == 1){
				var $tr = $table.find('.table-item').eq(pos);
				let method = $tr[0] ? 'after' : 'html';
				$tr[method](htmls.join('\n'));
			}else {
				$table.html(htmls.join('\n'));
			}
			me.curData = me.curData || {
				data: []
			};
			var td = me.curData.data;
			if (proq == 1) {
				if (!_.isUndefined(pos)) {
					_.each(data, function (item) {
						td.splice(++pos, 0, item);
					});
				} else {
					[].push.apply(td, data);
				}
			}
			me.trigger('completeRender');
		},
		addProduct(e, isId){
			let _id = isId ? e : $(e.currentTarget).data('id');
			let data = this.curData?.data || [];
			let parseSelectData = this.options.parseSelectData;
			if(data){
				let curData = _.find(data, (item) => item._id === _id );
				curData && this.trigger('addProduct', parseSelectData([curData]));
			}
		},
		mousedownAddCls(e) {
			$(e?.currentTarget?.parentElement).addClass('is-selected');
		},
		mouseupRemoveCls(e) {
			$(e?.currentTarget?.parentElement).removeClass('is-selected');
		},
		selectSku(e){
			e.stopPropagation();
			let $el = $(e.currentTarget),
				$wrapper = $el.closest('.table-item').find('.j-skus')
				spuId = $el.data('id');
			let isOpen = $el.hasClass('open');
			if($el.hasClass('loading')){
				return;
			}
			if(isOpen){
				// 隐藏
				$el.removeClass('open');
				$wrapper.hide();
			}else {
				this.$el.find('.j-select-spec.open').removeClass('open').closest('.table-item').find('.j-skus').hide();
				$el.addClass('open');
				$wrapper.show();
			}
			if(this._cacheSkus && this._cacheSkus[spuId]){
				return;
			}
			$el.addClass('loading');
			this.trigger('selectSku', spuId, (skuList) => {
				this._renderSkuList(skuList, $el.closest('.table-item'), spuId);
			}, () => {
				$el.removeClass('loading');
			});
		},
		_renderSkuList(skuList, $table, spuId){
			if(!this._cacheSkus){
				this._cacheSkus = {};
			}
			this._cacheSkus[spuId] = skuList;
			let $wrapper = $table.find('.j-skus');
			let items = [], maxSpecNameLen = 0, maxSpecNameItem = null, htmls = [];
			_.each(skuList, (sku) => {
				let leftColor = sku.pack_color__c || '#F2F3F5';
				let $item = `<div class="sku-item" style="border-left-color: ${leftColor}" data-id="${sku._id}" data-spuid="${spuId}"><span>${sku.spec_names}<span></div>`;
				items.push($item);
				if(sku.spec_names.length > maxSpecNameLen){
					maxSpecNameLen = sku.spec_names.length;
					maxSpecNameItem = $($item);
				}
			})
			let className = '', columnum = 3;
			if(maxSpecNameItem){
				maxSpecNameItem.css('display', 'inline-block');
				$wrapper.append(maxSpecNameItem);
				let width = maxSpecNameItem[0].offsetWidth;
				if(width){
					if(width <= 48){
						className = 'item-mini';
					}else if(width <= 96){
						className = 'item-middle';
					} else {
						className = 'item-big';
						columnum = 2;
					}
				}
			}
			$wrapper.addClass(className);
			let len = items.length;
			_.each(items, function(d,i){
				if(i % columnum === 0){
					if(i !== 0){
						htmls.push('</div>')
					}
					htmls.push('<div class="sku-item-wrapper">')
				}
				htmls.push(d);
				if(i === len - 1){
					//最后一个了
					htmls.push('</div>')
				}
			})
			$wrapper.html(htmls.join('\n'));
		},
		addSkuHandle(e){
			let $el = $(e.currentTarget),
				skuId = $el.data('id'),
			 	spuId = $el.data('spuid'),
				datas = this.curData?.data || [],
				parseSkuSelectData = this.options.parseSkuSelectData,
				skusData = this._cacheSkus && this._cacheSkus[spuId];

			if(skusData){
				let curSkuData = _.find(skusData, (v) => v._id === skuId);
				let curSpuData = _.find(datas, (item) => item._id === spuId );
				if(curSkuData && curSpuData){
					this.trigger('addProduct', parseSkuSelectData([curSpuData], {
						[curSpuData._idKey]: {
							datas: [curSkuData],
							formateDatas: curSkuData.formatData
						}
					}));
				}
			}
		},
		lookupHandle(e){
			let data = $(e.currentTarget).data();
			if(data.apiname){
				util.showDetail(data.id, data.apiname);
			}
		},
		// 获取列表name的apiName
		getMainNameKey(){
			let isSpu = this.options.isChooseSpu;
			if(!CRM._cache.openPriceList){
				// 如果没有开通价目表
				return 'name';
			}
			return  isSpu ? 'name':  (CRM._cache.priceBookPriority ? 'name' : PRODUCTNAME);
		}
	});
	module.exports = function (proto) {
		return {
			render(){
				proto.render.apply(this,arguments);
			},
			_renderCategory(){
				// 在这个钩子中，强制开启包含本单已选产品的配置项
				CRM.util.setIncludeSelected({
					key:'whether_filter_order_select_product',
					value: '1'
				}).then(function (res) {
					console.log('whether_filter_order_select_product', res);
				});
			},
			clickCategoryItemCb(){
				proto.clickCategoryItemCb.apply(this, arguments);
			},
			getSKUComponent(opts, cb, completCb){
				var me = this;
				var queryParam = me.paramCache,
					queryInfo = JSON.parse(queryParam.search_query_info);
				opts = _.extend(_.extend({
					spuId: '', //商品id
					filterIds: [],
					objectData: me.parseObjectData(),
					masterData: me.get('master_data'),
					opType: 3, // 4 为价目表添加产品
					queryInfo: queryInfo,
				}, opts));
				this._getSkuData(opts, cb, completCb)
			},
			_getSkuData({spuId: id, queryInfo, filterIds, objectData, masterData}, cb, completCb){
				var me = this;
				var sq = {
					"limit": 10000,
					"offset": 0,
					"filters": [],
					"wheres": queryInfo.wheres || []
				};
				if (filterIds.length >= 1) {
					sq.filters.push({
						field_name: '_id',
						field_values: filterIds,
						operator: 'NIN'
					})
				}
				var ispb = objectData ? objectData.pricebook_open : false;
				var apiName = ispb ? "PriceBookProductObj" : "ProductObj";
				let columns = JSON.parse(JSON.stringify(this.get('columns')));
				columns = this.formatColumns(columns, apiName);
				util.waiting();
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/RelatedList',
					data: {
						associate_object_data_id: id,
						associate_object_describe_api_name: "SPUObj",
						associated_object_describe_api_name: apiName,
						associated_object_field_related_list_name: "spu_sku_list",
						include_associated: true,
						search_query_info: JSON.stringify(sq),
						object_data: objectData,
						master_data: masterData,
					},
					success: function(res) {
						if (res.Result.StatusCode == 0) {
							//格式化table数据
							var skuList = res.Value.dataList;
							skuList = _.map(skuList, function (d) {
								var item = {},
									specNames = [],
									spec_and_value = d.spec_and_value;
								if (ispb) {
									spec_and_value = d.product_id__ro.spec_and_value;
								}
								_.each(spec_and_value, function(spec) {
									item[spec.spec_name] = spec.spec_value_id;
									specNames.push(spec.spec_value_name);
								});
								item.spec_names = specNames.join('/');
								item.formatData = me.getSkuTableData([{...d}], columns);
								return _.extend(d, item);
							})
							cb && cb(skuList);
						}
					},
					complete: function(){
						util.waiting(false);
						completCb && completCb();
					}
				});
			},
			getSkuTableData(data,columns){
				var me = this;
				data = _.map(data, function (item) {
					return _.clone(item);
				});
				_.each(columns, function (item) {
					if (item.data && item.data != 'null' && [9, 17, 34, 37, 38, 39].indexOf(item.dataType) == -1) {
						_.each(data, function (dItem) {
							var val = dItem[item.data];
							(val = Table.helper.getFormatVal(val, item, '', dItem, '', me.isEm6));
							val = val == '--' ? '' : val;
							dItem[item.data] = val;
						});
					}
				});
				return data;
			},
			// // 开了强制优先级，从产品添加时，添加一列价目表价格；
			// addPriceBookPriceColumn(columns, apiname) {
			// 	if (CRM.util.isGrayScale('CRM_SHOW_PRICEBOOK_PRICE') && CRM._cache.priceBookPriority && apiname === 'ProductObj') {
			// 		let index = util.findIndex(columns, item => item.api_name === 'price');
			// 		if (!util.hasValue(index)) index = util.findIndex(columns, item => item.api_name === 'name');
			// 		if (util.hasValue(index)) {
			// 			columns.splice(index + 1, 0, {
			// 				data: 'prick_book_price',
			// 				title: $t('价目表价格'),
			// 				render: function (data, type, full) {
			// 					if (full.extend_info && full.extend_info.realPriceInfo) {
			// 						return full.extend_info.realPriceInfo.pricebook_price;
			// 					}
			// 					return '';
			// 				}
			// 			})
			// 		}
			// 	}
			// 	return columns;
			// },
			bindTableEvents(table){
				proto.bindTableEvents.apply(this,arguments);
				table.on('selectSku', (spuId, cb, completCb) => {
					let me = this;
					this.getSKUComponent({
						spuId: spuId,
						filterIds: me.filterIds
					}, cb, completCb);
				});
			},
			_renderTable(){
				// 渲染表格
				var me = this;
				this.cloneFilterIds = _.extend([], this.options.filterIds);
				me.table && me.table.destroy();
				var options = me.getOptions();
				me.changeOptions(options);
				me.options.formatTableOption && me.options.formatTableOption(options);
				console.log(options);
				const apiname = me.get('apiname');
				me.table = new SimpleTable(_.extend({
					className: 'crm-table crm-table-noborder crm-table-open pickself_table',
					isMyObject: true,  //默认都是自定义对象
					noAllowedWrap: true,
					layoutTpl: layoutTpl,
					isChooseSpu: this.options.isChooseSpu,
					parseSelectData: (value) => {
						// 跟进规格选择产品的数据格式化
						if(this.options.isChooseSpu){
							// 有规格时的添加
							value =  this.__getValue(proto.__proto__.__getValue.call(this, value, true), true);
						}else {
							value = this.__getValue(value, true);
						}
						console.log(value);
						return value;
					},
					parseSkuSelectData:(value, selectedSku) => {
						this.selectedSku = selectedSku;
						value =  this.__getValue(proto.__proto__.__getValue.call(this, value, true), true);
						console.log(value);
						this.selectedSku = {};
						return value;
					},
					isPromotion: me.isPromotion(apiname) || me.isPricePolicy(apiname),
					hasHistoryBtn: me.get('hasHistoryBtn')
				}, options));
				me.bindTableEvents(me.table);
				me.copyDtEvents(me.table);
				me._proxyTableFn(me.table);
				me.tableComplete();
				me.trigger('complete', me);
			}
		}
	}
});
