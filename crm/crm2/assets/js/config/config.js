/**
 * @file config.js
 * @desc crm配置文件
 */
define(function (require, exports, module) {

	var objDes = require('./object'),
		detailActionConfig = require('./actionactionconfig'),
		version = '710', // 版本
		rPath = FS.ROOT_PATH + '/crm' + FS.PATH_SUFFIX, // 根目录
		aPath = rPath + '/assets', // 静态资源目录
		mPath = rPath + '/modules'; // 模块目录

	/**
	 * @desc crm 各个对象对象的文件夹
	 * 获取新建 详情模块的文件地址
	 * 自定义的对象统一my_开头
	 */
	var ObjFolder = {
		1: 'leadsobj', // 销售线索
		2: 'customer', // 客户
		3: 'contactobj', // 联系人
		4: 'product', // 产品
		5: 'cb', // 回款
		6: 'gb', // 退款
		8: 'opportunityobj', // 商机
		9: 'invoice', // 开票申请
		11: 'orderform', // 订单
		12: 'returnorder', // 退货单
		13: 'visit', // 拜访
		16: 'contract', // 合同
		19: 'rival', // 竞争对手
		20: 'market', // 市场活动
		21: 'inventory', // 盘点
		23: 'caseobj', // 盘点
		10003: 'rival', // 竞争对手
		1000: 'myobject', // 自定义对象
		'LeadsObj': 'leadsobj', // 销售线索
		'AccountObj': 'customer', // 客户
		'ConsultQuestionRecordObj': 'consultquestionrecordobj', // 咨询问题
		'MailObj': 'mailobj', // 邮件
		"PayChannelObj": "paychannelobj", //支付渠道信息
		"EnterprisePayOrderObj": "enterprisepayorderobj", //企业支付订单
		'ContactObj': 'contactobj', // 联系人
		'RefundObj': 'gb', // 退款
		'OpportunityObj': 'opportunityobj', // 商机
		'InvoiceApplicationObj': 'invoice', // 开票申请
		'SalesOrderObj': 'orderform', // 订单
		'ReturnedGoodsInvoiceObj': 'returnorder', // 退货单
		'VisitingObj': 'visit', // 拜访
		'ContractObj': 'contract', // 合同
		'CrmRival': 'rival', // 竞争对手
		'MarketingEventObj': 'market', // 市场活动
		'InventoryObj': 'inventory', // 盘点
		'CustomerAccountObj': 'customeraccountobj',
		'AccountTransactionFlowObj': 'accounttransactionflowobj', // 账户收支流水
		'AccountCheckRuleObj': 'accountcheckruleobj', // 账户校验规则
		'RewardTaskObj': 'rewardtaskobj', // TPM - 任务管理
		'FAccountAuthorizationObj':'faccountauthorizationobj', // 客户授信
		'CustomerCreditAuthObj':'customercreditauthobj', // 账户授权
		'EnterpriseRelationObj': 'enterpriserelationobj', // 互联企业
		'PublicEmployeeObj': 'publicemployeeobj', // 互联帐号
		'ErDepartmentObj': 'erdepartmentobj', // 互联部门
		'PrepayDetailObj': 'prepaydetailobj',
		'RebateIncomeDetailObj': 'rebateincomedetailobj',
		'PriceBookObj': 'pricebookobj',
		'PriceBookProductObj': 'pricebookproductobj',
		'PromotionObj': 'promotionobj',
		'AccountsReceivableNoteObj': 'accountsreceivablenoteobj', // 应收单
		'MatchNoteObj': 'matchnoteobj', // 核销单
		'SalesInvoiceObj': 'salesinvoiceobj', // 销售发票
		'QuoteObj': 'quoteobj',
		'QuoteLinesObj': 'quotelinesobj',
		'PaymentObj': 'paymentobj',
		'PayObj': 'payobj',
		'PaymentPlanObj': 'paymentplanobj',
		'OrderPaymentObj': 'orderpaymentobj',
		'GoodsReceivedNoteObj': 'goodsreceivednoteobj',
		'DeliveryNoteObj': 'deliverynoteobj',
		'StockObj': 'stockobj',
		'SparePartsMaintenancePlanObj':'sparepartsmaintenanceplanobj', // 备件维修计划
		'RequisitionNoteObj': 'requisitionnoteobj',
		'OutboundDeliveryNoteObj': 'outbounddeliverynoteobj',
		'EmployeeWarehouseAdjustmentNoteObj': 'employeewarehouseadjustmentnoteobj',
		'SparePartsApplicationObj': 'sparepartsapplicationobj',
		'PurchaseOrderObj': 'purchaseorderobj',
		'SupplierObj': 'supplierobj',
		'InternalSignCertifyObj': 'internalsigncertifyobj',
		'AccountSignCertifyObj': 'accountsigncertifyobj',
		'AccountDepartmentObj': 'accountdepartmentobj',
		// 'SignRecordObj':         'signrecordobj',
		'CasesObj': 'casesobj',
		'PartnerObj': 'partnerobj',
		// 'GoalValueObj': 'goalvalueobj',
		// 'ErpWarehouseObj':       'erpwarehouseobj',
		// 'ErpStockObj':           'erpstockobj',
		// 'RebateUseRuleObj':      'rebateuseruleobj',
		'StockCheckNoteObj': 'stockchecknoteobj',
		// 'StockDetailsObj':       'stockdetailsobj',
		'BatchObj': 'batchobj',
		'ExchangeGoodsNoteObj': 'exchangegoodsnoteobj', // 换货单
		'ExchangeReturnNoteObj': 'exchangereturnnoteobj', // 退换货单
		'my_saleslog': 'saleslog', // 销售记录
		'NewOpportunityObj': 'newopportunityobj',
    'PivotTableRuleObj': 'pivottableruleobj',
    'RelationTemplateObj': 'relationtemplateobj',
    'InteractionStrategyObj': 'interactionstrategyobj',
    'OperationsStrategyObj': 'operationsstrategyobj',
    'PivotTableInstanceObj': 'pivottableinstanceobj',
		'NewOpportunityLinesObj': 'newopportunitylinesobj',
		'SpecificationObj': 'specificationobj',
		'SpecificationValueObj': 'specificationvalueobj',
		'SPUObj': 'spuobj',
		'ProductObj': 'productobj',
		'CheckinsObj': 'checkinsobj',
		'VisitRouteObj': 'visitrouteobj',
		'FeeSettlementBillObj': 'feesettlementbillobj', // 费用结算单
		'FeeDetailObj': 'feedetailobj', //费用明细
		'object_AccessoryConsumeObj__c': 'object_accessoryconsumeobj__c', // 沃得 配件消耗表
		// 'object_AccessoryConsumeDetailObj__c': 'accessoryconsumedetailobj',  // 配件消耗明细
		'ReceiveMaterialBillObj': 'receivematerialbillobj', // 领料单
		'RefundMaterialBillObj': 'refundmaterialbillobj', // 退料单
		'PreventiveMaintenanceObj': 'preventivemaintenanceobj', // 维保计划
		'ServiceKnowledgeObj': 'serviceknowledgeobj', // 服务知识库
		'ServiceProjectObj': 'serviceprojectobj', // 服务项目
		'ServiceFaultCategoryObj': 'servicefaultcategoryobj', // 服务故障分类
		'ServiceFaultObj': 'servicefaultobj', // 服务故障（新）
		'DeviceComponentsObj': 'devicecomponentsobj', // 设备配件组成
		'CasesAccessoryUseInfoObj': 'casesaccessoryuseinfoobj', // 配件消耗明细
		// 'ActiveRecordObj': 'activerecordobj', // 跟进记录
		// 'TelesalesRecordObj':'telesalesrecordobj',//电销记录
		// 'ServiceLogObj':'servicelogobj',//服务记录
		// 'JournalObj':'journalobj',//日志
		// 'ScheduleObj': 'scheduleobj', //日程
		'TieredPriceBookObj': 'tieredpricebookobj', // 阶梯价目表
		'AccountAddrObj': 'accountaddrobj', // 客户地址
		'EmployeeSkillObj': 'employeeskillobj', // 员工技能
		// 'object_WorldDeliveryNote__c': 'object_worlddeliverynote__c', // 沃得发货单
		'ProductConstraintObj': 'productconstraintobj', // 产品约束条件
		'PersonnelObj': 'personnelobj', //人员
		'DepartmentObj': 'departmentobj', //部门
		'AvailableRangeObj': 'availablerangeobj', // 可售范围
		'CommonUnitObj': 'commonunitobj', //常用单位
		'EnterpriseInfoObj': 'enterpriseinfoobj', //企业库
		"AttributeObj": 'attributeobj', //属性
		'DevicePlanObj': 'deviceplanobj',
		'AttributePriceBookObj': 'attributepricebookobj', //属性价目表
		'TPMActivityUnifiedCaseObj': 'tpmactivityunifiedcaseobj', //TPM活动统案
		'TPMActivityObj': 'tpmactivityobj', //TPM活动方案
		'OpeningBalanceSettingObj': 'openingbalancesettingobj', //期初设置
		'TPMActivityAgreementObj': 'tpmactivityagreementobj', //TPM活动协议
    	'TPMActivityProofObj': 'tpmactivityproofobj', //活动举证
		'TPMStoreWriteOffObj': 'tpmstorewriteoffobj', //门店费用核销
		'TPMDealerActivityCostObj': 'tpmdealeractivitycostobj', //TPM活动费用核销
		'TPMActivityItemCostStandardObj': 'tpmactivityitemcoststandardobj', //TPM 活动项目费用标准
    'TPMBudgetAccountObj': 'tpmbudgetaccountobj', //TPM 费用预算表
    'TPMBudgetCarryForwardObj': 'tpmbudgetcarryforwardobj', //TPM 费用预算结转
		'TPMBudgetDisassemblyObj': 'tpmbudgetdisassemblyobj', //TPM 费用预算拆解
		'TPMBudgetAccrualObj': 'tpmbudgetaccrualobj', //TPM 费用预算拆解
    'TPMBudgetTransferDetailObj': 'tpmbudgettransferdetailobj', //TPM 费用预算调整单
		'AggregateRuleObj': 'aggregateruleobj', //聚合值
		'PricePolicyObj': 'pricepolicyobj', //价格政策
		'PricePolicyLimitAccountObj': 'pricepolicylimitaccountobj', //价格政策限量客户
		'AggregateValueObj': 'aggregatevalueobj', //聚合值
		'CostAdjustmentNoteObj': 'costadjustmentnoteobj', // 成本调价单
		'AccountMainDataObj': 'accountmaindataobj', //客户主数据
		'CampaignMembersObj': 'campaignmembersobj', // 活动成员
		'AreaManageObj': 'areamanageobj',
		'SuccessfulStoreRangeObj': 'successfulstorerangeobj',
		'ElectronicSignObj': 'electronicsignobj', //电子签
		'ProductCollectionObj': 'productcollectionobj',
		'accessoryexchangeobj': 'accessoryexchangeobj',
		'ProjectTaskObj': 'projecttaskobj',
		'ProjectObj': 'projectobj',
		'ProjectStageObj': 'projectstageobj',
		'AccountFinInfoObj': 'accountfininfoobj', //客户财务
		'DeviceObj': 'deviceobj',
		'SaleContractObj': 'salecontractobj', //销售合同
        'ProductCategoryObj': 'productcategoryobj', //产品分类
		'MarketingAttributionObj': 'marketingactivity', //营销归因
		'ForecastRuleObj':'oppforecastobj',
		'dealerreturnapplicationobj': 'dealerreturnapplicationobj',
    	'purchasereturnnoteobj': 'purchasereturnnoteobj',
		'CouponPlanObj': 'couponplanobj',
		'RebateObj': 'rebateobj',
		'SalesStatementsObj':'salesstatementsobj', //业务员对账单
		'RebateRuleObj': 'rebateruleobj',
		'RebatePolicyObj': 'rebatepolicyobj', // 返利产生政策
		'ProcurementRuleObj':'procurementruleobj', //招标市场洞察
		'BiddingSubscriptionRulesObj':'biddingsubscriptionrulesobj',//标讯订阅规则
		'BiddingSubscriptionObj':'biddingsubscriptionobj',//历史标讯导入
		'HistoricalBiddingImportObj':'historicalbiddingimportobj',
		'ProcurementSearchObj':'procurementsearchobj',//招投标查询
		'BizQuerySearchObj':'BizQuerySearchObj',//工商查询
		'RFMRuleObj': 'rfmruleobj',  // RFM规则,
		'CompetitorObj': 'competitorobj',  // 竞争对手
		'logisticsinfoobj': 'logisticsinfoobj',
        'object_Ow91q__c': 'object_ow91q__c', //好丽友  订单产品替换
		'object_o1Eoa__c': 'object_o1eoa__c', //好丽友  无订单退货
		'NewAdvertisementObj':'newadvertisementobj',  //广告（new）
		'ProjectResourceObj': 'projectresourceobj',  //项目资源
		'ReconciliationPlanObj': 'reconciliationplanobj',  // 对账方案
		'TransactionStatementObj': 'transactionstatementobj',  // 发货对账单
		'SparePartsReturnObj': 'sparepartsreturnobj', // 备件退运单
		'QualityInspectionRuleObj': 'qualityinspectionruleobj',  //质检规则设置
		'SaleActionNewObj': 'saleactionnewobj',  // 商机销售流程
		'AccountTreeRelationObj': 'accounttreerelationobj',  //客户树关系(主数据)
		'SubAccountTreeRelationObj': 'subaccounttreerelationobj',  //客户树关系
        'AttributeConstraintObj': 'attributeconstraintobj', // 属性级联约束
		'AdvancedFormulaObj': 'advancedformulaobj',  //高级公式
		'OpportunityDecisionChainObj': 'opportunitydecisionchainobj', // 商机决策链关系
		'OpportunityDecisionChainDetailObj': 'opportunitydecisionchaindetailobj', //商机决策链关系明细
		'WechatSessionAnalysisMasterObj': 'wechatsessionanalysismasterobj',  //企微会话分析规则
		'EnterpriseRiskObj': 'enterpriseriskobj',  //企业风险预警规则
		'ServiceMarketObj': 'servicemarketobj',     // 营销工作台
		'LoyaltyMemberObj': 'loyaltymemberobj',     // 会员管理
		'LoyaltyPointTypeObj': 'loyaltypointtypeobj',     // 积分分类
		'LoyaltyProgramObj': 'loyaltyprogramobj',     // 忠诚度计划
		'IncentiveMetricObj': 'incentivemetricobj', // 激励指标
		'IncentivePolicyObj': 'incentivepolicyobj', // 激励政策
		'IncentivePolicyRuleObj': 'incentivepolicyruleobj', // 激励政策规则
		'ExtendedAttributeObj': 'extendedattributeobj', // 高级属性
		'TransactionEventObj': 'transactioneventobj', // 交易事件
		'IncentiveCategoryObj': 'incentivecategoryobj', // 激励分类
		'ExtendedAttributePolicyObj': 'extendedattributepolicyobj', // 高级属性政策记录
		'ExtendedAttributeMemberObj': 'extendedattributememberobj', // 高级属性会员记录
		'ExtendedAttributeEventObj': 'extendedattributeeventobj', // 高级属性事件记录
		'ExtendedAttributeMemberIncentiveObj': 'extendedattributememberincentiveobj', // 高级属性会员激励记录
		'PartnerAgreementObj':'partneragreementobj', //合作伙伴协议
		'AccountworkBenchobj':'accountworkbenchobj',//经营计划伪造的对象
		'ProjectBudgetObj': 'projectbudgetobj',  //项目预算
		'CouponInstanceObj': 'couponinstanceobj', // 优惠券实例
		'AgentPromptVersionObj':'agentpromptversionobj',//提示词规则
		'SalaryRuleObj': 'salaryruleobj', // 工资规则
	};

	/**
	 * @desc 版本文件
	 * http://wiki.firstshare.cn/pages/viewpage.action?pageId=********
	 *
	 * TODO 打到模板中
	 */
	var verInfo = {
		'basic_edition': $t('crm.basicVersion'),
		'wechat_standard_edition': $t('crm.standardWechatVersion'),
		'kdweibo_edition': $t('crm.cloudHomeVersion'),
		'kis_edition': $t('crm.KISVersion'),

		'dealer_edition': $t('经销商版'),
		'agent_edition': $t('代理商版'),
		'promotion_sales_edition': $t('crm.visitToPinVersion'),
		'wechat_standardpro_edition': $t('crm.professionalWechatVersion'),

		'standardpro_edition': $t('专业版'),
		'strengthen_edition': $t('crm.Ultimate'),

		'enterprise_edition': $t('crm.groupVersion'),
		'office_edition': $t('crm.officeVersion')
	};


	var isGrayUrlReg = !FS.util.getUserAttribute('isGrayUrlReg');
    var regUrl = '';
    if(isGrayUrlReg) {
        regUrl = /^(((http[s]?|ftp):\/\/|www\.)[a-z0-9\.\-]+\.([a-z]{2,4})|((http[s]?|ftp):\/\/)?(([012]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))(\.(([012]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))){3})(:\d+)?(\/[a-z0-9\$\^\*\+\?\(\)\{\}\.\-_~!@#%&:;\/=<>]*)?/gi;
    } else {
        regUrl = /^(((http[s]?|ftp):\/\/|[a-zA-Z0-9]+\.)[a-z0-9.-]+\.[a-z0-9-]{2,}|((http[s]?|ftp):\/\/)?($([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|(([01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}([01]?\d{1,2}|2[0-4]\d|25[0-5])))(:\d+)?(\/[\w\$\^\*\+\?\(\)\{\}._~!@#%&:;\/=<>-]*)?/i;
    }

	/**
	 * ======================================================
	 * @desc 对外调用
	 * 不可再外部修改
	 * ======================================================
	 */
	var config = Object.freeze({
		VERSION: version, // 版本
		ROOT_PATH: rPath, // 根目录
		ASSETS_PATH: aPath, // 静态资源幕布
		MODULES_PATH: mPath, // 模块目录

		objDes: objDes, // 对象描述
		ObjFolder: ObjFolder,
		detailActionConfig: detailActionConfig, // 详情action配置
		verInfo: verInfo, // 版本信息
		// 一些特殊文本
		TEXT_LOADING: $t("加载中"),
		TEXT_DEFAULT: '--',
		TEXT_HIDE: '*****',
		HIDE_Field_TEXT: '*****', // 隐藏字段的替换文本
		COMPANY_NAME: $t("纷享销客"),
		MANAGER_ROLE_CODE: '00000000000000000000000000000006', // 管理员rolecode

		NUM_TIME_MIN: -9223372036854776000, // 时间类型的最小值
		NUM_TIME_MIN2: -28800000, // 时间默认值 1970年
		NUM_TIME_DEFAULT: 946656000000, // 时间默认值20000
		NUM_TIME_DEFAULT2: -62135596800000, // 时间默认值
		NUM_MIN: -2147483648, // 数字类型最小值
		Max_MONEY: 99999999999999.9999, // 金额最大值
		Min_MONEY: -79228162514264337593543950335, // 金额最小值
		NA: 'N/A',

		// 一些正则表达式
		regEmail: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
		regTel: /^(\d|\+|-|\(|\)){1,100}$/, // 业务的手机类型
		regUrl: regUrl
	});


	/**
	 * ====================================================
	 * @desc 配置seajs 引用路径
	 * seajs
	 * ====================================================
	 */
	seajs.config({
		paths: {
			'crm-assets': config.ASSETS_PATH,
			'crm-widget': config.ASSETS_PATH + '/widget',
			'crm-connect': config.ROOT_PATH + '/connect',
			'crm-mock': config.ROOT_PATH + '/mock',
			'crm-parse': config.ROOT_PATH + '/parse',
			'crm-modules': config.MODULES_PATH,
			'crm-components': config.MODULES_PATH + '/components',
			'crm-vcomponents': config.MODULES_PATH + '/vcomponents',
			'crm-detail': config.MODULES_PATH + '/detail',
			'crm-action': config.MODULES_PATH + '/action',
			'crm-api': config.MODULES_PATH + '/api',
			'crm-frurl': config.ROOT_PATH + '/frurl',
		}
	});
	module.exports = config;
});
