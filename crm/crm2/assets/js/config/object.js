/**
 * @file object.js
 * @desc crm对象配置文件
 *
 */
define(function (require, exports, module) {

	var objs = [
		["crm.销售线索", 'LeadsObj', 1, 101, 'SalesClueID', 'SalesClueName', false],
		["crm.客户", 'AccountObj', 2, 102, 'CustomerID', '', false],
		["crm.联系人", 'ContactObj', 3, 103, 'ContactID', '', false],
		["crm.产品", 'ProductObj', 4, 104, 'ProductID', '', true],
		["crm.退款", 'RefundObj', 6, 106, 'TradeRefundID', 'TradeRefundCode', false],
		["crm.商机", 'OpportunityObj', 8, 108, 'OpportunityID', '', false],
		["crm.开票申请", 'InvoiceApplicationObj', 9, 109, 'TradeBillID', 'TradeBillCode', false],
		["crm.销售订单", 'SalesOrderObj', 11, 111, 'CustomerTradeID', 'TradeCode', false],
		["crm.退货单", 'ReturnedGoodsInvoiceObj', 12, 112, 'ReturnOrderID', 'ReturnOrderCode', false],
		["crm.拜访", 'VisitingObj', 13, 113, 'VisitID', 'VisitName', false],
		["crm.合同", 'ContractObj', 16, 116, 'ContractID', 'ContractNo', false],
		["竞争对手", 'RivalObj', 19, 121, 'CompetitorID', '', false],
		["crm.市场活动", 'MarketingEventObj', 20, 120, 'MarketingEventID', '', false],
		["盘点", 'InventoryObj', 21, 122, 'FormTemplateID', '', false],
		["crm.退货单产品", 'ReturnedGoodsInvoiceProductObj', 27, -1, '', false],
		["crm.销售订单产品", 'SalesOrderProductObj', 28, 128, '', false],
		["crm.客户附件", 'AccountAttObj', 1000, 10001, '', false], // 特殊
		["crm.客户账户", 'CustomerAccountObj', 'CustomerAccountObj', 200, '_id', true],
		["crm.账户收支流水", 'AccountTransactionFlowObj', 'AccountTransactionFlowObj', 200, '_id', true],
		["crm.账户校验规则", 'AccountCheckRuleObj', 'AccountCheckRuleObj', 200, '_id', true],
		["crm.任务管理", 'RewardTaskObj', 'RewardTaskObj', 200, '_id', true],
		["crm.账户授权", 'FAccountAuthorizationObj', 'FAccountAuthorizationObj', 200, '_id', true],
		["crm.客户授信", 'CustomerCreditAuthObj', 'CustomerCreditAuthObj', 200, '_id', true],
		["crm.互联企业", 'EnterpriseRelationObj', 'EnterpriseRelationObj', 200, '_id', true],
		["crm.互联帐号", 'PublicEmployeeObj', 'PublicEmployeeObj', 200, '_id', true],
		["crm.互联部门", 'ErDepartmentObj', 'ErDepartmentObj', 200, '_id', true],
		["crm.信用档案", 'CreditFileObj', 'CreditFileObj', 200, '_id', true],
		["crm.预存款", 'PrepayDetailObj', 'PrepayDetailObj', 200, '_id', true],
		["crm.返利", 'RebateIncomeDetailObj', 'RebateIncomeDetailObj', 200, '_id', true],
		["crm.价目表", 'PriceBookObj', 'PriceBookObj', 200, '_id', true],
		["crm.价目表明细", 'PriceBookProductObj', 'PriceBookProductObj', 200, '_id', true],
		["crm.促销", 'PromotionObj', 'PromotionObj', 200, '_id', true],
		["crm.核销单", 'MatchNoteObj', 'MatchNoteObj', 200, '_id', true],
		["crm.销售发票", 'SalesInvoiceObj', 'SalesInvoiceObj', 200, '_id', true],
		["crm.报价单", 'QuoteObj', 'QuoteObj', 200, '_id', true],
		["crm.报价单明细", 'QuoteLinesObj', 'QuoteLinesObj', 200, '_id', true],
		["crm.回款", 'PaymentObj', 'PaymentObj', 200, '_id', true],
		["crm.payobj.form.title", 'PayObj', 'PayObj', 200, '_id', true],//付款单
		["crm.回款计划", 'PaymentPlanObj', 'PaymentPlanObj', 200, '_id', true],
		["crm.回款明细", 'OrderPaymentObj', 'OrderPaymentObj', 200, '_id', true],
		['crm.AccountsReceivableNoteObj', 'AccountsReceivableNoteObj', 'AccountsReceivableNoteObj', 200, '_id', true],
		["crm.仓库", 'WarehouseObj', 'WarehouseObj', 200, '_id', true],
		["crm.库存", 'StockObj', 'StockObj', 200, '_id', true],
		["crm.入库单", 'GoodsReceivedNoteObj', 'GoodsReceivedNoteObj', 200, '_id', true],
		["crm.发货单", 'DeliveryNoteObj', 'DeliveryNoteObj', 200, '_id', true],
		["crm.调拨单", 'RequisitionNoteObj', 'RequisitionNoteObj', 200, '_id', true],
		["crm.出库单", 'OutboundDeliveryNoteObj', 'OutboundDeliveryNoteObj', 200, '_id', true],
		["crm.备件申请", 'SparePartsApplicationObj', 'SparePartsApplicationObj', 200, '_id', true],
		["crm.备件退运单", 'SparePartsReturnObj', 'SparePartsReturnObj', 200, '_id', true],
		["crm.个人库调整单", 'EmployeeWarehouseAdjustmentNoteObj', 'EmployeeWarehouseAdjustmentNoteObj', 200, '_id', true],
		["crm.换货单", 'ExchangeGoodsNoteObj', 'ExchangeGoodsNoteObj', 200, '_id', true],
		["crm.退换货单", 'ExchangeReturnNoteObj', 'ExchangeReturnNoteObj', 200, '_id', true],
		["crm.内部签章认证", 'InternalSignCertifyObj', 'InternalSignCertifyObj', 200, '_id', true],
		["crm.客户签章认证", 'AccountSignCertifyObj', 'AccountSignCertifyObj', 200, '_id', true],
		["crm.签署记录", 'SignRecordObj', 'SignRecordObj', 200, '_id', true],
		["crm.工单", 'CasesObj', 'CasesObj', 200, '_id', true],
		["crm.合作伙伴", 'PartnerObj', 'PartnerObj', 200, '_id', true],
		["crm.目标值", 'GoalValueObj', 'GoalValueObj', 200, '_id', true],
		["Erp仓库", 'ErpWarehouseObj', 'ErpWarehouseObj', 200, '_id', true],
		["crm.ERP库存", 'ErpStockObj', 'ErpStockObj', 200, '_id', true],
		["crm.返利使用规则", 'RebateUseRuleObj', 'RebateUseRuleObj', 200, '_id', true],
		["crm.广告", 'AdvertisementObj', 'AdvertisementObj', 200, '_id', true],
		["crm.对账单", 'StatementObj', 'StatementObj', 200, '_id', true],
		["crm.盘点单", 'StockCheckNoteObj', 'StockCheckNoteObj', 200, '_id', true],
		["crm.出入库明细", 'StockDetailsObj', 'StockDetailsObj', 200, '_id', true],
		["crm.商机2", 'NewOpportunityObj', 'NewOpportunityObj', 200, '_id', true],
		["crm.配件调拨记录", 'AccessoryExchangeObj', 'accessoryexchangeobj', 200, '_id', true],
		["crm.经销商退货申请", 'DealerReturnApplicationObj', 'dealerreturnapplicationobj', 200, '_id', true],
		["crm.采购退货单", 'PurchaseReturnNoteObj', 'purchasereturnnoteobj', 200, '_id', true],
		["crm.物流信息", 'LogisticsInfoObj', 'logisticsinfoobj', 200, '_id', true],
		["NewOpportunityLinesObj.attribute.self.display_name", 'NewOpportunityLinesObj', 'NewOpportunityLinesObj', 200, '_id', true],
		["批次", 'BatchObj', 'BatchObj', 200, '_id', true],
		["序列号", 'SerialNumberObj', 'SerialNumberObj', 200, '_id', true],
		["自定义对象", 'MyObject', -1, 200, '_id', true],
		["SpecificationObj.attribute.self.display_name", 'SpecificationObj', 'SpecificationObj', 200, '_id', true],
		["SpecificationValueObj.attribute.self.display_name", 'SpecificationValueObj', 'SpecificationValueObj', 200, '_id', true],
		["SPUObj.attribute.self.display_name", 'SPUObj', 'SPUObj', 200, '_id', true],
		["crm.费用结算单", 'FeeSettlementBillObj', 'FeeSettlementBillObj', 200, '_id', true],
		["crm.费用结算明细", 'FeeDetailObj', 'FeeDetailObj', 200, '_id', true],
		["ProductObj.attribute.self.display_name", 'ProductObj', 'ProductObj', 200, '_id', true],
		['外勤', "CheckinsObj", "CheckinsObj", 200, '_id', true],
		['外勤路线', "VisitRouteObj", "VisitRouteObj", 200, '_id', true],
		['crm.采购订单', 'PurchaseOrderObj', 'PurchaseOrderObj', 200, '_id', true],
		['crm.供应商', 'SupplierObj', 'SupplierObj', 200, '_id', true],
		["crm.配件消耗表", 'object_AccessoryConsumeObj__c', 'object_AccessoryConsumeObj__c', 200, '_id', true],
		// ["crm.配件消耗明细", 'object_AccessoryConsumeDetailObj__c', 'object_AccessoryConsumeDetailObj__c', 200, '_id', true],
		["crm.领料单", 'ReceiveMaterialBillObj', 'ReceiveMaterialBillObj', 200, '_id', true],
		["crm.退料单", 'RefundMaterialBillObj', 'RefundMaterialBillObj', 200, '_id', true],
		["服务知识库", 'ServiceKnowledgeObj', 'ServiceKnowledgeObj', 200, '_id', true],
		["服务项目", 'ServiceProjectObj', 'ServiceProjectObj', 200, '_id', true],
		["阶梯价目表", 'TieredPriceBookObj', 'TieredPriceBookObj', 200, '_id', true],
		["产品约束条件", 'ProductConstraintObj', 'ProductConstraintObj', 200, '_id', true],
		["服务故障分类", 'ServiceFaultCategoryObj', 'ServiceFaultCategoryObj', 200, '_id', true],
		["服务故障", 'ServiceFaultObj', 'ServiceFaultObj', 200, '_id', true],
		["设备配件组成", 'DeviceComponentsObj', 'DeviceComponentsObj', 200, '_id', true],
		["配件消耗明细", 'CasesAccessoryUseInfoObj', 'CasesAccessoryUseInfoObj', 200, '_id', true],
		["邮件", 'MailObj', 'MailObj', 200, '_id', true],
		["通话记录", 'ServiceRecordObj', 'ServiceRecordObj', 200, '_id', true],
		["支付渠道信息", 'PayChannelObj', 'PayChannelObj', 200, '_id', true],
		["企业支付订单", 'enterprisepayorderobj', 'EnterprisePayOrderObj', 200, '_id', true],
		["咨询问题记录", 'ConsultQuestionRecordObj', 'ConsultQuestionRecordObj', 200, '_id', true],
		["员工技能", 'EmployeeSkillObj', 'EmployeeSkillObj', 200, '_id', true],
		// ["跟进记录", 'ActiveRecordObj', 'ActiveRecordObj', 200, '_id', true],
		// ["电销记录", 'TelesalesRecordObj', 'TelesalesRecordObj', 200, '_id', true],
		// ["服务记录", 'ServiceLogObj', 'ServiceLogObj', 200, '_id', true],
		// ["日志", 'JournalObj', 'JournalObj', 200, '_id', true],
		// ["日程", 'ScheduleObj', 'ScheduleObj', 200, '_id', true],
		["客户地址", 'AccountAddrObj', 'AccountAddrObj', 200, '_id', true],
		["沃得发货单", 'object_WorldDeliveryNote__c', 'object_WorldDeliveryNote__c', 200, '_id', true],
		["订购申请", 'object_requisition__c', 'object_requisition__c', 200, '_id', true],
		["crm.维保计划", 'PreventiveMaintenanceObj', 'PreventiveMaintenanceObj', 200, '_id', true],
		["人员", 'PersonnelObj', 'PersonnelObj', 200, '_id', true],
		["部门", 'DepartmentObj', 'DepartmentObj', 200, '_id', true],
		["可售范围", 'AvailableRangeObj', 'AvailableRangeObj', 200, '_id', true],
		["常用单位设置", 'CommonUnitObj', 'CommonUnitObj', 200, '_id', true],
		["企业库", 'EnterpriseInfoObj', 'EnterpriseInfoObj', 200, '_id', true],
		["AttributeObj.attribute.self.display_name", 'AttributeObj', 'AttributeObj', 200, '_id', true],
		["设备计划", 'DevicePlanObj', 'DevicePlanObj', 200, '_id', true],
		["设备", 'DeviceObj', 'DeviceObj', 200, '_id', true],
		["二维码", 'QrcodeObj', 'QrcodeObj', 200, '_id', true],
		["AttributePriceBookObj.attribute.self.display_name", 'AttributePriceBookObj', 'AttributePriceBookObj', 200, '_id', true],
		["TPM活动统案", 'TPMActivityUnifiedCaseObj', 'TPMActivityUnifiedCaseObj', 200, '_id', true],
		["crm.OpeningBalanceSettingObj.form.title", 'OpeningBalanceSettingObj', 'OpeningBalanceSettingObj', 200, '_id', true],//期初设置
		["TPM活动方案", 'TPMActivityObj', 'TPMActivityObj', 200, '_id', true],
		["TPM活动举证", 'TPMActivityProofObj', 'TPMActivityProofObj', 200, '_id', true],
		["TPM门店费用核销", 'TPMStoreWriteOffObj', 'TPMStoreWriteOffObj', 200, '_id', true],
		["TPM活动协议", 'TPMActivityAgreementObj', 'TPMActivityAgreementObj', 200, '_id', true],
		["TPM活动项目费用标准", 'TPMActivityItemCostStandardObj', 'TPMActivityItemCostStandardObj', 200, '_id', true],
		["TPM活动费用核销", 'TPMDealerActivityCostObj', 'TPMDealerActivityCostObj', 200, '_id', true],
		["费用预算表", 'TPMBudgetAccountObj', 'TPMBudgetAccountObj', 200, '_id', true],
		["预算结转", 'TPMBudgetCarryForwardObj', 'TPMBudgetCarryForwardObj', 200, '_id', true],
		["预算拆解", 'TPMBudgetDisassemblyObj', 'TPMBudgetDisassemblyObj', 200, '_id', true],
		["预算计提单", 'TPMBudgetAccrualObj', 'TPMBudgetAccrualObj', 200, '_id', true],
		["预算调整单", 'TPMBudgetTransferDetailObj', 'TPMBudgetTransferDetailObj', 200, '_id', true],
		["AggregateRuleObj.attribute.self.display_name", 'AggregateRuleObj', 'AggregateRuleObj', 200, '_id', true],
		["PricePolicyObj.attribute.self.display_name", 'PricePolicyObj', 'PricePolicyObj', 200, '_id', true],
		["PricePolicyLimitAccountObj.attribute.self.display_name", 'PricePolicyLimitAccountObj', 'PricePolicyLimitAccountObj', 200, '_id', true],
		["AggregateValueObj.attribute.self.display_name", 'AggregateValueObj', 'AggregateValueObj', 200, '_id', true],
		["crm.成本调价单", 'CostAdjustmentNoteObj', 'CostAdjustmentNoteObj', 200, '_id', true],
		["crm.客户主数据", 'AccountMainDataObj', 'AccountMainDataObj', 200, '_id', true],
		["区域管理", 'AreaManageObj', 'AreaManageObj', 200, '_id', true],
		["铺货/陈列标准定义", 'SuccessfulStoreRangeObj', 'SuccessfulStoreRangeObj', 200, '_id', true],
		["电子签", 'ElectronicSignObj', 'ElectronicSignObj', 200, '_id', true],
		["经营产品范围", 'ProductCollectionObj', 'ProductCollectionObj', 200, '_id', true],
		["任务", 'ProjectTaskObj', 'ProjectTaskObj', 200, '_id', true],
		["项目", 'ProjectObj', 'ProjectObj', 200, '_id', true],
		["项目阶段", 'ProjectStageObj', 'ProjectStageObj', 200, '_id', true],
		["crm.客户财务信息", 'AccountFinInfoObj', 'AccountFinInfoObj', 200, '_id', true],
		["销售合同", 'SaleContractObj', 'SaleContractObj', 200, '_id', true],
		["产品分类", 'ProductCategoryObj', 'ProductCategoryObj', 200, '_id', true],
		["营销归因", 'MarketingAttributionObj', 'MarketingAttributionObj', 200, '_id', true],
		["商机预测", 'ForecastRuleObj', 'ForecastRuleObj', 200, '_id', true],
		["CouponPlanObj.attribute.self.display_name", 'CouponPlanObj', 'CouponPlanObj', 200, '_id', true],
		["RebateObj.attribute.self.display_name", 'RebateObj', 'RebateObj', 200, '_id', true],
		["工资规则", 'SalaryRuleObj', 'SalaryRuleObj', 200, '_id', true],
		["SalesStatementsObj.attribute.self.display_name", 'SalesStatementsObj', 'SalesStatementsObj', 200, '_id', true],
		["RebateRuleObj.attribute.self.display_name", 'RebateRuleObj', 'RebateRuleObj', 200, '_id', true],
		["RebatePolicyObj.attribute.self.display_name", 'RebatePolicyObj', 'RebatePolicyObj', 200, '_id', true],
		["RFM规则", 'RFMRuleObj', 'RFMRuleObj', 200, '_id', true],
        ["竞争对手", 'CompetitorObj', 'CompetitorObj', 200, '_id', true],
        ["报告订阅规则", 'ProcurementRuleObj', 'ProcurementRuleObj', 200, '_id', true],
        ["订单产品替换", 'object_Ow91q__c', 'object_Ow91q__c', 200, '_id', true],
        ["无订单退货", 'object_o1Eoa__c', 'object_o1Eoa__c', 200, '_id', true],
		["广告2.0", 'NewAdvertisementObj', 'NewAdvertisementObj', 200, '_id', true],
		["招投标查询", 'ProcurementSearchObj', 'ProcurementSearchObj', 200, '_id', true],
		["工商查询", 'BizQuerySearchObj', 'BizQuerySearchObj', 200, '_id', true],
		["标讯公告", 'ProcurementInfoObj', 'ProcurementInfoObj', 200, '_id', true],
		["项目资源", 'ProjectResourceObj', 'ProjectResourceObj', 200, '_id', true],
		["标讯订阅规则", 'BiddingSubscriptionRulesObj', 'BiddingSubscriptionRulesObj', 200, '_id', true],
		["标讯订阅", 'BiddingSubscriptionObj', 'BiddingSubscriptionObj', 200, '_id', true],
		["市场洞察", 'MarketInsights', 'MarketInsights', 200, '_id', true],
		["对账方案", 'ReconciliationPlanObj', 'ReconciliationPlanObj', 200, '_id', true],
		["交易对账单", 'TransactionStatementObj', 'TransactionStatementObj', 200, '_id', true],
		["历史标讯导入", 'HistoricalBiddingImportObj', 'HistoricalBiddingImportObj', 200, '_id', true],
		["质检设置", 'QualityInspectionRuleObj', 'QualityInspectionRuleObj', 200, '_id', true],
		['质检结果', 'QualityInspection', 'QualityInspection', 200, '_id', true],
		['crm.商机销售流程', 'SaleActionNewObj', 'SaleActionNewObj', 200, '_id', true],
		['穿透工作台', 'PivotTableRuleObj', 'PivotTableRuleObj', 200, '_id', true],
		['关系模版', 'RelationTemplateObj', 'RelationTemplateObj', 200, '_id', true],
		['互动策略', 'InteractionStrategyObj', 'InteractionStrategyObj', 200, '_id', true],
		['客户部门', 'AccountDepartmentObj', 'AccountDepartmentObj', 200, '_id', true],
		// 运营策略规则
		['OperationsStrategyObj.attribute.self.display_name', 'OperationsStrategyObj', 'OperationsStrategyObj', 200, '_id', true],
		// 运营活动
		['OperationsActivityObj.attribute.self.display_name', 'OperationsActivityObj', 'OperationsActivityObj', 200, '_id', true],
		['客户树关系', 'AccountTreeRelationObj', 'AccountTreeRelationObj', 200, '_id', true],
		['客户树关系', 'SubAccountTreeRelationObj', 'SubAccountTreeRelationObj', 200, '_id', true],
		['属性级联约束', 'AttributeConstraintObj', 'AttributeConstraintObj', 200, '_id', true],
		['crm.PartnerAgreementObj.name', 'PartnerAgreementObj', 'PartnerAgreementObj', 200, '_id', true],
		['AdvancedFormulaObj.attribute.self.display_name', 'AdvancedFormulaObj', 'AdvancedFormulaObj', 200, '_id', true],
		['商机决策链关系', 'OpportunityDecisionChainObj', 'OpportunityDecisionChainObj', 200, '_id', true],
		['商机决策链关系明细', 'OpportunityDecisionChainDetailObj', 'OpportunityDecisionChainDetailObj', 200, '_id', true],
		['企微会话分析规则', 'WechatSessionAnalysisMasterObj', 'WechatSessionAnalysisMasterObj', 200, '_id', true],
		['企业风险预警规则', 'EnterpriseRiskObj', 'EnterpriseRiskObj', 200, '_id', true],
		['营销方案', 'ServiceMarketObj', 'ServiceMarketObj', 200, '_id', true],
		// 会员管理
		['LoyaltyMemberObj.attribute.self.display_name', 'LoyaltyMemberObj', 'LoyaltyMemberObj', 200, '_id', true],
		// 积分分类
		['crm_action_incentivepolicyruleobj_8', 'LoyaltyPointTypeObj', 'LoyaltyPointTypeObj', 200, '_id', true],
		// 忠诚度计划
		['crm_action_incentivemetricobj_1', 'LoyaltyProgramObj', 'LoyaltyProgramObj', 200, '_id', true],
		// 激励指标
		['IncentiveMetricObj.attribute.self.display_name', 'IncentiveMetricObj', 'IncentiveMetricObj', 200, '_id', true],
		// 激励政策
		['ExtendedAttributeMemberIncentiveObj.field.policy_id.label', 'IncentivePolicyObj', 'IncentivePolicyObj', 200, '_id', true],
		// 高级属性
		['ExtendedAttributeObj.attribute.self.display_name', 'ExtendedAttributeObj', 'ExtendedAttributeObj', 200, '_id', true],
		// 交易事件
		['TransactionEventObj.attribute.self.display_name', 'TransactionEventObj', 'TransactionEventObj', 200, '_id', true],
		// 激励分类
		['IncentiveCategoryObj.attribute.self.display_name', 'IncentiveCategoryObj', 'IncentiveCategoryObj', 200, '_id', true],
		// 高级属性激励政策记录
		['ExtendedAttributePolicyObj.attribute.self.display_name', 'ExtendedAttributePolicyObj', 'ExtendedAttributeMemberObj', 200, '_id', true],
		// 高级属性会员记录
		['ExtendedAttributeMemberObj.attribute.self.display_name', 'ExtendedAttributeMemberObj', 'ExtendedAttributeMemberObj', 200, '_id', true],
		// 高级属性交易事件记录
		['ExtendedAttributeEventObj.attribute.self.display_name', 'ExtendedAttributeEventObj', 'ExtendedAttributeMemberObj', 200, '_id', true],
		// 高级属性会员激励记录
		['ExtendedAttributeMemberIncentiveObj.attribute.self.display_name', 'ExtendedAttributeMemberIncentiveObj', 'ExtendedAttributeMemberObj', 200, '_id', true],
		['sfa.coupon_instance_obj.self.display_name', 'CouponInstanceObj', 'CouponInstanceObj', 200, '_id', true], //优惠券实例
		['经营计划', 'AccountworkBenchobj', 'AccountworkBenchobj', 200, '_id', true],//特殊不存在的对象
		["项目预算", 'ProjectBudgetObj', 'ProjectBudgetObj', 200, '_id', true], //项目预算
		["提示词规则", 'AgentPromptVersionObj', 'AgentPromptVersionObj', 200, '_id', true],//提示词规则
	];

	var obj = {};

	_.each(objs, function (item) {

		const temp = {
			name: item[0],
			apiName: item[1],
			objectType: item[2], // 区分对象(老对象用到 新版迁移统一用apiName)
			source: item[3], // 发送feed的对象区分 server定义
			mainKey: item[4],
			mainName: item[5] || 'Name',
			isFuture: item[item.length - 1] // 此字段为临时添加; 未来统一时要删除
		}

		const handler = {
			get: function(obj, prop) {
				if (prop === 'name') {
					return $t(temp.name);
				} else {
					return temp[prop];
				}
			}
		}

		const proxy = new Proxy(temp, handler);

		obj[item[2]] = obj[item[1].toLowerCase()] = proxy;

	});

	module.exports = obj;

});
