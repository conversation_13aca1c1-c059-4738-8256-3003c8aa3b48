/**
 * @desc crm路由控制中心
 * 自动分发路由
 * 补充全crm的单页模板
 * 页面开始(start):  module.render方法
 * 页面切换(switch): module.switchPage方法
 * 页面关闭前(beforeClose): module.beforeClosePage方法 返回false不关闭页面
 * 页面关闭(close):  module.closePage方法
 * 布局resize:  module.onLayoutResize方法
 * 页面属性:   module.__leaveConfig 离开的配置参数
 * 路由跳转 navigate(hash) 方法
 * 页面加载 route(module, submod, param) 指定加载的模块  不触发路由
 * author: qigb
 */
define(function(require, exports, module) {

    var config = require("./config/config"),
        util = require("crm-modules/common/util"),
        Layout = require("./layout/layout"),
        Aside = require("./layout/aside/aside"),
        baseData = require("./layout/aside/data");

    var isRedirection = false;

	//
	//
	// 有业务修改了FS_TEMPLATE_DATA
	// 25号临时修改
	// TODO 业务线修改
	var TEMPCHANNEL = window.FS_TEMPLATE_DATA ? window.FS_TEMPLATE_DATA.userWebMainChannelVO : null;
	var TEMPCHANNELLIST = TEMPCHANNEL ? util.deepClone(TEMPCHANNEL.mainChannelMenuVOList || []) : null;


    //
    // 列表的路由特殊处理对象
    //
    var RouteSpecialObj = [
        // 预置对象
        'ProjectObj',            // 项目管理
        'ProjectTaskObj',       // 项目管理任务
        'AccountObj',           // 客户
        'AreaManageObj',        // 片区管理
        'ServiceRecordObj',           // 通话记录
        'ConsultQuestionRecordObj', // 咨询问题
        'CrmRival',             // 竞争对手
        'GoalValueObj',         // 目标规则
        'HighSeasObj',          // 公海 c
		'LeadsPoolObj',         // 线索池
		'LeadsObj',         	// 销售线索
		'ContactObj',         	// 联系人
		'MailObj',         	    // 邮件
		"PayChannelObj",		//支付渠道信息
		"EnterprisePayOrderObj", //企业支付订
        'OpportunityObj',       // 商机
        'NewOpportunityObj',    // 商机2.0
        'PivotTableRuleObj',    // 穿透工作台
        'RelationTemplateObj',    // 关系模版
        'InteractionStrategyObj',    // 互动策略
        'AccountDepartmentObj',    // 客户部门
        'OperationsStrategyObj',    //运营策略规则
        'PivotTableInstanceObj',// 穿透规则明细
        'PartnerObj',           // 合作伙伴
        'SalesOrderObj',        // 销售订单
		'PaymentObj',           // 回款
		'OrderPaymentObj',		// 回款明细
        'VisitingObj',          // 拜访，走老逻辑
        'InventoryObj',         // 盘点，走老逻辑
        'WarehouseObj',         // 仓库
        'StockObj',             // 库存
        'StockDetailsObj',      // 出入库明细
        'SPUObj',               // 商品
        'ProductObj',           // 产品
        'ProductCategoryObj',   // 产品分类
        'PriceBookObj',         // 价目表
        'PriceBookProductObj',  // 价目表明细
        'PromotionObj',         // 促销，有引导
		"QuoteLinesObj",        // 报价单明细
		// "ActiveRecordObj",      //跟进记录
		// 'TelesalesRecordObj',   //电销记录
		// 'ServiceLogObj',         //服务记录
		// 'JournalObj',         //日志
		// 'ScheduleObj',           //日程
        "object_Nf6iC__c" ,     // 万马特殊需求 二开自定义对象列表
        "object_H2hvc__c" ,     // 好丽友 分配依据登录，
        "object_6h08G__c" ,     // 好丽友 汉询同步订单
        "object_Ow91q__c" ,     // 好丽友 订单产品替换
        'object_21Rp7__c',      // 好丽友 订单拆分比例
		'object_l7z1t__c',      // 好丽友 订单拆分比例试算预览
		'object_e2k54__c',      // 好丽友 im日报
        "object_6i6vR__c" ,     // 促销计划，邦士客开
        "AbutmentLog__c" ,     // A8对接
        "RewardTaskObj",        // 任务管理
		"RewardMetricObj",      // 任务指标
        "CheckinsObj",          // 高级外勤
		"AttributeObj",          // 属性
        "SLAResultObj",
        "CustomerServiceSessionObj",
        "fee_release__c",          //费用发放
		'CampaignMembersObj',       // 活动成员
		'DeliveryNoteObj',             // 发货单
        'SparePartsReturnObj',     //备件退运单
		'SparePartsDeliveryObj',         // 备件发运单
		'RequisitionNoteObj',         // 调拨单
        "SparePartsMaintenancePlanObj",// 备件维修计划
		'AccountTransactionFlowObj', 	// 账户收支流水
		'AccountCheckRuleObj', 			// 账户校验规则
		'AccountFrozenRecordObj',		// 账户冻结记录
	 	'ShopCategoryObj',                  // 商城类目对象
		'ShopMall',                     // 商城入口，非对象名称
		'IsolatedMall',                 // 独立商城，非对象名称
		'QuicklyOrder',                 // 快速下单入口，非对象名称
		'PromotionActivity',            // 促销活动
		'MyProducts',                     // 【渠道分销】我的商品，非对象名称
		'EnterpriseRelationObj',        // 互联企业
		'PublicEmployeeObj',            // 互联帐号
        'ErDepartmentObj',              // 互联部门
        'EngineerInfoObj',      // 服务工程师信息
        'TPMActivityBudgetObj',         //活动预算管理
        // 'TPMActivityProofObj', //活动举证
        'TPMStoreWriteOffObj', //门店费用核销
        'TPMDealerActivityCostObj',  //经销商费用核销
        'TPMActivityObj',  //活动方案
        'TPMActivityUnifiedCaseObj', //活动统案
        'EngineerInfoObj',      // 服务工程师信息
        'CasesObj',             // 工单
        'ForecastRuleObj',
        'TPMBudgetAccountObj',   //TPM 预算表
        'PricePolicyObj',        // 价格政策
        'QualityDocumentQuery', // 振德 质检单查询
        'MarketInsights', // 标讯市场洞察
        'ProcurementSearchObj',//招投标二期
        'BizQuerySearchObj',//工商查询
        'ProcurementInfoObj',//招投标二期标讯公告
        'ProjectResourceObj',  //项目资源
        'DeviceObj',            // 设备
        'QrcodeObj',            // 二维码
        'MonitorCenter',       // 监控中心（非常规列表）
        'WechatSessionObj',       // 企业微信会话存档
        'PurchaseOrderObj',       // 采购订单
		'NewCustomerAccountObj', // 客户账户余额
        'PromoterObj',         // 促销员对象
        'ReturnedGoodsInvoiceObj',  //退货单
        'RebateObj', //返利单
        'SalesStatementsObj', //业务员对账单
        'IncentiveMetricObj',  // 激励指标
        "IncentivePolicyObj",  // 激励政策
        "IncentivePolicyRuleObj",  // 激励政策规则
        "IncentiveCategoryObj", // 激励分类
        "ExtendedAttributeObj", // 高级属性
        "AccountworkBenchobj",
        "CasesCheckinsObj",
        "LoyaltyMemberObj", // 会员管理
        "LoyaltyPointPoolObj", // 积分池
        "CouponInstanceObj", // 优惠券实例
        "SalaryRuleObj", // 工资规则
    ];

    var control = Object.create({

		cleared: true,  // 默认是否已经清楚

		cached: false, // 是否在缓存

		cacheShowStatus: false,

		cacheMaxLink: 10,  // 当外部链接发生10变化时，强制清缓存

		cacheLinkNum: 0,

        /**
         * 将RouteSpecialObj暴露出去,paasapp使用
         */
        specialRoutes: RouteSpecialObj,

        /**
 		 * @type {Object}
		 * @desc 记录当前页面信息
		 */
		curPage: {
			path:   '',            // 模块文件路径
			$el:    null,          // 当前页面最外层元素
			cache:  false,         // crm模块暂不考虑缓存机制
            module: null,          // 当前页面module对象
			widget: null,          // TODO收集页面用到的组件 暂不考虑
		},

        // 分版code
        verCode: {},

        // 新建权限
        newAuthCode: {},

        // 路由次数
        //
        _routeNum:    0,

        // 最大路由次数
        //
        _routeMaxNum: 50,

        // 是否开启首页，最近访问，待办，提醒自定义功能
        isGrayMenuCustomize: Fx.util.getGray("paasIsGrayMenuCustomize"),
        
        /**
         * 获取菜单数据并缓存，支持Promise和回调两种方式
         * @param {Function} callback 可选的回调函数
         * @return {Promise} 返回Promise对象，便于异步处理
         */
        getMenuData: function(callback) {
            var me = this;
            return new Promise(function(resolve) {
                var menuData = Fx.util.getGray("newCRMAllMenuGray") ? window.CRM_All_menu_new : window.CRM_All_menu;
                if (menuData) {
                    callback && callback(menuData);
                    resolve(menuData);
                } else {
                    // 缓存无效，重新获取数据
                    CRM.util.getCrmCurrentMenu(function (menuData) {
                        callback && callback(menuData);
                        resolve(menuData);
                    });
                }
            });
        },

        /**
         * 查找有效的第一个菜单项
         * @param {Array} items 菜单项列表
         * @return {Object|null} 找到的菜单项或null
         */
        findValidMenuItem: function(items) {
            if (!items || !items.length) return null;

            // 过滤项配置，后续可以方便地扩展更多过滤项
            var filterIds = {
                'remind': true,
                'todo': true,
                'recent': true,
                'fs.crm.notify': true,
                'fs.todo': true
            };

            for (var i = 0; i < items.length; i++) {
                var item = items[i];

                // 检查是否是分组且有子项
                if (item.type === 'group' && item.children && item.children.length > 0) {
                    // 从子项中查找有效菜单
                    var validChild = this.findValidMenuItem(item.children);
                    if (validChild) return validChild;
                }
                // 检查非分组项是否有效
                else if (!filterIds[item.id] && item.type !== 'group') {
                    return item;
                }
            }

            return null;
        },

        /**
         * @desc 获取分版信息
         * verCode  分版code信息 正确测试应该server返回布局
         * verType: 版本类型 3 4 5 6,
         * isMini： 是否为mini版本 verType == 3
         * TODO: 前端正确姿势应该server直接下发最终布局，不应再前端处理,
         * 跟踪技术方案的变化
         * 2017-03 -16
         *  基础版     basic_edition
         *  企业微信版（标准版） wechat_standard_edition
         *  云之家版   kdweibo_edition
         *  KIS版      kis_edition

         *  经销商版   dealer_edition
         *  代理商版   agent_edition
         *  访销版     promotion_sales_edition
         *  企业微信版（专业版） wechat_standardpro_edition
         *  专业版     standardpro_edition

         *  旗舰版     strengthen_edition

         *  集团版     enterprise_edition
         *  办公版     office_edition
         */
		// _getVersionInfo: function(cb) {
		//     var me = this;
		//     if (!me._getLayoutStatus) {
		//         util.FHHApi({
		//             url: '/EM1HCRM/Layout/GetLayoutRightList',
		//             success: function (data) {
		//                 if (data.Result.StatusCode == 0) {
		//                     var codes = {};
		//                     data = data.Value;
		//                     _.each(data.LayoutRightInfoList, function (item) {
		//                         codes[item.LayoutCode] = item.Enabled;
		//                     });
		//                     me.verCode = codes;                        // 记录分版权限code
		//                     me.crmVersion = data.NewCRMVersion || '';  // CRM新版本判断
		//                     _.extend(me, {
		//                         // 不是为云之家用户和企业微信 导航检查属性用到
		//                         isNotYunZhiJia: window.FS_SOURCE != 'yunzhijia' && window.FS_SOURCE != 'wechat',
		//                         // 是为云之家用户
		//                         isYunZhiJia: window.FS_SOURCE == 'yunzhijia' ||  window.FS_SOURCE == 'wechat',
		//                         // 是否为企业微信
		//                         isWeChat:    window.FS_SOURCE == 'wechat'
		//                     });
		//                     me._getLayoutStatus = true;
		//                 }
		//                 cb && cb();
		//             }
		//         });
		//         return;
		//     }
		//     cb && cb();
		// },

        _getVersionInfo: function(cb) {
            var me = this;
            if (!me._getLayoutStatus) {
                util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/crm_version/service/get',
                    success: function (data) {
                        if (data.Result.StatusCode == 0) {
							me.crmVersion = data.Value.now_version || '';  // CRM新版本判断
                            _.extend(me, {
                                // 不是为云之家用户和企业微信 导航检查属性用到
                                isNotYunZhiJia: window.FS_SOURCE != 'yunzhijia' && window.FS_SOURCE != 'wechat',
                                // 是为云之家用户
                                isYunZhiJia: window.FS_SOURCE == 'yunzhijia' ||  window.FS_SOURCE == 'wechat',
                                // 是否为企业微信
                                isWeChat:    window.FS_SOURCE == 'wechat'
                            });
                            me._getLayoutStatus = true;
                        }
                        cb && cb(me.crmVersion);
                    }
                });
                return;
            }
            cb && cb(me.crmVersion);
        },

        /**
         * @desc  路由分发出口
         * @param {{String}} module 模块名称 默认 page
         * @param {{String}} submod 子模块名称
         * @param {{String}} param  当前路由参数
         * @param {{String}} sort  区分bi 和 crm
		 * @param {{Function}} pageLoaded 页面记载完成的回调函数
         * 规则：请求module/submod/submod.js文件
         * 如果module submod没有变化 只是param变化 页面不会再次请求文件
         * 此时会触发当前页面模块的switch事件
         */
        route: function(module, submod, param, sort, pageLoaded) {
            var me = this;
            // 如果是自定义菜单项，则清空。自定义菜单项类似于软连接，不是真正的模块
            if (module === 'custommenu') {
                module = '';
            }
			if (me.cacheShowStatus && me.curPage && me.curPage.hashPath == location.hash) {
				return;
			} // cathe重新改变hash时 避免重复加载

            if (me.cached) {
				if (location.hash == (me.isGrayMenuCustomize ? '#crm/home' : '#crm/index')) { // 左侧导航进入
					me.cacheShow();
					return;
				}
				me._clear();
            }

            // 开启首页自定义的企业跳转逻辑变更为跳转第一个菜单
            if (me.isGrayMenuCustomize) {
                // 标记重定向状态，只有在需要重定向的情况下才设置为true
                var needRedirection = (submod === 'home');

                if (needRedirection) {
                    isRedirection = true;

                    // 使用Promise处理菜单数据获取
                    me.getMenuData().then(function() {
                        var formattedData = Aside._formatData(Fx.util.getGray("newCRMAllMenuGray") ? window.CRM_All_menu_new : window.CRM_All_menu);
                        var curMenu = formattedData.menus.find(item => item.isCurrent);

                        if (curMenu && curMenu.items && curMenu.items.length > 0) {
                            // 查找符合条件的菜单项
                            var firstMenu = me.findValidMenuItem.call(me, curMenu.items);

                            if (firstMenu && isRedirection) {
                                // 获取本地自定义菜单hash
                                var baseMenu = baseData.pageData[firstMenu.id];
                                me.navigate(
                                    baseMenu ? baseMenu.hash : Aside._extendObjProperty(firstMenu).hash
                                );
                                isRedirection = false;
                                return; // 已进行重定向，不再执行后续路由
                            }
                        }

                        // 找不到有效菜单项或不需要重定向，继续正常路由流程
                        me._continueRouting(module, submod, param, sort, pageLoaded);
                    });
                } else {
                    // 不需要重定向，直接继续路由
                    me._continueRouting(module, submod, param, sort, pageLoaded);
                }
            } else {
                // 未开启自定义菜单，直接继续路由
                me._continueRouting(module, submod, param, sort, pageLoaded);
            }
        },

        /**
         * 继续路由过程的内部函数
         * @private
         */
        _continueRouting: function(module, submod, param, sort, pageLoaded) {
            var me = this;
            me.pageLoaded = pageLoaded || function() {};
            if (me._routeTimer) {clearTimeout(me._routeTimer); me._routeTimer = null;} // 防止快速切换路由
            me._routeTimer = setTimeout(function() {
                if (me._routeNum > me._routeMaxNum) {
                    location.reload();
                    return false;
                }
                me._routeNum += 1;
                me.beginRender(module, submod, param, sort);
            }, 30);
        },
        beginRender:function(module, submod, param, sort){
        	var me = this;
			var folder2apiname = _.invert(CRM.config.ObjFolder);
			me.startTime = new Date().getTime();
			me.cleared = false;
			module = module || 'page';
			sort   = sort || 'crm';
			param  = param ? me._parseParam(param) : null;

			// TODO bi 目录规范统一
			module = (sort === 'bi' && module === 'chart') ? 'page/chart' : module;

			// 列表页老链接重定向
			var apiname = folder2apiname[submod];
			if (module === 'page' && submod === 'myobject') {
				me.navigate('crm/list/=/' + param[0]);
				return;
			}else if (module === 'page' && apiname) {
				me.navigate('crm/list/=/' + apiname);
				return;
            }

            // bi页面重定向
            if (sort === 'bi') {
                var biRedirectMap = {
                    board: 'dashboard'
                };

                if (biRedirectMap[submod]) {
                    me.navigate('bi/' + biRedirectMap[submod]);
				    return;
                }
            }

			// 列表特殊逻辑判断
			if (module === 'page' && submod === 'list' && param && _.contains(RouteSpecialObj, param[0])) {
				submod = param[0].toLowerCase();
			}

			if (!me._checkAuthor(param)) { // 检查是否拥有crm权限
				me.navigate('stream');
				return;
			}

			require.async('crm-assets/style/all.css', function() {
				if (me.layout && module === 'setting' && !me.isAdmin) { // 检查是否有管理员权限
					me.navigate('crm/index');
					return;
				}

				if (!me._checkMod(module, submod)) {
                    me._404();
                    CRM.util.sendLog('404', location.hash.substring(location.hash.lastIndexOf('/') + 1), {
                        operationId: 'hashError'
                    });
					return;
				}

				// 模块路径文件
				me._path = [FS.ROOT_PATH, sort + FS.PATH_SUFFIX, 'modules', module, submod, submod];
				me._path = me._path.join('/') + '.js';

				// 样式文件
				// TODO 单独加载
				//
				me._cssPath = sort + '-assets/style/' + (sort === 'bi' ? 'all' : module) + '.css';

				// 模块的最外层样式
				me._modClassName = 'crm-' + module + ' ' + [sort, module.slice(0, 1), submod].join('-');
                console.log(me._path, me._cssPath)
				// 参数
				me._param = param;

				if (me.curPage.path == me._path // 即切换了参数
					&& !CRM.util.isNeedDev('hash', {params: me._param}) // 新模块不是客开模块
					&& !me.curPage.isNeedDev // 当前模块不是客开模块
				) {
					me._switch();
					me.layout.refresh(me._param);
					return;
				}

				me._addLayout(function () { // 确保添加布局完成后开启页面
				});

                // 新待办重定向
                // TODO：销毁？？ @wujing
				if (module === 'remind') {
					me._modClassName += ' crm-p-404'
					if(!me.remindInstance){
						var sdk = window.PAAS_CONFIG['remindApi'];
						if(!sdk) {
							if(FS.env === 'test'){
								sdk = 'sdk.8edb372c.js';
							} else {
								sdk = 'sdk.bc404e6e.js';
							}
						}
						require.async(FS.ROOT_PATH + '/remind-dist/' + sdk, function (remindSdk) {
							remindSdk.createNoticeAsync({
								container: document.querySelector(FS.theme ==='default'? '#crm-layout': '.crm-main-con'),
								activeId: submod, // 当前菜单默认选中
								appId: 'CRM',
								source: 'paasapp',
								oldCrm: true,
								history: 'history', // 路由历史记录模式，使用history.pushstate 不触发hashchage事件
								style: {
									position: 'absolute',
									left: '0',
									right: '0',
									top: '0',
									bottom: '0',
									zIndex: 10
								},
								on: {
									hide: () => {
										me.remindInstance && me.remindInstance.destroy();
										me.remindInstance = null;
									}
								}
							}).then((instance)=>{
								me.remindInstance = instance;
                                // me.curPage = _.extend({   // 清空为默认值
                                //     path: '',
                                //     $el: null,
                                //     cache: false,
                                //     module: null,
                                //     layout: null,
                                //     widget: null
                                // });
							})
						});
					}else{
						me.remindInstance.setCurrentMenuById(submod);
					}
				} else {
					me.remindInstance && me.remindInstance.destroy();
					me.remindInstance = null;
				}


				me._start();
			});
		},
        /**
         * @desc 解析参数
         * ps: 对老的参数形式做兼容
         * @return {{Array}}
         */
         _parseParam: function(param) {
            var result = null;
            if (param.indexOf('-') != -1) { // 老的参数形式
                result = [];
                param = param.split('/=/');
                _.each(param, function(item) {
                   item = item.split('-');
                   result.push(item[1]);
                });
                return result;
            }
            return param.split('/');
        },
        /**
         * @desc 路由跳转
         * route主要为模块加载 navigate操作hash
         */
        navigate: function(hash) {
            FS.tpl.navRouter.navigate(hash, {
                trigger: true
            });
        },

        /**
         * @desc 检查是否为有效的模块
         * 主要是为了确保路径的引用正确
         * 只是检测了module没有检测submod
         * submod通过请求文件 不存在确认
         */
        _checkMod: function(module, submod) {
            return _.contains(['page', 'setting', 'remind', 'page/chart'], module);
        },

        /**
         * @desc 检查有没有crm权限
         * 来源app的页面允许今天（新开通用户）
         * 暂时没有获取权限的接口
         */
        _checkAuthor: function(param) {
            var data = window.FS_TEMPLATE_DATA;
            if (TEMPCHANNEL) {
                var isCrm = TEMPCHANNELLIST.some(function(item) {
                    return item.appId === 'CRM';
                });
                return isCrm || (param && param[0] == 'source' && param[1] == 'app');
            } else if (data) {
                var crm = _.findWhere(data.titleMenu, {menu: 'CRM'});
                return (crm && crm.enable) || (param && param[0] == 'source' && param[1] == 'app');
            }

            return true;
        },


        /**
         * @desc 补全布局
         * 可以把crm想象为一个独立的应用
         * 他的默认模板布局应该为头部 左侧导航 右侧内容
         * 单页面载入只载入内容区域
         * 当前架构（2017-03-01）只切换是除导航的所有区域
         */
        _addLayout: function(callBack) {
            var me = this;
            if (!me.layout) {
                //me._pageTitle = FS.pageTitle;
                me.layout = new Layout();

				_.extend(me, {
					// 不是为云之家用户和企业微信 导航检查属性用到
					isNotYunZhiJia: window.FS_SOURCE != 'yunzhijia' && window.FS_SOURCE != 'wechat',
					// 是为云之家用户
					isYunZhiJia: window.FS_SOURCE == 'yunzhijia' ||  window.FS_SOURCE == 'wechat',
					// 是否为企业微信
					isWeChat:    window.FS_SOURCE == 'yunzhijia' || window.FS_SOURCE == 'wechat',
					isOrder:     false,  // 是否开通订货通
					isFastSale:  false,  // 是否开通快销 （多了拜访动作 盘点动作）
					isAdmin:     false,  // 是否为crm管理员
					newAuthCode: null    // 新建权限code
				});

                me.layout.on('ready', function(asideAuthor) {
                    var module = me.curPage.module;
                    // 扩展左侧权限到控制层
                    _.extend(me, asideAuthor || {});
					module && module.onLayoutReady && module.onLayoutReady();
                    callBack && callBack();
                    me._getVersionInfo();   // 预加载部分权限
                    util.getCrmAllConfig(); // 预加载功能权限
					CRM.api.get_licenses({
                        objectApiName: me._param && me._param[0],
                    }); // 预加载版本模块定义
                });

				me.layout.on('resize', function(status) {
                    var module = me.curPage.module;
					module && module.onLayoutResize && module.onLayoutResize(status);
                });
                me.layout.refresh(me._param);
            } else {
                callBack && callBack();
                me.layout.refresh(me._param);
            }
        },

        /**
         * @desc 刷新做了导航
         * 为了兼容灰度左侧导航
         */
        refreshAside: function() {
            var me = this;
            me.layout && me.layout.aside && me.layout.aside.refresh();
        },

         /**
         * @desc 为了兼容灰度左侧导航
         */
        getSettingData: function() {
            var me = this, result  = [];

            if (me.layout && me.layout.aside) {
                return me.layout.aside.settingData;
            }

            _.each(Aside.front_data.settingData, function(item) {
                item.children = _.filter(item.children || [], function(cItem) {
                    var checkedProp = cItem.checkedProp ? me[cItem.checkedProp] : true; // 检查属性
                    return  checkedProp;
                });
                result.push(item);
            });
            return result;
        },

        getAsideData: function() {
            return Aside.front_data;
        },

        /**
         * @desc 页面切换
         * 只是参数发生了变化
         */
        _switch: function () {
            console.log("_switch");
            var module = this.curPage.module;
            module && module.switchPage && module.switchPage(this._param);
            this.layout &&
                this.layout.aside &&
                this.layout.aside.setHighlight &&
                this.layout.aside.setHighlight();
			this.curPage.hashPath = location.hash;
        },

        /**
         * @desc 页面请求开始
         * TODO 未记录缓存页面
         * TODO 如果有缓存，开始的时候现在缓存中找到当前module
         */
        _start: function() {
            var me = this, curPage = me.curPage,
            module = curPage.module;

            // 不允许关闭页面
            if (module && module.beforeClosePage && !module.beforeClosePage(location.hash)) {
                window.history.back();
                return;
            }

            // 离开前确认
            if (module && module.__leaveConfig) {
                var needConfirm = module.__leaveConfig.needConfirm;
                if (_.isFunction(needConfirm)) {
                    needConfirm = needConfirm.call(module);
                }
                if (needConfirm) {
                    me._leaveConfirm();
                    return;
                }
            }

            me._close();
            // me.initPluginService();

            // 查找缓存
            // 找到缓存 执行start
            // 找不到执行下面语句
            if (me.layout) {
                me.layout.showPageLoading();
                me._loadModule();
                me.layout.aside && me.layout.aside.setHighlight && me.layout.aside.setHighlight();
            }
        },

        /**
         * @desc 二次确认是否离开页面
         */
        _leaveConfirm: function() {
            var me = this,
                module = me.curPage.module,
                config = module.__leaveConfig;

            var confirm = util.confirm(config.message || $t("确定要离开该页面"), $t("确认"), function() {
                confirm.destroy();
                me._close();
                if (me.layout) {
                    me.layout.showPageLoading();
                    me._loadModule();
                    me.layout.aside &&
                        me.layout.aside.setHighlight &&
                        me.layout.aside.setHighlight();
                }
            }, {
                hideFn: function() {
                    window.history.back();
                },
                stopPropagation: true
            });
        },


        /**
         * @desc 关闭页面
         * TODO 收集所有页面的widget统一销毁
         */
        _close: function() {
            var me = this, curPage = me.curPage,
                module = curPage.module;

            if (!module) { // 第一次载入页面
                return;
            }
            if (curPage.cache) {
				// curPage.$el.hide();
			} else {
                curPage.$el.off();
				curPage.$el.removeData();
                // TODO 清空页面的组件
                // _.each(curPage.widget, function(item) {item.destroy});
                module.closePage && module.closePage(); // 基本用不到 关闭时主要任务是destroy
                module.destroy && module.destroy(); // 可强制限制必须实现destroy方法
                curPage.$el = curPage.module = curPage.widget = null;
            }

            // me.destroyPluginService();
        },

        /**
         * @desc 载入模块
         * 采用闭包是为了确保加载的模块正好为当前模块
         */

        _loadModule: function() {
            var me = this;
            (function(jsPath, cssPath) {
                var _jsPath = jsPath;
                var _className = '';

                // 列表页统一走page/page.js
                if(window.location.hash.indexOf('crm/list/') > -1 || window.location.hash.indexOf('crm/custommenu/list/') > -1) {
                    jsPath = [FS.ROOT_PATH, 'crm' + FS.PATH_SUFFIX, 'modules', 'page', 'page'].join('/') + '.js';
                    _className = 'crm-module-wrap-flex';
                }

				function exec(Module, options = {}) {
					if (!me.layout) {return;}
                    if (Module) {
                        if (_jsPath != me._path) {return;}
                        var module = null,
                            $div = $('<div class="crm-module-wrap crm-scroll '+ [_className, me._modClassName].join(' ') +'"></div>');

                        me.layout.setCon($div);
                        try {
                            module = new Module({
                                wrapper: $div,
                                jsPath: _jsPath,
								modClassName: me._modClassName,
								listModule: options.listModule
                            });
                            module.render && module.render(me._param);

                            me.curPage.$el      = $div;
                            me.curPage.module   = module;
							me.curPage.isNeedDev = CRM.util.isNeedDev('hash', {params: me._param});
                            me.curPage.path     = me._path;
							me.curPage.hashPath = location.hash;

                            setTimeout(function() {
                                me.layout && me.layout.hidePageLoading && me.layout.hidePageLoading();
                                let mod = location.hash.substring(location.hash.lastIndexOf('/') + 1);
                                FS.MEDIATOR.trigger('crm.callCrmMenu', { mod, $el: me.curPage.$el });
                            }, 450);
							me.pageLoaded();
                            console.log('路由耗时:' + (new Date().getTime() - me.startTime));
                        } catch(error) {
                            //location.reload();
                        }
                        return;
                    }
                    if (me._modClassName.indexOf('crm-p-404') == -1) {
                        me._404();
                        CRM.util.sendLog('404', location.hash.substring(location.hash.lastIndexOf('/') + 1), {
                            operationId: 'loadModuleError'
                        });
                    }
				}

				if (CRM.util.isNeedDev('hash', {params: me._param})) {
					var params = _.extend({ea: CRM.ea, action: 'hash'}, {params: me._param});
					require.async([cssPath]);
					CRM2K.loadComp(params, function (Module, options = {}) {
						exec(Module, options);
					})
				} else {
					// 由于待办提醒迁移至remind项目，不在这里加载remind资源
					let remindModule = null;
					if(/crm-dist\/modules\/remind\/(.*?)\.js$/.test(jsPath)) {
						jsPath = ""
						remindModule = Backbone.View.extend({})
					}
					require.async([cssPath, jsPath], function(cssModule, Module) {
						exec(Module || remindModule);
					});
				}

            }(me._path, me._cssPath));
        },


        /**
         * @desc 显示404页面
         */
        _404: function() {
            this.route('page', '404');
        },

        //
        // 清楚一些滑层
        //
        _hackClearSlide: function() {
            if ($('.crm-c-slide').length > 0) {
                $('.crm-c-slide-mask').remove();
                $('#global-remind').removeClass('crm-slide-hide');
                $('.hd').css('zIndex', 100);
                $('#j-qx').css('zIndex', 100);
                $('.qx-chat-window-panel').css('zIndex', 100);
            }
        },

		//
		// 是否需要缓存
        isNeedCache: function () {
            return window.Fx &&
                !window.Fx.util.isFromUIDesktop() && // 新客户端直接返回false
                (
                    window.Fx.theme == "new" &&
                    this.layout &&
                    this.curPage &&
                    this.curPage.hashPath != "#crm/home" &&
                    this.curPage.hashPath != "#crm/info"
                );
		},

		//
		//
		//
		cacheHide: function () {
			this.layout.hide();
			$('.fx-cache-crm-detail-show').hide();
      if ($('.crm-w-table-filter-dialog').length){
        if ($('.crm-w-table-filter-dialog').css('display') !='none'){
          $('.j-filter-btn').click();//关掉筛选弹窗
        }
        $('.crm-w-table-filter-dialog').hide(); //
      }

			this.cached = true;
			this.cacheShowStatus = false;
		},

		//
		//
		cacheShow: function () {
			var domDetal = $('.fx-cache-crm-detail-show')[0];
			this.layout.show();
			// $('.crm-w-table-filter-dialog').show(); //
			this.cacheShowStatus = true;
			this.cached = false;
			location.hash = this.curPage.hashPath;
			$(window).trigger('resize');
			if (domDetal) {
				if (domDetal._detail) {
					domDetal._detail.dShow = true;
				} else {
					$(domDetal).show();
				}
			}
            FS.MEDIATOR.trigger('crmPageCacheShow');
		},

		//
		// 清除缓存
		clearCache: function () {
			this.cacheShowStatus = false;
			this.cached = false;
		},

		/**
         * @desc 清除crm痕迹
         * 切换到工作应用时调用
         */
        clear: function() {
            var me = this;


			if (me.cleared) {
				return;
			}


			if (me.cached) {
				if (me.cacheLinkNum > me.cacheMaxLink) {
					me.cacheLinkNum = 0;
					me._clear();
				}
				me.cacheLinkNum += 1;
				return;
			}

			if (me.isNeedCache()) {
				console.log('isNeedCache')
				me.cacheHide();
				return;
			}

			me._clear();

		},

		_clear: function () {
			var me = this;
			me._hackClearSlide();
			if (!me.layout) {
				return;
			}
			me._routeNum = 0;
			me._close();
			me.curPage.module && me.curPage.module.destroy && me.curPage.module.destroy();
			me.curPage = _.extend({   // 清空为默认值
				path: '',
				$el: null,
				cache: false,
				module: null,
				layout: null,
				widget: null
			});
            document.title = FS.pageTitle; // 还原pageTtile
			me.layout & me.layout.destroy();
			me.layout = null;
			me.cleared = true;
			me.cached = false;
			me.cacheShowStatus = false;
			if(me.remindInstance) {
				me.remindInstance.destroy();
				me.remindInstance = null;
			}
		},

        // initPluginService: function () {
        //     if (CRM && CRM.api && CRM.api.pluginService) {
        //         CRM.api.pluginService.init({
        //             appId: 'CRM'
        //         });
        //     }
        // },

        // destroyPluginService: function() {
        //     if (CRM && CRM.api && CRM.api.pluginService) {
        //         CRM.api.pluginService.destroy()
        //     }
        // }
    });

	module.exports = control;
});
