/**
 * @desc   crm左侧导航
 * 可通过CRM.control.layout.aside.author 获取左侧权限
 * 也可通过control获取权限
 * <AUTHOR>
 */
define(function (require, exports, module) {
    var util = require("./../../util/util"),
        // config = require("./../../config/config"),
        data = require("./data"),
        stpl = require("./template/stpl-html"),
        navTpl = require("./template/aside-html"),
        remindTpl = require("./template/remind-html"),
        todoTpl = require("./template/todo-html"),
        // menuPopover = require("./template/popover-html"),
        Auth = require("./auth"),
        History = require("./history/history"),
        Set = require("./set/set"),
        register = require("./dingtalk");

    var bufferTime = 150;

    var Aside = Backbone.View.extend({
        /**
         * @desc 状态 默认展开
         * fold | unfold
         */
        status: "unfold",

        /**
         * 配置
         */
        routeData: data,

        /**
         * @desc 记录默认代办项
         */
        pendData: null,

        /**
         * @desc 总记提醒的总条数
         */
        totalRemind: 0,

        /**n
         * @desc 当前导航类型
         * page setting
         */
        _curPage: "page",

        myPageData: [],

        /**
         * @desc 是否支持无icon模式收起灰度
         */
        noIconFoldGray: false,

        isFoldMenu: undefined,

        /**
         * @desc 是否横向标签模式
         */
        isMultipleTabs: false,

        /**
         * @desc 是否开启首页，最近访问，待办，提醒自定义功能
         */
        isGrayMenuCustomize: Fx.util.getGray("paasIsGrayMenuCustomize"),

        isRemoveConnectAppAside: Fx.util.getGray("paasIsRemoveConnectAppAside") || true,

        initialize: function (opts) {
            if (
                FS.util.getUserAttribute("openUrlByDingtalk") &&
                navigator.userAgent.indexOf("dingtalk") > -1
            ) {
                try {
                    register();
                } catch (error) {
                    console.log(error);
                }
            }
            // 新增loading
            CRM.util.showLoading_tip(
                $t("wx.qixin.todo.loading", null, "加载中..."),
                this.$el,
                "asideLoading"
            );

            this.status = opts.status || "unfold";
            this.isMultipleTabs = opts.isMultipleTabs || false;
            this.$el.addClass(this.status);
            this.initLifeCycles(opts);
            this.initPendDataKey();
            this._bindEvents();
            // if (this.options.isMultipleTabs == undefined) {
            //     this.options.isMultipleTabs = false;
            // }
            this.render().then((data) => {
                this.rendered && this.rendered(data);
            });

            this.pendData = localStorage.getItem(this.pendDataKey)
                ? JSON.parse(localStorage.getItem(this.pendDataKey))
                : {};
            // this.isNewMenuLayou = CRM.util.isGrayScale("CRM_NEW_MENU_LAYOUT");
        },

        events: {
            // 'click .quickly .list a': '_addHandle', // 快速新建
            "click .j-fold-btn": "toggleFold", // 折叠展开
            "click .j-menu-btn": "_showMenuPopover", // 设置导航
            "click .aside-item-url": "_addTabsAside", // 添加到tab标签页
            "click .aside-item-log": "_addAsideItemLog", // 点击列表item埋点事件
            "hover .aside-wrap": "_onHoverHandle", // 鼠标经过展开

            "click .set-search-reset": "_onResetSearch", // 重置搜索
            "click .help-btn": "_onLookHelp", // 文档统计

            "click .a-tit": "_onItemTitle", // 分组
            "click .menu-set": "_onSetMenu", // 设置菜单
            "click .mode-set": "_onSetMode", // 设置模式
            "click .custom-menu-sort-item": "_onSwitchMenu", // 切换菜单
            // "mouseleave .menus-switch__popover": "_onHideMenuPopover",
            // "blur .menus-switch__popover": "_onHideMenuPopover",
            "click .j-menu-item-openTab": "newTabOpenLink",
            "click .j-menu-add-btn": "addOneObject",
            "click .back-index": "onBackIndexClick",
            "hover .j-menu-add-btn": "hoverOneObject",
        },
        initLifeCycles: function (opts) {
            if (opts && opts.on) {
                _.isFunction(opts.on.beforeMount) &&
                    (this.beforeMount = opts.on.beforeMount);

                _.isFunction(opts.on.rendered) &&
                    (this.rendered = opts.on.rendered);
            }
        },

        initPendDataKey: function () {
            var curEmployee = FS.contacts.getCurrentEmployee();
            this.pendDataKey =
                "crm_todoremind_data_" +
                (curEmployee.enterpriseId
                    ? curEmployee.enterpriseId
                    : curEmployee.enterpriseAccount) +
                curEmployee.employeeID;
        },

        // 搜索逻辑
        _bindEvents: function () {
            var me = this;
            util.fixInput(
                ".search-ipt",
                function () {
                    var $el = me.$("." + me._curPage + "-item");
                    var key = $.trim(me.$(".search-ipt").val());
                    $(".search-result", $el).toggle(key != "");
                    me.$(".set-search-reset").toggle(key != "");
                    $("." + me._curPage + "-menu", $el).toggle(key == "");
                    me._showSearchResult(key);
                },
                me.$el
            );
            FS.MEDIATOR.on("crm.menu.fold", function () {
                if (
                    (me._curPage === "page" && me.isShowMenuIcon) ||
                    (me._curPage === "page" && me.noIconFoldGray)
                ) {
                    if (me.isFoldMenu === undefined) {
                        me.fold();
                    } else {
                        me.handleFoldunfold(me.isFoldMenu);
                    }
                }
            });
            FS.MEDIATOR.on("crm.menu.unfold", function () {
                if (me._curPage === "page") {
                    me.unfold();
                }
            });
            this.$addPanel && this.$addPanel.on("click", this.addOneObject);
            this.$addPanel &&
                this.$addPanel.on("mouseleave", function () {
                    $(this).hide();
                });
        },

        /**
         * @desc 绑定全局企信提醒
         * 协同团队提供 @contacts: dhp
         */
        _bindGlobalEvents: function () {
            var me = this;
            if (me.hasBindRemind) {
                return;
            }
            var adaptor = {
                // CRM通知相关key
                weakRemind: 405, // CRM通知
                sentbyme: "sentbyme", //发起的审批流程
                approvalPush: 454, //审批信息
                importhelper: 455, //导入助手
                workflowInfo: 458, //工作流消息
                stockwarn: 459, //库存补货与预警
                stockmature: 461, //临到期的库存
                erpdssnotify: "erpdssnotify", // 数据集成通知
                atmefeeds: 55, //@我的
                likefeeds: 90, //我的赞
                bimessage: 466, // 报表消息
                myrepliesfeeds: 30, // @我的回复
            };

            if (!FS.util.getUserAttribute("paas_remind_todo_v2")) {
                adaptor = Object.assign(adaptor, {
                    // CRM待办相关key
                    clueAssign: 401, //待分配的销售线索
                    clueHandle: 402, //待处理的销售线索
                    customerCheck: 403, //待审核的客户报备
                    leaderConfirm: 404, //待确认的销售订单
                    saleConfirm: 406, //待确认的销售阶段
                    gbConfirm: 408, //待确认的退款
                    invoiceConfirm: 410, //待确认的开票申请
                    returnConfirm: 411, //待确认的退货单
                    approvalByMe: 452, //待处理的审批流程
                    deliveryTobe: 456, //待发货的销售订单
                    flowHandle: 457, //待处理业务流程
                    waitapproval: "457WaitApproval", // 待审批的工单
                    waitassign: "457WaitAssign", // 待指派的工单
                    waitcheckins: "457WaitCheckins", // 待执行外勤的工单
                    waitdeal: "457WaitDeal", // 待处理的工单,
                    waittoorder: "457WaitToOrder", // 待接单的工单
                    saleaction2: 460, //待处理的阶段任务
                    electronicSign: "ElectronicSignTodo", //电子签待办
                    unhandleFeedApprove: "unhandleFeedApprove", //待处理的协同审批
                });
            }

            if (FS.util.getCloudName() === "zsy") {
                delete adaptor.atmefeeds;
                delete adaptor.likefeeds;
            }

            function updatePendData(msg) {
                for (var key in adaptor) {
                    var value = msg[adaptor[key]];
                    if (key === "electronicSign" && value) {
                        try {
                            var pdval = me.pendData[key]
                                ? me.pendData[key].value2
                                : "";
                            var isChange = pdval != value.value2;
                            isChange &&
                                FS.MEDIATOR.trigger(
                                    "refresh.electronicsign.list"
                                );
                        } catch (error) {
                            console.log(error);
                        }
                    }
                    value && (me.pendData[key] = value);
                }
                me.pendData["delegateLog"] = {};
                me.pendDataLog();
                !_.isEmpty(me.pendData) && me._renderPending();
            }

            FS.MEDIATOR.always("globalRemind", function (msg) {
                me.updateRemindData(msg, updatePendData);
            });

            if (FS.util.getUserAttribute("paas_remind_todo_v2")) {
                FS.MEDIATOR.always(
                    "globalRemind",
                    _.debounce(function () {
                        me.getServerRemindData().then((res) => {
                            me.todoDataV2 = res;
                            !_.isEmpty(me.pendData) && me._renderPending();
                        });
                    }, 2000)
                );
            }

            var debouncedGetChangeMyReminderCountData = _.debounce(() => {
                me.getServerRemindData().then((res) => {
                    me.todoDataV2 = res;
                    !_.isEmpty(me.pendData) && me._renderPending();
                });
            }, 2000); // 3秒内只触发一次

            if (FS.util.getUserAttribute("paas_remind_todo_v2")) {
                FS.MEDIATOR.always(
                    "changeMyReminderCount",
                    debouncedGetChangeMyReminderCountData
                );
            }

            me.hasBindRemind = true;
        },
        getServerRemindData: function () {
            var me = this;
            return new Promise((resolve, reject) => {
                window.FS.util.FHHApi(
                    {
                        url: "/EM1HTODO/fs-todo/systemTodoBizItem/getTodoGroupLayoutInfo",
                        success: (res) => {
                            var { Result = {}, Value = {} } = res;
                            if (
                                Result.FailureCode === 0 &&
                                Result.StatusCode === 0 &&
                                Value.todoBizItemViewList &&
                                Value.todoBizItemViewList.length > 0
                            ) {
                                var list = me.calculateSeparateCounts(
                                    Value.todoBizItemViewList
                                );
                                resolve(list);
                            } else {
                                reject(Result);
                            }
                        },
                        error: (err) => {
                            reject(err);
                        },
                        complete: () => {},
                    },
                    { errorAlertModel: 1 }
                );
            });
        },
        calculateSeparateCounts: function (data) {
            return data.reduce(
                (total, item) => {
                    // 如果当前项有 todoItemList，则对其进行处理
                    if (item.todoItemList && item.todoItemList.length > 0) {
                        item.todoItemList.forEach((todoItem) => {
                            const filterItemIds = [
                                "457WaitApproval",
                                "457WaitDeal",
                                "457_returnVisit",
                                "457WaitAssign",
                            ];
                            if (
                                todoItem.todoItemId.indexOf("_todoItemId_") ==
                                    -1 &&
                                !filterItemIds.includes(todoItem.todoItemId)
                            ) {
                                // 确保属性存在且值为数字
                                total.notReadCount +=
                                    typeof todoItem.notReadCount === "number"
                                        ? todoItem.notReadCount
                                        : 0;
                                total.notDealCount +=
                                    typeof todoItem.notDealCount === "number"
                                        ? todoItem.notDealCount
                                        : 0;
                            }
                        });
                    }
                    return total;
                },
                { notReadCount: 0, notDealCount: 0 }
            );
        },
        updateRemindData: function (msg, cb) {
            let res;
            if (_.isArray(msg)) {
                res = {};
                for (var i = 0; i < msg.length; i++) {
                    var item = msg[i];
                    res[item.value] = {
                        value: item.value,
                        value1: item.value1 || 0,
                        value2: item.value2 || 0,
                    };
                }
            } else {
                res = msg;
            }
            !_.isEmpty(res) && cb(res);
        },

        /**
         * @desc 渲染提醒待办列表数
         */
        _renderPending: function () {
            var me = this;
            if (
                FS.util.getUserAttribute("paas_remind_todo_v2") &&
                !FS.util.isConnectApp()
            ) {
                var $totaltodo = me.$(".total-todo");
                if (me.todoDataV2) {
                    var num = 0,
                        showflag;
                    if (
                        me.todoDataV2.notReadCount &&
                        me.todoDataV2.notReadCount > 0
                    ) {
                        num = me.todoDataV2.notReadCount;
                        showflag = "unread";
                    } else {
                        num = me.todoDataV2.notDealCount;
                        showflag = "undo";
                    }
                    $totaltodo.toggle(num > 0);
                    $totaltodo.html(num > 99 ? "99+" : num);
                    $totaltodo.toggleClass("undo", showflag === "undo");
                    num > 0 &&
                        me.$(".list-menu a[data-key=todo]").removeClass("hide");
                }
            } else {
                me._renderTodo();
                me.todoData.list.length &&
                    me.$(".list-menu a[data-key=todo]").removeClass("hide");
            }
            me._renderRemind();
            if (
                !me.isMultipleTabs ||
                location.hash.indexOf("#crm") >= 0 ||
                location.hash.indexOf("#bi") >= 0
            ) {
                me.setHighlight();
            }
            me.dataLog();
            var _data = {};
            for (var key in me.pendData) {
                _data[key] = _.extend({}, me.pendData[key], {
                    value1: 0,
                    value2: 0,
                });
            }
            try {
                localStorage.setItem(this.pendDataKey, JSON.stringify(_data));
            } catch (error) {}

            // console.log("pageData", me.pageData);
            const indexToDo = me.pageData.findIndex(
                (item) => item.id === "CrmToDo" || item.id === "todo"
            );

            const indexRemind = me.pageData.findIndex(
                (item) => item.id === "CrmRemind" || item.id === "remind"
            );
            me.trigger("remindTodoData", {
                remind: indexToDo >= 0 ? me.remindData : {},
                todo: indexRemind >= 0 ? me.todoData : {},
            });
        },
        dataLog() {
            const me = this;
            CRM.util.sendLog("crm", "aside", {
                operationId: "todoremind",
                pageData: {
                    pageId: location.hash,
                    pendData: me.pendData,
                },
            });
        },

        pendDataLog() {
            CRM.util.sendLog("CRMaside", "todo", {
                operationId: "PendDatalist",
                todoData: JSON.stringify(this.pendData),
            });
        },

        _formatRemindData: function (data) {
            // var notifyDomainList = FS.util.getUserAttribute('showNotifyPrivateClouds') || [];

            // if (
            //     util.isPrivateCloud() && notifyDomainList.indexOf(window.location.host) === -1
            // ) {
            //     return _.filter(data, function(item) {
            //         return item.key !== 'weakRemind';
            //     });
            // }

            return data;
        },

        // 获取提醒数据
        _getRemindData: function () {
            var me = this;
            var _total = 0;
            var remindData = me.routeData.getRemindData
                ? me.routeData.getRemindData()
                : me.routeData.remindData;
            var _data = _.map(
                me._formatRemindData(remindData),
                function (item) {
                    if (me.pendData[item.key]) {
                        item = _.extend(item, {
                            unread: me.pendData[item.key].value1 || 0,
                            undo: me.pendData[item.key].value2 || 0,
                        });
                        item.num = item.unread;
                        item.shownum =
                            item.num > 0
                                ? item.num < 100
                                    ? item.num
                                    : "99+"
                                : "";
                        _total += item.num;
                    }
                    item.isHidden = _.isUndefined(me.data["enable_" + item.key])
                        ? _.isUndefined(me.pendData[item.key])
                        : !me.data["enable_" + item.key];
                    item.key === "weakRemind" && (item.isHidden = false); //crm通知始终显示
                    item.key === "sentbyme" && (item.isHidden = false);
                    item.key === "erpdssnotify" && (item.isHidden = false);
                    return item;
                }
            );
            _data = _.filter(_data, function (item) {
                return !item.isHidden;
            });
            return {
                list: _data,
                total: _total,
            };
        },

        // 获取待办数据
        _getTodoData: function () {
            var me = this;
            var _total = 0;
            var _flag = "undo";
            var _curkey = "";
            var _data = _.map(
                me.routeData.getToDoData
                    ? me.routeData.getToDoData()
                    : me.routeData.todoData,
                function (item) {
                    if (me.pendData[item.key]) {
                        item = _.extend(item, {
                            showflag: me.pendData[item.key].value1
                                ? "unread"
                                : me.pendData[item.key].value2
                                ? "undo"
                                : "",
                            unread: me.pendData[item.key].value1 || 0,
                            undo: me.pendData[item.key].value2 || 0,
                        });
                        item.num = item[item.showflag] || 0;
                        item.shownum =
                            item.num > 0
                                ? item.num < 100
                                    ? item.num
                                    : "99+"
                                : "";
                        if (item.showflag === "unread") {
                            _flag = "unread";
                            item.num > 0 && !_curkey && (_curkey = item.key);
                        }
                    }

                    item.isHidden = _.isUndefined(me.pendData[item.key]);
                    return item;
                }
            );
            _.each(_data, function (item) {
                item.num > 0 && !_curkey && (_curkey = item.key);
                !_data.isHidden && item[_flag] && (_total += item[_flag]);
            });
            _data = _.filter(_data, function (item) {
                return !item.isHidden;
            });
            !_curkey && _data[0] && (_curkey = _data[0].key);
            // console.log(_data,_total,_flag,_curkey);
            return {
                list: _data,
                total: _total,
                showflag: _flag,
                curkey: _curkey,
            };
        },

        // 渲染提醒
        _renderRemind: function () {
            var me = this;
            var $totalremind = me.$(".total-remind");
            me.remindData = me._getRemindData();
            me.$(".remind-item").html(
                remindTpl(_.extend({}, me.remindData, me.getConfig()))
            );
            $totalremind.toggle(me.remindData.total > 0);
            $totalremind.html(
                me.remindData.total > 99 ? "99+" : me.remindData.total
            );

            me.remindData.curkey = "weakremind";
        },

        // 渲染待办
        _renderTodo: function () {
            var me = this;
            var $totaltodo = me.$(".total-todo");
            me.todoData = me._getTodoData();
            // me.trigger("remindTodoData", { todo: me.todoData });
            me.$(".todo-item").html(
                todoTpl(_.extend({}, me.todoData, me.getConfig()))
            );

            $totaltodo.toggle(me.todoData.total > 0);
            $totaltodo.html(me.todoData.total > 99 ? "99+" : me.todoData.total);
            $totaltodo.toggleClass("undo", me.todoData.showflag === "undo");

            me.todoData.curkey = "approvalByMe";

            $totaltodo.closest("a").attr(
                "href",
                me
                    .$(".remind-box[data-key=" + me.todoData.curkey + "]")
                    .closest("a")
                    .attr("href")
            );
        },

        /**
         * @desc 模块对外调用方法
         */
        render: function () {
            var me = this;
            return new Promise((resolve) => {
                me._getFoldStatus().then((res) => {
                    me._getData(function (data) {
                        me.isFoldMenu =
                            typeof res.isFoldMenu === "boolean"
                                ? res.isFoldMenu
                                : undefined;
                        me.modifyNavigationStatus(res, data);
                        var startTimer = new Date().getTime();
                        if (
                            location.host.indexOf("crm-di.cm-worklink.com") ===
                            -1
                        ) {
                            me._setStockAuth();
                        }

                        me.initAuthor();
                        me.renderTpl(data);
                        me._bindGlobalEvents();
                        me._checkUrlAuthor(me.author.isAdmin);

                        // 显示引导
                        // new Guide().show();

                        me.trigger("ready", me.author);

                        // todo 组件自己处理宽度，做响应式处理
                        if (me.noIconFoldGray && !data.curMenu.isShowMenuIcon) {
                            me.trigger(
                                "resize",
                                me.isFoldMenu ? "fold" : "unfold"
                            );
                        } else {
                            me.trigger(
                                "resize",
                                me.isFoldMenu && data.curMenu.isShowMenuIcon
                                    ? "fold"
                                    : "unfold"
                            );
                        }
                        console.log(
                            "CRM左侧导航渲染完成时间:",
                            new Date().getTime() - startTimer
                        );
                        // 隐藏loading
                        CRM.util.hideLoading_tip(me.$el, "asideLoading");
                        resolve(data);
                    });
                });
            });
        },
        modifyNavigationStatus: function (foldStatus, data) {
            var me = this;
            var viewportWidth =
                window.innerWidth ||
                document.documentElement.clientWidth ||
                document.body.clientWidth;
            // 优先判断是否用户操作过导航展开收起
            if (foldStatus && foldStatus.isFoldMenu != undefined) {
                // 用户操作过展开收起，根据接口返回的状态设置导航展开收起状态
                // 判断是否为灰度无icon支持收起菜单
                if (me.noIconFoldGray && !data.curMenu.isShowMenuIcon) {
                    me.handleFoldunfold(foldStatus.isFoldMenu);
                } else {
                    me.handleFoldunfold(
                        foldStatus.isFoldMenu && data.curMenu.isShowMenuIcon
                    );
                }
            } else {
                if (me.noIconFoldGray && !data.curMenu.isShowMenuIcon) {
                    me.handleFoldunfold(viewportWidth < 1280);
                } else {
                    me.handleFoldunfold(false);
                }
            }
        },

        _getMenusIntoMap: function (cb) {
            var me = this;
            var formMap = {};
            me.myPageData.forEach((item) => {
                formMap[item.hash] = {
                    text: item.text,
                    id: item.id,
                    hash: item.hash,
                };
                //存入分组下的菜单项目
                if (item.type === "group" && !!item.children.length) {
                    item.children.forEach((ite) => {
                        formMap[ite.hash] = {
                            text: ite.text,
                            id: ite.id,
                            hash: item.hash,
                        };
                    });
                }
            });
            cb && cb(formMap);
        },
        initAuthor: function () {
            this.author = {
                // -- 菜单接口不再下发，如需使用通过接口获取 --
                // API/v1/object/biz_config/service/get_config_values

                // isFastSale: data.IsFastSellingEnabled, // 是否开通快销 （多了拜访动作 盘点动作）
                // isShowGoalRule: data.IsGoalRuleEnabled, // 是否展示目标规则
                // isShowTarget: !data.IsGoalRuleEnabled, // 是否展示目标设置
                // IsShowServiceManage: data.IsShowServiceManage, // 显示服务管理
                // isCustomerAccountEnabled: data.IsCustomerAccountEnabled, // 是否开启客户账户
                // isOrder: data.IsDingHuoTongEnabled, // 是否开通订货通
                // me.author.showSalesCluePool = me.author.salesCluePoolList.length > 0; // 显示线索池
                // salesCluePoolList: data.SalesCluePoolShortInfoList || [],
                // newAuthCode: me._formatNo(data.FunctionRights), // 新建权限code
                // -- 菜单接口不再下发，如需使用通过接口获取 --

                isAdmin: FS.util.isCrmAdmin(), // 是否为crm管理员
                isShowDuplicateSearch: data.IsShowDuplicateSearch, // 是否展示查重工具
                // 不是为云之家用户 导航检查属性用到
                isNotYunZhiJia:
                    window.FS_SOURCE != "yunzhijia" &&
                    window.FS_SOURCE != "wechat",
                // 是为云之家用户
                isYunZhiJia:
                    window.FS_SOURCE == "yunzhijia" ||
                    window.FS_SOURCE == "wechat",
                // 是否显示库存   k3的特殊逻辑
                isShowShiporder:
                    (window.FS_SOURCE != "yunzhijia" &&
                        window.FS_SOURCE != "wechat") ||
                    window.FS_SOURCE == "K3" ||
                    window.FS_SOURCE == "k3",
                isWeChat:
                    window.FS_SOURCE == "yunzhijia" ||
                    window.FS_SOURCE == "wechat",
                isI18nGray: true, // 是否展示对象标签设置
                dfqGray: FS.util.getUserAttribute("dfqGray"), // 是否展示达芬奇
            };
        },
        /**
         * @desc 设置权限
         * 记录左侧权限
         * 渲染页面
         */
        renderTpl: function (data) {
            if (!this.beforeMount || this.beforeMount(data)) {
                var me = this;
                me.settingData = me._getSettingData();
                me.pageData = me._getPageData();
                me.newAddData = me._getNewAddData();
                me.$el.html(
                    navTpl(
                        _.extend(
                            {
                                settingData: me.settingData,
                                pageData: me.pageData,
                                newAddData: me.newAddData,
                                isShowMenuIcon: me.isShowMenuIcon,
                                noIconFoldGray: me.noIconFoldGray,
                                hiddenQuickCreate: data.hiddenQuickCreate,
                                hideModeSetting: true,
                                isMultipleTabs: me.isMultipleTabs || false,
                            },
                            me.author,
                            data,
                            me.options,
                            me.getConfig()
                        )
                    )
                );
                if (!data.curMenu.hiddenQuickCreate) {
                    this._initQuickAdd();
                }

                // 最近使用飘窗更换位置
                $(".aside-wrap").append(
                    '<div class="crm-menus-recent__container"></div>'
                );

                me._getMenusIntoMap(function (munus) {
                    // 渲染前先销毁实例
                    if (me.history) {
                        me.history.destroy();
                        me.history = null;
                    }

                    me.history = new History({
                        isMultipleTabs: me.isMultipleTabs,
                        el: $("body"),
                        data: munus,
                        appId: me.options.appId,
                        menuId: data?.curMenu?.id || me.options?.menu_id || "",
                    });
                    FS.MEDIATOR.on(
                        "crm.history.recent.itemClick",
                        function (options) {
                            me.checkAsidePushTabs(options, true);
                        }
                    );
                });

                if (!_.isEmpty(me.pendData)) {
                    me._renderPending();
                } else {
                    me.setHighlight();
                }

                me._setQuicklyHidden();
            }
        },
        // 初始化快速新建组件
        _initQuickAdd() {
            var me = this;
            var addList = this.newAddData;

            if (addList && addList.length) {
                this.quickAddVm = FxUI.create({
                    wrapper: this.$el.find(".quick-add-wrap")[0],
                    template: `
                        <fx-popover
                            popper-class="crm-aside-quick-add_popover"
                            placement="right-start"
                            width="230"
                            v-model="dVisible"
                            :popper-options="{ boundariesElement: 'viewport' }"
                            :append-to-body="false"
                            trigger="click">
                            <div class="quick-add_panel">
                                <div class="quick-search_wrap">
                                    <fx-input
                                        class="quick-search"
                                        :placeholder="$t('请输入')"
                                        v-model="dKeyword"
                                        size="small"
                                        prefix-icon="fx-icon-search"
                                    ></fx-input>
                                </div>
                                <div class="quick-add_list crm-scroll" @click="_onAdd">
                                    <div v-for="item in cAddList" class="quick-add_item text-truncate" :data-apiname="item.referenceApiname">{{item.text}}</div>
                                </div>
                            </div>
                            <fx-button slot="reference" size="mini" icon="fx-icon-add-2" circle plain></fx-button>
                        </fx-popover>
                    `,
                    computed: {
                        cAddList() {
                            const keyWord = this.dKeyword;

                            return this.dAddList.filter((item) => {
                                return item.text.indexOf(keyWord) > -1;
                            });
                        },
                    },
                    data() {
                        return {
                            dVisible: false,
                            dAddList: addList,
                            dKeyword: "",
                        };
                    },
                    methods: {
                        _onAdd(e) {
                            const target = e.target;
                            const data = this.dAddList.find(
                                (item) =>
                                    item.referenceApiname ===
                                    target.dataset.apiname
                            );

                            this.dVisible = false;
                            me._onQuickAdd(data);
                        },
                    },
                });
            }
        },

        // 快速新建处理
        _onQuickAdd(data) {
            var me = this,
                apiname = data.referenceApiname,
                api = CRM.api;

            var RouteFeedObjs = FS.util.getUserAttribute("feedObjs");
            if (apiname === "saleslog") {
                api.add_sales_log({
                    showDetail: true,
                    logType: 10001,
                    logSubMod: "quick",
                    show_type: "full",
                    From: 2,
                });
                return;
            } else if (RouteFeedObjs.indexOf(apiname) >= 0) {
                api.add_xt_object_dialog({
                    showDetail: true,
                    entry: "quick",
                    displayName: data.text,
                    apiname: apiname,
                    showLoadLastRecordBtn: true, //允许显示载入最近的一条草稿按钮
                    methods: {
                        success: function (activeRecordData) {
                            //跟进记录详情
                            FS.MEDIATOR.trigger("fs.feed2019.slide", {
                                url: "FsFeed/getFeedByObjectId",
                                data: {
                                    apiName:
                                        activeRecordData.object_describe_api_name,
                                    dataId: activeRecordData._id,
                                },
                                options: {
                                    zIndex: CRM.util.getzIndex(),
                                },
                            });
                        },
                    },
                });
                return;
            }

            api.add({
                showDetail: true,
                logType: 10001,
                logSubMod: "quick",
                show_type: "full",
                From: 2,
                displayName: data.text,
                apiname: apiname,
                success: function () {
                    FS &&
                        FS.MEDIATOR &&
                        FS.MEDIATOR.trigger &&
                        FS.MEDIATOR.trigger("crm.table.list.refresh", apiname);
                },
            });
        },

        getConfig() {
            return {
                homeRoute: this.isGrayMenuCustomize ? "crm/home" : "crm/index",
                hideMenuSetting: false,
                hideAppMenuBack:
                    util.getUrlParam(window.location.href, "noappmenuback") ==
                    "1",
                renderRemind: function (item, isOld) {
                    if (isOld) {
                        return "#crm/remind/" + item.key.toLowerCase();
                    }

                    return "#crm/remind/list/=/" + item.key;
                },
                renderTodo: function (item, isOld, isFromCasesobj) {
                    if (isFromCasesobj) {
                        return "#crm/remind/bpmcasesobj/=/" + item.key;
                    }

                    if (isOld) {
                        return "#crm/remind/" + item.key.toLowerCase();
                    }

                    return "#crm/remind/list/=/" + item.key;
                },
            };
        },

        parseRoute(value) {
            return value;
        },
        parseDetailUrl(hash) {
            let appId = this.options.appId;
            let parts = hash.split("#objdetail/");
            let result = parts.length > 1 ? parts[1] : "";
            return `#paasapp/detail/=/${appId}/${result}`;
        },
        /**
         * @desc 是否隐藏搜索
         */
        _setSearchHidden: function () {
            var num = 0;
            _.each(this.pageData, function (item) {
                num = num + 1 + (item.children ? item.children.length : 0);
            });
            this.$el.toggleClass("search-hide", num <= 20);
        },
        /**
         * @desc 无快速新建项
         */
        _setQuicklyHidden: function () {
            this.$el.toggleClass("quickly-hide", this.newAddData.length <= 0);
        },
        /**
         * @desc 根据参数展开收起导航
         */
        handleFoldunfold: function (isFoldMenu) {
            var me = this;
            if (isFoldMenu) {
                me.fold();
            } else {
                me.unfold();
            }
        },
        /**
         * @desc 刷新左侧导航
         */
        refresh: function (cb, menuId) {
            var me = this;
            // 新增loading
            CRM.util.showLoading_tip(
                $t("wx.qixin.todo.loading", null, "加载中..."),
                this.$el,
                "asideLoading"
            );
            me._getData(function (data) {
                me._setStockAuth();
                me.initAuthor();
                me.renderTpl(data);
                me._curPage = "page";
                me.hasBindRemind = false;
                me._scroll && me._scroll.destroy();
                me._scroll = null;
                me._bindGlobalEvents();
                me.setHighlight();
                me.myPageData = [];
                me.modifyNavigationStatus({ isFoldMenu: me.isFoldMenu }, data);
                cb && cb();
                // 隐藏loading
                CRM.util.hideLoading_tip(me.$el, "asideLoading");
            }, menuId);
        },

        /**
         * @desc 获取左侧数据 权限
         */
        _getData: function (callBack, menuId) {
            var me = this;
            util.getCrmCurrentMenu(
                function (data) {
                    if (!data) {
                        CRM.util.hideLoading_tip(me.$el, "asideLoading");
                        return;
                    }
                    me.data = me._formatData($.extend(true, {}, data));
                    callBack && callBack(me.data);
                },
                true,
                menuId
            );
        },
        /**
         * 设置库存相关权限及是否可见
         */
        _setStockAuth: function () {
            var me = this;
            Auth.getStockRemindAuth().then(function (data) {
                // 临到期的库存
                me.data.enable_stockmature = !!data.isShowExpireStockWarning;
                !me.data.enable_stockmature &&
                    me
                        .$(".remind-box[data-key=stockmature]")
                        .closest("li")
                        .remove();
                // 库存补货和预警
                me.data.enable_stockwarn = !!data.isShowNotEnoughStockWarning;
                !me.data.enable_stockwarn &&
                    me
                        .$(".remind-box[data-key=stockwarn]")
                        .closest("li")
                        .remove();
            });
        },

        /**
         * @desc 处理setting的最终数据
         * 权限的过滤
         */
        _getSettingData: function () {
            var me = this,
                result = [];

            // _.each(data.settingData, function(item) {
            // item.children = _.filter(item.children || [], function(cItem) {
            // var checkedProp = cItem.checkedProp ? me.author[cItem.checkedProp] : true; // 检查属性
            // return checkedProp;
            // });
            // result.push(item);
            // });
            return result;
        },

        /**
         * @desc 扩展对象属性
         */
        _extendObjProperty: function (obj) {
            return Aside._extendObjProperty(obj);
        },

        /**
         * @desc 获取工作台数据
         */
        _getPageData: function () {
            var me = this,
                result = [],
                pageData = me.routeData.getPageData
                    ? me.routeData.getPageData()
                    : me.routeData.pageData;
            var isConnectApp = FS.util.isConnectApp();

            // 灰度首页菜单自定义功能的新逻辑，todo：下个版本960视情况需要考虑删除
            if (me.isGrayMenuCustomize) {
                // 兼容互联应用自定义菜单也默认下发首页和最近使用
                if (isConnectApp) {
                    result.push(me.parseRoute(pageData.index));
                }
                
                if (window.Portal && ["FSAID_11490c84"].indexOf(window.Portal.appId) > -1 && !me.isRemoveConnectAppAside) {
                    result.push(me.parseRoute(pageData.crmRemind));
                    result.push(me.parseRoute(pageData.crmTodo));
                }

                function _handleMenus(memo, menus) {
                    if (!menus || !menus.length) {
                        return [];
                    }
                    menus.forEach((item) => {
                        var isAllowView = me._isListAddAuthor(item, "List");
                        // var isAllowAdd = me._isListAddAuthor(item, "Add");
                        if (isAllowView) {
                            item.children = _handleMenus([], item.children);

                            // 首页，最近访问，待办，提醒不支持新开窗口
                            const noSupportJump = [
                                "crmIndex",
                                "recent",
                                "todo",
                                "remind",
                            ];

                            item.isShowJumpBtn = !noSupportJump.includes(
                                item.menuItemId
                            );
                            // item.isShowAddBtn = isAllowAdd;
                            item.isShowAddBtn = false;
                            memo.push(me._extendObjProperty(item));
                            // 特殊处理，将业务通知和待办事项补充点击下钻依赖的data-key
                            if (
                                item.menuItemId === "fs.crm.notify" ||
                                item.menuItemId === "remind"
                            ) {
                                item.key = "remind";
                            }
                            if (
                                item.menuItemId === "fs.todo" ||
                                item.menuItemId === "todo"
                            ) {
                                item.key = "todo";
                            }
                            if (item.menuItemId === "recent") {
                                item.key = "recent";
                            }
                        }
                    });
                    return memo;
                }
                _handleMenus(result, me.data.curMenu.items);

                me.isShowMenuIcon = me.data.curMenu.isShowMenuIcon;
                me.myPageData = me.myPageData.concat(result);
                return result;
            }

            // 非菜灰度首页菜单自定义原始逻辑
            result.push(me.parseRoute(pageData.index));

            // 非互联应用下发最近使用
            if (!isConnectApp) {
                result.push(me.parseRoute(pageData.recentUsed));
            }
            
            if (
                !isConnectApp ||
                (window.Portal &&
                    ["FSAID_11490c84"].indexOf(window.Portal.appId) > -1 &&
                    !me.isRemoveConnectAppAside)
            ) {
                result.push(me.parseRoute(pageData.crmRemind));
                result.push(me.parseRoute(pageData.crmTodo));
            }

            _handleMenus(result, me.data.curMenu.items);

            function _handleMenus(memo, menus) {
                if (!menus || !menus.length) {
                    return [];
                }
                menus.forEach((item) => {
                    var isAllowView = me._isListAddAuthor(item, "List");
                    // var isAllowAdd = me._isListAddAuthor(item, "Add");

                    if (isAllowView) {
                        item.children = _handleMenus([], item.children);
                        // 逻辑已默认为true，先注释处理
                        // item.isShowJumpBtn =
                        //     true ||
                        //     (isAllowView &&
                        //         FS.util.isAllowNewWindow() &&
                        //         item.hash);
                        item.isShowJumpBtn = true; // 允许新窗口打开
                        // item.isShowAddBtn = isAllowAdd;
                        item.isShowAddBtn = false;
                        memo.push(me._extendObjProperty(item));
                        // 特殊处理，将业务通知和待办事项补充点击下钻依赖的data-key
                        if (
                            item.menuItemId === "fs.crm.notify" ||
                            item.menuItemId === "remind"
                        ) {
                            item.key = "remind";
                        }
                        if (
                            item.menuItemId === "fs.todo" ||
                            item.menuItemId === "todo"
                        ) {
                            item.key = "todo";
                        }
                    }
                });
                return memo;
            }

            me.isShowMenuIcon = me.data.curMenu.isShowMenuIcon;
            me.myPageData = me.myPageData.concat(result);
            return result;
        },
        //
        // TODO 放在底层
        //
        isPaasObj: function (apiName) {
            return apiName && apiName.indexOf("__c") != -1;
        },
        /**
         * @desc 判断是否有新建添加的权限
         * 判断云之家隐藏的菜单
         */
        _isListAddAuthor: function (obj, author) {
            author = author || "List";
            if (
                obj.referenceApiname == "VisitingObj" &&
                this.author.isYunZhiJia
            ) {
                return false;
            }
            return !obj.isHidden && _.contains(obj.privilegeAction, author);
        },

        /**
         * @desc 获取快速新建数据
         */
        _getNewAddData: function () {
            var me = this,
                result = [];

            _.each(me.data.curMenu.items, function (item) {
                _.each(item.children || [], function (cItem) {
                    if (me._isListAddAuthor(cItem, "Add")) {
                        result.push(cItem);
                    }
                });
                if (me._isListAddAuthor(item, "Add")) {
                    result.push(item);
                }
            });
            //我只是去掉了这里的灰度判断。以下是什么意思？把TelesalesRecordObj去掉，并且把ActiveRecordObj放在第一位？---jjy
            var first = _.filter(result, function (item) {
                return (
                    item.referenceApiname === "ActiveRecordObj" &&
                    item.referenceApiname !== "TelesalesRecordObj"
                );
            });

            var last = _.filter(result, function (item) {
                return (
                    item.referenceApiname !== "ActiveRecordObj" &&
                    item.referenceApiname !== "TelesalesRecordObj"
                );
            });

            result = first.concat(last);

            me.myPageData = me.myPageData.concat(result);
            return result;
        },
        /**
         * 格式化数据
         */
        _formatData: function (response) {
            return Aside._formatData(response);
        },

        /**
         * @desc 格式化权限数据
         */
        _formatNo: function (nos) {
            var result = {};
            _.each(nos || [], function (item) {
                result[item.FunctionNo] = item.Right == 1;
            });
            return result;
        },

        /**
         * @desc 判断url权限
         * 不是crm管理员进入管理页面
         * 此处合理应该在route时做判断
         * 但在route时拿不到用户crm管理权限
         */
        _checkUrlAuthor: function (isAdmin) {
            if (!isAdmin && location.hash.indexOf("#crm/setting") != -1) {
                FS.tpl.navRouter.navigate("crm/index", {
                    trigger: true,
                });
            }
        },

        /**
         * @desc 设置高亮
         * 1、每次路由结束后调用
         * 2、进入刷新时的高亮
         * 3、回退调用
         */
        setHighlight: function () {
            var me = this,
                $cur;
            if (me.isMultipleTabs) {
                $cur = me.$(
                    '.aside-item-url[href="' + me.getHash(location.hash) + '"]'
                ); // 当前元素
            } else {
                $cur = me.$('a[href="' + me.getHash(location.hash) + '"]'); // 当前元素
            }

            try {
                //新版crm "crm通知"子菜单显示
                _.each($cur, function (item, index) {
                    if (
                        item.className &&
                        item.className === "recent-menu-option_row"
                    ) {
                        $cur.splice(index, 1);
                    }
                });
            } catch (error) {}
            $cur = $cur[0]
                ? $cur
                : me.$('a[href="' + location.hash.split("/=/")[0] + '"]');
            if ($cur[0]) {
                var key = $cur.data("key");
                me.$(".list-menu a").removeClass("cur");
                if (!me.isMultipleTabs) {
                    $cur.addClass("cur");
                } else {
                    if (["remind", "todo"].indexOf($cur.data("id")) < 0) {
                        me.checkAsidePushTabs(
                            {
                                href: $cur.data("href"),
                                text: $cur.data("text"),
                                id: $cur.data("id"),
                            },
                            false
                        );
                    }
                }

                var id = $cur.data("id");
                if (
                    ["fs.crm.notify", "fs.todo", "todo", "remind"].indexOf(id) >
                    -1
                ) {
                    key =
                        id === "fs.crm.notify"
                            ? "remind"
                            : id === "fs.todo"
                            ? "todo"
                            : key;
                }

                me._changeChannel(key);
                //hack 列表页标题显示需要 crm-modules/page/list/list.js 使用
                me.setPageTitle(
                    (window.__crmMenuTitle = $(".text", $cur.eq(0)).text())
                );
                //todo: 确保所在分组展开
                me.openGroup($cur);
                me._doLog($cur);
            }
        },

        openGroup: function ($cur) {
            let $list = $cur.parents(".list.page-list");
            let $a = $list.prev("a");
            if (!$a.hasClass("selected")) {
                $list.css("display", "block");
                $a.toggleClass("selected");
            }
        },

        getHash: function (hash) {
            var _hash = hash.slice(1).split("/");

            // if (_hash[0] === 'bi') {
            //     var biRedirectMap = {
            //         dashboard: 'board'
            //     };

            //     if (biRedirectMap[_hash[1]]) {
            //         return '#' + [_hash[0], biRedirectMap[_hash[1]]].join('/');
            //     }
            // }

            return hash;
        },

        setPageTitle: function (title) {
            title =
                {
                    "#crm/remind/weakremind": $t("CRM通知"),
                    "#crm/index": $t("CRM首页"),
                }[location.hash] ||
                title ||
                "";
            title += title ? "-" + FS.pageTitle : FS.pageTitle;
            // FS.pageTitle = title;
            document.title = title;
        },

        // 更换频道
        // 刷新进入点亮频道
        _changeChannel: function (key) {
            var me = this;
            var page = "page";
            var hash = location.hash;
            key && (page = key);
            if (hash.indexOf("crm/setting/") != -1) {
                page = "setting";
            }
            // else if (hash.indexOf('crm/remind/') != -1) {
            //     page = 'remind'
            // } else {}

            // 支持旧crm、新crm、新机制paas应用
            var appInfo = window.PAAS.app.getInfo();
            var grayNewApps =
                FS.util.getUserAttribute("noGrayNewPaasApp") || [];

            if (
                ["todo", "remind"].indexOf(key) > -1 &&
                !PAAS.app.isCross() &&
                grayNewApps.indexOf(appInfo.appId) === -1
            ) {
                this._curPage = page;
                this.$el.removeClass("page remind setting todo");
                return;
            }

            if (this._curPage != page) {
                this.$("." + page + "-item")
                    .show()
                    .siblings()
                    .hide();
                this._curPage = page;
                this._onResetSearch();
                this.updataScroll();
                if (page != "page") {
                    this.unfold();
                }
            }
            if (["remind", "todo"].indexOf(page) > -1) {
                this.$el.removeClass("page remind setting todo").addClass(page);
            }
        },

        // 跟新滚动条
        updataScroll: function () {
            var me = this;
            me._scroll && me._scroll.updata();
        },

        // 重置搜索
        _onResetSearch: function () {
            var me = this;
            var $el = me.$("." + me._curPage + "-item");
            me.$(".search-ipt").val("");
            me.$(".set-search-reset").hide();
            $("." + me._curPage + "-menu", $el).show();
            $(".search-result", $el).hide();
        },
        _generReg(val) {
            return new RegExp(`(.*)(${val})(.*)`, "i");
        },
        /**
         * @desc 显示管理导航搜索结果
         */
        _showSearchResult: function (key) {
            var me = this,
                data = [],
                page = me._curPage,
                isMatchReg = false;
            var $el = me.$("." + page + "-item");
            var keyReg = me._generReg(key);
            _.each(me.pageData, function (item) {
                //分组
                if (item.type == "group") {
                    // 空分组不搜索
                    if (item.children.length >= 1) {
                        _.each(item.children, function (cItem) {
                            isMatchReg =
                                cItem.searchWords &&
                                cItem.searchWords.some((ite) => {
                                    return keyReg.test(ite);
                                });
                            if (isMatchReg) {
                                data.push(cItem);
                            }
                        });
                    }
                } else {
                    //模糊搜索
                    isMatchReg =
                        item.searchWords &&
                        item.searchWords.some((ite) => {
                            return keyReg.test(ite);
                        });
                    if (isMatchReg) {
                        data.push(item);
                    }
                }
            });
            // 判断是否为下游，修改搜索后的提醒hash
            PAAS.app.isCross() &&
                data.forEach((item) => {
                    if (
                        item.key === "remind" &&
                        me.remindData.list.length > 0
                    ) {
                        var remindItem = me.remindData.list[0] || {};
                        remindItem &&
                            (item.hash = this.getConfig()
                                .renderRemind(remindItem, remindItem.isOld)
                                .replace(/^#/, ""));
                    }
                });
            $(".search-result", $el).html(
                stpl(
                    _.extend(
                        {},
                        {
                            list: data,
                            isShowMenuIcon: me.isShowMenuIcon,
                            page: page == "page" ? "" : page + "/",
                            isMultipleTabs: me.isMultipleTabs || false,
                        },
                        me.getConfig()
                    )
                )
            );
            me._renderPending();
        },

        /**
         * @desc 判断是否为折叠状态
         */
        isFold: function () {
            return this.status == "fold";
        },

        /**
         * @desc 折叠
         */
        fold: function () {
            var me = this;
            if (me.isMultipleTabs) {
                me.trigger("resize", "fold");
                return;
            }
            if (me.isFold()) {
                return;
            }
            me.$el.addClass("animate");
            me.$el
                .animate(
                    {
                        width: 58,
                    },
                    220,
                    function () {
                        me.$(".hover-wrap").width(58);
                        me.$el
                            .removeClass("animate unfold hover-unfold")
                            .addClass("fold");
                        me.$(".j-fold-btn").attr("data-title", $t("展开导航"));
                    }
                )
                .css("overflow", "visible");
            me.status = "fold";
            me.trigger("resize", "fold");
            me._setFoldStatus(true);

            FS.MEDIATOR.trigger("crm.aside.resize", me.$el.offset().left + 58);
        },

        /**
         * @desc 展开
         */
        unfold: function () {
            var me = this;
            if (me.isMultipleTabs) {
                me.trigger("resize", "fold");
                return;
            }
            if (!me.isFold()) {
                return;
            }
            if (me.$el.hasClass("hover-unfold")) {
                me.$el.removeClass("hover-unfold");
                me.$el.css({
                    width:
                        $("html").hasClass("lang-en") ||
                        $("body").hasClass("lang-en")
                            ? 250
                            : 200,
                });
            } else {
                me.$el.addClass("animate");
                me.$el.animate(
                    {
                        width:
                            $("html").hasClass("lang-en") ||
                            $("body").hasClass("lang-en")
                                ? 250
                                : 200,
                    },
                    220,
                    function () {
                        me.$(".hover-wrap").width(
                            $("html").hasClass("lang-en") ||
                                $("body").hasClass("lang-en")
                                ? 250
                                : 200
                        );
                        me.$el.removeClass("animate fold").addClass("unfold");
                    }
                );
            }
            me.status = "unfold";
            me.trigger("resize", "unfold");
            me.$(".j-fold-btn").attr("data-title", $t("收起导航"));
            me._setFoldStatus(false);
            FS.MEDIATOR.trigger(
                "crm.aside.resize",
                me.$el.offset().left + $("html").hasClass("lang-en") ||
                    $("body").hasClass("lang-en")
                    ? 250
                    : 200
            );
        },

        /**
         * @desc 切换展开折叠状态
         */
        toggleFold: function (e) {
            var me = this;
            if (me.isFold()) {
                me.unfold();
                return false;
            }
            me.fold();
            e.stopPropagation();
            me.collapseNavigationLog();
        },

        collapseNavigationLog: function () {
            if (window.Fx) {
                Fx.log("collapseNavigation", "pv", { date: new Date() });
            } else {
                CRM.util.sendLog("crm", "aside", {
                    operationId: "collapseNavigation",
                    date: new Date(),
                });
            }
        },

        /**
         * @desc 打开菜单导航弹窗
         */
        _showMenuPopoverSet: function () {
            var wrap;
            var me = this;
            if (me.isMultipleTabs) {
                wrap = $(".crm-multiple-tabs-aside .custom-menu-sort");
            } else {
                wrap = $(".crm-aside .custom-menu-sort");
            }

            if (!wrap.children(".menus-switch__popover").length) {
                // 根据是否开启灰度的开关传入不同的值
                if (Fx.util.getGray("newCRMAllMenuGray")) {
                    if (window.CRM_All_menu_new) {
                        var data = me._formatData(
                            $.extend(true, {}, window.CRM_All_menu_new)
                        );
                        // 使用新方法创建菜单切换弹窗组件
                        me.customMenuSet = me._createMenuSwitchPopover(
                            wrap[0],
                            data.menus
                        );
                    } else {
                        CRM.util.getCurrentMenuList(function () {
                            var data = me._formatData(
                                $.extend(true, {}, window.CRM_All_menu_new)
                            );
                            // 使用新方法创建菜单切换弹窗组件
                            me.customMenuSet = me._createMenuSwitchPopover(
                                wrap[0],
                                data.menus
                            );
                        });
                    }
                } else {
                    if (window.CRM_All_menu) {
                        var data = me._formatData(
                            $.extend(true, {}, window.CRM_All_menu)
                        );
                        // 使用新方法创建菜单切换弹窗组件
                        me.customMenuSet = me._createMenuSwitchPopover(
                            wrap[0],
                            data.menus
                        );
                    } else {
                        CRM.util.getCrmAllMenu(function () {
                            var data = me._formatData(
                                $.extend(true, {}, window.CRM_All_menu)
                            );
                            // 使用新方法创建菜单切换弹窗组件
                            me.customMenuSet = me._createMenuSwitchPopover(
                                wrap[0],
                                data.menus
                            );
                        });
                    }
                }
            }
        },
        /**
         * @desc 创建菜单切换弹窗组件
         * @param {HTMLElement} wrapper 挂载的DOM节点
         * @param {Array} menus 菜单数据
         * @returns {Object} 返回创建的组件实例
         */
        _createMenuSwitchPopover: function (wrapper, menus) {
            return (
                FxUI.create &&
                FxUI.create({
                    wrapper: wrapper,
                    template: `
                    <div class="menus-switch__popover">
                        <fx-scrollbar
                            :noresize="noresize"
                            class="content"
                        >
                            <ul>
                                <li v-for="(menu,index) in menus" :key="index" :class="{cur:menu.isCurrent===true}" class="custom-menu-sort-item" :data-id="menu.id">
                                    <span class="menu-name template text-truncate-multi" style="--line-clamp: 2">{{menu.displayName}}</span>
                                </li>
                            </ul>
                        </fx-scrollbar>
                    </div>
                `,
                    computed: {},
                    data() {
                        return {
                            menus: menus,
                        };
                    },
                    methods: {},
                })
            );
        },
        /**
         * 打开themePopover
         */
        _showThemePopover: function (_flag = false) {
            if (_flag) {
                return;
            }
            if ($(".theme-popover").hasClass("on")) {
                $(".theme-popover").removeClass("on");
            } else {
                $(".theme-popover").addClass("on");
            }
        },
        _showMenuPopover: function () {
            this._showThemePopover();
            this._showMenuPopoverSet();
        },

        // _onHideMenuPopover: function () {
        //     $(".custom-menu-sort").hide();
        // },

        newTabOpenLink: function (e) {
            var target = e.target;
            CRM.util.sendLog("crm", "aside", {
                operationId: "newTabOpen",
                hash: target.dataset.hash,
            });
            if (target.dataset.hash) {
                if (
                    FS.util.getUserAttribute("openUrlByDingtalk") &&
                    navigator.userAgent.indexOf("dingtalk") > -1
                ) {
                    dd.biz.util.invokeWorkbench({
                        app_url: `${location.origin}/${location.pathname}${location.search}#${target.dataset.hash}`,
                        app_info: {
                            app_refresh_if_exist: true,
                            app_active_if_exist: true,
                        },
                    });
                } else {
                    window.open(`#${target.dataset.hash}`, "_blank");
                }
            }
            e.preventDefault();
        },
        onBackIndexClick() {
            this.$el.removeClass("remind setting todo").addClass("page");
        },

        addOneObject(e) {
            var $target = $(e.target);
            var objectApiName = $target.data("apiname");
            var recordType = $target.data("recordtype");

            if (recordType) {
                CRM.api.add({
                    apiname: objectApiName,
                    record_type: recordType,
                });
                this.$addPanel.hide();
            }
            e.preventDefault();
        },

        hoverOneObject: function (e) {
            var $target = $(e.target);
            var objectApiName = $target.data("apiname");
            var $addPanel = this.$addPanel;

            if (e.type === "mouseenter") {
                if ($addPanel.data("apiname") !== objectApiName) {
                    $addPanel.html("").hide();
                }
                getRecordType().then((res) => {
                    if (res.length > 1) {
                        var $lis = res.map((item) => {
                            return `<div data-recordtype="${item.api_name}" data-apiname="${objectApiName}">${item.label}</div>`;
                        });
                        var pos = $target.offset();
                        $addPanel
                            .html($lis)
                            .css({
                                top: pos.top - 10,
                                left: pos.left + 20,
                            })
                            .data("apiname", objectApiName)
                            .show();
                    }
                });
            } else {
                if (!$(e.toElement).closest(".crm-menus-add_panel").length) {
                    $addPanel.html("").hide();
                }
            }
            e.preventDefault();

            function getRecordType() {
                return new Promise((resolve) => {
                    const recordTypes = $target.get(0)._recordTypes;
                    if (recordTypes) {
                        resolve(recordTypes);
                    } else {
                        CRM.util
                            .getRecordType({
                                describeApiName: objectApiName,
                                is_only_active: true,
                            })
                            .then((res) => {
                                e.target._recordTypes = res;
                                if (res?.length == 1) {
                                    $target.data("recordtype", res[0].api_name);
                                }
                                resolve(res);
                            });
                    }
                });
            }
        },

        switchMenuLog: function (menuId) {
            CRM.util.sendLog("crm", "aside", {
                operationId: "switchMenu",
                menuId,
            });
        },
        /**
         * @private
         * @desc 导航到首页
         */
        _navigateToIndex: function () {
            const indexHash = this.isGrayMenuCustomize
                ? "crm/home"
                : "crm/index";
            window.Fx?.router?.navigate(indexHash, { trigger: true });
        },
        /**
         * @private
         * @desc 处理菜单切换后的刷新逻辑
         */
        _handleMenuSwitchRefresh: function (menuId) {
            if (this.isMultipleTabs) {
                this.trigger("clearMultipleTabsAside", this.options.baseData);
            }

            this.refresh(() => {
                this._navigateToIndex();
            }, menuId);
            this._onHoverHandle();
            this._showThemePopover();
            // 切换菜单后调用获取菜单list更新缓存
            if (FS.util.getUserAttribute("newCRMAllMenuGray")) {
                window.CRM.util.getMenuNavList();
            }
        },
        _onSwitchMenu: function (e) {
            var $target = $(e.currentTarget);
            var menuId = $target.attr("data-id");
            util.FHHApi({
                url: "/EM1HWebPage/userMenu/setCurrentMenu",
                data: {
                    menuId: $target.attr("data-id"),
                },
                success: this._handleMenuSwitchRefresh(menuId),
            });
            //切换菜单埋点
            this.switchMenuLog($target.attr("data-id"));
        },

        /**
         * @desc 设置导航
         */
        _onSetMenu: function () {
            var me = this;
            me._setMenu = new Set();

            me._setMenu.on("show", function () {
                me._setMenu.setTitle(
                    $t("crm_aside_menu_set_dialog-title", {}, "CRM菜单设置")
                );
            });
            me._setMenu.on("refresh", function (menuId) {
                me._onHoverHandle();
                if (FS.util.getUserAttribute("newCRMAllMenuGray")) {
                    // 更新菜单缓存
                    window.CRM.util.getCurrentMenuList(menuId);
                } else {
                    me.refresh();
                }
            });
            console.log("me.data.menus", me.data.menus);
            if (FS.util.getUserAttribute("newCRMAllMenuGray")) {
                me._setMenu.show(
                    $.extend(
                        true,
                        [],
                        Aside._formatData(_.clone(window.CRM_All_menu_new))
                            .menus
                    ),
                    Aside._formatData
                );
            } else {
                me._setMenu.show(me.data.menus);
            }

            // 恢复菜单目前在仓库中搜索未定位到事件暴露的地方，历史逻辑可能已移除
            me._setMenu.on("recover", function (cb) {
                me._getData(cb);
            });

            me._showThemePopover();
        },

        /**
         * @desc 设置模式crm.aside.resize
         */
        _onSetMode: function () {
            var me = this;
            me._showThemePopover(me.getConfig().hideMenuSetting);
            require.async("base-biz/sdk", function (sdk) {
                sdk.globalComponents["crmmodeset"] &&
                    sdk.globalComponents["crmmodeset"]().then(function (obj) {
                        obj.init({
                            appId: me.options.appId,
                        });
                    });
            });
        },

        // 分组
        _onItemTitle: function (e) {
            var me = this,
                $target = $(e.currentTarget),
                $list = $target.next(".list");

            $list.slideToggle(260);
            $target.toggleClass("selected");
        },

        /**
         * @desc 鼠标经过展开
         */
        _onHoverHandle: function (e) {
            var me = this;
            if (me.isMultipleTabs) {
                return;
            }
            if (!me.isFold()) {
                return;
            }
            if (e && $(e.target).closest(".theme-set").length > 0) {
                return;
            }
            if (e && e.type == "mouseenter") {
                me.$(".hover-wrap")
                    .stop(true, false)
                    .animate(
                        {
                            width:
                                $("html").hasClass("lang-en") ||
                                $("body").hasClass("lang-en")
                                    ? 250
                                    : 200,
                        },
                        180,
                        function () {
                            me.$el.removeClass("fold").addClass("hover-unfold");
                            me.$(".hover-wrap").css("overflow", "visible");
                        }
                    );
                me.$(".j-fold-btn").attr(
                    "data-title",
                    $t("crm.index.nav.fixed.navigation", null, "固定导航")
                );
            } else {
                var unfoldFun = function () {
                    me.$(".hover-wrap")
                        .stop(true, false)
                        .animate(
                            {
                                width: 58,
                            },
                            180,
                            function () {
                                me.$(".hover-wrap").css("overflow", "visible");
                                me.$el
                                    .removeClass("hover-unfold")
                                    .addClass("fold");
                                me.setHighlight();
                                me.updataScroll();
                            }
                        );
                    me.$(".j-fold-btn").attr("data-title", $t("展开导航"));
                };
                // 移除时新增判断是否input是获焦状态，不收起，点击其他区域收起
                // 新增判断：是否划入最近使用，如果是也不收起
                if (
                    document.querySelector(".b-g-ipt.search-ipt") ===
                    document.activeElement
                ) {
                    let __pasteHandles = function () {
                        unfoldFun();
                        document.removeEventListener("click", __pasteHandles);
                    };

                    document.addEventListener("click", __pasteHandles);
                } else {
                    unfoldFun();
                }
            }
        },

        /**
         * @desc 统计代码
         */
        _doLog: function ($target) {
            var me = this,
                log = $target.data("log"),
                hash = $target.attr("href").split("#")[1];

            if (log) {
                var mod = "setting";
                if (hash.indexOf("setting") == -1) {
                    mod = "crmhome";
                    log = util.logEnums[log] || log;
                }
                if (log == "paasobj") {
                    // 自定义对象
                    var apiName = hash.split("/") || [];
                    apiName = apiName[apiName.length - 1] || "";
                    log =
                        apiName && apiName.slice(apiName.length - 3) == "obj"
                            ? apiName
                            : log;
                }
                util.uploadLog(mod, "entry", {
                    eventType: "cl",
                    eventId: log,
                });
            }
        },

        _onLookHelp: function () {
            CRM.util.uploadLog("crmhome", "page", {
                eventId: "lookhelp",
                eventType: "cl",
            });
        },
        // 菜单折叠的参数
        _getMenuFoldParams: function () {
            return {
                menuId: "CRM",
            };
        },
        // 获取上一次菜单的折叠状态
        _getFoldStatus: function () {
            var me = this;
            var initData = {};
            var params = me._getMenuFoldParams();
            // 灰度获取赋值
            me.noIconFoldGray = window.Fx.util.getGray("iconFoldGray") || true;
            return new Promise((resolve, reject) => {
                return FS.util.FHHApi(
                    {
                        url: "/EM1HWebPage/userMenu/getUserFoldMenuStatus",
                        data: params,
                        success(res) {
                            resolve(res);
                        },
                        error(err) {
                            reject(err);
                        },
                    },
                    { errorAlertModel: 1 }
                );
            })
                .then((res) => {
                    if (
                        res.Result.StatusCode === 0 &&
                        res.Value.employeeConfig
                    ) {
                        return JSON.parse(res.Value.employeeConfig);
                    } else {
                        return initData;
                    }
                })
                .catch(() => {
                    return initData;
                });
        },
        // 保存菜单的折叠状态
        _setFoldStatus: function (isFoldMenu) {
            var me = this;
            var params = me._getMenuFoldParams();
            FS.util.FHHApi(
                {
                    url: "/EM1HWebPage/userMenu/saveUserFoldMenuStatus",
                    data: {
                        ...params,
                        employeeConfig: JSON.stringify({
                            isFoldMenu: isFoldMenu,
                        }),
                    },
                },
                { errorAlertModel: 1 }
            );
            me.isFoldMenu = isFoldMenu;
        },
        // 添加当前页面路由到标签页
        _addTabsAside: function (e) {
            var me = this;
            const { id } = e.currentTarget.dataset;
            const { appId, menu_id } = this.options;
            if (me.isMultipleTabs) {
                if (
                    ["remind", "todo"].indexOf(e.currentTarget.dataset.id) < 0
                ) {
                    // 阻止默认跳转事件，按照标签存储后时间跳转
                    e.preventDefault();
                    me.trigger("resize", "fold");
                    me.checkAsidePushTabs(e.currentTarget.dataset, true);
                }
            }

            // 点击菜单后触发上报当前菜单id根据appId和场景id存入缓存中
            let cacheMenuList = util.getCache("crm-page-list-menu-id") || {};
            const storyageId = appId + (menu_id ? "_" + menu_id : "");
            cacheMenuList[storyageId] = id;
            util.setCache("crm-page-list-menu-id", cacheMenuList);
        },
        // 点击item埋点上报事件
        _addAsideItemLog: function (e) {
            // crm埋点上报
            const { id, text, i18nkey } = e.currentTarget.dataset;
            const eventData = {
                module: "Menu",
                moduleDataId: id,
                moduleDataName: text,
                moduleDataI18nKey: i18nkey,
                actionType: "viewmenu",
            };
            console.log("埋点上报", eventData);
            window.logger?.action({
                eventId: "user_usage_analysis",
                eventName: "click",
                eventData,
            });
        },
        checkAsidePushTabs: function (dataset, isRender = false) {
            var multipleTabsAside;
            var me = this;
            // 标签多语支持处理
            me.undateMultipleTabsAsideDisplayName();
            multipleTabsAside = me.options.multipleTabsAside || [];
            me.options.multipleTabsAsideLength = multipleTabsAside.length;
            // 最大填充数量20
            if (multipleTabsAside.length < 20) {
                var isExistence = multipleTabsAside.findIndex(
                    (item) =>
                        item.href == dataset.href && item.objId == dataset.id
                );

                if (isExistence < 0) {
                    multipleTabsAside.push({
                        id: new Date().getTime(),
                        objId: dataset.id,
                        name: dataset.text,
                        href: dataset.href,
                    });
                    me.options.multipleTabsAside = multipleTabsAside;
                    me.trigger("undateMultipleTabsAside", multipleTabsAside);
                    if (window.location.hash != "#" + dataset.href) {
                        this.navigateToHref(dataset.href);
                    }
                } else {
                    this.navigateToHref(dataset.href);
                }
            } else if (multipleTabsAside.length == 20 && isRender) {
                var isExistence = multipleTabsAside.findIndex(
                    (item) =>
                        item.href == dataset.href && item.objId == dataset.id
                );
                // 判断当前跳转的是否在多标签页中，如果在跳转，不在提示message信息
                if (isExistence < 0) {
                    FxUI.Message({
                        type: "warning",
                        duration: 2000,
                        message: $t(
                            "crm_aside_aside-item_maximum_tips",
                            {},
                            "标签页超过最大展示数量，请手动删除数据后再次添加~"
                        ),
                    });
                } else {
                    this.navigateToHref(dataset.href);
                }
            }
        },
        // 兼容下游跳转地址
        navigateToHref(href) {
            if (Fx.util.isConnectApp()) {
                location.hash = href;
            } else {
                Fx.router &&
                    Fx.router.navigate(href, {
                        trigger: true,
                    });
            }
        },
        undateMultipleTabsAsideDisplayName: _.throttle(function () {
            var me = this;
            var menuData = me.pageData;
            // console.log("menuData", menuData, me.options.multipleTabsAside);
            // 过滤掉不存在于当前菜单的标签页
            me.options.multipleTabsAside = me.options.multipleTabsAside.filter(
                (item) => {
                    let exists = false;
                    // 检查标签页是否存在于当前菜单
                    menuData.forEach((data) => {
                        if (data.type == "group" && data.children.length > 0) {
                            exists =
                                exists ||
                                data.children.some(
                                    (child) => child.id === item.objId
                                );
                        } else {
                            exists = exists || data.id === item.objId;
                        }
                    });
                    return exists;
                }
            );

            // 更新存在标签页的名称
            me.options.multipleTabsAside.forEach((item) => {
                menuData.forEach((data) => {
                    if (data.type == "group" && data.children.length > 0) {
                        data.children.forEach((child) => {
                            if (child.id == item.objId) {
                                item.name = child.displayName;
                            }
                        });
                    } else if (item.objId == data.id) {
                        item.name = data.displayName || data.text;
                    }
                });
            });
            me.trigger("undateMultipleTabsAside", me.options.multipleTabsAside);
        }, 2000),
        destroy: function () {
            this._scroll && (this._scroll.destroy(), (this._scroll = null));
            this._delegateGuide &&
                (this._delegateGuide.destroy(), (this._delegateGuide = null));
            $(".crm-menus-recent__container").remove();
            if (this.$addPanel) {
                this.$addPanel.off().remove();
            }
            if (this.quickAddVm) {
                this.quickAddVm.$destroy();
            }
            if (this.customMenuSet) {
                this.customMenuSet.$destroy();
            }
            if (this._setMenu) {
                this._setMenu.$destroy();
            }
            if (this.history) {
                this.history.destroy();
            }
            FS.MEDIATOR.off("crm.menu.fold");
            FS.MEDIATOR.off("crm.menu.unfold");
        },
    });

    Aside.front_data = data;
    Aside._formatData = function (response) {
        var result = _.extend({}, response.configinfo || {});
        var menus = [];
        var pageData = data.getPageData
            ? data.getPageData()
            : data.pageData;
        // 分组与排序格式化
        _.each(response.menus, function (menu) {
            var pItems = [],
                cItems = [];
            _.each(menu.items, function (item) {
                item = _.extend(item, pageData[item.referenceApiname] || {});
                item.text = item.displayName;

                // 分组的privilegeAction必须有值
                item.type === "group" && _.isEmpty(item.privilegeAction) && (item.privilegeAction = ["List"]);
                if (!item.pid) {
                    item.children = [];
                    pItems.push(item);
                } else {
                    cItems.push(item);
                }
            });
            _.each(cItems, function (item) {
                var pItem = _.findWhere(pItems, {
                    id: item.pid,
                });
                if (pItem) {
                    pItem.children.push(item);
                }
            });
            _.each(pItems, function (item) {
                item.children = _.sortBy(item.children, function (cItem) {
                    return cItem.number * 1;
                });
            });
            pItems = _.sortBy(pItems, function (item) {
                return item.number * 1;
            });
            menu.items = pItems;
            menus.push(menu);
        });
        result.menus = menus;
        var curMenu = _.findWhere(result.menus, {
            isCurrent: true,
        });
        result.curMenu = !curMenu
            ? {}
            : {
                  displayName: curMenu.displayName,
                  id: curMenu.id,
                  isCurrent: curMenu.isCurrent,
                  isShowMenuIcon: curMenu.isShowMenuIcon,
                  dealHomeFlag: curMenu.dealHomeFlag,
                  isSystem: curMenu.isSystem,
                  hiddenQuickCreate: curMenu.hiddenQuickCreate,
                  menuId: curMenu.menuId,
                  items: _.map(curMenu.items, function (item) {
                      return _.extend({}, item);
                  }),
              };
        return result;
    };
    Aside._extendObjProperty = function (obj) {
        var apiName = obj.referenceApiname;
        if (!obj.icon && apiName) {
            var icon = !_.isUndefined(obj.iconIndex)
                ? "fx-icon-obj-app" + (obj.iconIndex + 1)
                : "fx-icon-obj-define";
            obj.icon = obj.fxIcon || icon;
            if (obj.iconSlot > 0) {
                obj.icon = "fx-icon-obj-app" + obj.iconSlot;
            } else {
                obj.icon = !(apiName && apiName.indexOf("__c") != -1)
                    ? icon + " fx-icon-obj-" + apiName.toLowerCase()
                    : icon;
            }
        }
        obj.text = obj.displayName;
        if (obj.useDefaultUrl && obj.url && obj.target != "_blank") {
            // 自定义页面路由
            if (/^crm\/custompage\/=\//.test(obj.url)) {
                obj.hash = obj.url;
            } else {
                obj.hash = "crm/thirdapp/=/" + apiName;
            }
        } else if (obj.webGoJumpUrl) {
            // 判断是否是全路径
            if (obj.target == "_blank") {
                if (
                    new RegExp(/(http|https):\/\/(\w+\.)+\S*/).test(
                        obj.webGoJumpUrl
                    )
                ) {
                    obj.hash = obj.webGoJumpUrl;
                } else {
                    obj.hash = window.location.origin + obj.webGoJumpUrl;
                }
            } else {
                // 注意：有的webGoJumpUrl需要补偿crm前缀，例如thirdapp/xxx，有的不需要，例如bi；兼容只下发thirdapp/xxx的情况，需补偿为crm/thirdapp/xxx
                if (obj.webGoJumpUrl.indexOf("thirdapp") > -1) {
                    obj.hash = "crm/" + obj.webGoJumpUrl.replace(/^crm\//g, "");
                } else {
                    obj.hash = obj.webGoJumpUrl;
                }
            }
        } else {
            var recordType =
                obj.url && obj.url.match(/thirdapprecordtype=(\S+__c)/);
            if (recordType) {
                var ti = obj.url.match(/apiname=(\S+)&/);
                if (ti) {
                    apiName = ti[1];
                } else {
                    recordType = null;
                }
            }
            obj.hash =
                obj.hash ||
                "crm/list/=/" +
                    apiName +
                    (recordType ? "/" + recordType[1] : "");
        }
        //obj.hash = obj.hash || (obj.useDefaultUrl && obj.url) || 'crm/list/=/' + apiName;
        // todo 后期接口将直接下发crm/custompage或者crm/custommenu/custompage，无需再由前端处理
        if (
            /^crm\/(custompage|list)\/=\//.test(obj.hash) &&
            /menu_(.*?)__c/.test(obj.menuItemId)
        ) {
            obj.hash = obj.hash.replace(
                /^crm\/(custompage|list)\/=\//,
                function (...args) {
                    return args[0].replace(args[1], `custommenu/${args[1]}`);
                }
            );
        }
        return obj;
    };

    module.exports = Aside;
});
