/**
 * @Desc: BOM 相关公共方法
 * @author: wangshaoh
 * @date: 3/11/24
*/
define(function (require, exports, module) {

	var project = {

		// server 接口，校验 bom 数据
		async checkBom_server(children, rootData, totalMoney){

			function parseDataToCheckBom() {

				let data = CRM.util.parseTreeToNormal(children, true);

				let groupList = _.filter(data, function (item) {
					if(item.parent_rowId) item.pid = item.parent_rowId;
					return item.isGroup;
				});

				function _findGroup(id) {
					return _.find(groupList, function (item) {
						return item.rowId == id;
					})
				}

				// server用的字段；
				let fields = ['new_bom_path', 'core_id', 'related_core_id', 'node_bom_core_type', 'node_bom_core_version', 'product_group_id__r', 'node_type',
					'product_id', '_id', 'product_status', 'product_id__r', 'adjust_price', 'object_describe_api_name', 'amount', 'product_group_id',
					'bom_id', 'modified_adjust_price', '__adjust_price', 'prod_pkg_key', 'parent_prod_pkg_key', 'price_book_id'];
				let subBomList = [];
				data.forEach(item => {
					if (item.isGroup) return;
					item.prod_pkg_key = item.rowId;
					delete item.product_id__ro;
					if (item.pid && !item.isGroup) {
						let group = _findGroup(item.pid);
						item.parent_prod_pkg_key = group ? group.pid : item.pid;
					}
					let o = _.pick(item, fields);
					subBomList.push(o);
				});

				let pbId = rootData.price_book_id ||  rootData.priceBookId || '';

				return {
					"productBomList": [{
						core_id: rootData.bom_core_id,
						node_bom_core_type: rootData.bom_type,
						node_bom_core_version: rootData.bom_version,
						rootProductId: rootData.product_id, // 根节点产品id
						newProductPrice: totalMoney, 	 // 调整后总金额
						subBomList,					     // 子产品数据，不含分组
						rootProdKey: rootData.rowId,				 // 根节点虚拟key
						rootBomId: rootData.bom_id, 		 // 根节点bom_id
						priceBookId: pbId					 // 根节点的价目表id
					}],
					priceBookId: pbId						 // 根节点的价目表id
				}
			}

			// 处理校验结果
			function parseCheckRes(obj) {
				if (obj.Result.StatusCode !== 0) {
					return false;
				}
				let msg = '';
				let result = obj.Value.result;
				_.each(result, function (item, key) {
					if (item.checkSingleResult) msg += item.checkSingleResult + '<br/>';
				});

				if (msg.length) {
					CRM.util.alert(msg);
					return false
				}
				return true;
			}

			let param = parseDataToCheckBom();
			console.log(param);
			CRM.util.showLoading_tip();
			let res = await CRM.util.checkBom(param);
			CRM.util.hideLoading_tip();
			let checkRes = parseCheckRes(res);
			return checkRes
		},

		// 处理数据，校验 bom 约束规则
		parseBomForCheckRule(data, cb, needAll = false) {

			// 非标属性
			function _parseNsAttr(nsAttr) {
				if (!nsAttr) return [];
				let res = [];
				_.each(nsAttr, (val, key) => {
					res.push({
						id: key,
						name: val.name,
						attributeValues: {
							value: val.value
						}
					})
				});
				return res;
			}

			// 标准属性
			function _parseAttr(attr) {
				if (!attr) return [];
				let res = [];
				_.each(attr, (val, key) => {
					let v = val.value_ids[0];
					res.push({
						id: key,
						name: val.name,
						attributeValues: {
							id: v.id,
							name: v.name,
						}
					})
				});
				return res;
			}

			// 根据nonstandard_attribute_json 处理已选非标属性
			function _parseNsAttrByJson(data){
				let res = [];
				if(data.nonstandard_attribute_json){
					_.each(data.nonstandard_attribute_json, (val, key) => {
						let f = data.nonstandardAttribute.find(d => d.id === key);
						res.push({
							id: key,
							name: f.name,
							attributeValues: {
								value: val
							}
						})
					})
				}
				return res;
			}

			// 根据 attribute_json 处理已选属性
			function _parseAttrByJson(data){
				let res = [];
				if(data.attribute_json){
					let attrInfo = data.__cacheAttribute || data.attribute;
					_.each(data.attribute_json, (val, key) => {
						let f = attrInfo?.find(d => d.id === key);
						if(!f) return;
						let valName = f.attribute_values.find(d => d.id === val).name;
						res.push({
							id: key,
							name: f.name,
							attributeValues: {
								id: val,
								name: valName,
							}
						})
					})
				}
				return res;
			}

			return data.map(item => {
				let r = {
					bomId: item.bom_id,
					nonAttributes: item.nsAttr ? _parseNsAttr(item.nsAttr): _parseNsAttrByJson(item),
					attributes: item.selectedAttr ? _parseAttr(item.selectedAttr) : _parseAttrByJson(item),
					product_group_id: item.product_group_id || '',
					current_root_new_path: item.current_root_new_path || '',
					amount: item.__amount,
					isRoot: item._isRoot,
				};
				cb && (r = cb(r, item));
				if (!r.nonAttributes.length) delete r.nonAttributes;
				if (!r.attributes.length) delete r.attributes;
				delete r.product_id__ro;
				delete item.product_id__ro;
				if(needAll){
					return Object.assign(item, r);
				}
				return r;
			})
		},

		// server 接口，校验 bom 属性约束
		async checkBomAttrRule_server(rootData, AttrConstraintRule = [], AttrAplFunctionList = []){
			let _this = this;

			function parseDataByBomRule(rootData) {
				let allCheckedData = CRM.util.parseTreeToNormal([rootData], true, true);
				allCheckedData = allCheckedData.filter(item => item.isChecked);
                
                if (AttrConstraintRule?.length || AttrAplFunctionList?.length) {
                    let bomAttributeConstraintLines = AttrConstraintRule.filter(item => item.current_root_new_path.includes(rootData.bom_id)); // ?
                    let aplList = AttrAplFunctionList.map(item => item.api_name);
                    return {
                        treeList: [ 
                            {
                                productName: rootData.product_id__r || rootData.name,
                                rootId: rootData.bom_id,
                                selectBomList: _this.parseBomForCheckRule(allCheckedData, (res, item) => {
                                    if (item.bom_id === rootData.bom_id) {
                                        item.product_id = rootData.product_id || rootData._id;
                                        item.amount = 1;
                                    }
                                    return _.extend({}, item, res)
                                }),
                                bomAttributeConstraintLines,// 普通约束模式
                                aplList, // apl约束模式
                            }
                        ]
                    }
                }
                return;
			}

			// 校验约束规则 接口
			function checkBomAttrRule(param) {
				CRM.util.showLoading_tip();
				return CRM.util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/bom/service/check_bom_constraint',
					data: param,
					success: function (res) {
						CRM.util.hideLoading_tip();
						if (res.Result.StatusCode === 0) {
							return res.Value
						}
						CRM.util.alert(res.Result.FailureMessage)
					}
				}, {
					errorAlertModel: 1
				})
			}

			// 提示校验结果
			function parseCheckRuleRes(res) {
				if (res.Value.success) return true;
				if (res.Value.msg) CRM.util.alert(res.Value.msg);
				return false;
			}

			let param = parseDataByBomRule(rootData);
			let r = true;
			if(!param) return r;
			let checkAttrRule = await checkBomAttrRule(param);
			r = parseCheckRuleRes(checkAttrRule);
			return r;
		},

		//格式化所选数据上的属性值
		parseDataAttr(tableData) {
			tableData = _.isArray(tableData) ? tableData : [tableData];
			CRM.util.forEachTreeData(tableData, data => {
				let selectedAttrArr = Object.keys(data.selectedAttr || {});
				if (!data.isGroup && data.selectedAttr && selectedAttrArr.length >= 1 && data.isChecked) {
					///
					let attrValues = data.selectedAttr,
						attrG = {},
						attrT = "";

					for (let k in attrValues) {
						let valIds = attrValues[k].value_ids;
						attrG[k] = valIds[0].id;
						attrT += attrValues[k].name + ":" + valIds[0].name + ";";
					}

					data.attribute_json = attrG;
					// 这里原始属性信息被覆盖了，先缓存一份；
					if (!data.__cacheAttribute) data.__cacheAttribute = data.attribute;
					data.attribute = attrT.slice(0, attrT.length - 1);
				}
				//非标属性
				if (!data.isGroup) {
					let keyArr = Object.keys(data.nsAttr || {});
					if (keyArr.length >= 1) {
						data.nonstandard_attribute_json = {},
							data.nonstandard_attribute = "";
						keyArr.forEach((attrId, idx) => {
							const item = data.nsAttr[attrId];
							data.nonstandard_attribute_json[attrId] = item.value;
							data.nonstandard_attribute += `${item.name}:${item.value}${idx == keyArr.length - 1 ? "" : ","}`
						})
					}
				}
			})
		},

		// 初始化子件数量；复用 bom 的子件数量需要成倍，并且需要记录一个相对于复用 bom 的数量比例字段
		setChildrenAmount(data, {baseAmount, amount, amount_any, related_core_id} = {}, cb, fieldMapping = {}){

			// 兼容取 数量任意值
			function _getAmountAny(item) {
				let _v = amount_any + '__v';
				return CRM.util.hasValue(item[_v]) ? item[_v] : item[amount_any];
			}

			function _initChildren(children, parentBom){
				function _fn(cd){
					for (let i = 0; i < cd.length; i++){
						let c = cd[i];
						c.__parentBomRowId = parentBom.rowId;
						if(_getAmountAny(c)) {
							if(c.children && !c[related_core_id]) _fn(c.children);
							continue
						}
						if(c.isGroup){
							_fn(c.children || []);
						}else{
							let newAmount = CRM.util.multiplicational(c[baseAmount],  parentBom[amount]);
							c[amount] = newAmount;
							if(cb){
								let r1 = cb(newAmount, c, parentBom);
								if(r1 === false) return;
							}
							if(c.children && !c[related_core_id]) _fn(c.children);
						}
					}
				}
				_fn(children);
			}

			CRM.util.forEachTreeData(data, item => {
				if(item.isGroup) return;
				if((item[related_core_id] || CRM.util.isBom(item, null, fieldMapping).isPackage) && item.children){
					_initChildren(item.children, item)
				}
			})
		},

		/**
		 * @desc 拷贝 bom关键数据，然后转为树形结构
		 * @param data
		 * @param fields Array 拷贝字段 ['rowId', 'pid', 'parent_rowId', 'amount', '__amount']
		 * @returns {*|Array}
		 */
		cloneBomData(data = [], fields = []){
			data = Array.isArray(data) ? data : [data];
			fields?.push('isGroup');
			let arr = [];
			CRM.util.forEachTreeData(data, item => {
				if(!_.isObject(item)) return;
				let o = {};
				fields.forEach(f => {
					o[f] = item[f]
				});
				// if(o.pid) o.parent_rowId = o.pid;
				arr.push(o)
			});

			return CRM.util.parseDataToTree(arr, true);
		},


		// 按公式计算bom 数量
		ajaxCalculateBomAmount: function (param) {
			let url = `/EM1HNCRM/API/v1/object/bom/service/formula_calculate`;
			return this.ajax_base(url, _.extend({
				formulas: [],
				selectBomList: [],
			}, param), null, false, true)
		},

		// 按公式计算bom 数量
		ajaxCheckFormula: function (param) {
			let url = `/EM1HNCRM/API/v1/object/bom/service/formula_check`;
			return this.ajax_base(url, _.extend({
				formulas: [],
				selectBomList: [],
			}, param), null, false, true)
		},

		// apl 计算
		ajaxCalculateByAPL: function (param) {
			let url = `/EM1HNCRM/API/v1/object/bom/service/calculate_by_apl`;
			return this.ajax_base(url, _.extend({
				"aplApiName": "", //TreeV1接口返回的赋值APL数组
				"triggerRowId": "",
				selectBomList:[]
			}, param), null, false, true)
		},

		getProductName(currentRow) {
			let {display_name, product_id__r, name} = currentRow;
			const pName = CRM._cache.openPriceList ? display_name || product_id__r || name || '' :  display_name || name || product_id__r || '';
			return pName;
		},

		// 必选或默认选中项是否计入默认产品包总金额；
		isIncludeDefSelect: function () {
			return CRM._cache.bom_price_calculation_configuration == '0';
		},

		/**
		 * @desc bom 请求 bom 子件，并计算整包金额；
		 * @param bomCoreId 必传
		 * @param productId 必传
		 * @param rootData 必传 产品包的产品数据或者价目表明细数据
		 * @param masterData 必传
		 * @param price_book_id 非必传
		 * @returns {Promise<*>}
		 */
		async fetchBomAndCalculatePrice({bomCoreId = '',  productId = '', rootData = {}, masterData = {}, price_book_id = '', isSupportPeriod = false } = {}) {
			let _this = this;
			let util = CRM.util;
			// 子件不在母件价目表内，需要阻断
			const noFlowRootPricebookProds = {};

			const getPrice = (data) => {
				if (CRM._cache.openPriceList && data.hasOwnProperty('pricebook_sellingprice')) {
					return data.pricebook_sellingprice
				}
				return data.price;
			};

			// 获取bom默认选中项成功的回调
			const _successCallback = async function (item, res) {
				// 开了报价器
				let bomData = util.flatBomData(res.dataMapList);
				let id = util.isBom(item).productId;
				let bomId = util.getBomId(res.dataMapList, id);
				if (!bomId) return util.alert(`【${_this.getProductName(item)}】：${$t('产品包为空')}`);
				let children = util.parseDataToBOM(bomData, bomId);
				if (children.length) {
					util.setBomChecked(children);
					children = util.getOnlyCheckedData(children);
					_.each(children, function (c) {
						c.pid = item.rowId;
						c.modified_adjust_price = c.adjust_price;

					});
					// //属性产品，补全默认选中属性值
					if(CRM._cache.openAttribute){
						CRM.util.forEachTreeData(children, data => {
							if (!data.isGroup) {
								util.setDefaultAttr([data])
							}
						});
						_this.parseDataAttr(children)
					}


					item.children = util.sortTreeData(children, 'order_field');

					// 判断子件不在母件价目表
					util.forEachTreeData(item.children, (c) => {
						const pdId = rootData.pricebook_id;
						const __isNotFlowRootPricebook = (
							pdId &&
							CRM.util.isGrayScale('CRM_BOM_FLOW_PRICEBOOK_EXTEND') &&
							c.price_mode === '2' &&
							c.price_book_id !== pdId
						);
						if (__isNotFlowRootPricebook) {
							if (noFlowRootPricebookProds[bomId]) {
								noFlowRootPricebookProds[bomId].push(c);
							} else {
								noFlowRootPricebookProds[bomId] = [c];
							}
							noFlowRootPricebookProds[bomId].product_name = _this.getProductName(item);
						}
					})
				} else {
					item.children = [];
				}

				// 默认选中项总金额
				let defSelectValue = tssp = getPrice(item);
				if(!_this.isIncludeDefSelect()){
					let {totalPrice, totalSingleSetPrice,} =util.calculateAllChildrenPrice(children, isSupportPeriod);
					defSelectValue = util.accAdd(defSelectValue, totalPrice);
					tssp = util.accAdd(tssp, totalSingleSetPrice);
				}

				item.newestPrice = defSelectValue;
				item.totalSingleSetPrice = tssp;

				item.bom_id = bomId;
				// 开了报价器，如果改了属性需要重新计算对应价格
				// let r1 = _this.calculatePriceForQuoter(item.children, defSelectValue);
				// if(r1 && util.hasValue(r1.newestPrice)) item.newestPrice = r1.newestPrice;
				return {
					totalPrice: defSelectValue,
					children: item.children,
					rootData: item,
					productId: item.product_id || item._id,
				};
			};
			const _params = {
				price_book_id: price_book_id || '',
				account_id: masterData ? masterData.account_id : '',
				partner_id: masterData ? masterData.partner_id : '',
				mc_currency: masterData ? masterData.mc_currency : '',
				child_search_query_info: JSON.stringify({
					"limit": 2000,
					"offset" :0,
					"filters": [
						{ "field_name":"enabled_status", "field_values":true, "operator":"EQ" },
						{ "field_name":"selected_by_default", "field_values":true, "operator":"EQ" }
					]
				}),
				object_data: masterData,
			};

			// 批量获取bom默认选中数据
			let res = await util.fetchBomAndRelatedBomData([productId], {
				bom_core_id: bomCoreId,
				..._params
			});

			let r = await _successCallback(rootData, res);

			if (!_.isEmpty(noFlowRootPricebookProds)) {
				let tips = [];
				_.each(noFlowRootPricebookProds, (prods, bomId) => {
					const prodsName = prods.map((prod) => `【${_this.getProductName(prod)}】`).join('');
					tips.push(`${prods.product_name}:${prodsName}`);
				});
				util.alert(tips.join('<br/>') + $t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表'));
				return;
			}
			return r;
		},

		/**
		 * @desc 批量查询 bom 数据并计算，返回计算后总金额；
		 * @param productList 传价目表明细或者产品明细
		 * @param bomCoreIds 对应的 bomcoreid 列表
		 * @param masterData 主对象数据
		 * @param doRealPrice 产品包是否需要取价，如果传价目表明细过来就不用取价了
		 * @returns {Promise<void>}
		 */
		async batchFetchBomAndCalculatePrice({productList = [], bomCoreIds = [],  masterData = {}, doRealPrice = false, isSupportPeriod} = {}){
			let pd = productList;
			if(doRealPrice){
				let realPrice = await CRM.util.replacePriceForPriceBook({
					accountId: masterData ? masterData.account_id : '',
					partnerId: masterData ? masterData.partnerId : '',
					mcCurrency: masterData ? masterData.mcCurrency : '',
					fullProductList: productList.map((d) => ({productId: d.product_id || d._id})),
					object_data: masterData,
				});
				let priceBookProductList = realPrice.newRst;
				if(productList.length !== priceBookProductList.length){
					return;
				}
				pd = priceBookProductList;
			}

			let fetchList = pd.map((item, index) => {
				return this.fetchBomAndCalculatePrice({
					bomCoreId: bomCoreIds[index],
					productId: item.product_id || item._id,
					rootData: item,
					masterData,
					price_book_id: item.pricebook_id || '',
					isSupportPeriod,
				})
			});

			return await Promise.all(fetchList);
		},

		/**
		 * @desc 批量查询 bomcore 数据；
		 * @param productId 产品 id  String || Array
		 * @returns {*}
		 */
		batchQueryBomCoreId(productId = []) {
			const isArrPrdId = Array.isArray(productId);
			const productIds = isArrPrdId ? productId : [productId];
			if (!productIds.length || !productId) return [];
			return CRM.util.fetchObjRelatedList('BomCoreObj', {
				associated_object_describe_api_name: 'BomCoreObj',
				search_query_info: JSON.stringify({
					limit: 2000,
					offset: 0,
					filters: [
						{
							field_name: 'product_id',
							field_values: productIds,
							operator: 'IN'
						}
					],
					orders: [
						{
							fieldName: 'last_modified_time',
							isAsc: false
						}
					]
				}),
				extractExtendInfo: true,
				ignore_scene_record_type: false,
				include_describe: false,
				serializeEmpty: false
			}, true).then(res => {
					let list = res?.dataList || [];
					let r = [];
					productIds.forEach(pId => {
						let f = list.filter(d => d.product_id === pId);
						r = r.concat(f)
					});
					return r;
				}
			);
		},


		// 计算所有选中总金额
		calculateSelectMoney: function (data, field = 'adjust_price', onlyDefSelect, ) {
			let money = 0;
			let util = CRM.util;

			function _getAmount(d){
				return util.hasValue(d.__amount) ? d.__amount : d.amount;
			}

			function _getSinglesSetPrice(item,){
				// if(isSingle) return util.multiplicational(util.multiplicational(item[field], item.amount), item.pricing_period || 1);
				return util.multiplicational(item[field], _getAmount(item))
			}

			function isDefSelect(data = {}) {
				return data.is_required || data.selected_by_default;
			}

			_.each(data, function (item) {
				if (((!onlyDefSelect && item.isChecked) || (onlyDefSelect && (isDefSelect(item)))) || item.isGroup) {
					if (item.isGroup) {
						if (item.children && item.children.length) {
							_.each(item.children, function (c) {
								if (onlyDefSelect) {
									if (isDefSelect(c)) money = util.accAdd(money, _getSinglesSetPrice(c));
								} else {
									if (c.isChecked) money = util.accAdd(money, _getSinglesSetPrice(c));
								}
							})
						}
					} else {
						money = util.accAdd(money, _getSinglesSetPrice(item));
					}
				}
			});
			return money;
		},

		// 子件单独定价，计算总金额；循环所有子件求和
		calculateSelectMoneyForChildren: function (data, field = 'adjust_price', onlyDefSelect, ) {
			let money = 0;
			let util = CRM.util;

			function _getAmount(d){
				return util.hasValue(d.__amount) ? d.__amount : d.amount;
			}

			function _getSinglesSetPrice(item,){
				return util.multiplicational(item[field], _getAmount(item))
			}

			function isDefSelect(data = {}) {
				return data.is_required || data.selected_by_default;
			}

			util.forEachTreeData(data, item => {
				if(item.isGroup) return;
				if (onlyDefSelect) {
					if (isDefSelect(item)) money = util.accAdd(money, _getSinglesSetPrice(item));
				} else {
					if (item.isChecked) money = util.accAdd(money, _getSinglesSetPrice(item));
				}
			})
			return money;
		},

		initBomData(data){
			let util = CRM.util;
			util.forEachTreeData(data, item => {
				if(item.isGroup) return;
				item.pricing_period  = item.pricing_period || 1;
				item.__pricing_period = item.pricing_period;
				item.pricing_mode = item.product_id__ro?.pricing_mode;
				item.price_per_set = util.multiplicational(item.adjust_price, item.__pricing_period);
			})
		}

	};


	module.exports = project;
});
