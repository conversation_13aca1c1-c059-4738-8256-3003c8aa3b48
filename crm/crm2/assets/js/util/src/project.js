/**
 * @desc 所有和业务的相关的方法
 * base下的可能会用到的方法
 *      getEnterpriseConfig    根据key获取个人配置信息
 *      getFscLink             获取图片或附件的预览地址或下载地址
 *      getPublishRange        获取最近的数据
 */
define(function (require, exports, module) {
	var util = require('base-modules/utils');
	var formatTime = require('./date').formatTime;
	var fetchFieldPrivilege = require('./ajax').fetchFieldPrivilege;
	var json = require('./format').json;
	var Helper = require('crm-modules/components/helper/helper');
	var requestHighSeasNum = 0; // 请求全部公海的次数
	// var window.readEmailInstance;

	// 详情的路由特殊处理对象
	var RouteSpecialObj = {
		// 'object_cYHHi__c': 'abc1',
		'CheckinsObj': 'checkinsobj', // 高级外勤对象
		'AccountObj': 'customer', // 客户
		'LeadsObj': 'leadsobj', // 线索
		'CasesObj': 'casesobj', // 工单
		'DeliveryNoteObj': 'deliverynoteobj', // 发货单
		'RefundObj': 'gb', // 退款
		'InventoryObj': 'inventory', // 盘点
		'OpportunityObj': 'opportunityobj', // 商机
		'SalesOrderObj': 'orderform', // 销售订单
		'PromotionObj': 'promotionobj', // 促销
		'ReturnedGoodsInvoiceObj': 'returnorder', // 退货单
		'VisitingObj': 'visit', // 拜访
		'VisitActionObj': 'visitaction', // 库存动作
		'StockObj': 'stockobj', // 库存
		'SparePartsMaintenancePlanObj': 'sparepartsmaintenanceplanobj', // 备件维修计划
		'WarehouseObj': 'warehouseobj', // 仓库
		'InvoiceApplicationObj': 'invoice', // 开票申请
		'ServiceKnowledgeObj': 'serviceknowledgeobj', // 服务知识库
		'ServiceProjectObj': 'serviceprojectobj', // 服务项目
		'ServiceFaultCategoryObj': 'servicefaultcategoryobj', // 服务故障分类
		'PartnerObj': 'partnerobj', // 合作伙伴
		'object_WorldDeliveryNote__c': 'object_worlddeliverynote__c', // 沃得发货单
		'object_requisition__c': 'object_requisition__c', // 订购申请
		// 'ActiveRecordObj': 'activerecordobj',
		// 'TelesalesRecordObj':'telesalesrecordobj',//电销记录
		// 'ServiceLogObj':'servicelogobj',//服务记录
		// 'JournalObj':'journalobj',//日志
		// 'ScheduleObj': 'scheduleObj', //日程
		'QuoteObj': 'quoteobj',
		'IncentiveMetricObj': 'incentivemetricobj',
		'ExtendedAttributeObj': 'extendedattributeobj',
	};
	var RouteFeedObjs = FS.util.getUserAttribute('feedObjs');

	var project = {
		/**
		 * ==================================================
		 * @desc 根据apiName 判断是否为预设对象
		 *
		 * @param  {{String}} apiName
		 * @return {{Boolean}}
		 * <AUTHOR>
		 * ==================================================
		 */
		isCrmPreObject: function (apiname) {
			if (!apiname) return false;
			apiname = (apiname + '').toUpperCase();
			return !!_.find(CRM.config.objDes, function (obj) {
				return obj.apiName.toUpperCase() === apiname && !obj.isFuture;
			});
		},

		/**
		 * ==================================================
		 * @desc 根据apiName 或 objectType 获取对象的名称
		 *
		 * @param  {{String}} apiName
		 * @return {{String}}
		 * <AUTHOR>
		 * ==================================================
		 */
		getPreObjectName: function (apiname) {
			var item = _.find(CRM.config.objDes, function (obj) {
				return obj.apiName === apiname || obj.objectType == apiname;
			});
			return item ? item.name : '';
		},

		/**
		 * 根据apinames获取多个对象名
		 * @param {Array} apinames
		 * @returns Promise
		 */
		getMultipleObjectName(apinames) {
			return new Promise((resolve) => {
				let namesObj = {};
				let needAsync = [];
				let crmCacheObjectName = CRM.get('crm_multiple_objectName');
				_.each(apinames, (apiname) => {
					if (this.getPreObjectName(apiname)) {
						namesObj[apiname] = this.getPreObjectName(apiname);
					} else if (crmCacheObjectName && crmCacheObjectName[apiname]) {
						namesObj[apiname] = crmCacheObjectName[apiname];
					} else {
						needAsync.push(apiname);
					}
				});
				if (!needAsync.length) {
					CRM.set('crm_multiple_objectName', _.extend({}, crmCacheObjectName, namesObj));
					resolve(namesObj);
					return namesObj;
				}
				CRM.util.getDescribeListByApiNames({describe_apiname_list: needAsync}).then((res) => {
					_.each(res.objectDescribeList, (object) => {
						namesObj[object.api_name] = object.display_name;
					})
					CRM.set('crm_multiple_objectName', _.extend({}, crmCacheObjectName, namesObj));
					resolve(namesObj);
					return namesObj;
				})
			})
		},

		/**
		 *
		 * ===================================================
		 * 通过hash 获取apiName
		 * 业务代码 配合头部使用
		 * ===================================================
		 */
		getApiNameByHash: function (h) {
			var hash = h || window.location.hash;
			if (hash.includes('#search/')) {
				return 'search'
			}
			if (hash.includes('crm') && hash.includes('myobject')) {
				return hash.split('=/')[1]
			} else {
				var res = '';
				if (hash.includes('#') && !hash.includes('crm') && !hash.includes('/')) {
					res = hash.split('#');
				} else {
					res = hash.split('/');
				}
				return res[res.length - 1]
			}
		},

		// 列表页是否属于自定义菜单
		isCustommenuListPage: function() {
			if(location?.hash?.indexOf('custommenu') > -1) {
				return true;
			}
			return false;
		},

		/**
		 *
		 * ===================================================
		 * 通过hash获取CRM当前页面的name
		 * 业务代码 配合头部使用
		 * @return {{String}};
		 * ===================================================
		 */
		getCurrentPageName: function (h, callBack) {
			var res = null;
			var hash = h || window.location.hash;
			var unSupportObj = { // 不规范的对象 ,hash值和all_menu中的url和referenceApiname都不匹配
				returnorder: {
					name: $t("crm.退货单"),
					apiName: 'ReturnedGoodsInvoiceObj'
				},
				orderform: {
					name: $t("crm.销售订单"),
					apiName: 'SalesOrderObj'
				}
			};
			var apiName = this.getApiNameByHash(hash);
			var CRM_All_menu = window.CRM_All_menu;

			function getCur() {
				var data = CRM_All_menu.menus;
				var obj = {};
				var len = data.length;
				for (var i = 0; i < len; i++) {
					var items = data[i];
					obj = _.find(items.items, function (item) {
						return item.referenceApiname === apiName || item.url === url || (item.referenceApiname && item.referenceApiname.toLowerCase() === apiName)
					});
					if (obj) {
						res = {
							name: obj.displayName,
							apiName: obj.referenceApiname
						};
						callBack && callBack(res);

					}
				}
			}

			if (hash.includes('crm')) {

				if (unSupportObj.hasOwnProperty(apiName)) {
					res = unSupportObj[apiName];
					callBack && callBack(res);
					return res
				}

				var url = 'crm/' + apiName;

				if (apiName === 'index') {
					res = {
						name: $t("首页"),
						apiName: 'crm'
					};
					callBack && callBack(res);
					return res
				} else {
					if (!CRM_All_menu) {
						window.CRM.util.getCrmAllMenu(function () {
							getCur();
						});
						// var int = setInterval(function () {
						// 	if (CRM_All_menu) {
						// 		clearInterval(int);
						// 		getCur();
						// 	}
						// }, 500);
					} else {
						getCur()
					}
				}
			}
			return res
		},

		/**
		 * @desc 获取crm各业务逻辑的路径 action detail
		 * @param {{String}} 对象的apiname
		 * @param {{String}} action(操作) detail(详情)
		 */
		getCrmFilePath: function (apiname, type) {
			if (!apiname || !type) return;

			apiname = '' + apiname;
			var folder = 'myobject';
			var objDes = CRM.config.objDes && CRM.config.objDes[apiname.toLowerCase()];

			if (objDes) {
				var objectType = objDes.objectType;

				if (type === 'action') {
					if (this.isUsePluginForAllComps(apiname)) {
						folder = 'myobject';
					}
					else {
						folder = CRM.config.ObjFolder[objectType] || 'myobject';
					}
				} else {
					folder = RouteSpecialObj[apiname] || 'myobject';
				}
			}
			if (RouteFeedObjs && RouteFeedObjs.indexOf(apiname) >= 0) {
				folder = 'feedobject'
			}

			if (type === 'detail' && this.getDetailVersion(apiname) !== 'v1') {
				// debugger
				return 'vcrm/detail';
			}
			// if(apiname === 'object_nZd2d__c'){
			// 	return ['crm-modules', type, 'myobjectcpq', 'myobjectcpq'].join('/');
			// }

			return ['crm-modules', type, folder, folder].join('/');
		},

		/*
		 *   判断当前页是否属于crm
		 * */
		isCRMPage: function () {
			return window.location.hash.includes('#crm/');
		},

		/*
		 *@desc代理获取自定义详情
		 *   当获取自定义详情时不包含附件 用此方法代理详情之后可拿到相应的附件数据
		 *@param {{Object}}
		 *  dataId 对象id example线索id require
		 *  source 见fetchFieldAttach的说明 require
		 *  url 获取详情的路径 require
		 *  param 获取详情需要的参数 require
		 *  success 获取详情后的回调 require
		 *  complete 可选
		 *  error 可选
		 **/
		proxyGetFieldDetail: function (obj, context) {
			this.fetchFieldAttach(_.pick(obj, ['dataId', 'source']), function (attachs) {
				util.FHHApi({
					url: obj.url,
					data: obj.param,
					success: function (res) {
						if (res.Result.StatusCode === 0 && res.Value && attachs[0]) {
							var fieldData = res.Value.UserDefineFieldDatas;
							var arr = _.each(res.Value.UserDefinedFields, function (item) {
								var val, temp, temp1;
								if (item.FieldType == 17) {
									val = _.filter(attachs, function (attach) {
										attach.FieldProperty = item.FieldProperty;
										return attach.FieldName === item.FieldName;
									});
									temp = _.pick(item, 'FieldName', 'FieldProperty');
									temp.FieldType = 17;
									temp.FieldValue = {};
									temp.FieldValue.Value = '';
									temp.FieldValue.Values = [];
									temp.FieldValue.UploadAttachs = val || [];
									temp1 = _.findWhere(fieldData, {
										FieldName: item.FieldName
									});
									temp1 ? _.extend(temp1, temp) : fieldData.push(temp);
								}
							});
							res.Value.UserDefineFieldDatas = [].concat(fieldData, arr || []);
						}
						_.isFunction(obj.success) && obj.success(res);
					},
					complete: obj.complete || function () {
					},
					error: obj.error || function () {
					}
				})
			}, {
				errorAlertModel: 1
			})
		},

		//crm自定义字段类型转为表格字段类型
		typeConversion: function (fieldType) {
			return {
				2: 1,
				3: 1,
				4: 2,
				5: 3,
				6: 3,
				7: 10,
				8: 6,
				9: 7,
				10: 9,
				14: 11,
				15: 4,
				22: 22
			}[fieldType] || 1
		},

		/**
		 * @desc 格式化关联对象数据
		 * @param data 数据
		 * @param fieldNames // 不做显示隐藏处理的字段
		 */
		parseRelationData: function (data, fieldNames) {
			var result = [],
				fieldList = {};
			_.each(json.parse(data.FieldLayoutJson), function (a) {
				fieldList[a.FieldName] = a;
			})
			fieldNames = fieldNames || [];
			_.each(data.Response, function (rItem) {
				var fData = {
					_od_: {}
				}, // 保存一个最原始的数据
					fieldData = json.parse(rItem.FieldInJson);
				_.each(fieldData || [], function (fItem) {
					var field = fieldList[fItem.FieldName];
					var ft = field && field.FieldType;

					field = _.extend({}, field, fItem);
					fData._od_[field.FieldName] = field.FieldDisplayValue || field.FieldValue;
					if (_.isUndefined(field.IsVisible) || field.IsVisible || fieldNames == 'all' || _.contains(fieldNames, field.FieldName)) { // 要显示项 和 自定义加入的项
						if (ft == 'DateTime' && !_.isNaN(+field.FieldDisplayValue)) {
							field.FieldDisplayValue = formatTime(field.FieldDisplayValue, 'time');
						} else if (ft == 'Date' && !_.isNaN(+field.FieldDisplayValue)) {
							field.FieldDisplayValue = formatTime(field.FieldDisplayValue, 'date');
						}

						fData[field.FieldName] = field.FieldDisplayValue;
						if (field.FieldValue) {
							fData[field.FieldName + '_ID'] = field.FieldValue;
						}
						if (field.FieldCaption && field.FieldDisplayValue) {
							fData[field.FieldName + '_C'] = field.FieldCaption + '：' + field.FieldDisplayValue;
						}
						fData[field.FieldName + '_noVisible'] = !field.IsVisible;
					} else {
						fData[field.FieldName] = CRM.config.HIDE_Field_TEXT;
						fData[field.FieldName + '_noVisible'] = true;
					}
					var apiName = field.ApiName;
					if (apiName) {
						if (apiName.slice(apiName.length - 3) == '_id') {
							fData[field.ApiName] = field.FieldValue;
						} else {
							fData[field.ApiName] = fData[field.FieldName];
						}
					}

				});
				fData._od_.DataID = rItem.DataID;
				result.push(_.extend({
					DataID: rItem.DataID,
					PageTime: rItem.PageTime
				}, fData));
			});
			return result;
		},

		/**
		 *@desc 处理字段级权限
		 *@param {Array} 自定义字段结构 [fieldList1,fieldList2] || fieldList1
		 *@param {Function} 回调接受两个参数。
		 * fn(fieldList,privileges) privileges为null时表示接口请求出错。
		 */
		handleFieldPrivilege: function (fieldList, cb, context) {
			var me = this;
			var tempObj = {};
			var types = [];
			if (!fieldList || !fieldList[0]) {
				cb && cb.apply(context, [fieldList, {}]);
				return;
			}

			fieldList[0].OwnerType && (fieldList = [fieldList]);
			_.each(fieldList, function (a) {
				var type = a[0].OwnerType;
				tempObj[type] = a;
				types.push(type);
			});
			fetchFieldPrivilege.call(this, types, function (privileges) {
				_.each(privileges, function (privilege, type) {
					var temp = {};
					var flist = tempObj[type];
					if (!flist) return;

					_.each(flist, function (a) {
						temp[a.FieldName] = a;
					});
					_.each(privilege, function (b) {
						var obj = temp[b.fieldName];
						var right = b.fieldRight;
						if (!obj) return;
						right === 0 ? (obj.noVisible = true) : (right === 1 && (obj.isEditable = false));
						obj.Right = right;
					})
				});
				cb && cb.apply(context, [fieldList, privileges]);
			}, false, this);
		},

		preProcessor: _.extend({
			ready: function (event, cb) {
				if (cb === undefined) {
					cb = event;
					event = 'ready';
				}
				if (cb) {
					this.onceAlways(event, function () {
						cb && cb();
					});
				}
			}
		}, FS.Events),

		// 事件完成时，触发事件
		preEventReady: function (event) {
			event = event || 'ready';
			this.preProcessor.snapshot(event, true);
			this.preProcessor.trigger(event);
		},

		/**
		 * @desc 设置wheres[0].filters，field_name存在的会被覆盖，不存在的会被push
		 * @param {*} oldWheres wheres[0].filters
		 * @param {*} filters filters
		 */
		setWheres: function (oldWheres, filters) {
			var result = [];
			var usedIndexs = [];
			_.each(oldWheres, function (where) {
				_.each(filters, function (filter, index) {
					if (where.field_name === filter.field_name) {
						usedIndexs.push(index);
						where = filter;
					}
				});
				result.push(where);
			});
			_.each(filters, function (filter, index) {
				if (usedIndexs.indexOf(index) === -1) result.push(filter);
			});
			return result;
		},

		/**
		 * 根据移动端布局获取格式化后数据
		 * @param    {[type]}   data 列表数据
		 * @param    {[type]}   res  列表接口返回的Value
		 * @param    {Function} cb   回调
		 */
		getFormatDataByListLayout: function (data, res, cb) {
			this.getFormatData(data, res.objectDescribe.fields, function (formatData) {
				if (res.listLayouts && res.listLayouts.length) {
					var fields = res.listLayouts[0].components[0].include_fields;

					_.map(formatData, function (item) {
						var list = [];

						_.map(fields, function (field) {
							var name = field.api_name || field.field_name;

							list.push({
								name: field.label || '',
								value: item[name] || '',
								apiname: name
							});
						});

						item.list = list;
					});
				}

				cb && cb(formatData);
			});
		},

		/**
		 * 根据字段类型获取格式化后数据
		 *
		 * @param    {Array}   objData 列表数据
		 * @param    {Array}   fields  字段描述
		 * @param    {Function}  cb      回调
		 */
		getFormatData: function (objData, fields, cb) {
			var me = this;

			require.async('crm-widget/table/table', function (table) {
				if (CRM.get('country_area_options')) {
					cb && cb(me._formatProductData(table.helper, objData, fields));
				} else {
					me.getCountryAreaOptions().then(() => {
						cb && cb(me._formatProductData(table.helper, objData, fields));
					})
				}
			});
		},

		/*
		 *   通过apiName获取对象信息
		 * */
		getObjByApiName: function (apiName) {
			var data = window.CRM_All_menu && window.CRM_All_menu.menus;
			if (!data) return;
			var obj = {};
			var len = data.length;
			for (var i = 0; i < len; i++) {
				var items = data[i];
				obj = _.find(items.items, function (item) {
					return item.referenceApiname === apiName || (item.referenceApiname && item.referenceApiname.toLowerCase() === apiName)
				});
				return obj
			}
		},

		/**
		 * @desc 获取读邮件的对象readEmail
		 * @param {{Function}} 回调函数
		 */
		getReadEmailObj: function (callback) {
			var me = this;
			require.async('app-email/app', function () {
				require.async('app-email-modules/common/readDialog/readDialog', function (readDialog) {
					/* if (!window.readEmailInstance || (window.readEmailInstance && !window.readEmailInstance.cid)) {
						window.readEmailInstance = new readDialog({
							zIndex: 598,
							width: 930,
							from: 'crm',
						});
									} */
					if (window.readEmailInstance) {
						window.readEmailInstance.destroy();
						window.readEmailInstance = null;
					}
					window.readEmailInstance = new readDialog({
						zIndex: 598,
						width: 930,
						from: 'crm',
					});

					// 保证写信页加载完毕
					setTimeout(() => {
						callback && callback(window.readEmailInstance);
					}, 200)
				});
			})
		},
		/**
		 * @desc 销毁读邮件的对象readEmail
		 * @param {{Function}} 回调函数
		 */
		destroyReadEmailObj: function () {
			if (!window.readEmailInstance) return
			window.readEmailInstance.destroy && window.readEmailInstance.destroy();
			delete window.readEmailInstance;
		},

		_formatProductData: function (helper, objData, objDescribe) {
			var data = _.map(objData, function (item) {
				return _.extend({
					_rawData: item,
				}, item);
			});
			var columns = _.extend({}, objDescribe);
			let areaData = CRM.get('country_area_options');
			_.each(columns, function (item) {
				/**
				 * columns数据处理，参考table的处理：
				 * detail/detail/components/md/md.js
				 * assets/widget/table/table.js
				 */

				item = _.extend({}, item, {
					data: item.api_name,
					dataType: item.type,
					returnType: item.return_type,
					options: _.map(item.options || [], function (a) {
						return {
							ItemCode: item.api_name == "record_type" ? a.api_name : a.value,
							ItemName: a.label,
							Children: _.map(a.child_options, function (b) {
								return {
									ItemCode: b.value,
									ItemName: b.label
								}
							}),
							font_color: a.font_color
						}
					}),
					referRule: item.type === 'employee' ? 'Employee' : '',
					isId: true
				});

				// 国家省市区的数据处理
				if (_.contains(['country', 'province', 'city', 'district'], item.type)) {
					item.options = _.map(areaData[item.type].options, (o) => {
						return {
							ItemCode: o.value,
							ItemName: o.label,
							Children: o.child_options
						}
					})
				}

				item.dataType = helper.typeMap[item.dataType] || item.dataType;
				item.returnType = helper.typeMap[item.return_type] || item.returnType;
				item = _.extend({
					defaultValue: '',
					isMyObject: true,
					range: item.dataType == 2 ? [18, 0] : [10, 6],
					isEdit: false,
					_options: item.options || []
				}, item);
				item.isEdit = [22, 35, 40].indexOf(item.dataType) != -1 ? false : item.isEdit;
				item.isEdit = !item.dataType || !item.data ? false : item.isEdit;
				if (item.dataType == 5) {
					item.options = [{
						ItemName: $t("是"),
						ItemCode: true
					}, {
						ItemName: $t("否"),
						ItemCode: false
					}];
				}
				/**
				 * objectData value映射，参考table的getCheckedFormatData方法
				 */
				if (item.data && [9, 17, 37].indexOf(item.dataType) == -1) {
					_.each(data, function (dItem) {
						var val = dItem[item.data];
						val = helper.getFormatVal(val, item, '', dItem);
						dItem[item.data] = val;
					});
				}
			});
			return data;
		},

		/**
		 * @desc 判断产品数据是否是正常的；状态正常，上架，可独立销售
		 * @param item
		 */
		isNormal: function (item) {
			return item.product_life_status__v === 'normal' && item.product_status__v === '1' && item.enabled_status;
		},

		/**
		 * @desc 给数据添加rowId
		 * @param data
		 * @param update 更新rowId
		 */
		addRowId: function (data, update) {
			var _this = this;
			var isArray = _.isArray(data);
			var newData = isArray ? data : [data];

			function _fn(list) {
				_.each(list, function (item, index) {
					if (!item.hasOwnProperty('rowId') || update) {
						item.rowId = _this.uniqueCode();
					}
					if (item.hasOwnProperty('children')) {
						_fn(item.children)
					}
				})
			}

			_fn(newData)
		},

		/**
		 * @desc 唯一code
		 * @returns {*}
		 */
		uniqueCode: function () {
			var code = new Date().getTime() + _.uniqueId();
			return String(code)
		},

		extendFn: function (fn) {

			if (!_.isUndefined(fn.extend)) {
				return fn;
			}

			fn.extend = function (proto, statics) {
				proto = proto || {};
				statics = statics || {};

				function f() {
					fn.apply(this, arguments);
				}

				_.map(statics, function (value, key) {
					f[key] = value;
				});

				f.prototype = new fn();

				_.extend(f.prototype, proto);

				return f;
			};

			return fn
		},

		/**
		 * @desc 显示详情
		 */
		showDetail: function (id, apiname, opts, refresh) {
			var me = this;

			require.async('crm-components/showdetail/showdetail', function (Detail) {
				me._detail && (me._detail.destroy(), me._detail = null);
				me._detail = new Detail(_.extend({
					apiName: apiname || '',
					top: 0,
					// zIndex: me.options.zIndex
					// showMask: true,
				}, opts));

				me._detail.on('refresh', function () {
					refresh && refresh();
				});

				me._detail.show(id);

			});
			return me._detail;
		},

		/**
		 * @desc 接口上传文件
		 */
		upLoadByInterface: function (opts) {

			let appId = (CRM.util.getConnectAppInfo() || {}).appId;

			function getConfigByType(type) {
				var config = {
					stream: {
						url: appId ? `/FSC/EM/AFile/UploadByStream?appid=${appId}` : '/FSC/EM/AFile/UploadByStream',
						contentType: 'application/octet-stream',
						dataFormat: function (data) {
							return new Blob([data], {
								type: 'text/plain'
							});
						}
					}
				};

				return config[type];
			}

			function formatData(fn, data) {
				return _.isFunction(fn) ? fn(data) : data;
			}

			opts = $.extend(true, {
				type: 'stream'
			}, opts);

			var me = this;
			var config = getConfigByType(opts.type);
			var data = formatData(config.dataFormat, opts.data);

			me._uploadAjax && me._uploadAjax.abort();

			me._uploadAjax = $.ajax(config.url, {
				data: data,
				type: 'post',
				dataType: 'json',
				processData: false,
				headers: {
					'Content-type': config.contentType
				}
			}).done(function (data) {
				opts.success && opts.success(data);
			}).fail(function (data) {
				opts.error && opts.error(data);
			}).complete(function () {
				me._uploadAjax = null;
			});
		},

		/**
		 * @desc 接口下载文件
		 */
		downLoadByInterface: function (opts) {

			var me = this;
			var url = CRM.util.getFscLink(opts.path, '', true);

			me._downloadAjax && me._downloadAjax.abort();

			me._downloadAjax = $.ajax(url, {
				type: 'post',
				dataType: 'text',
				processData: false,
				headers: {
					'Content-type': 'application/octet-stream'
				}
			}).done(function (data) {
				opts.success && opts.success(data);
			}).fail(function (data) {
				opts.error && opts.error(data);
			}).complete(function () {
				me._downloadAjax = null;
			});
		},

		/**
		 * @desc 详情页版本
		 * @param apiname
		 * @returns {string | *}
		 */
		getDetailVersion: function (apiname) {
			// v1版本详情页对象
			let v1 = [
				'OpportunityObj', // 商机
				'BehaviorIntegralDetailObj', //行为积分
			];

			if (_.contains(v1, apiname)) {
				return 'v1';
			}

			// 全网v3版详情对象
			return 'v3';
		},

		/**
		 * @desc 新版对象列表页灰度
		 * @param apiname
		 * @return boolean
		 */
		isGrayListLayout: function (apiname) {
			// @观斌，变量模版里面太大，建议先放到代码里面
			let blackObjectList = ["OpportunityObj", "ProcurementRuleObj", "BiddingSubscriptionRulesObj", "HistoricalBiddingImportObj", "MonitorCenter", "ConsultQuestionRecordObj", "CrmRival", "GoalValueObj", "LeadsPoolObj","PivotTableRuleObj", "OperationsStrategyObj", "PivotTableInstanceObj", "VisitingObj", "InventoryObj", "ProductCategoryObj", "ShopCategoryObj", "PromotionObj", "AccountCheckRuleObj", "ShopMall", "IsolatedMall", "MyProducts", "EnterpriseRelationObj", "PublicEmployeeObj", "ErDepartmentObj", "TPMActivityBudgetObj", "ForecastRuleObj", "QualityDocumentQuery", "MarketInsights", "ProcurementSearchObj", "BizQuerySearchObj", "FAccountAuthorizationObj", "AccountCheckRuleObj", "DeviceBindObj", "EmployeeListObj", "ForecastRuleObj", "SaleActionNewObj", "ServiceMarketObj", "ServiceMarketLogObj", "StageInstanceObj", "ApprovalInstanceObj", "BpmInstance"];
			if (CRM.util.isGrayScale('CRM_USE_NEWLAYOUT_LEADSPOOLOBJ')) {
				 blackObjectList = blackObjectList.filter(item => item !== "LeadsPoolObj");
			}
			return !blackObjectList.includes(apiname);


			// var globals = FS.util.getUserAttribute('grayWebListLayout') || [];
			// var sfaAllows = FS.util.getUserAttribute('graySFAWebListLayout') || [];
			// const appId = CRM.util.getPageCurAppId();
			// const dhtAppIds = ['FSAID_11490c84', 'FSAID_114910bc', 'FSAID_11491173'];
			// const customMenu = ['MyProducts'];
			// // 如果为空，默认没有灰度
			// if (globals.length === 0) return false;
			// // 预制变更单对象伪装成自定义对象
			// if (/.*ChangeObj$/.test(apiname)) {
			// 	apiname = apiname + '__c';
			// }

			// // 判断自定义对象
			// var key = '__c';
			// var isCustomObject = apiname.lastIndexOf(key) === (apiname.length - key.length);
			// return (
			// 	!FS.util.getUserAttribute("noListLayout") &&
			// 	(
			// 		isCustomObject ||
			// 		_.contains(globals, apiname) ||
			// 		_.contains(sfaAllows, apiname) ||
			// 		(_.contains(dhtAppIds, appId) && !customMenu.includes(apiname))
			// 	)
			// );
		},

		/**
		 * @desc 是否走新详情页（720作废）
		 * @param apiname
		 * @param from
		 * @returns {boolean | *}
		 */
		// isNewDetail: function (apiname, from) {
		// 	// 强制走老的详情页,方便本地调试功能
		// 	if (window.location.search.indexOf('crm_detail=old') > -1) {
		// 		return false
		// 	}
		//     // 全网新详情的对象
		//     var global = [];
		//     // 灰度新详情的对象 '__myObject'表示自定义对象
		//     var gray = [].concat(this.newDetailConfig);
		//     // 维护成本极高，不到万不得已不可使用！！！
		//     // 过滤该企业不想灰度的对象 ，仅限灰度，全网势不可挡 {ea: [apiname1, apiname2, ...]}
		//     var eaConfig = {};

		//     var _apiname = apiname;

		//     function check() {
		//         if (CRM.util.isGrayScale('CRM_NEW_DETAIL')) {
		//             var blackList = eaConfig[CRM.ea];

		//             if (blackList && _.contains(blackList, _apiname)) {
		//                 return false;
		//             }

		//             return true;
		//         }

		//         return false;
		//     }

		//     // 布局编辑器新建对象时没有apiname
		//     if (!_apiname && from === 'layoutDesigner') {
		//         return check();
		//     }

		//     // 判断自定义对象
		//     var key = '__c';
		//     var isMyObject = apiname.lastIndexOf(key) === (apiname.length - key.length);

		//     _apiname = isMyObject ? '__myObject' : _apiname;

		//     // 该对象详情页已全网
		//     if (_.contains(global, _apiname)) {
		//         return true;
		//     }

		//     // 该对象详情页在灰度，需要通过ea判断是否是灰度企业
		//     if (_.contains(gray, _apiname)) {
		//         return check();
		//     }

		//     return false;
		// },

		/**
		 * @desc 预设对象是否使用新布局设计器（720作废）
		 * @returns {boolean | *}
		 */
		// isUseNewDesigner: function (apiname) {
		//     // 使用新布局设计器的对象apiname
		//     return _.contains(this.newDetailConfig, apiname);
		// },

		compareMap: {
			1: 'EQ', //等于
			2: 'N', //不等于
			3: 'GT', //大于
			4: 'GTE', //大于等于
			5: 'LT', //小于
			6: 'LTE', //小于等于
			7: 'LIKE', //包含
			8: 'NLIKE', //不包含
			9: 'IS', //为空
			10: 'ISN', //不为空
			11: 'STARTWITH', //起始于
			12: 'ENDWITH',
			13: 'HASANYOF', // 属于
			14: 'NHASANYOF', // 不属于
			17: 'BETWEEN', // 时间段
			18: 'LTE', // 前N天
			19: 'GTE', // 后N天
			20: 'LTE', // 前N月
			21: 'GTE', // 后N月
			22: 'IN', // 文本类型的属于
			23: 'NIN' // 文本类型的不属于
		},

		/**
		 * 列处理
		 * @param {Array} headers 需要展示的字段
		 * @param {Object} fields 字段描述
		 * @param {object} config 相关配置
		 * {
		 * recordType  //isEdit为true时，必须有值
		 * isEdit
		 * isFilter
		 * objectDescribeExt  //存在引用字段时下发的数据
		 * }
		 */
		parseColumns: function (headers, fields, config = {}) {
			var columns = this.parseColumnsByHeadersAndFields(headers, fields, config);
			var showColumns = [],
				hideColumns = [],
				fixedLen = 0;

			_.each(columns, function (column) {
				fixedLen += column.fixed ? 1 : 0;
				if (!column.isHidden) {
					showColumns.push(column);
				} else {
					hideColumns.push(column);
				}
			});

			// 全部为固定列，自适应展示
			if (showColumns.length == fixedLen) {
				showColumns[fixedLen - 1] && (showColumns[fixedLen - 1].fixed = false);
			}

			columns = showColumns.concat(hideColumns);

			return columns;
		},

		parseColumnsByHeadersAndFields: function (headers, fields, config = {}) {
			var me = this;
			var columns = [];
			var isFilter = config.isFilter;
			var isEdit = config.isEdit;
			var roptions = fields.record_type ? _parseRecordTypeOptions(fields.record_type.options) : [];
			_.each(headers, function (item) {
				var fieldname = item.field_name;
				var field = fields[fieldname];
				if (!field) return;

				var rt = item.render_type || field.type;
				var tr = {};

				tr.__originType = rt;
				if (fieldname === 'owner_department') { //负责人所在部门重置为部门
					tr.__originType = rt = 'department';
					tr.render = function (data, type, full) {
						return _.escape(full.owner_department) || CRM.config.TEXT_DEFAULT
					}
				} else if (rt === 'relevant_team' || rt === 'embedded_object_list') { //相关团队重置为人员
					rt = 'employee';
					tr.render = function (data, type, full) {
						return _.escape(full.relevant_team__r) || CRM.config.TEXT_DEFAULT
					}
					tr.filterCompare = [9, 10];
				} else if (rt === 'record_type') {
					rt = 'select_one';
					field.is_index = false; //因为有单独的业务类型筛选入口，放开会有点问题
				}

				var attrs;
				if (isEdit) {
					attrs = {
						isEdit: !item.is_readonly,
						isRequired: !!item.is_required,
						placeHolder: field.default_value,
						showLookupText: true // lookup 展示文本
					}
				} else {
					attrs = {
						isHidden: item.isHidden,
						isOrderBy: isFilter && field.is_index && fieldname !== 'related_object_data', //是否支持排序
						orderValues: [1, 2], // 1降序 0升序
						isFilter: isFilter && field.is_index //是否支持过滤
					}
					item.width && (attrs.width = item.width);
				}

				if (rt == 'quote') {
					var objectDescribeExt = config.objectDescribeExt || {
						fields: {}
					};
					objectDescribeExt.fields = objectDescribeExt.fields || {};
					var fieldExt = objectDescribeExt.fields[fieldname] || {};
					field.options = fieldExt.options || field.options || null;
					field.length = fieldExt.length || 0;
					field.decimal_places = fieldExt.decimal_places || 0;
					if (field.quote_field_type === 'image') { //引用字段引用图片，重置为图片类型
						attrs.isEdit = false;
						rt = 'image';
					}
				}

				_.extend(tr, field, item, attrs, {
					id: fieldname,
					data: fieldname,
					dataType: rt,
					title: field.label,
					options: field.options && _parseFieldOptions(field, roptions, config),
					returnType: field.quote_field_type || field.return_type,
					range: rt === 'percentile' ? [10, !field.auto_adapt_places ? 6 : field.decimal_places] : [field.length || 0, field.decimal_places || 0],
					referRule: _.contains(['employee', 'department'], (field.quote_field_type || rt)) && rt !== 'department' && 'Employee',
					isId: true,
					fixed: !isEdit && fieldname == 'name',
					type: field.quote_field_type || field.type,
					isDisable: field.is_active === false // 是否禁用
				});

				if (tr.data == 'record_type') {
					tr.isEdit = tr.isFilter = false;
				}

				tr._options = tr.options;

				columns.push(tr);
			})

			function _parseRecordTypeOptions(options) {
				_.each(options, function (a) {
					a.value = a.api_name;
				})

				return options;
			}

			function _parseFieldOptions(field, roptions, config) {
				var options = [];
				var isEdit = config.isEdit;
				var recordType = config.recordType || '';
				_.each(field.options, function (a) {
					(a.not_usable && isEdit) || options.push(_.extend({}, a, {
						ItemCode: a.value,
						ItemName: a.label,
						Children: field.type === 'multi_level_select_one' ? _.map(a.child_options, function (b) {
							return {
								ItemCode: b.value,
								ItemName: b.label
							}
						}) : a.child_options,
						isCustom: a.value === 'other',
						customRequired: a.is_required
					}))
				})

				if (!isEdit) return options;

				if (field.cascade_parent_api_name === 'record_type') {
					var rp = _.findWhere(roptions, {
						api_name: recordType
					})
					var map = {};
					rp && rp.child_options && _.each(rp.child_options[0], function (arr, k) {
						var temp = {};
						_.each(arr, function (v) {
							temp[v] = 1
						})
						map[k] = temp;
					})
					var mr = map[field.api_name];
					options = !mr ? [] : _.filter(options, function (b) {
						return mr[b.ItemCode];
					})
				}

				return options;
			}

			return columns;
		},

		parseFields: function (fields, helper, config = {}) {
			var _headers = _.values(fields);
			_headers = _.map(_headers, function (field) {
				field.field_name = field.api_name;
				return field;
			});
			var columns = [];
			var _columns = this.parseColumnsByHeadersAndFields(_headers, fields, config);

			_.each(_columns, function (item) {
				item._options = item.options || [];
				item = _.extend({
					data: null,
					placeHolder: '',
					range: item.dataType == 2 ? [18, 0] : [10, 6], // 数字类型的位数
					isEdit: false,
					isRequired: false,
					dataType: 1,
				}, item);
				item.dataType = helper.typeMap[item.dataType] || item.dataType;
				item.returnType = helper.typeMap[item.returnType] || item.returnType;
				item.range = item.dataType == 33 ? [10, 6] : item.range;
				// 业务类型 计算字段 引用不允许编辑
				item.isEdit = [22, 35, 36, 40].indexOf(item.dataType) != -1 ? false : item.isEdit;
				item.isEdit = !item.dataType || !item.data ? false : item.isEdit;
				if (item.dataType == 5 || item.returnType == 5) { // 补充全boolean类型
					item.options = item.options && item.options.length ? item.options : [{
						ItemName: $t('是'),
						ItemCode: true
					}, {
						ItemName: $t('否'),
						ItemCode: false
					}];
				}
				if (item.dataType != 35 && item.dataType != 36 && item.dataType != 40) { // 计算和统计
					item.returnType = '';
				}
				if (item.default_is_expression) {
					item.expression = item.placeHolder;
					item.placeHolder = '';
				}
				item.options = _.filter(item.options || [], function (o) { // 过滤掉点选多选中删除的值
					return !o.IsDeleted;
				});
				columns.push(item);
			})

			return columns;
		},

		// 增加rowId和pid
		// todo
		parseDataKey: function (data, key = '_id', pkey = 'parent_account_id') {
			_.each(data, function (item) {
				item.rowId = item[key]; //|| item['id'];
				item.pid = item[pkey];
			});
			return data;
		},

		// 将数据处理成树形型结构
		// 没找到父级的作为根节点
		parseDataToTree2: function (data, uniq) {
			var _data = [];
			var obj = {};
			_.each(data, function (item) {
				obj[item.rowId] = item;
				if (uniq) {
					obj[item.rowId].children && (obj[item.rowId].children = []);
				}
			});
			_.each(obj, function (item) {
				if (!item.pid || !obj[item.pid]) {
					item._isRoot = true;
					_data.push(item);
					return;
				}
				!obj[item.pid].children && (obj[item.pid].children = []);
				obj[item.pid].children.push(item);
			});
			return _data;
		},

		/**
		 * @desc 拍平BOM分组和子节点数据
		 * @param data
		 * @return {Array}
		 */
		flatBomData: function (data, addGroupProductId = true) {
			let def = {
				dataList: []
			};

			var pac = _.find(data, function (item) {
				return item.describeApiName === 'ProductGroupObj'
			}) || def;

			var sub = _.find(data, function (item) {
				return item.describeApiName === 'BOMObj'
			}) || def;

			_.each(pac.dataList, function (item) {
				if (addGroupProductId) item.product_id = item.product_id__r = item.name;
				item.isGroup = true;
				item.bom_id = item._id;
				item.children = [];
			});

			_.each(sub.dataList, function (item) {
				item.bom_id = item._id;
			});

			return pac.dataList.concat(sub.dataList)
		},

		/**
		 * @desc 判断是否是BOM，返回结果和产品id
		 * @param data
		 * @returns {{productId: *, isPackage: *}}
		 */
		isBom: function (data, apiname, fieldMapping) {
			let obj = CRM.util.getSpecialFieldByApiName(apiname);
			if(fieldMapping) obj = Object.assign({}, obj, fieldMapping);
			let isPackage__v = obj.is_package + '__v';
			var res = {
				isPackage: data[obj.is_package],
				productId: data._id
			};

			if (CRM.util.hasValue(data[isPackage__v])) {
				res.isPackage = data[isPackage__v];
				res.productId = data.product_id || data._id;
			}

			if (data.pricebook_product_id__ro && data.pricebook_product_id__ro.product_id__ro) {
				res.isPackage = data.pricebook_product_id__ro.product_id__ro.is_package__v;
				res.productId = data.pricebook_product_id__ro.product_id__ro._id;
			}

			if (data.product_id__ro && data.product_id__ro.hasOwnProperty('is_package__v')) {
				res.isPackage = data.product_id__ro.is_package__v;
				res.productId = data.product_id__ro._id;
			}
			if(data.pid || data.parent_rowId || data.rebate_coupon_id || data.parent_gift_key) res.isPackage = false;
			return res
		},

		/**
		 * @desc 将树型数据转为平铺；
		 * @param data
		 * @param delChildren 是否需要删除children；
		 * @param delGroup 不需要分组；
		 * @returns {Array}
		 */
		parseTreeToNormal: function (data, delChildren = true, delGroup) {
			let res = [];
			let cloneData = CRM.util.cloneObjArr(data);

			function _fn(list) {
				_.each(list, function (item) {
					if (item.children) {
						let c = item.children;
						if (delChildren) delete item.children;
						if ((delGroup && !item.isGroup) || !delGroup) res.push(item);
						_fn(c)
					} else {
						if ((delGroup && !item.isGroup) || !delGroup) res.push(item);
					}
				})
			}

			_fn(cloneData);
			return res;
		},
		/**
		 * 将树形数据转为平铺的对象
		 * @param {Array} list
		 * @returns {Object}
		 */
		parseTreeToFlatObject: function (data, key) {
			let res = {};
			// let cloneData = CRM.util.cloneObjArr(data);
			let _key = key || 'rowId';

			function _fn(list) {
				_.each(list, function (item) {
					res[item[_key]] = item;
					item.children && _fn(item.children);
				})
			}

			_fn(data);
			return res;
		},

		/**
		 * @desc 展开树型数据
		 * @param list
		 * @returns {Array}
		 */
		openTreeData: function (list) {
			var res = [];

			function _fn(data) {
				_.each(data, function (item) {
					if (item.children && item.children.length) {
						res.push(item);
						_fn(item.children)
					} else {
						res.push(item)
					}
				})
			}

			_fn(list);

			return res

		},

		parseDataToBOM: function (data, rootId) {
			if (!data.length) return data;
			let newData = CRM.util.cloneObjArr(data);
			return this.updateRowIdAndPid(this.parseDataToTreeForBom(newData, rootId));
		},

		/**
		 * @desc 更新rowId和pid
		 * @param data
		 */
		updateRowIdAndPid: function (data) {
			let newData = _.isArray(data) ? data : [data];

			function _fn(list, pid) {
				_.each(list, function (item) {
					item.rowId = CRM.util.uniqueCode();
					if (pid) item.pid = pid;
					if (item.children) {
						_fn(item.children, item.rowId)
					}
				})
			}

			_fn(newData);
			return data
		},

		/**
		 * @desc bom组装树结构
		 * @param data
		 * @param rootId 根结点的bom_id
		 * @returns {Array}
		 */
		parseDataToTreeForBom: function (data, rootId) {
			let rootData = [];
			let subData = [];
			_.each(data, function (item) {
				if (item.parent_bom_id === rootId && !item.product_group_id) {
					rootData.push(item)
				} else {
					subData.push(item)
				}
			});

			function findChildren(id, isGroup) {
				return _.filter(subData, function (item) {
					if (isGroup) {
						return item.product_group_id === id;
					} else {
						if (item.product_group_id) {
							return
						}
						return item.parent_bom_id === id;
					}
				})
			}

			function _fn(list) {
				_.each(list, function (item) {
					let id = item._id || item.bom_id;
					if (!id) return;
					let children = findChildren(id, item.isGroup);
					if(item.new_bom_path){
						let parentBomPath = item.new_bom_path;
						if(item.isGroup){
							parentBomPath = item.new_bom_path.split('.');
							parentBomPath.pop();
							parentBomPath = parentBomPath.join('.')
						}
						children = children.filter(c => !c.new_bom_path || c.new_bom_path.includes(parentBomPath));
					}
					if (children.length) {
						item.children = CRM.util.cloneObjArr(children);
						_fn(item.children)
					}
				})
			}

			_fn(rootData);

			return rootData;

		},

		/**
		 * @desc 转化平铺数据为树形结构；每条数据都需要有rowId; 子对象需要有pid，值为主对象的rowId
		 * @param data
		 * @param noRoot 支持全是子件时组装树形
		 * @return {Array}
		 */
		parseDataToTree: function (data, noRoot = false) {
			function _getPid(item){
				return item.pid || item.parent_rowId;
			}
			let childrenArr = [];
			let root = data.filter(item => {
				let pd = _getPid(item);
				if(noRoot){
					if(pd){
						let findParent = data.find(c => c.rowId === pd);
						if(!findParent) return item;
						childrenArr.push(item)
					}else{
						return item;
					}
				}else{
					if(!pd) return item;
					childrenArr.push(item);
				}
			});

			function findChildren(pid) {
				return childrenArr.filter(item => {
					return _getPid(item) == pid;
				})
			}

			function toTree(list) {
				list.forEach(item => {
					let children = findChildren(item.rowId);
					if (children.length) {
						item.children = children;
						toTree(item.children)
					}
				})
			}

			toTree(root);

			return root

		},

		parseDataToBomForMd: function (data, apiname, fieldsMapping) {
			return CRM.util.parseDataToTree(CRM.util.parseTreeDataForMd(...arguments));
		},

		/**
		 * @desc 通过productId、parentProductId找bom节点；用于二次编辑，替换数据;
		 * @param data
		 * @param productId
		 * @param parentProductId
		 * @returns {*}
		 */
		findDataByPIdAndPPId: function (data, productId, parentProductId) {
			let res = null;

			function _fn(nData) {
				let len = nData.length;
				while (len--) {
					let item = nData[len];
					if (item.product_id === productId && item.parent_product_id === parentProductId) {
						res = item;
						return;
					}
					if (item.children) {
						_fn(item.children)
					}
				}
			}

			_fn(data);
			return res;
		},

		// 根据bomId查数据
		findDataByBomId: function (data, bomId, field) {
			let res = null;
			let f = field || 'bom_id';

			function _fn(nData) {
				let len = nData.length;
				while (len--) {
					let item = nData[len];
					if (item._id === bomId || item[f] === bomId) {
						res = item;
						return;
					}
					if (item.children) {
						_fn(item.children)
					}
				}
			}

			_fn(data);
			return res;
		},

		// 根据bomPath查数据
		findDataByBomIdAndPath: function (data, bomId, rootPath) {
			let res = null;
			let f = 'bom_id';
			function _fn(nData) {
				let len = nData.length;
				while (len--) {
					let item = nData[len];
					if ((item._id === bomId || item[f] === bomId) && item.current_root_new_path === rootPath) {
						res = item;
						return;
					}
					if (item.children) {
						_fn(item.children)
					}
				}
			}
			_fn(data);
			return res;
		},

		findDataByNewBomPath(data, newBomPath){
			let res = null;
			function _fn(nData) {
				let len = nData.length;
				while (len--) {
					let item = nData[len];
					if (item.new_bom_path === newBomPath) {
						res = item;
						return;
					}
					if (item.children) {
						_fn(item.children)
					}
				}
			}
			_fn(data);
			return res;
		},

		/**
		 * @desc 循环tree，特殊处理分组
		 * @param data Arr
		 * @param cb
		 */
		forEachTreeData: function (data, cb) {
			if (!Array.isArray(data)) return;
			let groupPDataList = [];

			function findPData(rowId) {
				return _.find(groupPDataList, function (item) {
					return item.rowId == rowId;
				})
			}

			function _fn(list, pData) {
				_.each(list, function (item) {
					if (item.isGroup && pData) groupPDataList.push(pData);

					// 如果父节点是分组，则加上分组的父节点信息；
					if (pData && pData.isGroup) {
						let groupPData = findPData(pData.pid); // 如果父级是分组，找分组的父级
						cb && cb(item, pData, groupPData);
					} else {
						cb && cb(item, pData);
					}
					if (item.children) _fn(item.children, item)
				})
			}

			if (data.length) {
				_fn(data)
			}
		},

		/**
		 * 展开rowId节点的直系父级节点
		 * @param {String} rowId 当前rowId
		 * @param {Object} obj 当前树的对象数据
		 */
		setTreeShowType: function (rowId, obj) {
			const me = this;
			if (!obj[rowId]) return;
			var aids = _.keys(obj);
			var pids = me.getParents(obj[rowId], obj, 'rowId', true);
			_.each(aids, function (id) {
				obj[id].isShow = false;
			});
			if (!pids.length) {
				obj[rowId].isShow = true;
				return;
			}
			_.each(pids, function (id, index) {
				obj[id].isShow = true;
				_.each(obj[id].children, function (item) {
					!_.contains(pids, item.rowId) && (item.isShow = true);
				});
			})
		},

		/**
		 * @desc 校验BOM节点数量是否满足：“该框输入的数值=最小数量+增减数量幅度*n”
		 * @param table
		 * @param data 表格数据
		 * @param otherTable 其他表格错误提示
		 * @param changeField 修改的字段
		 * @param showMsg 是否需要弹框提示
		 */
		validAmountBeforeSubmit: function (table, data, otherTable, changeField = '', showMsg = false, ) {
			var newData = data ? data : table.getCurData();
			let util = CRM.util;
			let msg = '';
			let isOpenPeriod = util.getConfigStatusByKey('periodic_product') === '1';

			function _addTableError(rowId, field) {
				table && table.addCellsErrorByRowId(rowId, field);
				otherTable && otherTable.addCellsErrorByRowId(rowId, field);
			}

			// 整期售卖
			function _isWholeSale(item){
				if(item.product_id__ro){
					return item.product_id__ro?.whole_period_sale && item.product_id__ro?.whole_period_sale === 'yes'
				}
				return item.whole_period_sale && item.whole_period_sale === 'yes'
			}

			function _isDecimal(num){ // 是小数
				return String(Number(num)).split('.').length > 1;
			}

			function map(datas) {
				var len = datas.length;
				while (len--) {
					var item = datas[len];
					if (!item.isGroup) {
						let amount = item.hasOwnProperty('__amount') ? Number(item.__amount) : util.hasValue(item.amount) ? Number(item.amount): null;
						var max_amount = util.hasValue(item.max_amount) ? Number(item.max_amount) : null;
						var min_amount = util.hasValue(item.min_amount) ? Number(item.min_amount) : null;
						var increment = util.hasValue(item.increment) ? Number(item.increment) : null;
						var pricing_period = util.hasValue(item.pricing_period) ? Number(item.pricing_period) : null;
						var rowId = item.rowId;
						table && table.removeErrorByRowId(rowId, ['increment', 'max_amount', 'min_amount', 'amount']);
						otherTable && otherTable.removeErrorByRowId(rowId, ['increment', 'max_amount', 'min_amount', 'amount']);
						let bomPath = '【' + CRM.util.getBomPath(item) + '】';
						if (increment < 0) {
							_addTableError(rowId, 'increment');
							msg += bomPath + $t('增加数量幅度不能小于零') + '<br>';
						}

						if (max_amount < 0) {
							_addTableError(rowId, 'max_amount');
							msg += bomPath + $t('最大数量应大于零') + '<br>';
						}

						if (min_amount < 0) {
							_addTableError(rowId, 'min_amount');
							msg += bomPath + $t('最小数量应大于零') + '<br>';
						}

						if (amount <= 0) {
							_addTableError(rowId, 'min_amount');
							msg += bomPath + $t('数量应该大于0') + '<br>';
						}

						if (isOpenPeriod && (changeField === 'pricing_period' || !changeField)) {
							if(pricing_period && pricing_period <= 0){
								_addTableError(rowId, 'pricing_period');
								msg += bomPath + $t('crm.sfa.checkPricingPeriod1') + '<br>'; // 期数应该大于0
							}
							// 整期售卖的产品，期数必须是整数
							if(_isWholeSale(item) && _isDecimal(item.pricing_period)){
								_addTableError(rowId, 'pricing_period');
								msg += bomPath + $t('sfa.crm.bom.checkPricingPeriod2') + '<br>'; // 整期售卖的产品，期数必须是整数
							}
						}

						if (min_amount > max_amount && util.hasValue(max_amount) && util.hasValue(min_amount)) {
							_addTableError(rowId, changeField || 'min_amount');
							msg += changeField === 'min_amount' ? bomPath + $t('最小数量应小于等于最大数量') + '<br>' : bomPath + $t('最大数量应大于等于最小数量') + '<br>';
						}

						if (amount > max_amount && util.hasValue(max_amount) && util.hasValue(amount)) {
							_addTableError(rowId, changeField || 'amount');
							msg += changeField === 'amount' ? bomPath + $t('数量应该小于等于最大数量') + '<br>' : bomPath + $t('最大数量应该大于等于数量') + '<br>';
						}

						if (amount < min_amount && util.hasValue(amount) && util.hasValue(min_amount)) {
							_addTableError(rowId, changeField || 'amount');
							msg += changeField === 'amount' ? bomPath + $t('数量应该大于等于最小数量') + '<br>': bomPath + $t('最小数量应该小于等于数量') + '<br>';
						}

						if (increment && util.hasValue(amount)) {
							if (!CRM.util.exactDivision(amount, increment)) {
								_addTableError(rowId, 'amount');
								msg += bomPath + $t('数量应该为增减数量幅度({{increment}}）的整数倍', {
									increment: increment
								}) + '<br>';
							}
						}
					}

					if (item.children && item.children.length) {
						map(item.children)
					}
				}
			}

			map(newData);
			if (showMsg && msg) CRM.util.alert(msg);
			return !msg;
		},

		/**
		 * @desc 校验BOM分组 ;  分组的最少可选个数 <= 分组中当前勾选个数 <= 分组的最大可选个数；
		 * @param onlyOneLevel 只校验第一层分组
		 */
		validGroupData: function (table, data, onlyOneLevel, notShowError = false) {
			let util = CRM.util;
			let msg = '';
			let newData = data ? data : table?.getCurData();

			function _fn(list, pData) {
				_.each(list, function (item) {
					var res = true;
					var rowId = item.rowId;
					if (item.isGroup && (!pData || (pData && pData.isChecked))) {
						var isSelect = 0;
						_.each(item.children || [], function (c) {
							if (c.isChecked) isSelect++;
						});
						var max = item.max_prod_count;
						var min = item.min_prod_count;
						let bomPath = '【' + CRM.util.getBomPathFromData(item, newData) + '】';
						if (isSelect > max && util.hasValue(max)) {
							msg += bomPath + '，' + $t('最多选择{{max}}个产品', {
								"max": max
							}) + '<br/>';
							table?.addTrError(rowId);
							res = false;
						}

						if (isSelect < min && util.hasValue(min)) {
							msg += bomPath + '，' + $t('最少选择{{min}}个产品', {
								"min": min
							}) + '<br/>';
							table?.addTrError(rowId);
							res = false;
						}
						if (res) table?.removeErrorByRowId(rowId);
					}
					if (item.children && item.children.length && !onlyOneLevel) {
						_fn(item.children, item)
					}
				})
			}

			_fn(newData);

			if (msg && !notShowError) CRM.util.alert(msg);

			return !msg

		},

		/**
		 * 获取当前节点的所有父级id
		 * @param {Object} rowData 当前节点数据
		 * @param {Object} allData 数据
		 * @returns {Array}
		 */
		getAllParents: function (rowData, allData, key, hasGroup, parentKey = 'pid') {
			var _key = key || 'product_id';
			var ids = [];

			function fn(data) {
				if (!data) return;
				data[_key] && (hasGroup || !data.isGroup) && ids.push(data[_key]);
				if (data[parentKey]) fn(CRM.util.getDataByKey(data[parentKey], allData, 'rowId'))
			}

			if (rowData[parentKey]) fn(CRM.util.getDataByKey(rowData[parentKey], allData, 'rowId'));

			return ids;
		},

		getAllSiblings: function (data, allData, key, hasGroup) {
			var _key = key || 'product_id';
			var ids = [];

			if (data.pid) {
				let pData = CRM.util.getDataByKey(data.pid, allData, 'rowId');
				if (pData.isGroup) pData = CRM.util.getDataByKey(pData.pid, allData, 'rowId');
				if (pData && pData.children) {
					var _list = [];
					if (!hasGroup) {
						_.each(pData.children, function (item) {
							// 如果是分组，需要取分组下的第一层子产品；
							if (item.isGroup && item.children) {
								_.each(item.children, function (c) {
									!c.isGroup && _list.push(c)
								})
							} else {
								_list.push(item)
							}
						})
					}
					ids = _.pluck(_list, _key);
				}
			}
			return ids;
		},

		/**
		 * 获取当前节点的所有父级id
		 * @param {Object} data 当前节点数据
		 * @param {Object} obj 树形数据拍平的对象数据
		 * @returns {Array}
		 */
		getParents: function (data, obj, key, hasGroup) {
			var _key = key || 'product_id';
			var ids = [];

			function fn(data) {
				data[_key] && (hasGroup || !data.isGroup) && ids.push(data[_key]);
				obj[data.pid] && fn(obj[data.pid]);
			}

			if (data.pid && obj[data.pid]) fn(obj[data.pid]);

			return ids;
		},

		getSiblings: function (data, obj, key, hasGroup) {
			var _key = key || 'product_id';
			var ids = [];

			if (data.pid && obj[data.pid] && obj[data.pid].children) {
				var _list = obj[data.pid].children;
				if (!hasGroup) {
					_list = _.filter(_list, function (item) {
						return !item.isGroup;
					})
				}
				ids = _.pluck(_list, _key);
			}
			return ids;
		},

		// 获取所有子级，如果子级是分组，则取分组的第一层子级；
		getChildren: function (data, key, hasGroup) {
			var _key = key || 'product_id';
			if (!data.children) return [];
			var _list = [];
			if (!hasGroup) {
				_.each(data.children, function (item) {
					// 如果是分组，需要取分组下的第一层子产品；
					if (item.isGroup && item.children) {
						_.each(item.children, function (c) {
							!c.isGroup && _list.push(c)
						})
					} else {
						_list.push(item)
					}
				})
			}
			var ids = _.pluck(_list, _key);
			return ids;
		},

		// 只返回勾选行数据，并保留层级关系；
		getOnlyCheckedData: function (data) {
			let res = [];
			let allData = $.extend(true, [], data);

			function _fn(data) {
				_.each(data, function (item) {
					// 过滤掉空分组;
					if (item.isGroup) {
						if (!item.children) return;
						let hasChecked = _.filter(item.children, function (c) {
							return c.isChecked;
						});
						if (!hasChecked.length) return;
					}
					if (item.isChecked) {
						let children = item.children;
						if (children) {
							delete item.children;
							res.push(item);
							_fn(children)
						} else {
							res.push(item)
						}
					}
				})
			}

			_fn(allData);

			return CRM.util.parseDataToTree(res)
		},

		// 检测分组是否需要选中；若有子节点选中，则该节点也要选中；
		checkGroupIsChecked: function (data) {
			let _this = this;

			function _fn(data) {
				_.each(data, function (item) {
					if (item.isGroup) {
						item.isChecked = _this.checkChildrenIsChecked(item.children);
					}
					if (item.children) _fn(item.children);
				})
			}

			_fn(data)
		},

		/**
		 * @desc 检测子节点是否有选中项，有一个就返回true；
		 * @param data
		 * @returns {boolean}
		 */
		checkChildrenIsChecked: function (data) {
			let res = false;
			if (!data) return res;

			function _fn(list) {
				let len = list.length;
				while (len--) {
					let item = list[len];
					if (item.isChecked) {
						res = true;
						return
					}
					if (item.children) _fn(item.children);
				}
			}

			_fn(data);
			return res;
		},

		/**
		 * @desc bom子节点添加选中状态、bomid、调整金额;
		 * @param data
		 */
		addIsChecked: function (data) {
			CRM.util.forEachTreeData(data, function (item) {
				item.isChecked = item.is_required || item.selected_by_default;
				if (!item.isGroup) {
					item.bom_id = item._id;
					item.modified_adjust_price = item.adjust_price;
				}
			})
		},

		/**
		 * @desc 初始化BOM配置的勾选项；
		 * @desc 1、只有父级勾选，子节点才能勾选。
		 * @desc 2、如果第一层数据是分组，则需要校验分组下的第一层数据是否需要勾选。
		 * @desc 3、如果父级不允许编辑价格，那价格是0的子级也不允许编辑价格，但允许编辑数量；价格不是0的子级，价格、数量都不允许编辑，且出了默认选中和必须之外的，都不可勾选；
		 * @param data
		 * @param fn
		 * @param onlyRequired 是否只勾选必选项；
		 * @param noNeedSetEdit 是否需要设置字段可编辑；
		 * @param reset 是否需要重置；
		 */
		setBomChecked: function (data, fn, onlyRequired, noNeedSetEdit, reset, noCheckParent) {
			if (!data) return;
			CRM.util.forEachTreeData(data, function (item, pData, groupPdata) {
				if (reset) item.isChecked = false;
				if (!pData || (pData && pData.isChecked) || (pData && pData.isGroup && !groupPdata) || (pData && pData.isGroup && groupPdata && groupPdata.isChecked)) {
					// 如果父级是分组，且为单选，且已经有选中的子节点，return；
					if (pData && pData.isGroup && pData.group_options_control) {
						let hasChecked = _.filter(pData.children, function (c) {
							return c.isChecked;
						});
						if (hasChecked.length) return;
					}
					// 父级价格不可编辑 且子集价格不为0，且子集为非必选，不勾选
					// let pd = groupPdata || pData;
					// if(pd && !pd.isGroup && Number(item.adjust_price)!== 0 && !pd.price_editable && !item.is_required){
					// 	return;
					// }
					// noSelectDef：该数据为默认选中，但是用户手动反选了，所以就不再自动默认选中;
					// __isConstraintSetting：该数据被约束关系禁选，所以不能自动选中；
					if ((!item.isChecked && !item.noSelectDef && !item.__isConstraintSetting) || reset) {
						item.isChecked = onlyRequired ? item.is_required : item.is_required || item.selected_by_default;
						fn && fn(item, pData, groupPdata)
					}
				}
				if (item.node_type === 'temp') return;
				// 设置子级可编辑字段;
				// if (pData && !noNeedSetEdit) {
				// 	let pd = groupPdata || pData;
				// 	if (pd.hasOwnProperty('price_editable') && !pd.price_editable) {
				// 		if (!item.isGroup) {
				// 			if (Number(item.adjust_price) == 0) {
				// 				CRM.util.deleteArrChildren(item.isEditField, ['modified_adjust_price']);
				// 			} else {
				// 				// 价格不是0，且父级价格不能编辑，则不能选中；
				// 				item.disabled = true;
				// 				CRM.util.deleteArrChildren(item.isEditField, ['modified_adjust_price', 'amount']);
				// 			}
				// 		}
				// 	}
				// }
			});
			this.checkGroupIsChecked(data);
		},

		/**
		 * @des 包的数量变化时，子产品的数量也要变; 子产品数量 = 默认数量 * 包的数量
		 * @param table
		 * @param rowData
		 */
		setSubQuantity: function (table, rowData, noNeed, field = 'quantity') {
			if (!rowData.children) return;
			var pq = rowData[field];
			CRM.util.forEachTreeData(rowData.children, function (c, groupData, groupPdata) {
				if (c.hasOwnProperty('defQuantity') && !c.isGroup) {
					let q = CRM.util.multiplicational(c.defQuantity, pq);
					let des = table.getDescribeByField('quantity');
					if (des) q = CRM.util.formatDecimalPlace(q, des.decimal_places);
					c[field] = q;
					!noNeed && table.setCellsValueByRowId({
						[field]: c[field]
					}, c.rowId);
				}
			})
		},

		/**
		 * @desc 从对象数据处理，转BOM树；
		 * @param data
		 * @returns {Array}
		 */
		parseTreeDataForMd: function (data, apiname, fieldsMapping) {
			var res = [];
			var util = CRM.util;
			var obj = fieldsMapping || CRM.util.getSpecialFieldByApiName(apiname);
			let { product_id, node_type, product_group_id, parent_prod_pkg_key, prod_pkg_key, temp_node_group_id } = obj;
			let product_group_id__v = product_group_id + '__v';
			let product_id__r = product_id + '__r';

			// 给临时子件补分组信息
			if (CRM._cache.bom_temp_node) {
				data.forEach(item => {
					if (item[node_type] === 'temp') {
						item[product_group_id] = item[temp_node_group_id + '__r'];
						item[product_group_id__v] = item[temp_node_group_id];
					}
				})
			}
			_.each(data, function (item, index) {
				// 如果有分组信息，则插入分组信息；
				if (item[product_group_id] && item[parent_prod_pkg_key]) {
					let groupRowId = util.uniqueCode();
					let findGroup = _.find(res, function (c) {
						return c[product_id] === item[product_group_id__v] && item[parent_prod_pkg_key] === c.pid;
					});
					if (findGroup) {
						item.pid = findGroup.rowId;
					} else {
						res.push({
							[product_id]: item[product_group_id__v],
							[product_id__r]: item[product_group_id],
							rowId: groupRowId,
							pid: item[parent_prod_pkg_key],
							isGroup: true,
							groupId: item[product_group_id__v],
							bom_id: item[product_group_id__v],
						});
						item.pid = groupRowId;
					}
					item.rowId = item[prod_pkg_key] || util.uniqueCode();

				} else {
					if (item[parent_prod_pkg_key]) { // 子对象
						item.rowId = item[prod_pkg_key] || util.uniqueCode();
						item.pid = item[parent_prod_pkg_key];
					} else {
						item.rowId = item[prod_pkg_key] || util.uniqueCode();
					}
				}
				res.push(item);
			});

			return res;
		},

		/**
		 * @desc 检测产品包的选中项是否是默认选中，且数量和价格也是默认
		 * @param defaultData
		 * @param newData
		 * @param priceField        价格字段
		 * @param quantityField     数量字段
		 * @param idField           id字段
		 * @param parentQuantity    产品包当前数量
		 */
		checkSelectIsDefault: function (defaultData, newData, priceField = 'modified_adjust_price', quantityField = 'amount', idField = '_id', parentQuantity = 1) {
			var res = true;
			var _this = this;

			CRM.util.forEachTreeData(newData, function (item) {
				if (!item.isGroup) {
					var def = _this.getDataByKey(item[idField], defaultData, idField);
					if (def) {
						if (Number(def.adjust_price) != Number(item[priceField]) || def.amount != _this.division(item[quantityField], parentQuantity)) {
							res = false;
						}
					} else {
						res = false;
					}
				}
			});

			return res;
		},

		/**
		 * @desc 查找数据
		 * @param value 值
		 * @param data 总数据
		 * @param key
		 * @param searchAll 查所有；可能不止一条数据符合条件
		 * @returns {*}
		 */
		getDataByKey: function (value, data = [], key = 'rowId', searchAll = false) {
			let res = searchAll ? [] : null;

			function _fn(list) {
				let len = list.length;
				while (len--) {
					let item = list[len];
					if (item[key] == value) {
						if (searchAll) {
							res.push(item);
						} else {
							res = item;
							return
						}
					}
					if (item.children && item.children.length) _fn(item.children);
				}
			}

			_fn(data);
			return res;
		},

		/**
		 * @desc 查找field值中包含keyword的数据
		 * @param keyword
		 * @param field
		 * @param data
		 * @returns {Array}
		 */
		getDataByKeyWord(keyword, field, data) {
			let res = [];
			CRM.util.forEachTreeData(data, function (item) {
				const data = item[field + '__r'] || item[field];
				if (data && data.includes(keyword)) res.push(item)
			});
			return res;
		},

		/**
		 * @desc 处理筛选条件；
		 * @param conditions 筛选组件给的条件
		 * @param fields 描述
		 */
		parseConditions: function (conditions, fields) {
			return _.map(conditions, function (a) {
				var comparison = a.Comparison;
				var tt = fields[a.FieldName] ? fields[a.FieldName].type : '';
				var field_values = _.isArray(a.FilterValue) ? a.FilterValue : [a.FilterValue];
				var type = {};
				var fieldname = a.FieldName;
				if (tt == 'employee' || tt == 'department' || fieldname == 'owner_department') {
					var column = fields[fieldname];
					if (field_values && field_values.__includeChildrenCircle) {
						type.is_cascade = true;
					}

					if (column.is_single && tt == 'employee') {
						var o = {};
						o[tt] = _.map(a.FilterValue || [], function (item) {
							return item + ''
						});
						if (a.FilterValue.__circle) {
							o.department = _.map(a.FilterValue.__circle, function (item) {
								return item + ''
							})
						}
						type.value_type = 5;
						field_values = [JSON.stringify(o)];
					} else {
						type.value_type = 0;
						field_values = a.FilterValue;
					}

					if ((comparison == 9 || comparison == 10) && tt == 'department') {
						field_values = ['']
					}
				} else if (tt === 'date' && comparison == 1) {
					//日期的等于按between来操作
					field_values = [field_values[0] * 1, field_values[0] * 1 + 86399999];
					comparison = 17;
				} else if (tt === 'date_time' && comparison == 1) {
					//日期时间的等于按between来操作
					field_values = [field_values[0] * 1, field_values[0] * 1 + 59999];
					comparison = 17;
				} else if (comparison == 18 || comparison == 19) { // 前N天
					type.value_type = 3;
					field_values = ['day', a.FilterValue];
				} else if (comparison == 20 || comparison == 21) { // 前N月 后N月
					type.value_type = 3;
					field_values = ['month', a.FilterValue];
				} else if (comparison == 17 && !_.isArray(a.FilterValue)) { // 时间段中的快捷筛选
					type.value_type = 3;
					field_values = [a.FilterValue];
				} else if (tt === 'object_reference' || tt === 'master_detail') {
					fieldname += '.name'
				} else if (tt === 'relevant_team' || fieldname === 'relevant_team') {
					fieldname += '.teamMemberEmployee';
					field_values = a.FilterValue;
				}

				return _.extend({
					field_name: fieldname,
					field_values: field_values,
					operator: CRM.util.compareMap[comparison]
				}, type)
			})
		},

		// 不同对象的价格字段不一样；闹心
		getPriceFieldByApiName: function (apiname) {
			let config = {
				'SalesOrderProductObj': 'product_price',
				'SaleContractLineObj': 'product_price',
				'ReturnedGoodsInvoiceProductObj': 'returned_product_price',
			};
			return config[apiname] || 'price'
		},

		// 是否私有云
		isPrivateCloud: function () {
			var host = ['www.fxiaoke.com', 'www.ceshi112.com', 'www.ceshi113.com', 'crm.ceshi112.com', 'crm.xfs.com', 'localhost'];

			if (!_.contains(host, window.location.host)) {
				return true;
			}

			return false;
		},

		// 收集从对象数据
		mdDataList: function (md, apiname) {
			let res = [];
			if (md) {
				_.each(md.widgets, function (data, key) {
					if (data.length && (!apiname || (apiname && key === apiname))) {
						_.each(data, function (table) {
							if (table.getTableAllData) {
								let d = table.getTableAllData();
								if (d) res = res.concat(d)
							}
						})
					}
				});
			}
			return res;
		},

		// treev1接口返回的数据，查找产品id对应的bom_id
		getBomId: function (dataMapList, productId) {
			let productData = _.find(dataMapList, res => res.describeApiName === "ProductObj").dataList;
			let pd = _.find(productData, item => item._id === productId);
			if (pd) return pd.bom_id;
			return ''
		},

		// 支持BOM的主对象
		isSupportBom(apiname) {
			var supportBom = ['QuoteObj', 'SalesOrderObj', 'SaleContractObj'];
			return supportBom.includes(apiname)
		},

		// 支持BOM的从对象
		isSupportBomMdApiname(apiname) {
			return [
				"QuoteLinesObj",
				"SalesOrderProductObj",
				"SaleContractLineObj"
			].includes(apiname);
		},

		// 支持取价服务的从对象
		isSystemFollowObject(apiname) {
			return [
				"QuoteLinesObj",
				"SalesOrderProductObj",
				"NewOpportunityLinesObj",
				"SaleContractLineObj"
			].includes(apiname);
		},

		/*
		 * 生成笛卡尔集
		 * 输入oArr格式示例：
		 * [{name:"颜色",value:"1",options:[{name:"蓝色"，value:"11"},{name:"绿色"，value:"12"}]},
		 * 	{name:"材质",value:"2",options:[{name:"铜"，value:"21"}]}]
		 * 输出数组：
		 * [{颜色:11,材质:21},{颜色:12,材质:21}]
		 */
		createCpList: function (oArr) {
			/*
			 * 一些工具方法
			 */
			//过滤禁用值
			const filterDisabled = function (originArr) {
				let parsedArr = [];
				originArr.forEach(a => {
					let optArr = [];
					a.options.forEach(o => {
						if (o.active !== false || o.status == "1") {
							optArr.push(o)
						}
					})
					let item = _.extend({}, a, {
						options: optArr
					})
					parsedArr.push(item);
				})
				return parsedArr;
			};
			//添加对象属性
			const addProp = function (obj, key, value) {
				let prop = {};
				prop[key] = value;
				return _.extend(obj, prop);
			};
			//生成一组新笛卡尔值
			const cpHandle = function (preArr, curArr, key) {
				let dataList = [];
				preArr.forEach(item => {
					for (let i = 0; i < curArr.length; i++) {
						let data = _.extend({}, item);
						data = addProp(data, key, curArr[i].value);
						dataList.push(data);
					}
				})
				return dataList;
			};
			//生成全部笛卡尔集
			const reCartesian = function (i, arr) {
				let val = arr[i].options,
					key = arr[i].name;
				if (i <= 0) {
					let initData = [];
					val.forEach(v => {
						let item = addProp({}, key, v.value);
						initData.push(item);
					})
					return initData;
				} else {
					return cpHandle(reCartesian([i - 1], arr), val, key);
				}
			};

			/*
			 * 生成数据
			 */
			let arr = filterDisabled(oArr),
				i = arr.length - 1;
			return reCartesian(i, arr);
		},

		//=============================BOM约束规则====================================

		/**
		 * @desc 找出某个约束节点的关系链， 所有必须选择和不能选择的被约束节点id
		 * @param upId 约束节点id
		 * @param data 约束条件列表
		 * @param isBOM 是否是BOM
		 */
		findAllConstraint: function (upId, data, isBOM) {
			let d = isBOM ? 'down_bom_id' : 'down_product_id';
			let u = isBOM ? 'up_bom_id' : 'up_product_id';

			// 找出约束关系链，从upId开始，一层层所有和upId有约束关系的 down_bom_id/down_product_id
			function _findAllDw(upId, cst) {
				let r = [];
				let isScs = true;

				function _loop(id) {
					let len = data.length;
					while (len--) {
						let item = data[len];
						if (item[u] === id && item.constraint_type == cst) {
							let dd = item[d];
							if (dd === upId || r.includes(dd)) { // 成环了；
								isScs = false;
								return
							}
							r.push(dd);
							// 不允许选择项不用继续往下找了，760解除不允许选择的联动
							if (cst == '1') {
								_loop(dd);
							}
						}
					}
				}

				_loop(upId);
				if (!isScs) return isScs;
				return _.uniq(r);
			}

			let ms = _findAllDw(upId, 1); // 必须选择的节点列表
			let ns = _findAllDw(upId, 2); // 不允许选择的节点列表

			if (!ms || !ns) return false;

			return {
				mustSelect: ms, // 必须选择的节点列表
				cannotSelect: ns // 不允许选择的节点列表
			}
		},

		/**
		 * @desc 生效bom约束关系；
		 * @param data 需要处理约束关系的数据
		 * @param allData 全部数据
		 * @returns {{msg: string, status: boolean}}
		 */
		todoBomConstraint: function (data, allData) {
			let res = {
				status: true,
				msg: '',
				disabled: [], // 要设置disabled的数据
				checked: [] // 需要设置勾选的数据
			};

			// 校验父级；
			// 1、如果父级是单选分组，校验是否已有勾选项；
			// 2、父级是否已经被禁止选中；
			function _checkParent(pid, cst, curRow) {
				let pData = CRM.util.getDataByKey(pid, allData, 'rowId');
				if (!pData) {
					res.status = false;
					res.msg = $t('未找到父节点');
					return false;
				}

				// if(pData.isChecked && pData.__isConstraintSetting) return;

				// 父级已经选中了，则校验分组是否是单选，且已经有选中项，如果已有，则提示；
				if (pData.isChecked) {
					if (pData.isGroup && pData.group_options_control) {
						let hasChecked = _.filter(pData.children, item => item.isChecked);
						if (hasChecked.length) {
							// 如果已勾选的数据中包含自己，则不提示
							let findMe = _.find(hasChecked, item => item.rowId == curRow.rowId);
							if (!findMe || hasChecked.length > 1) {
								res.status = false;
								res.msg = cst + '，' + $t('但所在分组[{{group}}]为单选，且已经有选中项', {
									group: pData.name
								});
								return false;
							}
						}
					}
				} else {
					// 如果已经被禁止选中，则提示；
					if (pData.disabled) {
						res.status = false;
						let ppMsg = pData.__isConstraintSetting ? $t('但父级产品[{{P}}]，受其它约束关系影响，不允许选择', {
							P: CRM.util.getBomPath(pData)
						}) : $t('但父级产品[{{P}}]，受其父级产品价格不可编辑影响，不允许选择', {
							P: CRM.util.getBomPath(pData)
						});
						res.msg = cst + '，' + ppMsg;
						return false;
					}

					pData.isChecked = true;
					res.checked.push(pData);

					CRM.util.setBomChecked(pData.children, function (item) {
						if (item.isChecked) res.checked.push(item);
						if (item.disabled) res.disabled.push(item);
					}, false, true);

					if (pData.pid) return _checkParent(pData.pid);
				}

				return pData
			}

			// 执行必须选择节点；选中，并置灰，不允许反选；
			function _doMustSelect(rowData) {
				if (rowData.__hasRunConstraint) return;
				let ms = rowData.mustSelect;
				if (rowData.isChecked && ms && ms.length) {
					let l = ms.length;
					while (l--) {
						let id = ms[l];
						let d = CRM.util.findDataByBomId(allData, id);
						let continueLoop = []; // 缓存还需要执行约束规则的数据；

						if (d) {
							// 如果已经被禁止选中，则提示；
							if (!d.isChecked && d.disabled) {
								res.status = false;
								if (res.__isConstraintSetting) {
									res.msg = $t('已设置约束关系：选[{{A}}]必选[{{B}}]，但[{{B}}]因为其它约束关系无法被选择', {
										A: CRM.util.getBomPath(rowData),
										B: CRM.util.getBomPath(d)
									});
								} else {
									res.msg = $t('已设置约束关系：选[{{A}}]必选[{{B}}]，但[{{B}}]受父级产品价格不可编辑影响，不允许选择', {
										A: CRM.util.getBomPath(rowData),
										B: CRM.util.getBomPath(d)
									});
								}

								return res;
							}

							if (d.isChecked && d.disabled && d.__isConstraintSetting) continue;

							// 如果还没选，则选中；
							if (!d.isChecked) {
								d.isChecked = true;
								d.__checkedByConstraint = true; // 加标识；因为约束关系被选中；
								res.checked.push(d);
							}

							// 如果没置灰，则置灰；
							if (!d.disabled) {
								d.disabled = true;
								d.__isConstraintSetting = true; // 加标识，是通过约束条件置灰的；用于取消约束条件时，去掉置灰；避免和通过bom规则置灰冲突；
								res.disabled.push(d);
							}

							// 如果还没选中，且有子级，初始化子级，并需要执行约束规则；
							if (d.__checkedByConstraint && d.children && d.children.length) {
								d.__checkedByConstraint = false;
								CRM.util.setBomChecked(d.children, function (item) {
									if (item.isChecked) res.checked.push(item);
									if (item.disabled) res.disabled.push(item);
								}, false, true);
								continueLoop = continueLoop.concat(d.children);
							}

							// 如果有父级；需要将父级勾选，且置灰；
							if (d.pid) {
								let cst = $t('已设置约束关系：选[{{A}}]必选[{{B}}]', {
									A: CRM.util.getBomPath(rowData),
									B: CRM.util.getBomPath(d)
								});
								let pd = _checkParent(d.pid, cst, d);
								if (!pd) return res;

								// 若此父级还没执行过约束条件，需要执行；
								if (!pd.__hasRunConstraint) {
									pd.__hasRunConstraint = true;
									continueLoop = continueLoop.concat([pd]);
								}
							}

							if (continueLoop.length) _loop(continueLoop);

						} else {
							res.status = false;
							res.msg = $t('未找到被约束的子产品');
							return res;
						}
					}
				}
			}

			// 执行不允许选择节点；置灰；
			// 如果已经被选中，提示；
			function _doCannotSelect(rowData) {
				let ns = rowData.cannotSelect;
				if (rowData.isChecked && ns && ns.length) {
					let l = ns.length;
					while (l--) {
						let id = ns[l];
						let d = CRM.util.findDataByBomId(allData, id);
						if (d) {
							// 如果已经被勾选，提示；
							if (d.isChecked) {
								res.status = false;
								res.msg = $t('已设置约束关系：选[{{A}}]不允许选择[{{B}}]，但[{{B}}]已经被勾选', {
									A: CRM.util.getBomPath(rowData),
									B: CRM.util.getBomPath(d)
								});
								return res;
							}

							if (!d.disabled) {
								d.disabled = true;
								res.disabled.push(d);
								d.__isConstraintSetting = true;
							}
						} else {
							res.status = false;
							res.msg = $t('未找到被约束的子产品');
							return res;
						}
					}
				}
			}

			function _loop(list) {
				CRM.util.forEachTreeData(list, function (item, pData, groupPdata) {
					if (!item.isGroup) {
						_doMustSelect(item);
						_doCannotSelect(item);
					}
				});
			}

			_loop(data);

			res.checked = _.uniq(res.checked, item => item.rowId);
			res.disabled = _.uniq(res.disabled, item => item.rowId);

			return res;
		},

		/**
		 * @desc 撤销约束关系；
		 * @desc 去掉约束条件中，必须选择和不允许选择的节点的置灰；有勾选的还保留勾选；
		 * @param data
		 * @param allData
		 * @param revokeAll 不判断是否选中
		 * @returns {{msg: string, status: boolean}}
		 */
		revokeBomConstraint: function (data, allData, revokeAll) {
			let res = {
				status: true,
				msg: '',
				disabled_f: [] // 要取消disabled的数据
			};

			// 去掉父级的置灰；
			// function _checkParent(pid) {
			// 	let pData = CRM.util.getDataByKey(pid, allData, 'rowId');
			// 	if (!pData) {
			// 		res.status = false;
			// 		res.msg = $t('未找到父节点');
			// 		return false;
			// 	}

			// 	// 如果父级是因为约束关系被禁止选中的；
			// 	if (pData.disabled && pData.__isConstraintSetting) {
			// 		pData.disabled = false;
			// 		pData.__isConstraintSetting = false;
			// 		res.disabled_f.push(pData);
			// 	}

			// 	if (pData.pid) _checkParent(pData.pid);

			// 	return pData
			// }

			// 移除子产品的置灰（ 因为约束关系被置灰 ）
			function _removeChildrenDisable(data) {
				CRM.util.forEachTreeData(data, function (c) {
					if (c.disabled && c.__isConstraintSetting) {
						c.disabled = false;
						c.__isConstraintSetting = false;
						res.disabled_f.push(c);
					}
				})
			}

			let as = [];

			CRM.util.forEachTreeData(data, function (item, pData, groupPdata) {
				if ((item.isChecked || revokeAll) && item.mustSelect && item.mustSelect.length) as = as.concat(item.mustSelect);
				if ((item.isChecked || revokeAll) && item.cannotSelect && item.cannotSelect.length) as = as.concat(item.cannotSelect);
				if (item.children && item.children.length) _removeChildrenDisable(item.children)
			});

			as = _.uniq(as); // 必须选中项ids

			let l = as.length;
			while (l--) {
				let id = as[l];
				let d = CRM.util.findDataByBomId(allData, id);
				if (d) {
					if (d.disabled && d.__isConstraintSetting) {
						d.disabled = false;
						d.__isConstraintSetting = false;
						res.disabled_f.push(d);
						// if(d.pid) _checkParent(d.pid);
					}
				} else {
					res.status = false;
					res.msg = $t('未找到被约束的子产品');
					return res;
				}
			}

			res.disabled_f = _.uniq(res.disabled_f, item => item.rowId);
			return res;
		},

		/**
		 * @desc 获取数据中约束条件
		 * @param list
		 * @returns {{cannotSelect: *, mustSelect: *}}
		 */
		getConstraints: function (list) {
			let ms = [];
			let ns = [];

			CRM.util.forEachTreeData(list, function (item, pData, groupPdata) {
				if (item.isGroup) return;
				if (item.isChecked && item.mustSelect && item.mustSelect.length) ms = ms.concat(item.mustSelect);
				if (item.isChecked && item.cannotSelect && item.cannotSelect.length) ns = ns.concat(item.cannotSelect);
			});

			return {
				mustSelect: _.uniq(ms), // 必须选中项ids
				cannotSelect: _.uniq(ns) // 不允许选中项ids
			}
		},

		/**
		 * @desc 校验BOM约束规则；该选的要选，不该选的不能选；
		 * @param checkedData 当前选中数据
		 * @param allData 全量数据
		 * @returns {{msg: string, status: boolean}}
		 */
		validConstraintRuleForBom: function (checkedData, allData) {
			let res = {
				status: true,
				msg: ''
			};
			let cstObj = CRM.util.getConstraints(checkedData); // 当前勾选项，因为约束关系，涉及到的必须选，和不能选的数据；

			// 查查必须选的是否在当前已选数据中；
			function _find(id) {
				return CRM.util.getDataByKey(id, checkedData, '_id')
			}

			// 检查必选的是否都选了
			if (cstObj.mustSelect.length) {
				_.each(cstObj.mustSelect, id => {
					if (!_find(id)) {
						let pd = CRM.util.getDataByKey(id, allData, '_id');
						res.status = false;
						res.msg += '[' + CRM.util.getBomPath(pd) + ']' + '，' + $t('约束关系提示，此产品为必选') + '<br/>'
					}
				})
			}

			// 检查不允许选择的是否选了
			if (cstObj.cannotSelect.length) {
				_.each(cstObj.cannotSelect, id => {
					if (_find(id)) {
						let pd = CRM.util.getDataByKey(id, allData, '_id');
						res.status = false;
						res.msg += '[' + CRM.util.getBomPath(pd) + ']' + '，' + $t('约束关系提示，此产品为不允许选择') + '<br/>'
					}
				})
			}

			if (!res.status) util.alert(res.msg);
			return res.status
		},

		// 处理bom子节点的展示路径；
		getBomPath: function (data, noRoot = false) {
			if (!data) return '';

			// 处理子节点路径展示
			function _fn(pathList) {
				let r = '';
				_.each(pathList, (item, index) => {
					if(noRoot && !index) return;
					let ic = index + 1 < pathList.length ? '>' : '';
					r += item + ic
				});
				return r
			}
			if (data.crumb_bread_name) return _fn(data.crumb_bread_name);
			if (data.product_bom_path) return _fn(data.product_bom_path);

			return data.product_id__r || '';
		},

		// 从数据中解析bom节点路径
		getBomPathFromData(row, allData, pKey = 'pid'){
			let r = [row];
			let p = '';
			function _fn(id) {
				if (!id) return;
				let pd = CRM.util.getDataByKey(id, allData);
				if (!pd) return;
				r.unshift(pd);
				if(pd[pKey]) _fn(pd[pKey]);
			}
			_fn(row[pKey]);
			r.forEach((item,index) => {
				let ic = index + 1 < r.length ? '>' : '';
				p += item.product_id__r + ic;
			});
			return p;
		},

		//==================================================================================


		//为了提高计算性能。server会在描述里下发计算字段的计算顺序
		//每次计算的时候需要把顺序再传给server
		//由于字段无论在那个字段下计算顺序都是固定的。底层需要遍历描述生成字典，发起计算的时候传给server
		calculateFieds2OrderMap: function (res) {
			var orderMap = {};

			function tfn(item) {
				_.each(item.calculate_relation && item.calculate_relation.relate_fields, function (arr, apiname) {
					_.each(arr, function (aitem) {
						orderMap[apiname + aitem.fieldName] = aitem.order;
					})
				})
			}

			function bdes(fields) {
				_.each(fields, function (field) {
					tfn(field);
				})
			}

			tfn(res.objectDescribe);
			bdes(res.objectDescribe.fields);
			_.each(res.detailObjectList, function (a) {
				tfn(a.objectDescribe);
				bdes(a.objectDescribe.fields);
			})

			return orderMap;
		},

		// 为计算字段添加order
		getCalculateFieldOrder: function (orderMap, modifiedObjectApiName) {
			if (!orderMap) return;

			var orderFields = {};

			_.each(modifiedObjectApiName, function (fields, apiname) {
				orderFields[apiname] = _.map(fields, function (fieldname) {
					return {
						fieldName: fieldname,
						order: orderMap[apiname + fieldname]
					}
				})
			})

			if (_.isEmpty(orderFields)) return;

			return orderFields;
		},

		/**
		 * @desc 文本超出框框时中间部分显示省略号，保留头尾；
		 * @param str 字符串
		 * @param dom 框元素
		 * @param fontSize 字大小
		 * @returns {*}
		 */
		showEllipsisOnMiddle: function (str, dom, fontSize) {
			let wid = dom.width();
			if (str.length * fontSize > wid) {
				let max = parseInt(wid / fontSize);
				let wl = parseInt((max - 1) / 2);
				return str.substring(0, wl) + '...' + str.substring(str.length - wl, str.length);
			}
			return str;
		},

		// 某些对象的特殊字段，newmdTreeTable组件在用的时候需要统一。
		// 自定义对象可以自己新建对应字段。
		getSpecialFieldByApiName: function (apiname) {
			let config = {
				'SalesOrderProductObj': {
					price: 'product_price',
					sales_price: 'sales_price',
					subtotal: 'subtotal',
				},
				'QuoteLinesObj': {
					price: 'price',
					sales_price: 'selling_price',
					subtotal: 'total_amount',
				},
				'SaleContractLineObj': {
					price: 'product_price'
				},
				'ReturnedGoodsInvoiceProductObj': {
					price: 'returned_product_price'
				},

			};

			// 自定义对象，需要支持BOM的从对象，自己新建这些字段
			if (apiname && apiname.includes('__c')) {
				return {
					node_type: 'node_type__c',
					temp_node_group_id: 'temp_node_group_id__c',
					product_id: 'product_id_cpq__c', // 查找关联产品名称字段
					product_id__r: 'product_id_cpq__c__r', // (不用新建)
					price: 'price_cpq__c', // 金额
					quantity: 'quantity_cpq__c', // Number
					bom_id: 'bom_id_cpq__c', // 文本
					bom_id__r: 'bom_id_cpq__c__r', // (不用新建)
					parent_prod_pkg_key: 'parent_prod_pkg_key_cpq__c', // 文本
					prod_pkg_key: 'prod_pkg_key_cpq__c', // 文本
					product_group_id: 'product_group_id_cpq__c', // 文本
					product_group_id__v: 'product_group_id_v_cpq__c', // 文本
					product_group_id__r: 'product_group_id__r_cpq__c', // 文本（不用新建）
					root_prod_pkg_key: 'root_prod_pkg_key_cpq__c', // 文本
					is_package: 'is_package_cpq__c', // Boolean
					is_package__v: 'is_package__v_cpq__c', // Boolean(不用新建)
					account_id: 'account_id_cpq__c', // 查找关联客户名称字段 (主对象)
					// 780
					node_discount: 'node_discount__c', // 选配折扣
					node_price: 'node_price__c', // 标准选配价格
					sales_price: 'sales_price__c', // 销售单价
					discount: 'discount__c', // 折扣
					subtotal: 'subtotal__c', // 小计

					// 订单特有的
					node_subtotal: 'node_subtotal__c', // 部件小计
					share_rate: 'share_rate__c', // 分摊比例

					// 870
					new_bom_path: 'new_bom_path__c',
				}
			}

			return _.extend({
				node_type: 'node_type',
				temp_node_group_id: 'temp_node_group_id',
				price: 'price',
				quantity: 'quantity',
				product_id: 'product_id',
				product_id__r: 'product_id__r',
				product_id__v: 'product_id__v',
				bom_id: 'bom_id',
				bom_id__r: 'bom_id__r',
				parent_prod_pkg_key: 'parent_prod_pkg_key',
				prod_pkg_key: 'prod_pkg_key',
				product_group_id: 'product_group_id',
				product_group_id__v: 'product_group_id__v',
				product_group_id__r: 'product_group_id__r',
				root_prod_pkg_key: 'root_prod_pkg_key',
				is_package: 'is_package',
				is_package__v: 'is_package__v',
				account_id: 'account_id',
				new_bom_path: 'new_bom_path',
				// 780
				node_discount: 'node_discount', // 选配折扣
				node_price: 'node_price', // 标准选配价格
				sales_price: 'sales_price', // 销售单价
				discount: 'discount', // 折扣
				subtotal: 'subtotal', // 小计

				// 订单特有的
				node_subtotal: 'node_subtotal', // 部件小计
				share_rate: 'share_rate', // 分摊比例

			}, config[apiname])
		},

		// 预设对象，主从对象映射表；根据主对象apiname找从对象；
		getMdApiNameByMasterApiName: function (masterApiName) {
			return {
				'SalesOrderObj': 'SalesOrderProductObj',
				'QuoteObj': 'QuoteLinesObj',
				'NewOpportunityObj': 'NewOpportunityLinesObj',
				'PaymentObj': 'OrderPaymentObj', // 回款
				'ReturnedGoodsInvoiceObj': 'ReturnedGoodsInvoiceProductObj', // 退货单
				'InvoiceApplicationObj': 'InvoiceApplicationLinesObj', // 开票申请
				'SaleContractObj': 'SaleContractLineObj' // 销售合同

			}[masterApiName] || '';
		},

		// 不同对象的销售单价和价格字段；
		getSalesPriceFieldByApiName: function (apiname) {
			let config = {
				'SalesOrderProductObj': ['sales_price', 'subtotal'],
				'QuoteLinesObj': ['selling_price', 'total_amount'],
				'NewOpportunityLinesObj': ['sales_price', 'total_amount'],
			};
			return config[apiname] || []
		},

		//获取比较符
		getCompare: function (operator, type) {
			return Helper.getCompareByType(operator, type)
		},

		/**
		 * @desc 校验子产品数量规则； 如果超过最大或最小值，回填最大或最小；如果不符合规则( 数量 = {增减数量幅度}*n)，不回填，给报错红框提示
		 * @param data 行数据
		 * @param curAmount 当前数量
		 * @return {*}
		 */
		checkRowData: function (data, curAmount, table, field, isOpenPeriod) {
			var amount = CRM.util.hasValue(curAmount) ? Number(curAmount) : null;
			var max_amount = CRM.util.hasValue(data.max_amount) ? Number(data.max_amount) : null;
			var min_amount = CRM.util.hasValue(data.min_amount) ? Number(data.min_amount) : null;
			var increment = CRM.util.hasValue(data.increment) ? Number(data.increment) : null;
			let pp1 = CRM.util.hasValue(data.pricing_period) ? Number(data.pricing_period) : null;
			var pricing_period = data.hasOwnProperty('__temp_pricing_period') ? Number(data.__temp_pricing_period) : pp1;
			var msg = '';


			if (!CRM.util.hasValue(amount) || Number(amount) <= 0) {
				msg = $t('数量应该大于0');
				table.addCellErrorAndErrorMsg(data.rowId, field, msg);
				return false
			}
			if (isOpenPeriod && (!CRM.util.hasValue(pricing_period) || Number(pricing_period) <= 0)) {
				msg = $t('crm.sfa.checkPricingPeriod1'); // 期数应该大于0
				table.addCellErrorAndErrorMsg(data.rowId, field, msg);
				return false
			}
			if (amount > max_amount && CRM.util.hasValue(amount) && CRM.util.hasValue(max_amount)) {
				msg = $t('请输入小于等于{{max}}的数值', {
					max: max_amount
				});
				table.addCellErrorAndErrorMsg(data.rowId, field, msg);
				return false
			}
			if (amount < min_amount && CRM.util.hasValue(amount) && CRM.util.hasValue(min_amount)) {
				msg = $t('请输入大于等于{{min}}的数值', {
					min: min_amount
				});
				table.addCellErrorAndErrorMsg(data.rowId, field, msg);
				return false
			}
			if (increment && CRM.util.hasValue(amount)) {
				if (!CRM.util.exactDivision(amount, increment)) {
					msg = $t('数量应该为增减数量幅度({{increment}}）的整数倍', {
						increment: increment
					});
					table.addCellErrorAndErrorMsg(data.rowId, field, msg);
					return false
				}
			}
			return true
		},

		// 添加赠品标识；
		addGiveawayIcon(data) {
			let newData = _.isArray(data) ? data : [data];
			_.each(newData, item => {
				if (['1', '2'].includes(item.is_giveaway)) {
					item._giveawayIcon = {
						label: item.rebate_coupon_id ? $t('返') : $t('赠'),
						type: 'circle'
					}
				}else if(item.price_policy_id){
					item._giveawayIcon = {
						label: $t('促') ,
						type: 'block'
					}
				}
			})
		},

		/* --------------- start: 价格政策：rule_condition/execution_result解析 --------------- */
		//解析条件数据
		parseCondition(data) {
			let condition =
				typeof data == "string" ? JSON.parse(data) : data || [],
				conditionText = "",
				conditionLen = condition.length;
			if (conditionLen >= 1) {
				condition.forEach((c, i) => {
					c.filters =
						typeof c.filters == "string" ?
							JSON.parse(c.filters) :
							c.filters || [];
					let filtersLen = c.filters.length;
					c.filters.forEach((f, j) => {
						let compare = CRM.util.getCompare(f.operator, f.type);
						f.operator__s = (compare && compare.name) || "";
						const left = f.object_api_name__s ? `【${f.object_api_name__s}.${f.field_name__s}】` : `【${f.field_name__s}】`;
						conditionText +=
							left +
							" " +
							f.operator__s +
							" " +
							(f.field_values__s == undefined ? `（${$t("已删除")}）` : f.field_values__s) +
							(j < filtersLen - 1 ? ` （${$t('且')}） ` : "");
					});
					conditionText += i < conditionLen - 1 ? ` ,${$t('或')} ` : "";
				});
			}

			return conditionText;
		},

		//解析每满条件
		parseCycle(data) {
			let cycle = typeof data == "string" ? JSON.parse(data) : data || [],
				str = "";
			cycle.forEach((c) => {
				str += c.field_name__s + ` ${$t('每满')} ` + c.field_value;
			});
			return str;
		},

		//解析执行数据
		parseExecution(data) {
			let execution =
				typeof data == "string" ? JSON.parse(data) : data || {},
				executionText = "";

			let ruleType = this.getRuleType(execution),
				conditionStr = this.parseCondition(execution.condition),
				cycleStr = execution.cycle_info ?
					this.parseCycle(execution.cycle_info) :
					"",
				ruleStr = "";
			switch (ruleType) {
				case "gift":
					ruleStr = this.getGiftRule(execution);
					break;
				case "tiered":
					ruleStr = this.getTieredRule(execution);
					break;
				default:
					ruleStr = this.getNormalRule(execution);
			}
			executionText += execution.object_api_name__s + ": ";
			if (ruleType == "tiered") {
				executionText +=
					(conditionStr ? "[" + conditionStr + "] ->" : "") +
					"," +
					ruleStr;
			} else {
				executionText +=
					// "," +
					(conditionStr ?
						"[" +
						conditionStr +
						(cycleStr ? ` （${$t('且')}） ` + cycleStr : "") +
						"] -> ," :
						(cycleStr ? "[" + cycleStr + "] -> ," : '')) +
					ruleStr;
			}

			return executionText;
		},
		getRuleType(data) {
			if (data.expressions) {
				return "expressions";
			} else if (data.gift_list) {
				return "gift";
			} else {
				return "tiered";
			}
		},

		getNormalRule(data) {
			let arr = [];
			data.expressions.forEach((ee) => {
				ee.operator__s = this.getExecutionOperator(ee.operator);
				let str = ee.left__s + " " + ee.operator__s + " " + (ee.right__s ? ee.right__s : ee.right);
				arr.push(str);
			});
			return arr.join('，');
		},
		getGiftRule(data) {
			let strArr = "";
			let hasLimit = data.limit_info && data.limit_info.limit_type;
			let limitType = ['GIFT_QUANTITY', 'ORIGINAL_QUANTITY'].includes(data.limit_info &&
				data.limit_info.limit_type);
			let label_total = limitType ? $t('限该赠品总数量') : $t('限该赠品总金额');
			let label_each = limitType ? $t('限该赠品每客户数量') : $t('限该赠品每客户金额');
			const specialUnitMap = {
				"large": $t("sfa.crm.pricepolicy.largeUnit"),
				"medium": $t("sfa.crm.pricepolicy.mediumUnit"),
				"small": $t("sfa.crm.pricepolicy.smallUnit"),
				"baseUnit": $t("sfa.crm.pricepolicy.baseUnit"),
				"currentProductUnit": $t("sfa.crm.pricepolicy.selfUnit"),
			}

			if (data.type !== "FIX") {

				let totalStr = CRM.util.isEmptyValue(data.gift_total_num) ? $t("不限") : data.gift_total_num,
					kindStr = CRM.util.isEmptyValue(data.gift_kind_upper_limit) ? $t("不限") : data.gift_kind_upper_limit,
					str =
						`${$t('赠送')} ${$t('赠品总量')}：` +
						totalStr +
						`；${$t('赠品种类限制')}：` +
						kindStr +
						",";
				strArr += str;
			} else {
				strArr += $t("赠送") + ",";
			}
			data.gift_list.map((g) => {
				let msg = '';
				if (data.type === 'FIX') {
					msg = 'x' + (CRM.util.isEmptyValue(g.max_value) ? $t('不限') : g.max_value);
				} else {
					if (CRM.util.isEmptyValue(g.max_value) && CRM.util.isEmptyValue(g.min_value)) {
						msg = 'x' + $t('不限');
					} else if (g.max_value == g.min_value) {
						msg = 'x' + g.max_value;
					} else {
						msg = (CRM.util.isEmptyValue(g.min_value) ? $t('不限') : g.min_value) + ' ~ ' + (CRM.util.isEmptyValue(g.max_value) ? $t('不限') : g.max_value);
					}
				}
				msg += g.unit_id ? `(${specialUnitMap[g.unit_id] || g.unit__s})` : '';

				strArr += g.product_id__s + ' ' + msg;

				if (hasLimit && CRM.util.hasValue(g.policy_max_value)) {
					strArr += ` ${label_total} ${g.policy_max_value} `
				}
				if (hasLimit && CRM.util.hasValue(g.account_max_value)) {
					strArr += ` ${label_each} ${g.account_max_value} `
				}
				strArr += ' ,'
			});
			let giftCondition = data.gift_condition && (typeof data.gift_condition == "string") ? JSON.parse(data.gift_condition) : data.gift_condition || [];

			if (giftCondition.length >= 1) {
				let unitStr = data.gift_condition_unit_id ? `（${specialUnitMap[data.gift_condition_unit_id]}）` : "";
				strArr += `${$t("按范围赠送")}${unitStr}: ,`

				strArr += this.parseCondition(giftCondition);
			}

			return strArr;
		},
		// 所有赠品限额限量dom
		getAllLimitDom(data) {
			let str = "";
			let hasLimit = data.limit_info && data.limit_info.limit_type;
			if (!hasLimit) return str;

			// 赠品or本品
			let isGift = ['GIFT_QUANTITY', 'GIFT_AMOUNT'].includes(data.limit_info && data.limit_info.limit_type);
			// 限额or限量
			let limitType = ['GIFT_QUANTITY', 'ORIGINAL_QUANTITY'].includes(data.limit_info && data.limit_info.limit_type);

			let label_money = isGift ? $t('限总赠品金额') : $t('总优惠金额');
			let label_money_each = isGift ? $t('每客户赠品金额') : $t('每客户优惠金额');

			let label_all_total = limitType ? $t('限') + $t('总数量') : label_money;
			let label_all_each = limitType ? $t('限每客户数量') : label_money_each;
			let label_all = isGift ? $t('所有赠品') : $t('促销本品');

			if ((CRM.util.hasValue(data.limit_info.total_max) || CRM.util.hasValue(data.limit_info.each_max))) {
				str += `${label_all}: `
			}

			if (CRM.util.hasValue(data.limit_info.total_max)) {
				str += ` ${label_all_total} ${data.limit_info.total_max} `
			}

			if (CRM.util.hasValue(data.limit_info.each_max)) {
				str += ` ${label_all_each} ${data.limit_info.each_max} `
			}

			return str
		},

		getTieredRule(data) {
			let str = "";
			data.tiered_list.forEach((t) => {
				str += data.field_name__s + ` ${$t("ava.bi-table.filter.equal")} ` + t.result_value + ",";
			});
			return str;
		},

		getTieredCondition(data) {
			let execution =
				typeof data == "string" ? JSON.parse(data) : data || [],
				str = `${$t("crm.productQuantity")}：,`;
			if (!execution.tiered_list) {
				return "--";
			}

			execution.tiered_list.forEach((t, i) => {
				if (i == execution.tiered_list.length - 1) {
					str += $t("crm.gtWithParam", { num: t.starting_quantity });
				} else {
					str += $t('crm.gtAndlteqWithParam', { num1: t.starting_quantity, num2: t.ending_quantity });
				}
			});
			return str;
		},
		//执行操作符mapping
		getExecutionOperator(value) {
			const operators = {
				EQUAL: $t("ava.bi-table.filter.equal"),
				ADD: $t("加"),
				SUBTRACT: $t("减"),
				MULTIPLY: $t("乘以"),
				DIVIDE: $t("除以"),
			};
			return operators[value];
		},
		/* --------------- end: 价格政策：rule_condition/execution_result解析 --------------- */

		// 混合第一次计算结果；
		mixCalRes: function (newObj, res) {
			_.each(newObj.detailDataMap || [], (item, key) => {
				if (res && res[key]) {
					_.each(item, (rowData, rowId) => {
						item[rowId] = Object.assign(rowData, res[key][rowId] || {});
					})
				}
			})
		},

		// 格式化取价服务需要的列表参数
		// 取价服务支持计算bom根节点价格
		parseDataForPriceService: function (data = [], apiname = '') {
			return data.map(d => {
				return {
					"productId": d.product_id || d._id || '',
					"priceBookId": d.pricebook_id || d.price_book_id || '',
					"attrMap": d.attribute_group || d.attribute_json || {},
					'rootProdKey': d.root_prod_pkg_key || d.rootKey || '',
					'parentProdKey': d.parent_prod_pkg_key || d.pid || '',
					'prodKey': d.prod_pkg_key || d.rowId || '',
					'bomId': d.bom_id || '',
					'price': d[CRM.util.getPriceFieldByApiName(apiname)] || d.newestPrice || d.modified_adjust_price || d.adjust_price || '',
					'baseUnit': d.unit__v || '', // 基础单位key值
					'unit': d.actual_unit || '', // 当前单位key值
					'amount': d.amount || d.quantity || '1'
				}
			});
		},

		// 走取价服务计算产品包价格时，子产品的数量需要除以产品包的数量；
		resetChildrenAmount: function (data = []) {
			let rootData = [];
			let childrenData = [];
			_.each(data, item => {
				item.parentProdKey ? childrenData.push(item) : rootData.push(item);
			});
			_.each(rootData, rd => {
				if (rd.amount != '0') {
					_.each(childrenData, child => {
						if (child.rootProdKey == rd.prodKey) {
							child.amount = CRM.util.division(child.amount, rd.amount)
						}
					})
				}
			});
			return data;
		},

		// 是否整除
		exactDivision: function (a, b) {
			let r = CRM.util.division(a, b);
			return !r.toString().includes('.');
		},

		/**
		 * @desc 校验BOM分摊比例，同一层比例相加之和 <=100，且不能为负数;
		 * @param table
		 * @param data Array
		 */
		checkBomShareRate: function (table, data) {
			let res = {
				status: true,
				msg: ''
			};
			let newData = data || table.getCurData() || [];
			let util = CRM.util;

			function _cal(data) {
				return data.reduce((r, item) => {
					return r + (util.hasValue(item.share_rate) ? Number(item.share_rate) : 0);
				}, 0)
			}

			function map(datas) {
				let len = datas.length;
				let total = 0;
				while (len--) {
					let item = datas[len];
					if (util.hasValue(item.share_rate) && Number(item.share_rate) < 0) {
						res.status = false;
						res.msg = $t('分摊比例必须大于等于0');
						table && table.addTrError(item.rowId);
						return;
					}
					// 分组不算层级；需要先算分组下级的子产品之和
					if (item.isGroup && item.children && item.children.length) {
						total += _cal(item.children);
					} else {
						total += (util.hasValue(item.share_rate) ? Number(item.share_rate) : 0);
					}
					if (total > 100) {
						res.status = false;
						res.msg = $t('每层子产品分摊比例之和必须小于等于100%');
						table && table.addSomeRowError([item], 'share_rate');
						return;
					}
					if (item.children && item.children.length) map(item.children);
				}
			}

			map(newData);
			return res;
		},

		// 不清销售单价字段
		noClearSalesPrice(){
			return ['fktest1199', 'quicktron', 'mkfe168'].includes(CRM.ea);
		},

		// BOM子产品这些字段需要展示为'--'
		getSpecialFields_bom: function () {
			let res = {
				allFields: ['sales_price', 'discount', 'total_amount', 'subtotal', 'extra_discount', 'extra_discount_amount', 'system_discount_amount', 'selling_price', 'sales_amount', 'total_discount', 'base_subtotal', 'base_sales_price',
					'base_order_product_amount', 'base_delivery_amount', 'base_sales_amount', 'base_total_amount', 'base_selling_price',
					/* 销售合同产品 */
					'executed_order_subtotal', 'unexecuted_order_subtotal', 'unexecuted_quantity', 'executed_quantity',
					// 780 分摊
					'node_subtotal', 'price_book_price', 'price_book_subtotal', 'policy_dynamic_amount', 'policy_subtotal', 'policy_price', 'policy_discount', 'gift_amortize_price', 'gift_amortize_subtotal'
				],
				shareFields: ['sales_price', 'discount', 'node_subtotal',]
			}
			if(this.noClearSalesPrice()){
				res.allFields = res.allFields.filter(f => !['sales_price', 'selling_price', 'discount', 'extra_discount'].includes(f))
			}
			return res;
		},

		// 设置BOM子产品特殊字段值；
		setBomSpecialFieldsVal(data = [], val = null) {
			if (CRM._cache.cpqStatus) {
				let sf = CRM.util.getSpecialFields_bom();
				let allFields = sf.allFields;
				let shareFields = sf.shareFields;
				data = _.isArray(data) ? data : [data];
				data.forEach(item => {
					if(item.sale_strategy__v !== 'sub' && item.children?.length){
						CRM.util.forEachTreeData(item.children, item => {
							if (item.parent_prod_pkg_key) {
								_.each(allFields, field => {
									if (CRM.util.hasValue(item.share_rate) && shareFields.includes(field)) {
										if (!CRM.util.hasValue(item[field])) item[field] = '0';
									} else {
										item[field] = val;
									}
								})
							}
						})
					}
				})
			}
		},

		// 是否为价格政策赠品；
		isGiveaway: function (data) {
			return !!(data.is_giveaway == '1' && data.parent_gift_key);
		},

		// 切换客户和日期不清空从对象；
		noClearMd: function () {
			return !CRM._cache.advancedPricing && CRM._cache.openPriceList;
			// return true;
		},
		// 获取工商查询特殊字段
		getIndustry_ext(data, key) {
			key = key || 'industry_ext';
			let industry_ext = {};
			try {
				data[key] && (industry_ext = JSON.parse(data[key]));
			} catch (error) {
			}
			return industry_ext;
		},
		createFxIcon: function (opts, vmOptions = {}) {
			var opts = _.extend({
				el: '',
				rClassName: '',
				title: '',
				style: '',
				className: 'el-icon-s-unfold',
			}, opts);

			return FxUI.create({
				wrapper: opts.el,
				template: `
                    <span
                        style="${opts.style}"
                        class="${opts.rClassName}"
                        title="${opts.title}"
                        @click="hideFloatCategory">
                        <i class="${opts.className}"></i>
                    </span>
                `,
				methods: {
					hideFloatCategory: function (e) {
						this.$emit('fold');
					}
				},
				...vmOptions
			})
		},

		//计算后，按小数位数&（截取/保留位数）返回结果
		calWithDecimals(val1, val2, decimals, operate, isToFix) {
			let result = null;
			switch (operate) {
				case 'add':
					result = CRM.util.accAdd(val1, val2);
					break;
				case 'subtract':
					if (val1 == Infinity) {
						return Infinity;
					}
					result = CRM.util.accSub(val1, val2);
					break;
				case 'multiply':
					result = CRM.util.multiplicational(val1, val2)
					break;
				case 'divide':
					result = CRM.util.division(val1, val2)
					break;
			}
			let strRes = result.toString(),
				dotPos = strRes.indexOf('.');
			if (dotPos >= 0) {
				if (isToFix) {
					strRes = Number(strRes).toFixed(decimals);
				} else {
					strRes = strRes.substring(0, dotPos + decimals * 1 + 1 * 1);
				}
			}
			return Number(strRes);
		},

		// 详情页组件替换灰度控制；
		getDetailRelatedList(type = 'RelatedList') {
			if (type === 'RelatedList') {
				return 'RelatedlistNew';
			} else {
				return 'MultiTableNew';
			}
		},

		/**
		 * 根据客户id和产品id显示促销产品的价格政策信息
		 * param{masterApiName:主对象apiname,accountId:客户id,dataId:商品/产品id,limit:请求数量}:请求接口参数
		 * apiname :列表对象是商品/产品
		 * domInfo{$el:容器dom,$item:鼠标点击元素dom,zIndex} :dom展示位置
		 * notTip :不展示政策信息框，默认false-展示
		 * @returns :格式化后的价格政策信息
		 */
		showPolicyInfo(param, apiname, domInfo, notTip) {
			const me = this,
				targetUrl = apiname == 'SPUObj' ? 'findPolicyByAccountAndSpuId' : 'findPolicyByAccountAndProductId';

			return new Promise((resolve, reject) => {
				CRM.util.FHHApi(
					{
						url: `/EM1HNCRM/API/v1/object/price_policy/service/${targetUrl}`,
						data: {
							"masterObjectApiName": param.masterApiName,
							"accountId": param.accountId,
							[apiname == "SPUObj" ? "spuId" : "productId"]: param.dataId,
							"limit": param.limit
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								const policies = res.Value.pricePolicyList || [],
									policyInfo = me.parsePolicyInfo(policies);

								if (!notTip) {
									me.showPolicyInfoDom(policyInfo, domInfo);
								}
								resolve({
									"originRes": policies,
									"policyInfo": policyInfo
								});
								return;
							}
							CRM.util.alert(res.Result.FailureMessage);
						},
					},
					{
						errorAlertModel: 1,
					}
				);
			});
		},
		//隐藏促销产品的价格政策信息
		hidePolicyInfo(el) {
			el && el.find('.crm-product-policy-tip').remove()
		},
		//显示促销产品的价格政策信息
		showPolicyInfoDom(policies, domInfo) {
			this.hidePolicyInfo(domInfo.$el);
			if (!policies || policies.length <= 0) {
				return;
			}
			const scrollTop = domInfo.$el.scrollTop(),
				$elOffset = domInfo.$el.offset(),
				$itemOffset = domInfo.$item.offset(),
				$itemHeight = domInfo.$item.outerHeight();

			// 计算 y 坐标
			const y = ($itemOffset.top - $elOffset.top + $itemHeight) + scrollTop;
			const x = $itemOffset.left - $elOffset.left;

			let $dom = `<div class="crm-product-policy-tip" style="position:absolute;left:${x}px;top:${y}px;z-index:${domInfo.zIndex};padding:16px;background:#fff;border:1px solid #eee;box-shadow:3px 3px 3px rgba(0, 0,0,0.1)">`;

			policies.forEach(item => {
				$dom += `<div style="margin-bottom:4px">${item.name}</div>`;
				item.rules.forEach(r => {
					$dom += '<div style="margin-bottom:2px">';
					r.labels.forEach(l => {
						$dom += `<span style="display:inline-block;border:1px solid #FF8000;color: #FF8000;background: #FFF2E6; font-size: 12px;height:18px;line-height:18px;padding:0 4px;margin-right:4px;border-radius: 2px;">${l}</span>`
					})
					$dom += `<label style="display:inline-block;width:200px; height:18px;vertical-align: middle;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">${r.name}</label></div>`;
				})
				$dom += `</div>`
			})
			$dom += `</div>`;
			domInfo.$el.append($dom)
		},
		//解析价格政策&规则信息
		parsePolicyInfo(policies) {

			const data = policies.map(p => {
				let item = {
					name: p.name,
					startDate: p.startDate ? CRM.util.formatTime(FS.util.convertTimestampFromTZ8(p.startDate), 'date') : "",
					endDate: p.endDate ? CRM.util.formatTime(FS.util.convertTimestampFromTZ8(p.endDate), 'date') : "",
					rules: []
				};
				item.rules = p.rules.map(r => {
					r.execution_result = typeof r.executionResult == "string" ? JSON.parse(r.executionResult) : r.executionResult || {};
					let parsedRule = {
						name: r.name,
						ruleType: r.ruleType,
						labels: this.parseLabel(r).filter(l => l.color == 'orange').map(l => l.text)
					};
					if (r.execution_result.gift_list) {
						parsedRule.gifts = this.getGiftRule(r.execution_result)

					}
					return parsedRule;
				});
				return item;
			})
			console.log(data);
			return data;
		},
		//解析价格政策规则的标签
		parseLabel(data) {
			let ruleType = data.rule_type || data.ruleType,

				labelArr = [];
			//检查组合还是单独
			if (ruleType.slice(0, 11) == 'combination') {
				labelArr.push($t('crm.freeCombination'));
			} else {
				labelArr.push($t('单独产品'));
			}
			//检查赠品规则还是金额规则
			if (ruleType.slice(ruleType.length - 4) == 'gift') {
				//赠品
				if (data.execution_result.type == 'FIX') {
					labelArr.push($t('固定赠品'));
				} else {
					labelArr.push($t('可选赠品'));
				}
			} else {
				let expressions = data.execution_result.expressions,
					//折扣||乘除=>打折
					hasDiscount = expressions.find(e => {
						if (e.left == 'virtual_discount' || ['MULTIPLY', 'DIVIDE'].includes(e.operator)) {
							return true;
						}
					}),
					//非折扣字段&&(加/等于) => 一口价
					hasFixPrice = expressions.find(e => {
						if (e.left !== 'virtual_discount' && ['EQUAL', 'ADD'].includes(e.operator)) {
							return true;
						}
					}),
					//非折扣字段&&(减) =>减价
					hasSales = expressions.find(e => {
						if (e.left !== 'virtual_discount' && ['SUBTRACT'].includes(e.operator)) {
							return true;
						}
					});
				hasDiscount && labelArr.push($t('打折'));
				hasFixPrice && labelArr.push($t('一口价'));
				hasSales && labelArr.push($t('减价'));
			}

			return labelArr.map(l => {
				return {
					text: $t(l),
					color: l == $t("crm.freeCombination") ? "blue" : (l == $t("单独产品") ? "green" : "orange")
				}
			})
		},

		isUsePlugin(apiName = '') {
			let blackList = CRM.util.getUserAttribute('crmMd20BlackList');
			let feedObjList = CRM.util.getUserAttribute('crmfeedMd20');
			if (feedObjList && blackList) {
				blackList = blackList.filter(item => !feedObjList.includes(item));
			}
			if (/__c$/.test(apiName) || (blackList && !_.contains(blackList, apiName))) {
				return true;
			}

			return CRM.util.isGrayScale(`CRM_USE_PLUGIN_${apiName.toUpperCase()}`);
		},

		isUseNewCustomPlugin(apiName = '') {
			var ea = CRM.ea;
			var isCross = window.PAAS ? window.PAAS.app.isCross() : false;
			if(isCross) {
				var appInfo = window.PAAS.app.getInfo() || {};
				ea = appInfo.upstreamEa || ea;
			}
			if(apiName === 'ReturnedGoodsInvoiceObj' && ['767835_sandbox', 'orion2020'].indexOf(ea) > -1) {
				return this.isUsePlugin(apiName) || !apiName;
			}
			return FS.util.getUserAttribute('paas_form_plugin_v2') && (this.isUsePlugin(apiName) || !apiName);
		},

		/**
		 * @description: 池-并发fishNum=6个worker，通过执行asyncWork异步任务处理sourceWater中的数据
		 * @param {Promise}  asyncWork                       用于处理数据的异步任务
		 * @param {Array}    sourceWater                     要处理的数据队列
		 * @param {Function} [allFinishCb]                   所有数据处理完的回调, 参数：成功数据，失败数据，各worker执行情况统计信息
		 * @param {Number}   [fishNum=6|sourceWater.length]  要开启多少个worker，不传值时：依据sourceWater的长度变化,大于6时则为6。
		 * @param {Number}   [retriesNum]                    单个worker失败重试次数
		 * @internal {Array} targetWater                     中间变量，数据最终处理结果，不保证顺序
		 * @internal {Array} errorWater                      中间变量，sourceWater中处理失败的数据
		 * @return {undefined)
		 */
		pool(asyncWork, sourceWater, allFinishCb = (() => {
		}), fishNum, retriesNum = 3, restArgs = {}) {
			let { progressCb } = restArgs;
			let srcWater = sourceWater?.slice?.() || [];
			const LEN = srcWater.length;
			const fISH_NUM = _.isNumber(fishNum)
				? fishNum
				: ((LEN > 6) ? 6 : LEN);
			let finishNum = 0, targetWater = [], errorWater = [];
			let statistics = {}; // 各worker执行情况统计

			for (let start = 0; start < fISH_NUM; start++) {
				(async function workingFish(water) {
					if (workingFish.serial == void (0)) {
						workingFish.serial = 'worker' + (start + 1);
					}
					statistics[workingFish.serial] = statistics[workingFish.serial] || {
						success: 0,
						fail: 0,
						failData: []
					};

					// 防止worker空转
					if (!water) {
						return;
					}
					;

					// 错误重传
					for (let num = 1; num <= retriesNum; num++) {
						try {
							let rst = await asyncWork(water);
							statistics[workingFish.serial].success++;
							targetWater.push(rst);
							break;
						} catch (err) {
							// 重传3次仍失败，则结束。当前worker处理下一个任务
							if (num === retriesNum) {
								errorWater.push(water);
								statistics[workingFish.serial].fail++;
								statistics[workingFish.serial].failData.push(water);

								// 所有都reject
								whetherNextWork();
								return;
							}
						}
					}

					// 所有异步任务执行完毕要执行的回调
					finishNum++;

					function whetherNextWork() {
						let progress = finishNum + errorWater.length;
						if (progress >= LEN) {
							console.log(statistics);
							progressCb?.({
								total: LEN,
								progress: LEN
							});
							allFinishCb?.(targetWater, errorWater, statistics);
							return;
						} else {
							progressCb?.({
								total: LEN,
								progress
							});
						}

						// 队列被清空后就暂停
						let nextTsk = srcWater.shift();
						nextTsk && workingFish(nextTsk);
					}

					whetherNextWork();
					// 为啥不用下标，有可能worker1执行完了第一个任务，worker2没执行完第二个任务，此时worker1开始执行第三个，但实际finish还是1取的就还是第二个任务。
				})(srcWater.shift());
			}
		},

		isUsePluginForAllComps(apiName = '') {
			let apiNames = ['quoteobj'];
			apiName = apiName.toLowerCase();
			return this.isUsePlugin(apiName) && apiNames.includes(apiName);
		},

		/*格式化属性/非标属性相关数据
		* dataArr：Array<{
		*		selectedAttr:{key:object}    //（已选属性）
		*		nsAttr:Array                 //（已选非标属性）
		*       ......
		*	}>
		* autoComposition：boolean    //是否自动组合属性值,默认false
		* fromAttributePriceBook:boolean //是否来自属性价目表选数据，默认false
		*/
		generateAttrsInfo(dataArr, autoComposition, fromAttributePriceBook) {
			if (autoComposition) {
				return this.parseCartesianAttrData(dataArr);
			} else {
				return dataArr.map(d => {
					return CRM.util.compose(
						this.parseNsAttrData,
						CRM.util.curry(2, this.parseAttrData)(fromAttributePriceBook),
					)(Object.assign({}, d))
				})
			}
		},

		// 获取已选属性文本
		getSelectAttrText(data, fromAttributePriceBook){
			let attrIdArr = Object.keys(data.selectedAttr || {});
			let attrG = {},
				attrT = "";
			attrIdArr.forEach(aId => {
				const attrItem = data.selectedAttr[aId];
				if (attrItem.value_ids && attrItem.value_ids.length >= 1) {
					attrT += `${attrItem.name}:`
					if (fromAttributePriceBook) {
						//{"属性1":["属性code1","属性code2"],"属性2":["属性code","属性code"]}
						attrG[aId] = [];
						const lastIdx = attrItem.value_ids.length - 1;
						attrItem.value_ids.forEach((p, idx) => {
							attrG[aId].push(p.id);
							attrT += `${p.name}${idx == lastIdx ? ";" : ","}`
						})
					} else {
						//{"属性1":"属性值code1","属性2":"属性值code2",}
						attrG[aId] = attrItem.value_ids[0].id;
						attrT += `${attrItem.value_ids[0].name};`
					}
				}
			});
			return {
				attrG,
				attrT,
			}
		},

		//不自动组合，多属性值产品
		parseAttrData(fromAttributePriceBook, item, onlyJson) {
			let attrIdArr = Object.keys(item.selectedAttr || {});
			if (attrIdArr.length >= 1) {
				let {attrG, attrT,} = CRM.util.getSelectAttrText(item, fromAttributePriceBook);
				item.attribute_json = attrG || null;
				// 这里原始属性信息被覆盖了，先缓存一份；
				if(!item.__cacheAttribute) item.__cacheAttribute = item.attribute;
				if(!onlyJson){
					item.attribute_group = attrG;
					item.attribute_group_text = attrT.slice(0, attrT.length - 1);
					item.attribute = item.attribute_group_text || null;
				}
			}
			return item;
		},

		//自动组合属性
		parseCartesianAttrData(data) {
			let arr = [],
				flag = true;
			data.forEach(d => {
				let attrValues = d.selectedAttr,
					attrValuesArr = Object.keys(attrValues || {});
				if (attrValues && attrValuesArr.length <= 0) {
					CRM.util.alert($t("请选择产品的属性值"));
					flag = false;
					return;
				}
				let arrForCp = this.parseParamForCp(attrValues),
					cpData = this.parseDataFromCp(arrForCp);
				cpData.forEach(cd => {
					arr.push(Object.assign({}, d, cd));
				})
			})
			if (!flag) {
				return false;
			}
			return arr;
		},

		/* 数据格式化成笛卡尔积参数格式
		 *  [{name:"颜色",options:[{value:{...}}]}...]
		 */
		parseParamForCp(data) {
			let arr = [];
			for (let key in data) {
				let value = data[key];
				arr.push({
					name: key,
					options: value.value_ids.map(v => {
						return {
							value: _.extend({
								attrName: value.name,
							}, v)
						}
					})
				})
			}
			return arr;
		},

		// 格式化笛卡尔积返回数据
		parseDataFromCp(args) {
			let data = CRM.util.createCpList(args),
				arr = data.map(d => {
					let obj = {
						attribute_group: {},
						attribute_group_text: ""
					};
					for (let k in d) {
						let val = d[k];
						obj.attribute_group[k] = [val.id];
						obj.attribute_group_text += val.attrName + ":" + val.name + ";"
					}
					return obj;
				});
			return arr;
		},

		// 获取非标属性文本和 json
		getNsAttrText(data){
			let keyArr = Object.keys(data.nsAttr || {});
			let attrT = '';
			let attrG = {};
			if (keyArr.length >= 1) {
				data.nonstandard_attribute_json = {};
				data.nonstandard_attribute = "";
				keyArr.forEach((attrId, idx) => {
					const nsAttrItem = data.nsAttr[attrId];
					attrG[attrId] = nsAttrItem.value;
					attrT += `${nsAttrItem.name}:${nsAttrItem.value}${idx == keyArr.length - 1 ? "" : ","}`
				})
			}
			return {
				attrT,
				attrG
			}
		},

		//格式化非标属性值
		parseNsAttrData(item) {
			let keyArr = Object.keys(item.nsAttr || {});
			if (keyArr.length >= 1) {
				let{attrT, attrG} = CRM.util.getNsAttrText(item);
				item.nonstandard_attribute_json = attrG;
				item.nonstandard_attribute = attrT;
			}
			return item;
		},
		initICSelctor($select, options) {
			$select.append('<div></div>');
			var vcm = {
				components: {
					icSelector: (resolve) => window.seajs.use('icmanage-modules/icselector/icselector', (mod) => resolve(mod.icSelector)),
				},
				template: '<ic-selector ref="icsel" v-bind="icselProps" @change="onChange"></ic-selector>',
				data() {
					return {
						icselProps: $.extend(true, {
							mode: 'input',
							isAllFlags: [4, 4, 4],
							icsOptions: {
								idType: 'String', // PAAS对象 id 应该统一用字符串型
								// identityType: 1, // 1 只列出企业型数据，2 只列出个人型数据，undefined 不区分企业和个人
							},
						}, options.icselProps),
					}
				},
				methods: {
					onChange() {
						options.onChange && options.onChange.apply(this, arguments);
					},
					destroy() {
						this.$destroy();
					},
					getOValue() {
						return this.$refs.icsel.getValue();
					},
					setOValue(value) {
						this.icselProps.selected = value;
					},
					setValue(value) {
						let ex = _.extend({
							outerTenantIds: value.exEnterprise,
							outerUids: value.exEmployee,
						}, value);
						delete ex.exEnterprise;
						delete ex.exEmployee;
						this.icselProps.selected = ex;
					},
					getValue() {
						let value = this.$refs.icsel.getValue();
						value.exEnterprise = value.outerTenantIds;
						value.exEmployee = value.outerUids;
						delete value.outerTenantIds;
						delete value.outerUids;
						delete value.isAll;
						return value;
					},
					getSelectedItems() {
						return this.$refs.icsel.getSelectedItems();
					},
					clearAll() {
						return this.$refs.icsel.lineClearAll();
					},
					updateTabData(icsOptions) {
						this.clearAll();
						this.icselProps.selected = { outerTenantIds: [], outerUids: [] };
						this.icselProps.propsInput.tabs[0].icsOptions = icsOptions; //{ outerTenantIds: [value]};
						this.$refs.icsel.refill();
					},
				},
			};
			let selector = new Vue(vcm).$mount($('div', $select)[0]);
			return selector;
		},
		initICSelctorV2($select, options) {
			const me = this
			$select.append('<div></div>');
			var vcm = {
				components: {
					icSelector: (resolve) => window.seajs.use('icmanage-modules/icselectorV2/icselector', (mod) => resolve(mod.icSelectorInput)),
				},
				template: `<icSelector v-bind="icsProps" ref="icselector" @change="icsChange"></icSelector>`,
				data() {
					return {
						icsProps: $.extend(true, {
							selectorOpts: {
								addBtnLabel: $t("选择互联用户"),
								tabs: [
									"outerUids",
									"outerTenantIds",
									"outerTgroupIds",
									"outerRoleIds"
								],
								// defaultSelectedItems: {
								// 	outerTenantIds: ['300112036'],
								// 	outerUids:["300411338","300405249"],
								// 	outerTgroupIds: ['64b502eac1913500012bc37b']
								// }
							},
						}, options.icsProps),
					}
				},
				methods: {
					icsChange() {
						options.onChange && options.onChange.apply(this, arguments);
					},
					destroy() {
						this.$destroy();
					},
				},
			};
			let selector = new Vue(vcm).$mount($('div', $select)[0]);
			return selector;
		},
		initShowRichTextComp($select, options) {
			const me = this;
			$select.append('<div></div>');

			// 返回Promise以处理异步加载
			return new Promise((resolve, reject) => {
				require.async(['fs-modules/feed/map'], function (feedMap) {
					require.async(['xx-xxvui/sdk.js'], function (asyncMod) {
						let comp = window.FxUI.create({
							wrapper: $('div', $select)[0],
							template: '<XXVUIEditorRenderDoc :node="content"></XXVUIEditorRenderDoc>',
							computed: {
								content() {
								return $.extend(true,{}, options.props);
								}
							},
							methods: {}
						});

						// 确保组件实例创建完成后再resolve
						if(comp) {
							resolve(comp);
						} else {
							reject(new Error('Failed to create component'));
						}
					});
				});
			});
		},
		 initEditRichTextComp($select, options) {
			$select.append('<div></div>');

			// 返回Promise以处理异步加载
			return new Promise((resolve, reject) => {
				require.async([FS.ROOT_PATH + '/xx/xxvui/'+window.PAAS_CONFIG["xxXxvuiSdkXxeditorWeb"]], function (asyncMod) {
					asyncMod.xxeditor().then(
						function (xxeditor) {
							let editor = xxeditor.init(_.extend({
								content: {__xt:{__json:{type:'doc',
									content:[],
								}}},//初始化数据
								editor: $('div', $select)[0],
								editable: false,//是否可编辑
								extensionsConfig:{
									table: false,
									tableRow: false,
									tableHeader: false,
									tableCell: false,
									blockquote: false,
									bold: true,
									bulletList: true,
									code: false,
									codeBlock: false,
									document: true,
									dropcursor: true,
									gapcursor: true,
									hardBreak: false,
									heading: false,
									history: true,
									horizontalRule: true,
									italic: true,
									listItem: true,
									orderedList: true,
									text: true,
									taskList: false,
									at: false,
									topic: false,
									paasobj: false,
									paragraph: true,
									strike: true,
									customTaskItem: false,
									image: false,
									file: false,
									pasteImage: false,
									link: false,
									textStyle: true,
									color: true,
									fontSize: true,
									emoji: false,
									vote: false,
								},//插件配置
								showMenu: false,//是否显示菜单
								showBubbleMenu: false,//是否显示气泡菜单
								needRemoveMenus: [],//强制不显示的菜单
							}, options.props));
							 editor.$on('update', () => {
								editor.$emit('update', editor.getContent())
							});
							 resolve(editor);
						}
					).catch(err => {
						reject(err);
					});
				});
			});
		},

		getIcsOptions(value) {
			let icsOptions = {};
			value ? (icsOptions.outerTenantIds = [value]) : (icsOptions.itemList = []);
			return icsOptions;
		},

		// 格式化bom属性约束条件，展示用
		parseBomConditionRange(list = []) {
			let r = '';
			let reg = /； $/gi;
			list.forEach(con => {
				r += con.product_name || con.group_name;
				let a = '';
				if (con.attribute && con.attribute.length) {
					con.attribute.forEach(attr => {
						let attrDom = '';
						attr.attribute_values.forEach(val => {
							attrDom += val.name + '； '
						});
						a += attr.name + ': ' + attrDom
					});
				}
				if (con.nonstandardAttribute && con.nonstandardAttribute.length) {
					con.nonstandardAttribute.forEach(attr => {
						let attrDom = '';
						attr.attribute_values.forEach(val => {
							attrDom += val.minValue + ' - ' + (val.maxValue || $t('不限'));
							attrDom += '； ';
						});
						a += attr.name + ': ' + attrDom
					});
				}
				a = a.replace(reg, "");
				if (a.length) r += ' : ' + a;
				r += '； ';
			});
			return r.replace(reg, "");
		},

		//返利相关修改记录数据格式化
		parseOperationLog_rebate(dataStr, parseFieldFun) {
			if (CRM.util.isEmptyValue(dataStr)) {
				return "";
			}

			const unitMap = {
				'baseUnit': $t("sfa.crm.pricepolicy.baseUnit"),
				"small": $t("sfa.crm.pricepolicy.smallUnit"),
				"medium": $t("sfa.crm.pricepolicy.mediumUnit"),
				"large": $t("sfa.crm.pricepolicy.largeUnit")
			},

				data = (typeof dataStr == "string") ? JSON.parse(dataStr) : dataStr,
				unit = data.gift_condition_unit_id ? unitMap[data.gift_condition_unit_id] : null,

				unitStr = unit ? `${$t("单位")} ${$t("等于")} ${unit}，` : "",
				content = data.data;
			let contentStr = "";
			if (content && content[0] && content[0].filters) {
				contentStr = this.parseCondition(data.data);
			} else {
				contentStr = data.data.reduce((accStr, item) => {

					let str = parseFieldFun && parseFieldFun(item) || "";
					accStr += item.product_id__r + str + ","
					return accStr;
				}, "");
				contentStr = contentStr.slice(0, contentStr.length - 1);
			}

			return `${unitStr}${contentStr}`;
		},

		// 设置默认属性&非标属性
		setDefaultAttr(data, targetAttrInfo) {
			data = _.isArray(data) ? data : [data];
			data.forEach(item => {
				if (item.attribute && item.attribute.length && !item.selectedAttr) {
					item.selectedAttr = item._defaultAttr = this.getDefaultAttr(item, targetAttrInfo);
				}
				if(item?.nonstandardAttribute?.length && ! item.nsAttr){
					item.nsAttr = this.getDefaultNsAttr(item, targetAttrInfo);
				}
			})
		},

		/**
		 * 存筛选时，希望默认选中的属性属性值Map
		 * 只在属性值唯一时做特殊默认选中
		 * 支持场景：/来自报价器的筛选，缓存filters,否则清空
		 */
		getQuoterParam(quoterParam = {}) {
			const extractSingleValueFilters = (data, queryType) => {
				if (!data || typeof data !== 'object') {
					return {};
				}
				const queryInfo = JSON.parse(data[queryType] || "{}");
				return (queryInfo.filters || []).reduce((acc, item) => {
					if (item.field_values&&item.field_values.length === 1) {
						acc[item.field_name] = item.field_values[0];
					}
					return acc;
				}, {});
			};

			const targetAttrMap = extractSingleValueFilters(quoterParam, "standard_attribute_query_info");
			const targetNsAttrMap = extractSingleValueFilters(quoterParam, "nonstandard_attribute_query_info");

			return {
				targetAttrMap,
				targetNsAttrMap
			}
		},

		//获取默认选中属性值
		getDefaultAttr(data, targetAttrInfo) {
			let attrObj = {},
				attrArr = data.attribute,
				attrJson = data.attribute_json || {};
			const targetAttrMap = targetAttrInfo?.targetAttrMap;
			// 灰度无属性默认值
			let isGrayNoDefault = CRM.util.isGrayScale('CRM_ATTR_NO_DEFAULT');
			attrArr && attrArr.forEach(a => {
				const { attribute_values, id, name, fieldNum } = a,
					targetVal = targetAttrMap?.[`attribute${fieldNum}`] || null;

				let defaultVal = attribute_values.length && (attribute_values.find(a => a.is_default == '1'));
				// 灰度无属性默认值
				if(!isGrayNoDefault && !defaultVal){
					defaultVal = attribute_values && attribute_values[0];
				}
				let attrVal = attrJson && attrJson[a.id] || targetVal || defaultVal?.id;
				if (attrVal) {
					attrObj[id] = {
						name: a.hasOwnProperty('name__r') ? a.name__r : name,
						value_ids: _.filter(a.attribute_values, item => item.id == attrVal) || []
					}
				}
			});
			return attrObj;
		},

		//获取报价器传入选中非标属性值
		getDefaultNsAttr(data, targetAttrInfo) {
			const nsAttr = {},
				targetNsAttrMap=targetAttrInfo?.targetNsAttrMap||{};
			(data.nonstandardAttribute || []).forEach(ns => {
				const { id, name, value=[] } = ns;
				let default_value = ns.hasOwnProperty('default_value') ? ns.default_value : ns.containerDocument?.default_value; // server 数据格式不一致，端上先兼容处理
				if (!nsAttr[id]) {
					let val = (value.length?value[0]:targetNsAttrMap[id])??"";
					if(!CRM.util.hasValue(val)) val = CRM.util.hasValue(default_value) ? default_value: '';
					nsAttr[id] = {
						name: ns.hasOwnProperty('name__r') ? ns.name__r : name,
						value: val.toString(),
					}
				}
			});
			return nsAttr;
		},

		/**
		 * @desc 计算所有层级子产品价格，从最底层算起
		 * @param children 子产品
		 * @returns val 返回 第一层子产品的价格合计
		 */
		calculateAllChildrenPrice(children, isPeriod) {
			let util = CRM.util;
			let isIncludeDefSelected = CRM._cache.bom_price_calculation_configuration == '0'; // 包含默认选中子件价格
			let _cacheData = {};
			let val = totalSingleSetPrice =  0;
			//pricing_period price_per_set
			function _getPrice(data, isPer) {
				if(isPer) return data.price_per_set;
				return util.hasValue(data.modified_adjust_price) ? data.modified_adjust_price : data.adjust_price;
			}

			// 统计下级子产品的价格
			function _statisticsChildren(selfChildren, isPer) {
				let r = 0;
				selfChildren.forEach(c => {
					if (c.isGroup && c.children) {
						c.children.forEach(i => {
							let p = _getPrice(i, isPer);
							r = util.accAdd(r, util.multiplicational(Number(p), Number(i.amount)));
						})
					} else {
						let p = _getPrice(c, isPer);
						r = util.accAdd(r, util.multiplicational(Number(p), Number(c.amount)));
					}
				});
				return r;
			}

			// 更新所有父级的价格
			function _updateParentPrice(pData){
				// 自己的价格 + 子件的总价
				let pdPrice = util.accAdd(pData.adjust_price, _statisticsChildren(pData.children));
				// 有默认选中金额，减去默认选中金额
				if(pData._defSelectMoney) pdPrice = util.accSub(pdPrice, Number(pData._defSelectMoney));
				pData.modified_adjust_price = pdPrice;

				// 周期性产品单套价格
				if(isPeriod){
					let defSinglePrice = isIncludeDefSelected ? util.calculateSelectMoney(pData.children, '__price_per_set', true) : 0;
					let p = util.accAdd(pData.price_per_set, _statisticsChildren(pData.children, true));
					p = util.accSub(p, defSinglePrice);
					pData.price_per_set = p;
				}

				if(pData.pid){
					let pd = _cacheData[pData.pid];
					if (!pd) return;
					pd = pd.isGroup ? _cacheData[pd.pid] : pd;
					if (pd) _updateParentPrice(pd);
				}
			}

			// 找到最底层的子产品，层层更新父级
			let pdList = [];
			util.forEachTreeData(children, item => {
				if(item.isGroup) return;
				// 周期性单套价格
				if(isPeriod){
					let pp = util.hasValue(item.__pricing_period) ? item.__pricing_period : item.pricing_period;
					item.__price_per_set = util.multiplicational(item.adjust_price, pp || 1);
					item.price_per_set = util.multiplicational(item.adjust_price, item.pricing_period || 1);
				}
				if(!util.hasValue(item.modified_adjust_price)) item.modified_adjust_price = item.adjust_price;

				_cacheData[item.rowId] = item;
				if (!item.children && !item.isGroup && item.pid) {
					let pData = _cacheData[item.pid];
					if (pData) {
						let pd = pData.isGroup ? _cacheData[pData.pid] : pData;
						if (pd) pdList.push(pd);
					}
				}
			});

			pdList = _.uniq(pdList, item => item.rowId);
			pdList.forEach(pd => {
				_updateParentPrice(pd);
			});

			// 算总金额
			_.each(children, function (item) {
				if (item.isGroup && item.children) {
					_.each(item.children, c => {
						val = util.accAdd(val, util.multiplicational(Number(c.modified_adjust_price), Number(c.amount)));
						if(isPeriod) totalSingleSetPrice = util.accAdd(totalSingleSetPrice, util.multiplicational(c.price_per_set, c.amount));
					})
				} else {
					val = util.accAdd(val, util.multiplicational(Number(item.modified_adjust_price), Number(item.amount)));
					if(isPeriod) totalSingleSetPrice = util.accAdd(totalSingleSetPrice, util.multiplicational(item.price_per_set, item.amount));
				}
			});

			_cacheData = null;
			return {
				totalPrice: val,
				totalSingleSetPrice,
			};
		},

		getConfigStatusByKey(key) {
			const target = (CRM._cache.crmAllConfig || []).find(c => c.key == key);
			if (target) {
				return target.value;
			}
			return null;
		},

		//判断对象是否开启价格政策
		isOpenPricePolicy(apiName) {
			return this.getConfigStatusByKey(`price_policy_${apiName}`) == '1';
		},
        isOpenNotShowBom() {
            return this.getConfigStatusByKey('not_show_bom') == '1';
        },
		/**
		 * 新的国家省市区数据处理
		 * @param {Object} fields
		 * @returns {Object} fields
		 */
		parseAreaFields(fields) {
			if (CRM.util.getUserAttribute('crmNewArea') || CRM.util.getUserAttribute('crmAreaV3') || CRM.util.getUserAttribute('crmGrayTownFilter')) {
				let types = ['country', 'province', 'city', 'district', 'town', 'village'];
				_.each(fields, a => {
					if (a.group_type === 'area') {
						_.each(a.fields, (b, k) => {
							let fieldAreaLabels = [];
							let field = fields[b];
							if (!field) return;
							let index = _.indexOf(types, field.type);
							if (index < 0) return;
							_.each(types, (t, i) => {
								if (i > index) return;
								let tt = fields[a.fields['area_' + t]];
								fieldAreaLabels.push(tt ? tt.label : t);
							})

							field.fieldAreaLabels = fieldAreaLabels;
						})
					}
				})
				let a1 = $t('国家'), a2 = $t('省'), a3 = $t('市'), a4 = $t('区'), a5 = $t('乡镇'), a6 = $t('village', null, '村');
				let aa = {
					country: [a1],
					province: [a1, a2],
					city: [a1, a2, a3],
					district: [a1, a2, a3, a4],
					town: [a1, a2, a3, a4, a5],
					village: [a1, a2, a3, a4, a5, a6]
				}
				_.each(fields, a => { //处理引用字段，引用国家省市区
					if (aa[a.quote_field_type]) {
						a.fieldAreaLabels = aa[a.quote_field_type];
					}
				})
			}
			return fields;
		},
		setAreaCache(result) {
			let _cache = CRM.get('areaCache') || {}
			_.each(result, (item) => {
				_cache[item.value] = item;
			})
			CRM.set('areaCache', _cache);
		},

		delRowByKey(val, allData, field = 'rowId') {
			function _fn(data) {
				var len = data.length;
				while (len--) {
					var item = data[len];
					if (item[field] === val) {
						data.splice(len, 1);
						return
					} else if (item.children && item.children.length) {
						_fn(item.children)
					}
				}
			}
			_fn(allData);
		},

		/**
		 * 价目表字段配置数据范围，相关接口追加从对象details数据，过滤解析原始details
		 * @param {*} details {salesorder: [a, b, ...]}
		 * @param {*} priceBookDesc 价目表字段描述 {apiname: 'pricebook_id', wheres: []} || true 跳过
		 * @param {*} pickTargetApiName 筛选指定对象
		 * @param {*} swapToFirstParams 将当前行提升到首位
		 * @returns {*} details {salesorder: [a, b, ...]}
		 */
		parsePriceBookDataRangeDetails(
			details = {},
			priceBookDesc = null,
			pickTargetApiName = null,
			swapToFirstParams = null,
		) {

			// 未设置筛选条件
			// priceBookDesc = true 跳过
			if (typeof priceBookDesc === 'boolean' && priceBookDesc) { }
			else if (!priceBookDesc || !priceBookDesc?.wheres?.length) return void 0;

			const swapTargetDetailToFirst = (details, { field, value }) => {
				const result = {};

				for (const [objApiName, datas] of Object.entries(details)) {
					let firstItem = null;
					const others = [];

					for (const data of datas) {
						if (data[field] === value && !firstItem) {
							firstItem = data;
						} else {
							others.push(data);
						}
					}

					result[objApiName] = firstItem ? [firstItem, ...others] : others;
				}

				return result;
			}

			const filterEachDetailData = (details = {}) => {
				const delAttrs = ['product_id__ro', 'children', '_cellStatus'];
				const regex = /(__r|__l)$/;
				const shouldIncludeKey = (key) => !delAttrs.includes(key) && !regex.test(key);

				const filterDatas = (datas) => {
					return datas.map((data) => {
						const nData = {};
						Object.keys(data)
							.filter(shouldIncludeKey)
							.forEach((key) => {
								nData[key] = data[key];
							});
						return nData;
					});
				}

				const result = {};
				Object.entries(details).forEach(([objApiName, datas]) => {
					result[objApiName] = filterDatas(datas);
				});

				return result;
			}

			const pick = (details, apiName) => {
				const result = {};
				Object.entries(details)
					.filter(([key]) => key === apiName)
					.forEach(([key, value]) => {
						result[key] = value;
					})
				return result;
			}

			let result = details;
			if (pickTargetApiName) {
				result = pick(result, pickTargetApiName);
			}

			if (swapToFirstParams) {
				result = swapTargetDetailToFirst(result, swapToFirstParams);
			}

			result = filterEachDetailData(result);

			return result;
		},

		isCrmRulesNewArea() {
			return (CRM.util.getUserAttribute('crmNewArea') || CRM.util.getUserAttribute('crmAreaV3')) && CRM.util.isGrayScale('CRM_RULES_NEW_AREA');
		},

		/**
		 * @desc 从某个树节点的子级中，收集所有最后一层包含children的节点数据
		 * @param data: {name:1, children:[{name:11, children:[]}, {name:12}]}
		 * @returns {Array}
		 */
		findLastNodesWithChildren(data = {}) {
			if (!data.children) return [data];
			let result = [];
			function _fn(list) {
				for (let node of list) {
					if (node.children && node.children.length) {
						let f = node.children.find(c => c.children);
						if (!f) {
							result.push(node);
						} else {
							_fn(node.children)
						}
					}
				}
			}
			let f = data.children.find(c => c.children && c.children.length);
			if (!f) {
				result.push(data);
			} else {
				_fn(data.children);
			}
			return result;
		},

		/**
			* paas公式解析 —— 级联组件(cascader)获取翻译过得label
			* @param    {object} options 参数
			* @param    {boolean} options.data Array 和cascader组件的data数据一致 默认[]
			* @param    {boolean} options.value String 选中的值 默认[]
			* @param    {boolean} options.index Number data[index] 不传从全部查找
			*/
		getCascaderCompile({ value = [], data = [], index }) {
			let results = [...value],
				newOptions,
				hit = false;
			if (index == undefined) {
				newOptions = data;
			} else {
				let _v = data[index];
				newOptions = [_v];
			}

			const findValues = (children = [], index = 0) => {
				children.forEach(child => {
					let val = value[index] || "";
					// 兼容以前老的数据，业务类型比较api_name ,单选多选比较label
					if (
						child.value == value[index] ||
						(val + "._value" == child.value && "record_type" == value[index]) ||
						(val + "._label" == child.value && "record_type" != value[index])
					) {
						index == value.length - 1 && (hit = true);
						results[index] = child.label;
						if (child.children && child.children.length > 0) {
							findValues(child.children, ++index);
						}
					}
				});
			};
			for (let i = 0; i < newOptions.length; i++) {
				let option = newOptions[i];
				if (!hit) {
					findValues(option.children);
				} else {
					//跳出for循环
					break;
				}
			}
			return results;
		},

		/**
		 * paas公式解析 —— 公式组件(expression)获取翻译过公式
		 * @param    {object} options 参数
		 * @param    {boolean} options.varData Array 添加字段cascader组件的data数据一致 默认[]
		 * @param    {boolean} options.value String 公式
		 */
		getExpressionCompile(options = {}) {
			let { value: expression = "", var_data: data = [], varTransformFn, parseBeforeCompile, varReg } = options;
			const reg = varReg || /\$([a-zA-Z0-9_.-]*)\$/g;

			const getTransform = (val = "") => {
				if (varTransformFn) {
					return varTransformFn(val);
				}
				// 如果是数组，变量转字符串
				let _v = val.substring(1, val.length - 1);
				return _v.split("__r.");
			};

			// 如果存在解析前的处理函数，调用它进行预处理
			if (typeof parseBeforeCompile === 'function') {
				expression = parseBeforeCompile(expression);
			}

			// 使用正则表达式处理表达式字符串
			return expression.replace(reg, word => {
				const words = getTransform(word) || [];
				const labelWords = this.getCascaderCompile({ data, value: words });
				const label = labelWords.length === 0 ? words.join(".") : labelWords.join(".");
				return options.replaceFn ? options.replaceFn(label) : `${label}`;
			});
		},

		// 校验非标属性是否都有值
		checkNsAttrAllVal(nsAttrJson, nsAttrList) {
			if (!nsAttrJson) return {status: false, msg: []};
			let status = true;
			let msg = [];
			nsAttrList.forEach(item => {
				if (!CRM.util.hasValue(nsAttrJson[item.id])) {
					status = false;
					msg.push(item.name)
				}
			});
			return {
				status,
				msg,
			};
		},
        /**
         * 判断是否存在验证规则
         * @param {Object} obj
         */
        assertValidationRule(obj) {
			return obj && ((obj.blockMessages && obj.blockMessages.length) || (obj.nonBlockMessages && obj.nonBlockMessages.length));
        },
        /**
         * 组装验证规则提示文案
         * @param {Object} obj
         */
        parseValidationRule(obj) {
            var messages = [];
            if(obj.blockMessages && obj.blockMessages.length) {
                messages.push({
                    type: 'red',
                    content: obj.blockMessages.join('; ')
                })
            }
            if(obj.nonBlockMessages && obj.nonBlockMessages.length) {
                messages.push({
                    type: 'orange',
                    content: obj.nonBlockMessages.join('; ')
                })
			}

            return messages;
        },
        /**
         * 验证规则提示信息
         * @param {Array} messages
         */
        validateruleHandle(messages) {
            var htmlStr = '';
            for (let i = 0; i < messages.length; i++) {
                var item = messages[i];
                htmlStr += `<div class="crm-field-action-ruletip">
                    <div class="tip-item">
                        <span class="tip-icon tip-icon-${item.type || 'orange'}">!</span>
                        <div class="tip-content">${item.content}</div>
                    </div>
                </div>`
            };
            var isContinue = messages[0].type !== 'red';
            return new Promise((resolve, reject) => {
                if(isContinue) {
                    CRM.util.confirm(htmlStr, $t('提示'), function(e) {
                        //继续保存不再走验证规则
                        resolve(true);
                    }, {
                        btnLabel: {
                            confirm: $t('继续保存')
                        },
                        zIndex: 10010
                    })
                } else {
                    CRM.util.alert(htmlStr);
                    resolve(false);
                }
            });
        },
        /**
         * edit校验结果处理
         * @param {Object} res
         * @param {*} cb
         */
        updateResult(res, cb) {
            //编辑函数验证
            let vr = res.validationRuleMessage;
            let fr = res.funcValidateMessage;
            //命中了一些验证规则，需要提示用户
            if(this.assertValidationRule(vr)) {
                const msg = this.parseValidationRule(vr);
                this.validateruleHandle(msg).then((permiss) => {
                    return cb(permiss);
                });
            };
            //命中了验证函数规则，需要提示用户
            if(this.assertValidationRule(fr)) {
                const msg = this.parseValidationRule(fr);
                this.validateruleHandle(msg).then((permiss) => {
                    return cb(permiss);
                });
            };
            //存在审批流情况
            if(res?.writeDB === false) {
                CRM.util.alert(`<div class="crm-gantt-dialog"><p>${$t('操作失败，可能由以下原因造成:')}</p>
                <ul>
                    <li>${$t('1.触发审批流')}</li>
                    <li>${$t('2.函数验证')}</li>
                    <li>${$t('3.验证规则')}</li>
                </ul>
                <p>${$t('建议到详情页中手动编辑。')}</p></div>`, null);
                return cb(false);
            };
            if(!fr && !vr) return cb(true);
        },

		// 校验属性是否都有值
		validAttribute(tableData, notTree = false) {
			let r = true;
			tableData = Array.isArray(tableData) ? tableData : [tableData];

			function _fn(d){
				if (d.__cacheAttribute || d.attribute) {
					let sl = Object.keys(d.selectedAttr).length;
					let attr = d.__cacheAttribute || d.attribute;
					if ((!sl || sl !== attr.length)) {
						r = false;
					}
					for (let dKey in d.selectedAttr) {
						let data = d.selectedAttr[dKey];
						if (data.value_ids.length == 0) {
							r = false;
						}
					}
				}
			}

			if(notTree){
				tableData.forEach(d => _fn(d));
			}else{
				CRM.util.forEachTreeData(tableData, d =>{
					_fn(d);
				});
			}

			return r;
		},

		// 拼接多语；arr: ['手动','修改']
		getI18n(arr = []){
			let r = '';
			arr.forEach(k => {
				r += $t(k)
			});
			return r;
		},

		/**
		 * 忠诚度
		 * 根据信息和类型解析指标信息
		 * @param {Object} metricInfo 信息
		 * @param {String} metricType 类型
		 * @returns String
		 */
		parseMetricInfo: function(metricInfo, metricType) {
			if (!metricInfo) return '';
			info = typeof metricInfo === 'string' ? JSON.parse(metricInfo) : metricInfo;
            function parseAggregateData(metricInfo = {}) {
                return metricInfo.aggregate_name || '';
            }
            function parseAplData(metricInfo = {}) {
                return `${metricInfo.apl_api_name || ''}(${metricInfo.apl_name || ''})`;
            }
            function parseFieldData(metricInfo = {}) {
                return `${metricInfo.object_label || ''}/${metricInfo.field_label || ''}`;
            }
			function parseAttributeData(metricInfo = {}) {
				return metricInfo.attribute_name || '';
			}
            switch (metricType) {
                case 'aggregate':
                    return parseAggregateData(info);
                case 'apl':
                    return parseAplData(info);
				case 'attribute':
					return parseAttributeData(info);
                case 'field':
                default:
                    return parseFieldData(info);
            }
        },

		/**
		 * 忠诚度
		 * 根据字段数据和规则过滤出需要的字段
		 * @param {Object} fields 字段规则
		 * @param {Object} model 字段数据
		 * @returns
		 */
		filterIncentiveActionFields: function(fields, model) {
			const keys = Object.keys(fields);
			const { params = {} } = Object.getPrototypeOf(fields) || {};
			return keys.filter(key => {
				const field = fields[key];
				const show = field.show;
				if (show === true) return true;
				if (typeof show === 'function') return show(model, params);
				if (typeof show !== 'object') return false;
				const rulesKey = Object.keys(show);
				return rulesKey.every(key => {
					const valueSet = new Set(show[key]),
						value = model[key];
					return valueSet.has(value);
				});
			});
		},

		/**
		 * 忠诚度
		 * 根据修改记录的数据解析激励动作
		 * @param {Array} data 激励动作
		 */

		parseRecordIncentiveAction: function(data) {
			data = typeof data === 'string' ? JSON.parse(data) : data;
			function filterKeys(str, item) {
				return str.includes('_label') || typeof item[str] === 'object' || str === 'name' || str.includes('_id');
			};

			const res = data.map(item => {
				const keys = Object.keys(item);
				return keys.filter(key => {
					if (filterKeys(key, item)) {
						return false;
					} else {
						return true;
					};
				}).reduce((prev, current) => {
					const key = item[current + '_label'];
					const value = item[current + '_option_label'] || item[current + '_content'] || item[current];
					if (!!key && !!value) {
						return prev + `;${key}: ${value}`;
					}
					return prev;
				}, `${item.name_label}: ${item.name}`);
			});
			return res.toString();
		},

		// 校验非标属性值不能为空
		validNsAttr(data = [], alertMsg = false){
			let r = [];
			data = Array.isArray(data) ? data : [data];
			data.forEach(item => {
				if(item.nonstandardAttribute && item.nonstandardAttribute.length){
					if(!item.nsAttr) return r.push(item.product_id__r || item.name);
					item.nonstandardAttribute.forEach(attr => {
						if(!item.nsAttr[attr.id] || !CRM.util.hasValue(item.nsAttr[attr.id].value)) return r.push(item.product_id__r || item.name + '[' + attr.name + ']');
					})
				}
			});
			if(r.length && alertMsg) CRM.util.alert(r.join('、') + '，' + $t('非标属性值不能为空'));
			return !r.length;
		},

		// 初始化属性选中
		initDataSelectedAttrByJson(list) {
			list = Array.isArray(list) ? list: [list];
			list.forEach(data => {
				if (!data.selectedAttr && data.attribute) {
					//用已选值(二次配置)/默认值初始化
					data.selectedAttr = data.attribute.reduce((attrObj, attr) => {
						const findActive = (item, reset, valueId) => {
								return reset ? (item.id == valueId) : (item.is_default == '1')
							},
							isReset = data.attribute_json,
							valueId = isReset ? data.attribute_json[attr.id] : null,
							defaultItem = (attr.attribute_values || []).find(v =>
								findActive(v, isReset, valueId)
							);
						if(defaultItem){
							attrObj[attr.id] = {
								name: attr.name,
								value_ids: [{
									id: defaultItem.id,
									name: defaultItem.name
								}]
							};
						}
						return attrObj;
					}, {})
				}
				if(!data.nsAttr){
					data.nsAttr={};
					if(data.nonstandard_attribute_json){
						for(let key in data.nonstandard_attribute_json){
							const defaultItem= data.nonstandardAttribute.find(n=>n.id==key);
							if(!defaultItem) return;
							data.nsAttr[key]={
								value: data.nonstandard_attribute_json[key],
								name:defaultItem.name
							}
						}
					}
				}
			});
		},
		
		
		/**
		 * 基础政策数据解析：统一处理
		 * 1) 全部（ALL / All）
		 * 2) 指定范围（含 filters 的对象数组 or 字符串）
		 * 3) 指定产品列表
		 * @param {*} data 原始 data 字段
		 * @returns {String} 解析后的文本；若无法解析返回空串
		 */
		parseBasicPolicyData(data) {
			if (data === undefined || data === null) return "";
			
			data = typeof data === "string" ? JSON.parse(data) : (data || []);	
			if (Array.isArray(data)) {
				const hasCondition = data.some(d => Object.prototype.hasOwnProperty.call(d, "filters"));

				// 1 指定范围（字符串或带 filters 的数组）
				if (hasCondition) {
					return this.parseCondition(data);
				}
				// 2 指定产品列表
				const productList = data.map(d=>{
					const unit = d.unit__s ? `（${d.unit__s}）` : "";
					return `${d.product_id__s||d.product_id__r}${unit}`;
				}).join("，");
				return `【${$t("产品名称")}】：${productList}`;
				
			}
			return "";
		},

		parseUnitType(unitId){
			const specialUnitMap = {
				"large": $t("sfa.crm.pricepolicy.largeUnit"),
				"medium": $t("sfa.crm.pricepolicy.mediumUnit"),
				"small": $t("sfa.crm.pricepolicy.smallUnit"),
				"baseUnit": $t("sfa.crm.pricepolicy.baseUnit"),
				"currentProductUnit": $t("sfa.crm.pricepolicy.selfUnit"),
			}
			return specialUnitMap[unitId] || "";
		},

		// 返利单解析
		parseRebate(item,value){
			const text = this.parseBasicPolicyData(value.data);
			let unitText =""
			if(value.gift_condition_unit_id){
				const unit = this.parseUnitType(value.gift_condition_unit_id);
				unitText = unit ? `【${$t("单位")}】 ${$t("等于")} “${unit}”；` : "";
			}
			return `${unitText}${text}`;
		},
	
		// 优惠券方案解析
		parseCouponPlan(item,value){
			let { data , must_rela } = value;
			data = typeof data === "string" ? JSON.parse(data) : (data || []);	
			
			// 非复杂产品条件，走基础处理
			if(!must_rela){
				return this.parseBasicPolicyData(data);
			}
				
			/* ---------- 复杂产品条件 ---------- */
			const productDesc = data.map(d => {
				const name = d.type === "Product"
					? `【${$t("产品名称")}】${d.product_id__s||d.product_id__r}`
					: `【${$t("聚合规则")}】${d.product_id__s||d.product_id__r}`;
		
				const containTxt = d.must_contain
					? `: ${$t("必含")}--${d.contain_num}`
					: `: ${$t("非必含")}`;
		
				const unitTxt = (d.unit__s || d.unit__r)? `${d.unit__s||d.unit__r}` : "";
				return `${name} ${containTxt}${unitTxt}`;
			}).join("; ");

			const relaTxt = item.must_rela === "ALL"
			? $t("全部必含")
			: `${$t("sfa.crm.pricepolicy.mustContain")} ${value.must_num} ${$t("种")}`;
	
			const containNumTxt = `${$t("必含商品总数量")} ${$t("sfa.crm.coupon.atLeast")} ${value.must_num}`;
			const limitTotalTxt  = `${$t("产品总数量")} ${$t("sfa.crm.coupon.atLeast")} ${value.limit_total}`;
			
			return `${productDesc};\n【${$t("汇总")}】: ${relaTxt}; ${containNumTxt}; ${limitTotalTxt}`;
		},
		// 返利使用规则解析
		parseRebateRule(item,value){
			switch(item.api_name){
				case "rule_content":
					return this.parseRebateRuleContent(value);
				case "rebate_condition":
					return this.parseCondition(value?.data||[]);
				default:
					return "";
			}
		},
		/**
 		 * 返利使用规则 —— 规则内容解析
 		 * 两种计算方式：
 		 *   1) EXPRESSION  按全量表达式计算
 		 *   2) CYCLE       按“每满”阶梯计算
 		 * @param {String|Object} data
 		 * @return {String}
 		*/
		parseRebateRuleContent(data){
			// 1. 数据预处理
			if (!data) return "";
			const rule = typeof data === "string" ? JSON.parse(data) : data;
			if (!rule) return "";

			const {
				calculate_type: type = "EXPRESSION",
				expressions = [],
				cycle_info = {},
				master_condition = []
			} = rule;
			
			// 操作符映射
			const opMap = { ADD: "+", SUBTRACT: "-", MULTIPLY: "x" };

			// 字符串累加器
			let text = master_condition?.length ? this.parseCondition(master_condition) : "";
			
			/* ---------- 按全量计算 ---------- */
			if (type == "EXPRESSION") {
				// 处理 expressions 可能是 JSON 字符串的情况
				const list = typeof expressions === "string" ? JSON.parse(expressions || "[]") : expressions;
				
				const expStr = (list || []).map(e => {
					if (!e) return "";
					
					if (e.execute_type === "CONSTANT") return `<= ${e.right}`;

					const left = e.left ? `${e.left.object_api_name__s}.${e.left.field_name__s}` : "";
					return `<= ( "${left}" ${opMap[e.operator] || ""} ${e.right} )`;
				}).filter(Boolean).join("; ");
				
				return text ? `${text}; ${$t("按全量计算")} ${$t("使用额度")}: ${expStr}`
					: `${$t("按全量计算")} ${$t("使用额度")}: ${expStr}`;
			} 
			
			/* ---------- 按“每满”计算 ---------- */
			const cycles   = typeof cycle_info === "string" ? JSON.parse(cycle_info || "{}") : cycle_info;
			const cycleArr = Array.isArray(cycles.cycle_data) ? cycles.cycle_data : [];
			
			const cStr = cycleArr.map(c => {
				if (!c?.left) return "";
				const left = `${c.left.object_api_name__s}.${c.left.field_name__s}`;
				return `${left} ${$t("每满")} ${c.field_value} ${$t("使用金额")} <= ${c.used_amount}`;
			}).filter(Boolean).join("; ");
			
			let perText = `${$t("按每满计算")} ${$t("使用额度")}: ${cStr}`;
			if (CRM.util.hasValue(cycles.max_amount)) {
				perText += ` ${$t("整单金额上限")}: ${cycles.max_amount}`;
			}
			
			return text ? `${text}; ${perText}` : perText;
		},
		//价格政策解析
		parsePricePolicy(item,value){
			switch(item.api_name){
				case "rule_condition":
					return this.parseCondition(value);
				case "execution_result":
					return this.parseExecution(value);
				case "multiple_json":
					return this.parseMultipleJson(value);
				default:
					return "";
			}
		},
		parseMultipleJson(data){
			if (!Array.isArray(data)) return "";

			return data.map((item, idx) => {
				const condStr = this.parseCondition(item.rule_condition);
				const execStr = this.parseExecution(item.execution_result);
		
				// objectApiName 通常就是 _s 结尾的中文名，也可能已经包含在 execution_result 中
				// 若想显式展示，可自行决定是否再拼一次
				const objName = item.objectApiName__s || item.objectApiName || "";
		
				// 组装成 “对象: [条件] -> 规则” 这样的格式
				let part = objName ? `${objName}: ` : "";
				if (condStr) part += `${$t("规则条件")}:[${condStr}]  `;
				part += `${$t("sfa.crm.pricepolicy.executionResult")}:${execStr}`;
		
				return part;                    // 返回单条字符串
			}).filter(Boolean)
			  .join("；\n");        
		}
	}
	module.exports = project;
});
