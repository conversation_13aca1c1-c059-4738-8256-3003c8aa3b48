define(function (require, exports, module) {

    let timeShortcuts = [
        [$t('本年度'), 6],
        [$t('上一年度'), 7],
        [$t('下一年度'), 10],
        [$t('本季度'), 13],
        [$t('上一季度'), 14],
        [$t('下一季度'), 15],
        [$t('本月'), 4],
        [$t('上月'), 5],
        [$t('下月'), 8],
        [$t('本周'), 2],
        [$t('上周'), 3],
        [$t('下周'), 9],
        [$t('今天'), 11],
        [$t('昨天'), 1],
        [$t('明天'), 12]
    ];
    
    function getOperators() {
        return  {
            EQ: $t("等于"),
            N: $t("不等于"),
            GT: $t("大于"),
            GTE: $t("大于等于"),
            LT: $t("小于"),
            LTE: $t("小于等于"),
            LIKE: $t("包含"),
            NLIKE: $t("不包含"),
            IS: $t("为空"),
            ISN: $t("不为空"),
            STARTWITH: $t("起始于"),
            ENDWITH: $t("结束于"),
            IN: $t("属于"),
            NIN: $t("不属于"),
            BETWEEN: $t("介于"),
            _LT: $t('早于'), // 日期 时间 日期时间使用
            _GT: $t('晚于'), // 日期 时间 日期时间使用
            LT_DAY: $t('N天前'), //日期 日期时间适用
            GT_DAY: $t('N天后'), //日期 日期时间适用
            LT_WEEK: $t("N周前"), //日期 日期时间适用
            GT_WEEK: $t("N周后"), //日期 日期时间适用
            _LTE: $t('不早于'), // 日期 时间 日期时间使用 
            _GTE: $t('不晚于'), // 日期 时间 日期时间使用
            LTE_DAY: $t("前N天"), //日期 日期时间适用
            GTE_DAY: $t("后N天"), //日期 日期时间适用
            LTE_MONTH: $t("前N月"), //日期 日期时间适用
            GTE_MONTH: $t("后N月"), //日期 日期时间适用
            HASANYOF: $t("属于"),
            NHASANYOF: $t("不属于"),
        };
    }
    
    // 分配规则解析
    function parseAllocateRule(allocateRule, fields, personelFields, isOpenPRM) {
        let rule = allocateRule.RuleFilterList;
        let memberRule = allocateRule.RuleFilterGroupList || [];
        let memberRuleText = parseFiltersRule(memberRule, personelFields);
        let pData = {
            ruleText: parseFiltersRule(rule, fields),
            memberText: parseAllocateMember(allocateRule.MemberList, memberRuleText, isOpenPRM),
        }
        return pData;
    }
    // 收回规则解析
    function parseRecycleRule(recycleRule, fields, apiname) {
        let rule = recycleRule.RecyclingFilterList || (recycleRule.wheres && JSON.parse(recycleRule.wheres));
        let pData = {
            ruleText: parseFilterRule(rule, fields),
			scopeText: parseScope(recycleRule, apiname),
        }
        return pData;
    }
    // 解析有或关系的数据格式
    function parseFiltersRule(rule, fields) {
        if (rule && _.isString(rule)) rule = JSON.parse(rule);
        if (!rule || !rule.length) return '';
        let rows = [];
        for (let i = 0; i < rule.length; i++) {
            let condition = rule[i];
            let text = parseFilterRule(condition, fields);
            if (i == 0) {
                rows.push(`<p>${text};</p>`)
            } else {
                rows.push(`<p>${$t('或')}: ${text};</p>`)
            }
        }
        return rows.join('');
    }
    // 解析只有且关系的数据
    function parseFilterRule(rule, fields) {
        if (rule && _.isString(rule)) rule = JSON.parse(rule);
        if (!rule.filters || !rule.filters.length) return '';
        let row = [];
        _.each(rule.filters, (filter) => {
            filter = hackORule(filter);
            if (!fields) {
                let _label = filter.field_name__s || filter.field_name;
                let _text = filter.field_values__s || filter.field_values.join($t('，'));
                let operator = filter.operator_name ? $t(filter.operator_name) : parseOperator(filter);
                row.push(`"${_label}" ${operator} ${_text ? '"'+ _text + '"' : ''}`);
                return;
            }   
            let field = _getField(fields, filter.field_name);
            if (field) {
                let text = parseFieldValue(field, filter.field_values, filter.operator);
                text = adaptorValue(text, filter.operator);
                filter.type = field.type;
                let operator = parseOperator(filter);
                row.push(`"${field.label}" ${operator} ${text ? '"'+ text + '"' : ''}`)
            }
        })
        return row.join(`${$t('，')}${$t('且')} `);
    }

    function adaptorValue(value, compare) {
        return CRM.util.isEmptyValue(value) ? (_.contains(['IS', 'ISN', 9, 10], compare) ? '' : '<span style="color:red;">--</span>') : value;
    }

    // 字段解析
    function parseFieldValue(field, value, compare, oValue) {
        var item;
        value = _.isArray(value) ? value : [value];
        if (['date', 'date_time', 'time'].indexOf(field.type) != -1) {
            if (['BETWEEN', 'CUSTOM'].indexOf(compare) != -1) {
                if (value[0].indexOf('|') != -1) {
                    value = value[0].split('|')
                }  
                // 快捷时间方式
                if (value.length < 2 && (value[0] + '').length < 3) {
                    let _tx = '';
                    timeShortcuts.forEach((item) => {
                        if (_.indexOf(item, value[0]*1) > -1) {
                            return _tx = item[0];
                        }
                    }) 
                    return _tx;
                }
                
                let _pts = {
                    time: 'HH:mm',
                    date: 'YYYY-MM-DD',
                    // date_time: 'YYYY-MM-DD HH:mm',
                }
                let _pt = _pts[field.type] || 'YYYY-MM-DD';
                return FS.moment(value[0] * 1,null,null,null,true).format(_pt) + '~' + FS.moment(value[1] * 1,null,null,null,true).format(_pt);
            } else if (value[0]) {
                let _pts = {
                    time: 'HH:mm',
                    date: 'YYYY-MM-DD',
                    date_time: 'YYYY-MM-DD HH:mm',
                }
                let _pt = _pts[field.type] || 'YYYY-MM-DD';
                return FS.moment(value[0] * 1,null,null,null,true).format(_pt);
            }
        } else if (['select_one', 'select_many', 'record_type', 'true_or_false'].indexOf(field.type) != -1) {
            let data = value.map((v, index) => {
                if (['record_type'].indexOf(field.type) != -1) {
                    item = _.findWhere(_stringTobool(field.options), {api_name: _stringTobool(v)});
                } else {
                    item = _.findWhere(_stringTobool(field.options), {value: _stringTobool(v)});
                }
                if (item) {
                    return item.label;
                }
            })
            let _link = $t("或");
            if (field.type == 'select_many' && !_.contains(['IN', 'NIN', 13, 14], compare)) {
                _link = $t("和");
            }
            return _.compact(data).join('"' + _link + '"');
        } else if (['country', 'province', 'city', 'district'].indexOf(field.type) != -1) {
            let data = value.map((v, index) => {
                // 旧版省市区数据缓存
                item = _.findWhere((CRM.get('country_area_options') && CRM.get('country_area_options')[field.type].options) || [], {
                    value: v
                });
                // 灰度了新版省市区
                // if (CRM.util.isCrmRulesNewArea()) {
                if (!item){
                    // 取新版省市区数据缓存
                    item = CRM.get('areaCache') && CRM.get('areaCache')[v];
                }
                if (item) {
                    return item.label;
                }
            })

            return _.compact(data).join('"' + $t("或") + '"');
        } else if (['employee', 'department'].indexOf(field.type) != -1) {
            let nameType = field.type == 'department' ? 'g' : 'p',
                // 创建新数组，去掉所有_y后缀，不修改原始数据 
                processedValue = value.map(v => v.endsWith && v.endsWith('_y') ? v.slice(0, -2) : v),
                nameV = CRM.util.getNameByIds(processedValue, nameType, true);
            return nameV
        } else if (['exemployee', 'exenterprise'].indexOf(field.type) != -1) {
            let nameType = field.type == 'exenterprise' ? 'en' : 'em',
                infos = CRM.util.getExContactByIds(value, nameType),
                names = _.pluck(infos, 'name');
            return names.join($t("，"));
        } else if (oValue && field.type === 'object_reference') {
            return _.pluck(oValue, 'name').join($t("，"));
        }
        return _.isArray(value) ? value.join($t("，")) : value;
    }

    // 比较符解析
    function parseOperator(filter) {
        let _operator;
        if (_.contains(['date', 'date_time'], filter.type) && filter.field_values && _.contains(['day', 'week', 'month'], filter.field_values[0])) {
            _operator = filter.operator + '_' + filter.field_values[0].toUpperCase();
        } else if(_.contains(['date', 'time', 'date_time'], filter.type)) {
            _operator = '_' + filter.operator;
        }
        let operators = getOperators();
        return operators[_operator] || operators[filter.operator];
    }

    // 范围解析
    function parseScope(ruleData, apiname) {
        var arr = [],
            text = '';
        let type = ruleData.RecyclingRuleType || ruleData.recycling_rule_type; 
        let deal = ruleData.DealDays || ruleData.deal_days;
        let follow = ruleData.FollowUpDays || ruleData.follow_up_days;
		let	remindday = (ruleData.RecyclingRemindRuleList && ruleData.RecyclingRemindRuleList[0] && ruleData.RecyclingRemindRuleList[0].RemindDays) || (ruleData.recycling_remind_rule_list && ruleData.recycling_remind_rule_list[0] && ruleData.recycling_remind_rule_list[0].remind_days);

        let data = {
            type: '不自动回收',  //[ignore-i18n]
            deal: '没有新的成交',  //[ignore-i18n]
            follow: '未跟进',  //[ignore-i18n]
            text: '自动收回到{{pool}}',  //[ignore-i18n]
            remindday: '提醒{{objectName}}负责人及相关团队'  //[ignore-i18n]
        }
        if (apiname == 'LeadsObj') {
            data = _.extend(data, {
                deal: '未转换',  //[ignore-i18n]
                follow: '未跟进',  //[ignore-i18n]
            })
        }

        if (type == 1) {
            return $t(data.type);
        }

        if (deal > 0) {
            if (apiname == 'LeadsObj') {
                arr.push($t(`{{day}}天{{hours}}小时${data.deal}`, {
                    day: Math.floor(deal / 24),
                    hours: deal % 24
                }))
            } else {
                arr.push($t(`{{deal}}天${data.deal}`, {
                    deal: deal
                }));
            }
        }
        if (follow > 0) {
            if (apiname == 'LeadsObj') {
                arr.push($t(`{{day}}天{{hours}}小时${data.follow}`, {
                    day: Math.floor(follow / 24),
                    hours: follow % 24
                }))
            } else {
                arr.push($t(`{{follow}}天${data.follow}`, {
                    follow: follow
                }));
            }

        }
        text = arr.join($t("或")) + '，' + $t(data.text);
        if (remindday > 0) {
            if (apiname == 'LeadsObj') {
                text = text + '；' + $t(`提前{{day}}天{{hours}}小时${data.remindday}`, {
                    day: Math.floor(remindday / 24),
                    hours: remindday % 24,
                    objectName: $t('crm.销售线索'),
                });
            } else {
                text = text + '；' + $t(`提前{{remindday}}天${data.remindday}`, {
                    remindday: remindday,
                    objectName: $t('crm.客户'),
                });
            }

        }
        let config = {
            AccountObj: {
                text: $t('crm.公海'),
                text_zh: $t('公海'),
            },
            LeadsObj: {
                text: $t('crm.线索池'),
                text_zh: $t('线索池'),
            }
        }
        let _pool = $t('本{{pool}}', {pool: config[apiname].text});
        let _tName = ruleData.HighSeasName  || ruleData.target_pool_name;
        let _tId = ruleData.HighSeasID || ruleData.target_pool_id;
        let _dId = ruleData.DataId || ruleData.DataID || ruleData.data_id;
        if (_tId && _tId != _dId) {
            _pool = $t('其他{{pool}}', {
                pool: config[apiname].text
            }) + (_tName ? '：' + _tName : '');
        }
        text = text.replace('{{pool}}', _pool);
        return text;
    }

    function parseAllocateMember(memberList, memberRuleText, isOpenPRM) {
        let memberTextObj = parseMemberList(memberList);
        let memberInContactText = memberTextObj.inContact.join('、');
        let memberExContactText = memberTextObj.exContact.join('、');
        let _arr = [];
        if (!isOpenPRM) {
            memberInContactText && _arr.push(memberInContactText);
            memberRuleText && _arr.push(memberRuleText);
        } else {
            if (memberInContactText) {
                _arr.push(`<p>${$t('内部：')}${memberInContactText}</p>`);
            }
            memberRuleText && _arr.push(memberRuleText);
            if (memberExContactText) {
                _arr.push(`<p>${$t('外部：')}${memberExContactText}</p>`);
            }
        }
        return _arr.join('');
    }

    function parseMemberList(memberList) {
        let obj = {
            inContact: [],
            exContact: [],
        };
        let outer = _.extend({
            contacts: [],
            contactsObject: {},
            employeesObject: {},
        }, CRM.get('outer'));
        _.each(memberList, function (item) {
            switch(item.MemberType) {
                case 1:
                    let _a = CRM.util.getEmployeeById(item.MemberID);
                    _a && _a.name && (obj.inContact.push(_a.name));
                    break;
                case 2:
                    let _b = CRM.util.getCircleById(item.MemberID);
                    _b && _b.name && (obj.inContact.push(_b.name));
                    break;
                case 3:
                    let _c = outer.contactsObject[item.MemberID];
                    _c && _c.name && (obj.exContact.push(_c.name));
                    break;
                case 4:
                    let _d = outer.employeesObject[item.MemberID];
                    _d && _d.name && (obj.exContact.push(_d.name));
                    break;
                case 5:
                    let _e = CRM.util.getUserGroupByIds(item.MemberID)[0];
                    _e && _e.name && (obj.inContact.push(_e.name));
                    break;
                case 6:
                    let _f = CRM.util.getRoleByIds(item.MemberID)[0];
                    _f && _f.name && (obj.inContact.push(_f.name));
                    break;
            }
        });
        return obj;
    }

    function _stringTobool(data, key) {
        if (_.isArray(data)) {
            return data.map((option) => {
                if (option[key] === 'false') {
                    option[key] = false;
                } else if (option[key] === 'true') {
                    option[key] = true;
                }
                return option;
            })
        }
        if (_.isString(data)) {
            if (data === 'false') {
                data = false;
            } else if (data === 'true') {
                data = true;
            }
        }
        return data;
    }

    function _getField(fields, apiname) {
        let _field = {};
        if (_.isArray(fields)) {
            _field = _.find(fields, function (fl) {
                return fl.api_name == apiname;
            });
        } else {
            _field = fields[apiname];
        }
        if (_field && _field.api_name == 'out_owner') {
            _field.type = 'exemployee';
        }
        return _field;
    }

    function hackORule(rule) {
        rule.field_values = _.isString(rule.field_values) && rule.field_values.includes('|') ? rule.field_values.split('|') : rule.field_values;
        rule.field_values = _.isArray(rule.field_values) ? rule.field_values : [rule.field_values];
        rule.operator = rule.operator == 'NEQ' ? 'N' : rule.operator;
        rule.operator = rule.operator == 'CUSTOM' ? 'BETWEEN' : rule.operator;
        return rule;
    }

    let qirOperatorMap = {
        precise: $t('任一命中'),
        combined: $t('同时命中'),
    }
    // 解析质检设置的关键词规则
    function parseQIRRule(data) {

        if (_.isString(data)) {
            try {
                data = data ? JSON.parse(data) : [];
            } catch(err){}
        }
        let _htmls = [];
        _.each(data, (item) => {
            if (qirOperatorMap[item.operator]){
                _htmls.push(qirOperatorMap[item.operator] + '：' + item.value.join(' '));
            }
        })
        let _html = _htmls.join(' '+$t('或') + ' ');
        return _html;
    }

    /**
     * 获取规则中国家省市区code
     * @param {String/Object/Array} rule 
     * @returns {Array} area
     */
    function getRuleAreaCode(rule) {
        if (!rule || rule === 'null') return [];
        if (_.isString(rule)) {
            try {
                rule = JSON.parse(rule);
            } catch(err) {}
        }
        let area = [];
        rule = _.isArray(rule) ? rule : [rule];
        _.each(rule, (condition) => {
            _.each(condition.filters, (filter) => {
                // provice兼容错误数据
                if (_.contains(['country', 'province','provice', 'city', 'district', 'town'], filter.type)){
                    let values = _.isArray(filter.field_values) ? filter.field_values: [filter.field_values];
                    area = _.union(area, values);
                }
            })
        })
        return area;
    }

    /**
     * 获取规则列表中的国家省市区code
     * @param {Array} ruleList 
     * @param {String} key 
     * @returns {Array}
     */
    function getRuleListAreaCode(ruleList, key) {
        if (!ruleList) return [];
        let area = [];
        key = key || 'wheres';
        _.each(ruleList, (item) => { 
            let _area = getRuleAreaCode(item[key]);
            area = _.union(area, _area);
        })
        return area;
    }

    // 解析风险预警规则的提醒内容
    function parseERNotice(data, apiname) {
        let param = {
            describe_apiname: apiname,
            include_layout: false,
            include_related_list: false,
            get_label_direct: true,
            include_describe_extra: false
        };
        return CRM.util.FHHApi({
            url: "/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName",
            data: param
        }).then(({Value}) => {
            return {
                label: data.replace(/\$\{(.*?)\}/g, function(match, variable) {
                        return Value.objectDescribe.fields[variable].label;
                    }),
                html: data.replace(/\$\{(.*?)\}/g, function(match, variable) {
                    const label = Value.objectDescribe.fields[variable].label;
                    return `<span contenteditable="false" class="editor-var" data-varid="${variable}">\$\{${label}\}</span>`;
                }),
            }
        });
    }

    module.exports = {
        parseAllocateRule: parseAllocateRule,
        parseRecycleRule: parseRecycleRule,
        parseFiltersRule: parseFiltersRule,
        parseFilterRule: parseFilterRule,
        parseFieldValue: parseFieldValue,
        getOperators: getOperators,
        parseOperator: parseOperator,
        parseScope: parseScope,
        parseMemberList: parseMemberList,
        parseAllocateMember: parseAllocateMember,
        hackORule: hackORule,
        parseQIRRule: parseQIRRule,
        getRuleAreaCode: getRuleAreaCode,
        getRuleListAreaCode: getRuleListAreaCode,
        parseERNotice: parseERNotice,
    }
})