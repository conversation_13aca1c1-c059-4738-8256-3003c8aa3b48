/**
 * @Desc: 属性 相关公共方法
 * @author: lishsh
 * @date: 24/8/29
*/
define(function (require, exports, module) {
    var project = {
        /**
         * 根绝filter条件遍历树
         * @param {Object} data 
         * @param {Function} filter 遍历条件
         * @param {Function} cb 回调
         * @returns 
         */
        getLeafNode: function (data, filter, cb) {
            if (!data || !filter || !cb) {
                return;
            }
            if (filter(data)) {
                cb && cb(data);
                // return;
            }
            data.children && data.children.forEach(item => {
                this.getLeafNode(item, filter, cb);
            })
        },
        /**
         * 获取属性级联约束规则
         * @param {Object} param 
         * @returns 
         */
        queryAttributeConstraintById(param = {id: ''}) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/quoter_sfa/service/query_by_id',
                    data: param,
                    success(res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result.FailureMessage);
                    },
                    error(res) {
                        reject(res.Result && res.Result.FailureMessage);
                    }
                }, {errorAlertModel: 1});
            });
        },
       /**
        * 根据属性约束规则 获取属性&值
        * @param {Array} list 属性约束规则
        * @returns {Map} 属性及属性值数据
        */
        async getAttributeConstraintAttrsMap(list) {
            let _val = CRM.util.deepClone(list);
            // 编辑时，批量获取选中的属性和值
            let seletedAttr = {};
            let seletedNsAttr = {};
            _val.forEach(item => {
                this.getLeafNode(item, (data) => {
                    return data.nodeType === 1;
                }, (data) => {
                    let _attr_id = data.values[0];
                    _attr_id && (data.isStandardAttribute
                        ? seletedAttr[_attr_id] = true
                        : seletedNsAttr[_attr_id] = true);
                })
            })
            let attrIds = Object.keys(seletedAttr);
            let nsAttrIds = Object.keys(seletedNsAttr);

            return await this.batchFetchAttributeObjectData(attrIds, nsAttrIds)
            
        },
        
        /**
         * 
         * @param {Array} attrIds 标准属性id
         * @param {Array} nsAttrIds 非标属性id
         * @returns {Map} attrsMap
         */
        batchFetchAttributeObjectData(attrIds, nsAttrIds, options = {
            fetchAttributeObj: null,
            returnType: 'object' // array
        }) {
            let arr = [];
            let _deletedAttrs = [];
            if (!attrIds.length && !nsAttrIds.length) return null;
            if (attrIds?.length) {
                _deletedAttrs = _deletedAttrs.concat(attrIds)
            }
            if (nsAttrIds?.length) {
                _deletedAttrs = _deletedAttrs.concat(nsAttrIds)
            }
            if (options.fetchAttributeObj) {
                arr.push(options.fetchAttributeObj())
            } else {
                arr.push(this.fetchAttributeObjectDataList('AttributeObj', attrIds));
            }
            arr.push(this.fetchAttributeObjectDataList('AttributeValueObj', attrIds));
            arr.push(this.fetchAttributeObjectDataList('NonstandardAttributeObj', nsAttrIds));
            
            return Promise.all(arr).then(([attrData, attrValueData, NsAttrData]) => {
                let attrsMap = {};
                if (attrData && attrValueData) {
                    let _valMap = attrValueData.reduce((res, item) => {
                        // let data = _.pick(item, ['attribute_id', '_id', 'name', 'is_default']);
                        let data = item;
                        res[item.attribute_id] ? res[item.attribute_id].push(data) : (res[item.attribute_id] = [data]);
                        return res;
                    }, {})
                    attrsMap = attrData.reduce((res, item) => {
                        let idx = _deletedAttrs.indexOf(item._id);
                        idx > -1 && _deletedAttrs.splice(idx, 1);
                        res[item._id] = {
                            ...item,
                            // ..._.pick(item, ['_id', 'name', 'field_num', 'object_describe_api_name', '']),
                            children: _valMap[item._id]?.sort((a, b) => a.order_field - b.order_field)
                        }
                        return res;
                    }, {})
                }
                if (NsAttrData) {
                    NsAttrData.forEach(item => {
                        let idx = _deletedAttrs.indexOf(item._id);
                        idx > -1 && _deletedAttrs.splice(idx, 1);
                        attrsMap[item._id] = 
                        item 
                        //_.pick(item, ['_id', 'name', 'object_describe_api_name']);
                    })
                }
                console.log('不存在的属性id', _deletedAttrs);
                if (_deletedAttrs.length) {
                    _deletedAttrs.forEach(item => {
                        attrsMap[item] = {
                            is_deleted: true,
                            name: '',
                            _id: item,
                            children: []
                        }
                    })
                }
                return options.returnType == 'object' ? attrsMap : Object.values(attrsMap);
            }).catch(err => {
                return null;
            })
        },
        /**
         * 根据属性id, 获取属性对象列表数据
         * @param {String} apiname 对象apiname
         * @param {Array} attr_ids 属性id
         */
        async fetchAttributeObjectDataList(apiname, attr_ids) {
            let _children = null;
            if (!attr_ids || !attr_ids.length) {
                return _children;
            }
            let p = {
                search_query_info: JSON.stringify({
                    limit: 6000,
                    offset: 0,
                    orders: [{ fieldName: 'create_time', isAsc: true }],
                    filters: [{
                        field_name: apiname !== 'AttributeValueObj' ? '_id' : 'attribute_id',
                        field_values: attr_ids,
                        operator: 'IN' // 包含
                    }]
                })
            };
            // 请求
            let res = await this.fetchObjList(apiname, p);
            _children = res?.dataList.map((item) => {
                let { object_describe_api_name, attribute_id, _id, name, name__r, field_num, is_default, order_field } = item;
                return {
                    groupId: item.attribute_group_id                        ,
                    groupName: item.attribute_group_id__r,
                    groupNo: item.attribute_group_serial_no,
                    object_describe_api_name,
                    attribute_id,
                    _id,
                    name: item.name__r || item.name,
                    name__r,
                    field_num,
                    is_default,
                    order_field
                }
            });
            return _children;
        },
        /**
         * 格式化属性 根据分组排序
         * @param {Array} attrList 
         * @param {Function} parseDataBefore 
         * @returns {Array}
         */
        sortAttrByGroup(attrList, {parseDataBefore = null, attr_id = 'id'} = {}) {
            let groups = {};
            let hasAttrNo = attrList.length && attrList[0].hasOwnProperty('attrNo');
            attrList.forEach((item, idx) => {
                // parseDataBefore && parseDataBefore(item);
                let groupId = item.groupId || '99999999';
                let groupNo = item.groupId ? item.groupNo : 99999999;
                let groupName = item.groupName || $t('crm.attr_no_group', null, '未分组');

                item.name = item.name__r || item.name;
                if (!item.groupId) {
                    item.groupId = groupId;
                    item.groupNo = groupNo;
                    item.groupName = groupName;
                }
                let isStandardAttribute = item.object_describe_api_name === 'AttributeObj' || item.isStandardAttribute;
                if (groups[groupId]) {
                    // 去重
                    let findData = groups[groupId].options.find(o => o[attr_id] === item[attr_id]);
                    if (findData) return;
                    groups[groupId].options.push(item);
                    isStandardAttribute 
                    ? groups[groupId].attrList.push(item)
                    : groups[groupId].nonAttrList.push(item)
                } else {
                    groups[groupId] = {
                        groupId,
                        groupName,
                        groupNo, 
                        options: [item],
                        attrList: isStandardAttribute ? [item] : [],
                        nonAttrList: !isStandardAttribute ? [item] : []
                    };
                }
                if (hasAttrNo) {
                    groups[groupId].options.sort((a, b) => a.attrNo - b.attrNo)
                }
            })
            let res = Object.values(groups);
            return res.sort((a, b) => a.groupNo - b.groupNo);
        },
        // 保存属性范围
        submitAttributeRange(param = {dataId: '', apiName: '', dataList: []}) {
            CRM.util.showLoading_new();
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/attribute/service/attributeRange',
                    data: param,
                    success(res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            // isSuccess
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result.FailureMessage);
                    },
                    error(res) {
                        CRM.util.hideLoading_new();
                        reject(res.Result && res.Result.FailureMessage);
                    }
                }, {errorAlertModel: 1});
            });
        },
        // 获取属性范围
        fetchAttributeRange(param = {dataIds, apiName}) {
            return new Promise((resolve, reject) => {
                CRM.util.showLoading_new();
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/attribute/service/queryAttributeRange',
                    data: param,
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result.FailureMessage);
                        CRM.util.alert(res.Result.FailureMessage);
                    },
                    error(res) {
                        CRM.util.hideLoading_new();
                        reject(res.Result && res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1,
                });
            })
        },
        // 获取cpq高阶版
        getCpqAttributeLicense() {
            return new Promise((resolve) => {
                CRM.api.get_licenses({
                    key: ['cpq_attribute_app'],
                    cb: (licenses) => {
                        resolve(licenses.cpq_attribute_app)
                    }
                })
            })
        },
        /**
         * 产品列表 根据分类id获取筛选属性及默认值
         * @param {String} id 分类id
         * @returns {Array} 属性列表
         */
        getAttributeByCategory(id) {
			return new Promise((resolve, reject) => {
				CRM.util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/attribute/service/getAttributeAndValueByCategoryId',
					data: {
						categoryId: id
					},
					success: function (res) {
						if (res.Result.StatusCode === 0) {
							let result = res.Value && res.Value.attributes;
							if (!result || result.length <= 0) {
								result = [];
							}
							resolve(result)
							return
						}
						CRM.util.alert(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1,
				});
			})
		},
    }
    module.exports = project;
})
