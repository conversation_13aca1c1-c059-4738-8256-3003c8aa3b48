<div style="display:inline-block;font-weight:700;font-size:13px;white-space:nowrap;" class="tb-cell{{showHelpTip && item.help_text ? ' tb-cell-help' : ''}}">
  ## var _t = item.data && _.escape(item.title) ##
  ## var hasBatchEdit = item.isEdit && !item.disabledEdit && !item.noSupportBatchEdit && !item.clearOnly ##
  {{item.showRequiredTip ? '<em class="required-tip">*</em>' : ''}}
  {{item.titleHtml ? item.titleHtml : ''}}
  {{item.data ? '<span class="icon-title">' + _t + '</span>' : item.title}}
  ## if(showHelpTip && item.help_text) { ##
    <span class="ico-help el-icon-question"></span>
  ## } ##
  ## if(item.isOrderBy) { ##
    <span style="display:inline-block;position:static;width:16px;" class="ico-sort" data-sortval="1,2"></span>
  ## } ##
  ## if(
    (item.fixed && item.data && !item.noSupportLock) ||
    (!item.fixed && !item.noLock)
  ) { ##
    <span style="display:inline-block;position:static;" class="ico-th ico-lock fx-icon-lock"></span>
  ## } ##
  ## if(item.isFilter) { ##
      <span style="display:inline-block;position:static;" class="ico-operate fx-icon-filter ico-th"></span>
  ## } ##
  ## if(hasBatchEdit) { ##
      <span style="display:inline-block;position:static;" class="ico-batchEdit ico-th el-icon-edit"></span>
  ## } ##
  {{item.colResize && !item.nocolResize ? '<span class="resize-line"></span>' : ''}}
</div>