/**
 * 计算单元格内容实际宽度
 */
define(function (require, exports, module) {
  var helper = require('./common/helper');
	var dynamicColWidthTpl = require('./template/dynamic-col-width-html');

  const keepAtMostNProperties = (obj, n) => {
    const keys = Object.keys(obj);
    const len = keys.length;
    if (len <= n) return;

    // Fisher-Yates 洗牌
    for (let i = len - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [keys[i], keys[j]] = [keys[j], keys[i]];
    }

    // 需要删除的 key
    for (let i = n; i < len; i++) {
      delete obj[keys[i]];
    }
  };

  const requestIdleTask = (task) => {
    if (window.requestIdleCallback) {
      window.requestIdleCallback(task);
      return;
    }
    // fallback
    setTimeout(task, 20);
  };

  return {
    _measureColumnWidth: function(column) {
			if (!column) {
				return;
			}

			if (!this._$colSizer || this._$colSizer.length !== 1) {
				this._$colSizer = $(`
					<div class="dynamic-col-width" style="height:0;opacity:0;position:absolute;z-index:-100;pointer-events:none;left:-9999px;top:-9999px"></div>
				`);
				this._$colSizer.cache = CRM.getLocal('table-columns-width-calc-cache') || {};
				this.$el.append(this._$colSizer);
				requestIdleTask(() => {
					const cache = this._$colSizer.cache;
					this._$colSizer.remove();
					this._$colSizer = null;
					keepAtMostNProperties(cache, 200);
					CRM.setLocal('table-columns-width-calc-cache', cache);
				});
			}

			const label = _.escape(column.title);
			const cache = this._$colSizer.cache;

			// 缓存命中
			if (cache && label && cache[label]) {
				return cache[label];
			}

			let $dynamicCol;
			try {
				$dynamicCol = $(dynamicColWidthTpl({
					item: column,
					showHelpTip: this.options.showHelpTip,
				}));
			} catch (error) {
				if (this._$colSizer) {
					this._$colSizer.remove();
					this._$colSizer = null;
				}
				console.error(error);
			}

			if (!$dynamicCol) {
				return;
			}

			if (this._$colSizer) {
				this._$colSizer.html($dynamicCol);
			}

			let contentWidth = $dynamicCol.outerWidth();
			// if (contentWidth) {
			// 	// contentWidth += 40; // 40 是右边icon大概所占的空间
			// }

			cache[label] = contentWidth;

			return contentWidth;
		},

		_initMeasureWidth: function(column) {
			column.needsMeasureWidth = true;
			column.measuredWidth = this._getColumnMeasuredWidth(column);
		},
  
    _getColumnMeasuredWidth: function(column) {
			if (column.isHidden) return;

			if (column.measuredWidth) return column.measuredWidth;

			const defaultWidth = helper.getTdWidth(column.dataType) || 100;
			const contentWidth = this._measureColumnWidth(column) || 0;
			return Math.max(contentWidth, defaultWidth);
		},

		_ensureColumnMeasuredWidthIfNeeded: function(column) {
			if (column.measuredWidth) return;

			if (column.needsMeasureWidth) {
				column.measuredWidth = this._measureColumnWidth(column);
			}
		},

    _cleanMeasureWidth: function(column) {
      column.needsMeasureWidth = false;
      column.measuredWidth = void 0;
    },
  };
});