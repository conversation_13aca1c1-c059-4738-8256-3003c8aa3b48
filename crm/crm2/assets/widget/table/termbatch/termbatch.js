/**
 * @desc 检索批量区域
 * 检索 高级检索 标签管理
 * 批量按钮渲染
 * 筛选按钮 筛选区域渲染
 * @param searchTerm
 * {
        pos:    'C',
        title: '场景',           // 标题
        type: '',                // 检索时提交请求的字段 支持QueryInfo.FilterMainID形式以 逗号区分
        showManage: true,        // 显示标签管理
        showCustom: true,        // 显示自定义高级筛选
        defaultValue: '',        // 默认值的id
        options: [{              // 检索项
            id: '',              // 检索id
            name: '',            // 检索名称
            type: '1',           // 允许编辑删除 1不允许 2 允许
            isNotRequest: false  // 不允许请求 为true时 触发term.change事件 但不不请求数据
        }]
    }
   @param batchBtns
    [{
        text: '',        // 按钮文本
        className: '',   // 按钮样式
        attrs:    '',    // 属性
        isHidden: false  // 是否隐藏兼容老代码的 如果为true 会过滤掉本条数据
        isFold: false    // 是否折叠
    }]
   @param otherBtns 同batchBtns  不存在isHidden isFold属性
   @param termBatchComplete 渲染完成回调函数
   @param showFilerBtn     是否显示过滤按钮

   事件
   otherbtn.change 其他按钮改变事件
   term.change     检索切换事件
 */

define(function (require, exports, module) {
    var doc          = document,
        util         = CRM.util,
        DropDown     = require('crm-assets/widget/dropdown/dropdown'),
        Term         = require('./term/term'),
        Term20       = require('./term/term20'),
        Filter       = require('./filter/filter'),
        InlineFilter = require('./filter/inlinefilter'),
        SetColumn    = require('./setcolumn/setcolumn'),
        mainTpl      = require('./template/main-html'),
        itemTpl      = require('./template/item-html'),
        TagFilter    = require('./filter/tagfilter');

    var OutFilter = require('./outfilter/outfilter');
    var Model = require('./model');

    var TermBatch = Backbone.View.extend({

        options: {
			FilterClass: null ,         // 重写筛选类
            searchTerm: null,
            batchBtns:  [],             // 批量按钮
            showBatchBtns: false,       // 是否显示批量操作按钮
            otherBtns:  [],             // 批量按钮后显示其他按钮
            showFilerBtn: true,         // 是否显示过滤按钮
            filterOr:     false,        // 搜索支持或关系
            showMoreBtn: true,          // 是否显示设置列按钮
            columns:    null,           // 所有列

            hideTermNum: false,         // 不显示场景总数据量

            filterAloneColumns:  null,  // 筛选中独立的列
            filterColumns: null,        // 筛选中条件的列
            filterDatas:   null,        // 筛选的默认数据

            addColumns: null,           // 新建的所有条件列
            addOrderColumn: null,

            orderColumn: null,

            orderColumns: null,

            termBatchComplete: null,    // 渲染完成回调函数
            alwaysShowTermBatch: false, // 始终显示检索区域 由于扩展配置条件
            separateCheckCount: false, // 始终显示选中数量
            zIndex: 1000,
            supportMultiFieldSort: false, // 是否支持多字段排序

			isSimpleTable: false
        },

        initialize: function() {
            this.model = new Model(this.options);
            this.options.showTagBtn && this.options.filterColumns && this.options.filterColumns.unshift(this.model.get('tagColunm'));
            this._renderMain();
        },

        set: function(key, value) {
            this.options[key] = value;
        },

        isCrossFilters: function() {
            return !!this.options.crossFilterService;
        },

        events: {
          'click .other-con span':   '_onOtherItem'     // 点击其他按钮时的操作事件
        },

        /**
         * @desc 渲染主体
         */
        _renderMain: function() {
            var opts = this.options;
            this.$el.html(mainTpl(_.extend({
                showTagBtn: false,
                showTableConfig: !opts.hideIconSet && (opts.showMoreBtn || opts.showSize || opts.showPage),
                refreshCallBack: ''
            }, opts)));
            var $cols = this.$('.j-cols');
            console.log('$cols', $cols)
            opts.termBatchComplete && opts.termBatchComplete(this.$el);

            this.$termCols = this.$('.j-cols');
            this.$termCols.hide();

            this._initTerm();
            this._initBatchOperate();
            this._initFilter();
            this._initMultiFieldSort();
            this._initSetColumns();
            this._initTagFilter();
            this._initOutFilter();
            //this._initAllCheckData();
        },

        /**
         * @desc 渲染批量操作
         */
        _initBatchOperate: function() {
            var superRoot = this;
            var separateCheckCount = this.options.separateCheckCount;
            var batchBtns = _.filter(this.options.batchBtns || [], function(a) {return !a.isHidden});
            
            if (!batchBtns.length && !separateCheckCount) {
                return;
            }

            var isCustom = _.findWhere(batchBtns, {isCommon: true}); //自定义对象通用按钮 触发方式不同
            var foldBtns = [];
            var visibleBtns = [];
            if(!isCustom) {
                _.each(batchBtns, function(item) {
                    if (!item.isFold) {
                        visibleBtns.push(`<span class="crm-btn ${item.className}" ${item.attrs}>${item.text}</span>`);
                    } else {
                        foldBtns.push(item);
                    }
                })
            }
            superRoot.batchComp = FxUI.create({
                wrapper: this.options.$batchOpereate[0],
                template: `
                    <div v-show="dShow || dForceShow" ref="batchitem" class="item batch-item">
                        <span v-html="cCheckNum" class="item-tit j-show__allchecked"></span>
                        <span v-if="!dForceShow" @click="hide" class="dt-close-batch el-icon-close" title="${$t('关闭')}"></span>
                        <div ref="batchcon" v-if="!onlyShowCount && dCustom" class="item-con batch-con"></div>
                        <div ref="batchcon1" v-else-if="!onlyShowCount" class="item-con batch-con crm-btn-groups">
                            <div v-html="btn" v-for="btn in dVisibleBtns" class="crm-btn-group">
                            </div>
                            <div v-if="dShowFlods" class="crm-btn-group">
                                <span ref="fold" class="crm-btn show-fold el-icon-more"></span>
                            </div>
                        </div>
                    </div>
                `,
                data: function() {
                    return {
                        dCustom: isCustom,
                        dVisibleBtns: visibleBtns,
                        dShowFlods: foldBtns.length,
                        dCheckNum: 0,
                        dShow: false,
                        dForceShow: superRoot.options.showBatchBtns
                    }
                },
                mounted: function() {
                    if (this.onlyShowCount) return;
                    this.$nextTick(() => {
                        if(isCustom) {
                            require.async('crm-modules/components/flexgroup/flexgroup', Comp => {
                                if(!this.$refs) return;
                                this._flexButton = new Comp({
                                    el:$(this.$refs.batchcon)
                                }).render(_.map(batchBtns, function(a) {
                                    return _.extend({
                                        label: a.text
                                    }, a);
                                }))
                                this._flexButton.on('change', function(item) {
                                    superRoot.trigger('batchbtn.change', item);
                                })
                            })
                        } else if(foldBtns.length > 0) {
                            this._morBatch = new DropDown({
                                $target:   $(this.$refs.fold),
                                $parent:   $(this.$refs.batchcon1),
                                trigger:   'click',
                                width:     150,
                                space:     2,
                                data:      _.map(foldBtns, (btn) => ({...btn, text: _.escape(btn.text)}))
                            });
                        }
                    })
                },
                computed: {
                    cCheckNum: function() {
                        return $t('已选择{{count}}条', {count: '<em>' + this.dCheckNum + '</em>'});
                    },
                    onlyShowCount: function() {
                        if (batchBtns.length) {
                            return false;
                        }
                    
                        return separateCheckCount;
                    }
                },
                methods: {
                    show: function() {
                        if(this.dShow) return;
                        this.dShow = true;
                        this.$nextTick(() => {
                            this._flexButton && this._flexButton.resize();
                            superRoot.trigger('height.change');
                        })
                    },
                    hide: function() {
                        if(!this.dShow) return;
                        this.dShow = false;
                        superRoot.trigger('term.bactchHide');
                        this.$nextTick(() => {
                            superRoot.trigger('height.change');
                        })
                    }
                },
                beforeDestroy: function() {
                    this._flexButton && this._flexButton.destroy();
                    this._morBatch && this._morBatch.destroy();
                    this._flexButton = this._morBatch = null;
                }
            })
        },

        /**
         * @desc 实例化筛选场景
         */
        _initTerm: function() {
            var me = this, opts = me.options, searchTerm = opts.searchTerm;
            var Comp = opts.isTerm20 && opts.sceneRenderType !== 'drop_down' ? Term20 : Term;
            if (searchTerm) {
                me._term = new Comp({
                    $target:    opts.$termWrap && opts.$termWrap[0] ? opts.$termWrap.find('.term-con') : me.$('.term-con'),
                    $parent:    opts.$termWrap && opts.$termWrap[0] ? opts.$termWrap : me.$('.term-item'),
                    data:       searchTerm,
                    pos:        searchTerm.posStyle || 'bottom left',

                    columns:    opts.columns || [],              // 当前场景的列
                    addColumns:      opts.addColumns,            // 所有添加的列
                    addOrderColumn:  opts.addOrderColumn,
                    filterColumns:   opts.filterColumns,         // 筛选的列
					filterAloneColumns: opts.filterAloneColumns, // 筛选的独立列

                    filterOr:   opts.filterOr,            // 支持或关系
                    stopPropagation: false,
                    searchTerm: searchTerm,

                    hideTermNum: opts.hideTermNum,

                    objectApiname: opts.objectApiname,

                    thirdAddRecordType: opts.thirdAddRecordType,

                    supportMultiFieldSort: opts.supportMultiFieldSort
                });
                me._term.on('term.modify', function(data) {
                    me.options.searchTerm.options = data;
                    me.trigger('term.modify', data);
                });
                me._term.on('term.change', function(id, term) {
                    me.filter && me.filter.setTerm(term);
                    me._setColumn && me._setColumn.setTerm(term);
                    me.trigger('term.change', id, term);
                    // me.changeFilterNum(term); 暂时不需要 需要的时候加参数 自定义对象表格切场景的时候重新实力画表格了
                });
                me._term.render && me._term.render();
            }
        },

        //
        // 切换为自定义场景时带入条件
        //
        changeFilterNum: function(term) {
            var me = this, searchTerm = me.options.searchTerm;
            if (me.filter) {
                if (!term.type || term.type == 1 || term.type == 4) { // 清空选择
                    me.model.parseFilter(term).then(function(filter) {
                        me.filter && me.filter.setFilterData(filter || []);
                        me.outFilter && me.outFilter.setFilterData(me.model.filterOutFilters(filter));
                    })
                    if (me.options.$table && me.options.$table[0]) {
						$('>.dt-main .j-th-filter', me.options.$table).removeClass('cur');
                    }
                    return;
                }

                var param = {
                    url:  searchTerm.getByIdUrl,
                    data: searchTerm.parseParam('getById', _.extend({
                        id: term.id
                    }, term)),
                    success: function (data) {
                        if (data.Result.StatusCode == 0) {
                            data = searchTerm.parseResult('getById', data.Value, me.options.filterColumns || me.options.columns) || [];
                            me.model.parseFilter(term, data.filter).then(function(filter) {
                                me.filter && me.filter.setFilterData(filter);
                                me.filter && me.filter.toggleQuickCleanButton(filter);
                                me.outFilter && me.outFilter.setFilterData(me.model.filterOutFilters(filter));
                            })
                            me.model.set('aloneValues', data.aloneValues || null);
                            me.filter.setAloneValues(data.aloneValues || null);
                            if (me.options.$table && me.options.$table[0]) {
                                _.each(data.filter, function(item) {
                                    $('.th-' + item.FieldName.toLowerCase() + ' .j-th-filter', me.options.$table).addClass('cur').parent().addClass('filtercur')
                                });
                            }
                        }
                    }
                };

                
                if(term.fetch && term.fetch(param) !== false) return;
                
                me._getByIdAjax = util.FHHApi(param, {
                    errorAlertModel: 1
                })
            }
        },

        /**
         * @desc 筛选
         */
        _initFilter: function() {
            var me = this, opts = me.options;
            var isCrossFilters = me.isCrossFilters();
            var curTerm = me._term ? me._term.getCurTerm() : null;
            if (opts.showFilerBtn || isCrossFilters) {
				var FilterClass = isCrossFilters ? InlineFilter : (opts.FilterClass ? opts.FilterClass(Filter) : Filter);
                me.filter = new FilterClass({
                    $table:     opts.$table,
                    tableContext: opts.tableContext,
                    filterOr:   opts.filterOr,
                    $target:    me.$('.j-filter-btn'),
                    trigger:   'click',
                    stopPropagation: false,
                    pos:       'bottom left',
                    filterAloneColumns:  opts.filterAloneColumns,
                    filterDatas: opts.filterDatas,
                    columns:     opts.filterColumns || opts.columns || [],
                    tableColumns: opts.columns || [], // 当前表格的列
                    orderColumn: opts.orderColumn,
                    addOrderColumn: opts.addOrderColumn,
                    zIndex:      me.getTermBatchIndex(),
                    searchTerm:  opts.searchTerm,
                    model: me.model,
                    hideAloneFilter: opts.hideAloneFilter,
                    crossFilterEndpoint: opts.crossFilterEndpoint,
                    crossFilterService: opts.crossFilterService,
                    supportMultiFieldSort: opts.supportMultiFieldSort,
                    aloneValueChangeCallBack: opts.aloneValueChangeCallBack,
                    ...(isCrossFilters ? {
                        filterOr: true,
                        $parent: me.options.$outFilter,
                    } : {}),
                });
                me.filter.on('filter', function(data, aloneValues) {
                    me.model.cacheFilter(data, curTerm);
                    if(me.outFilter) {
                        let sf = me.outFilter.getSysFilter();
                        if(sf.length) {
                            [].unshift.apply(data, sf);
                        }
                    }
                    var td = _.filter(data, function(a) {
                        return me.model.validateFilter(a);
                    })
                    me.trigger('term.advance', td, aloneValues, true);
                    me.triggerOutFilterHeightChange();
                    me.updateRecordType(aloneValues && aloneValues.record_type);
                });
                me.filter.on('term.change', function(id, term) {
                    me._term && me._term.changeTerm(term);
                });
                me.filter.on('show', function() {
                    me.trigger('filter.show');
                });
                me.filter.on('cleancache', function(term) {
                    term && me.model.cleanCacheFilter(term.id);
                });
                me.filter.on('inline.change:height', function(term) {
                    me.trigger('height.change', {
                        resizeTable: true,
                    });
                });
                if (curTerm) {
                    me.filter.setTerm(curTerm);
                    if (!opts.filterDatas) { // 不存在默认数据的时候
                        me.changeFilterNum(curTerm);
                    }
                }
                if (opts.filterDatas && opts.filterDatas.filters) {
                    me.filter.setFilterData(opts.filterDatas.filters);
                }
            }
        },

        /**
         * 多字段排序
         */
        _initMultiFieldSort: function() {
            const supperRoot = this, opts = this.options;
            const $conainer = this.$('.j-multi-field-sort-btn');

            if (!opts.supportMultiFieldSort) {
                $conainer.hide();
                return;
            }

            const sysSortFields = opts.orderColumns;

            supperRoot.multiFieldSort = FxUI.create({
                wrapper: $conainer[0],
                template: `
                    <fx-popover :value="visible" :visible-arrow="false" placement="bottom-start" width="388" popper-class="crm-table-multi-field-sort" trigger="manual">
                        <div class="multi-field-sort-content">
                            <h3 class="mfsc-title">
                                <span>{{$t('crm.table.multifield.sort', null, '高级排序')}}</span>
                                <fx-tooltip :content="$t('crm.table.multifieldsort.desc', null, '按顺序依次往下')" placement="right">
                                    <span class="fx-icon-question"></span>
                                </fx-tooltip>
                                <span class="fx-icon-close" @click="handleExit"></span>
                            </h3>
                            <div class="mfsc-fields-wrapper" ref="fields"></div>
                            <div class="mfsc-buttons">
                                <div class="mfsc-btn-group">
                                    <fx-button v-if="showSaveBtn" @click="handleSave" size="small">{{$t('保 存')}}</fx-button>
                                    <fx-button v-if="showSaveAsBtn" size="small" @click="handleSaveAs">{{$t('另存为')}}</fx-button>
                                </div>
                                <div class="mfsc-btn-group">
                                    <fx-button size="small" type="primary" @click="handleSort">{{$t('确定')}}</fx-button>
                                    <fx-button size="small" :title="$t('恢复默认')" @click="handleReset">{{$t('恢复默认')}}</fx-button>
                                </div>
                            </div>
                        </div>
                        <span slot="reference" :class="{'sort-num': actived}" class="crm-btn term-multi-field-sort-btn fx-icon-paixu1" @click="visible=true">
                            {{$t('crm.table.multifield.sort', null, '高级排序')}}<span v-if="count>0" class="term-multi-field-sort-num"><em class="ne">{{count}}</em></span>
                        </span>
                    </fx-popover>
                `,
                data() {
                    return {
                        count: 0,
                        visible: false,
                        actived: false,
                    };
                },
                computed: {
                    showSaveBtn() {
                        const curTerm = supperRoot._term?.getCurTerm() || null;
                        return curTerm?.type === 2;
                    },
                    showSaveAsBtn() {
                        const curTerm = supperRoot._term?.getCurTerm();
                        return curTerm?.apiname?.indexOf('All') !== -1 || curTerm?.type != 1;
                    }
                },
                methods: {
                    hide() {
                        this.visible = false;
                    },
                    delay() {
                        return new Promise((resolve) => {
                            setTimeout(() => {
                                resolve();
                            }, 20);
                        });
                    },
                    updateCount(count) {
                        this.count = count;
                    },
                    updateStatus(actived) {
                        this.actived = actived;
                    },
                    updateFields(fields) {
                        if (!fields) {
                            fields = [];
                        }
                        this.sortFields?.setFields(fields);
                        supperRoot.model.setMultiSortFields(fields);
                    },
                    getFields(validate) {
                        return this.sortFields?.getFields(validate);
                    },
                    resetToValidFields() {
                        this.updateFields(supperRoot.model.getMultiSortFields());
                    },
                    handleExit() {
                        this.hide();
                        this.resetToValidFields();
                    },
                    handleSave() {
                        this.hide();
                        this.delay().then(() => {
                            supperRoot.saveAsTerm(supperRoot._term?.getCurTerm(), {
                                title: $t('paas.crm.table.save_scene', null, '保存场景'),
                            });
                            this.resetToValidFields();
                        });
                    },
                    handleSaveAs() {
                        this.hide();
                        this.delay().then(() => {
                            supperRoot.saveAsTerm();
                            this.resetToValidFields();
                        });
                    },
                    handleSort() {
                        this.sortFields.getFields(true).then((sortFields) => {
                            this.hide();
                            this.updateStatus(true);
                            this.updateCount(sortFields.length);
                            supperRoot.model.setMultiSortFields(sortFields);
                            supperRoot.trigger('multi.field.sort.change', sortFields);
                        });
                    },
                    handleClean(event) {
                        event.stopPropagation();
                    },
                    handleReset() {
                        this.sortFields.setFields(sysSortFields);
                    },
                },
                mounted() {
                    require.async('./components/sortfields/sortfields', (SortFields) => {
                        const vcs = SortFields.parsevfs({
                            columns: opts.columns,
                            sortFields: sysSortFields,
                        });
                        const vsf = SortFields.parsesfs(sysSortFields, vcs);

                        this.updateCount(vsf.length);
                        this.sortFields = new SortFields({
                            wrapper: this.$refs.fields,
                            sortFields: vsf,
                            fields: vcs,
                        });
                        supperRoot.model.setMultiSortFields(vsf);
                    });
                },
                beforeDestroy() {
                    this.sortFields?.destroy();
                }
            });
        },

        updateMsFields: function(curSortFields) {
            this.multiFieldSort?.updateFields(curSortFields);
        },

        toggleMsStatus: function(actived) {
            this.multiFieldSort?.updateStatus(actived);
        },

        getFiltersExtraData() {
            return this.filter && this.filter.getFiltersExtraData();
        },

        _initTagFilter: function() {
            var me = this;
            var opts = me.options;
            if(!opts.showTagBtn) return;
            me.$('.j-tagfilter-btn').hide();
        },

        updateRecordType: function(type) {
            if(!this.model) return;
            var obj = this.model.get('aloneValues');
            if(obj) {
                obj.record_type = type;
            } else {
               this.model.set('aloneValues', {record_type: type});
            }
            this.__aloneValues = {record_type: type};
        },

        _initOutFilter: function() {
            var me = this;
            if(!me.options.showOutFilter) return;

            me.outFilter = new OutFilter({
                el: me.options.$outFilter,
                model: me.model,
                enableLiveFiltering: this.options.enableLiveFiltering,
            });

            me.outFilter.on('change:height', function() {
                me.triggerOutFilterHeightChange();
            })
            me.outFilter.on('filter', function(data) {
                var filters = me.model.parseOutFilters(data);
                me.model.cacheFilter(data, me._term && me._term.getCurTerm());
                me.trigger('term.advance', filters, me.__aloneValues || me.model.get('aloneValues'));
                me.filter && me.filter.setTargetNum(filters);
            })

            me.model.set('__outFilter', me.outFilter);

            me.outFilter.render();
            if(me.options.customOutFilters) {
                me.outFilter.addSysFilters(me.options.customOutFilters);
            }
        },

        _updateColumnsTitle(titles) {
            this.outFilter && this.outFilter.updateTitle(titles);
        },

        triggerOutFilterHeightChange: function() {
            if(!this.options.showOutFilter) return;
            if(!this.__handleOutHeight) {
                this.__handleOutHeight = _.debounce(() => {
                    var hh = this.getOutFilterHeight() + 'px';
                    if(this._outHeight === hh) return;
                    this._outHeight = hh;
                    this.trigger('height.change');
                },50)
            }
            this.__handleOutHeight();

        },

        shouldCalcOutFilterHeight: function() {
            // 跨对象筛选组件，渲染到outfilter的容器中
            return !!(this.outFilter || this.isCrossFilters());
        },

        getOutFilterHeight: function() {
            return this.shouldCalcOutFilterHeight()
                ? this.options.$outFilter.height()
                : undefined;
        },

        getBatchOperateHeight: function() {
            return this.batchComp && this.batchComp.dShow ? 36 : 0;
        },

        getTermBatchIndex:function(){
            var len = window.CRM_DIALOG_ZINDEX ? window.CRM_DIALOG_ZINDEX.length : 0;
            if(len ){
                return window.CRM_DIALOG_ZINDEX[len - 1] + 10;
            }
            return this.options.zIndex
        },

        _parseSetColumns: function(columns) {
            if(!columns) return _.filter(this.__columns, a => a.data);
            var filterNames = ['data', 'width', 'fixed', 'isHidden', 'title', 'fixedIndex', 'disableSet', 'measuredWidth', 'needsMeasureWidth'];
            var fixed = [];
            var sc = [];
            var cc = [];
            _.each(columns, function(column) {
                if(column.data === 'tag' || column.noSupportShow) return;
                if (!column.isDisable && !column.noRight && column.data) {
                    var tt = _.pick(column, filterNames);
                    column.fixed && !column.isHidden ? fixed.push(tt) : !column.isHidden ? sc.push(tt) : cc.push(tt);
                }
            });
            fixed = _.sortBy(fixed, function(a) {
                return a.fixedIndex;
            })

            this.__columns = _.union(fixed, sc, cc);
        },

        getCurTerm: function() {
            return this._term && this._term.getCurTerm();
        },

        hancColumnsChange: function(columns, isSaveAs, curFilterData) {
            var originColumns = this._originColumns;
            var newColumns = _.map(columns, function(a) {
                var tt = _.findWhere(originColumns, {data: a.value});
                if(!isSaveAs) {
                   tt.isHidden = a.isHidden;
                }
                tt = _.extend({}, tt);
                tt.isHidden = a.isHidden;
                return tt;
            })

            if(!isSaveAs) {
                newColumns = newColumns.concat(_.filter(originColumns, function(a) {
                    return !a.data;
                }));
                this.__columns = newColumns;
                
                //用最新数据同步_originColumns
                let pos = 0;
                this._originColumns = _.map(originColumns, a => {
                    return _.findWhere(newColumns, {data: a.data}) ? newColumns[pos++] : a;
                })

                if (this.filter) {
                    this.filter.setTableColumns(_.extend({}, newColumns));
                }
                this.trigger('columns.change', newColumns);
            } else {//另存为
                this.saveAsTerm({
                    filter: curFilterData || this.filter.getFilterData(),
                }, {
                    columns: newColumns,
                    filterColumns:  newColumns,
                });
                // var opts = this.options;
                // var save = new Term.Add({
                //     title:          $t('另存为场景'),
                //     searchTerm:     opts.searchTerm,
                //     columns:        newColumns,
                //     addColumns:     null,
                //     addOrderColumn: opts.orderColumn,
                //     filterColumns:  newColumns,
                //     filterAloneColumns: opts.filterAloneColumns,
                //     zIndex:        opts.zIndex * 1 + 10,
                //     filterOr:      opts.filterOr,
                //     supportMultiFieldSort: opts.supportMultiFieldSort
                // });

                // save.on('refresh', (status, term) => {
                //     this._term && this._term.changeTerm(_.extend({type: 2}, term));
                // });

                // save.show({
                //     filter: curFilterData || this.filter.getFilterData(),
                //     aloneValues: this.filter.getAloneValues(),
                //     sortFields: this.multiFieldSort.getFields(),
                //     type:  2
                // })
            }
        },

        saveAsTerm: function(data, options) {
            var opts = this.options;
            var save = new Term.Add(_.extend({
                title:          $t('另存为场景'),
                searchTerm:     opts.searchTerm,
                columns:        opts.columns || [],
                addColumns:     null,
                addOrderColumn: opts.orderColumn,
                filterColumns:  this.model?.filterTagColumn(
                    opts.filterColumns || opts.columns || []
                ),
                filterAloneColumns: opts.filterAloneColumns,
                zIndex:        opts.zIndex * 1 + 10,
                filterOr: opts.filterOr,
                supportMultiFieldSort: opts.supportMultiFieldSort
            }, options || {}));

            save.on('refresh', (status, term) => {
                this._term && this._term.changeTerm(_.extend({type: 2}, term));
            });

            save.show({
                type:  2,
                filter: this.filter.getFilterData(),
                aloneValues: this.filter.getAloneValues(),
                sortFields: this.multiFieldSort?.getFields(),
                ...(data || {})
            });
        },

        /**
         * @desc 设置列
         */
        _initSetColumns: function() {
            var me = this, opts = me.options;
            if (opts.showMoreBtn) {
				var columns = _.clone(opts.columns || []);
                _.each(opts.addColumns || [], function(column) {  // 列设置展示所有的列
                    var o = _.findWhere(columns, {data: column.data});
                    if (!o) {columns.push(_.extend(column, {isHidden: true}))}
                });
				if(opts.isSimpleTable){ // true 为极简订单逻辑， add by hgl
					var curTerm = me._term ? me._term.getCurTerm() : null;
					if(opts.parseSetColums){
						columns = opts.parseSetColums(columns);
					}
					me._setColumn = new SetColumn({
						$target:    me.$('.j-tb-setcolumn'),
						trigger:   'click',
						pos:       'bottom left',
						stopPropagation: false,
						columns:     _.filter(columns, function(a) {
							return a.data !== 'tag';
						}),
						orderColumn: opts.orderColumn,
						addOrderColumn: opts.addOrderColumn,
						zIndex:     opts.zIndex,
						searchTerm: opts.searchTerm,
						filterAloneColumns: opts.filterAloneColumns,
						filterOr: opts.filterOr
					});
					me._setColumn.on('show', function() {
						if (me.filter) {
							me._setColumn.set('conditionData', me.filter.getFilterData());
							me._setColumn.set('aloneValues', me.filter.getAloneValues());
						}
					});
					me._setColumn.on('save', function(data) {
						if (me.filter) {
							me.filter.setTableColumns(_.extend({}, data));
						}
						me.trigger('columns.change', data);
					});
					me._setColumn.on('reset', function (data) {
						me.trigger('columns.reset', data);
					});
					me._setColumn.on('term.change', function(id, term) {
						me._term && me._term.changeTerm(term);
					});
					me._setColumn.setTerm(curTerm);
					return;
				}
                columns = _.filter(columns, function(a) {
                    return a.data !== 'tag';
                })

                this._originColumns = columns;
                this._parseSetColumns(columns);
                

				// me._setColumn = new SetColumn({
                //     $target:    me.$('.j-tb-setcolumn'),
                //     trigger:   'click',
                //     pos:       'bottom left',
                //     stopPropagation: false,
                //     columns:     _.filter(columns, function(a) {
                //         return a.data !== 'tag';
                //     }),
                //     orderColumn: opts.orderColumn,
                //     addOrderColumn: opts.addOrderColumn,
                //     zIndex:     opts.zIndex,
                //     searchTerm: opts.searchTerm,
                //     filterAloneColumns: opts.filterAloneColumns,
                //     filterOr: opts.filterOr
                // });
                // me._setColumn.on('show', function() {
                //     if (me.filter) {
                //         me._setColumn.set('conditionData', me.filter.getFilterData());
                //         me._setColumn.set('aloneValues', me.filter.getAloneValues());
                //     }
                // });
                // me._setColumn.on('save', function(data) {
                //     if (me.filter) {
                //         me.filter.setTableColumns(_.extend({}, data));
                //     }
                //     me.trigger('columns.change', data);
                // });
				// me._setColumn.on('reset', function (data) {
				// 	me.trigger('columns.reset', data);
				// });
                // me._setColumn.on('term.change', function(id, term) {
                //     me._term && me._term.changeTerm(term);
                // });
                // me._setColumn.setTerm(curTerm);
			}
        },

        handleColumnSave: function(columns) {
            if (this.filter) {
                this.filter.setTableColumns(_.extend({}, columns));
            }
            this.trigger('columns.change', data);
        },

        getSetColumnsPreVisible: function(num, flag) {
            if(this._setColumn) {
                var arr = [];
                var index = 1;
                var originColumns = this._originColumns;
                var ss = flag ? '#$$$##' : 'name';
                _.find(this.__columns, function(a) {
                    if(a.data && !a.isHidden && a.data !== ss) {
                        var tmp = _.findWhere(originColumns, {data: a.data});
                        if(tmp) {
                            arr.push(tmp);
                            index++;
                        }
                    }
                    return index > num;
                })
                return arr;
            } else {
                var ss = flag ? '#$$$##' : 'name';
                var arr = _.filter(this.options.columns, function(a) {
                    return a.data && !a.isHidden && a.data !== ss && a.fixed && a.data !== 'crm_tag__c'
                })
                if(arr.length < num) {
                    _.find(this.options.columns, a => {
                        a.data && !a.isHidden && a.data !== ss && !a.fixed && arr.push(a);
                        return arr.length >= num;
                    })
                }

                return arr;
            }
        },


        /**
         * @desc 刷新场景
         * 表格刷新时跟新场景
         */
        refresh: function() {
            this._term && this._term.refresh();
        },

        /**
         * @desc 点击其他按钮时的操作事件
         * 切换样式
         * 触发事件
         */
        _onOtherItem: function(e) {
            var $target = $(e.currentTarget);
            if ($target.hasClass('cur')) {return;}
            $target.addClass('cur').siblings().removeClass('cur');
            this.batchComp && this.batchComp.hide();
            this.trigger('otherbtn.change', $target, e);
        },

        // 显示批量按钮
        showBatchBtn: function() {
            this.batchComp && this.batchComp.show();
        },

        // 隐藏批量按钮
        hideBatchBtn: function() {
            this.batchComp && this.batchComp.hide();
        },

        // 切换
        toggleBatchBtn: function(flag) {
            this.setCheckNum(flag);
            if (flag) {
                this.showBatchBtn();
            } else {
                this.hideBatchBtn();
            }
        },

        updateColumnStatus: function(column) {
            try {
                var tt = _.findWhere(this.__columns, {data: column.data});

                if (!tt) return;

                var cc = _.findWhere(this._originColumns, {data: column.data}),
                    hasChanged = false;

                if (!cc) return;

                // update fixed
                if (!!cc.fixed !== !!column.fixed) {
                    hasChanged = true;
                    cc.fixed = tt.fixed = column.fixed;
                    cc.fixedIndex = tt.fixedIndex = column.fixedIndex;
                }

                // update width
                if (cc.width !== column.width) {
                    hasChanged = true;
                    cc.width = tt.width = column.width;
                    cc.measuredWidth = tt.measuredWidth = column.measuredWidth;
                    cc.needsMeasureWidth = tt.needsMeasureWidth = column.needsMeasureWidth;
                }

                if (hasChanged) {
                    this._parseSetColumns(this._originColumns);
                }
            } catch(e) {}
        },

        //
        // 设置选中数
        //
        setCheckNum: function(num) {
            this.batchComp && (this.batchComp.dCheckNum = num);
        },


        //显示已选择的数据
        _initAllCheckData: function() {
            var me = this;
            if(!me.options.showAllCheckDatas) return;
            var $wrap = me.$('.j-show__allchecked');
            if(!$wrap.length) return;
            $wrap.css('position', 'relative');
            me._popoverAllDatas = FxUI.create({
                wrapper: me.$('.j-show__allchecked')[0],
                template: '<div>' +
                        '<fx-popover width="680" @show="handleShow" @hide="handleHide" v-model="dVisiable" trigger="hover" placement="bottom-start" :offset="0">' +
                            '<div ref="list"></div>' +
                            '<div style="position:absolute;right:0;left:0;top:0;bottom:0" slot="reference"></div>' +
                        '</fx-popover>' +
                    '</div>',
                data: function() {
                    return {
                        dVisiable: false
                    }
                },
                methods: {
                    handleShow: function() {
                        me.trigger('term.showallcheck', (datas, columns) => {
                            this.renderList(datas, columns);
                        });
                    },
                    handleHide: function() {
                        this.destroyTable();
                    },
                    destroyTable: function() {
                        this.table && (this.table.destroy(), this.table = null);
                    },

                    renderList: function(datas, columns) {
                        this.destroyTable();
                        datas = _.map(datas, function(item) {
                            return _.extend({}, item);
                        })
                        require.async('crm-assets/widget/table/table', Table => {
                            this.table = new Table({
                                $el: $(this.$refs.list),
                                doStatic: true,
                                noSupportLock: true,
                                scrollLoad: true,
                                scrollLoadY: true,
                                showSize: false,
                                showMultiple: true,
                                showFilerBtn: false,
                                showTerm: false,
                                showManage: false,
                                showPage: false,
                                searchTerm: null,
                                checked: {
                                    idKey: '_id',
                                    data: datas
                                },
                                postData: {},
                                columns: _.map(columns, function(item) {
                                    return _.extend({}, item, {
                                        isOrderBy: false,
                                        fixed: false,
                                        isFilter: false,
                                        showLookupText: true
                                    })
                                }),
                                height: datas.length < 7 ? void 0 : 340
                            });

                            this.table.render();
                            this.table.on('trclick', (data, $tr, $target, a, noRealllyClick) => {
                                if(noRealllyClick) return;
                                if(!data.object_describe_api_name || !data._id) return;
                                this.dVisiable = false;
                                CRM.api.show_crm_detail({
                                    type: data.object_describe_api_name,
                                    apiName: data.object_describe_api_name,
                                    id: data._id
                                })
                            });
                            this.table.on('checkbox.click', () => {
                                var datas = this.table.getRemberData();
                                var tids = _.pluck(datas, '_id');
                                if(!tids.length) this.dVisiable = false;
                                me.options.tableContext._toggleCheckDatas(tids, datas);
                            })
                            this.table.doStaticData(datas);
                            setTimeout(() => {
                                this.table.resize();
                            },30)
                        })
                    }
                },
                beforeDestroy: function() {
                    this.destroyTable();
                }
            })
        },

        // 销毁
        destroy: function() {
            var me = this;
            me._getByIdAjax && me._getByIdAjax.abort();
            _.each(['_term', 'filter', '_setColumn', 'tagFilter', 'outFilter', 'batchComp', 'multiFieldSort'],  function(item) {
                me[item] && (me[item].destroy(), me[item] = null);
            });
            try {
                me.off();
				if (me.model) {
					me.model.destroyme && me.model.destroyme();
					me.model.destroy();
					me.model = null;
				}
				me._outHeight = null;
                me._popoverAllDatas && me._popoverAllDatas.destroy();
            } catch(e) {
                console.error(e);
            }
        }
    });


    module.exports = TermBatch;
});
