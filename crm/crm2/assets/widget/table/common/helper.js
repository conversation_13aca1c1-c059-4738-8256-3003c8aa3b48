/**
 * @desc 工具类函数
 * 与表格的业务相关
 */
define(function(require, exports, module) {

    var util = CRM.util;
    var cUtil = require('crm-modules/common/util');
    var imgTpl = require('./template/img-html');
    var attachTpl = require('./template/attach-html');
    var signatureTpl = require('./template/signature-html');
    var operateTpl = require('./template/operate-html');
    var operateTpl20 = require('./template/operate20-html');

    var useNewImagePath = FS.util.getUserAttribute('paasObjectNewImgPath');
    var DELETESPAN = '<span style="text-decoration: line-through">' + $t('crm.选项已删除') + '</span>';

    const fieldTypeMap = {
        text: 1,
        rich_text: 1,
        html_rich_text: 1,
        big_text: 1,
        auto_number: 101,
        comment: 101,
        long_text: 101,
        number: 2,
        currency: 3,
        date_time: 4,
        true_or_false: 5,
        country: 6,
        province: 6,
        city: 6,
        district: 6,
        town: 6,
        village: 6,
        record_type: 6,
        select: 6,
        select_one: 6,
        select_many: 7,
        checkbox: 7,
        employee: 8,
        department: 8,
        image: 9,
        date: 10,
        multi_level_select_one: 11,
        file_attachment: 17,
        big_file_attachment: 17,
        time: 30,
        phone_number: 31,
        email: 32,
        percentile: 33,
        url: 34,
        formula: 35,
        count: 36,
        signature: 37,
        object_reference: 38,
        master_detail: 38,
        location: 39,
        quote: 40,
        what_list_data: 41,
        dimension: 42, //多维度
        out_employee: 43, //外部人员
        employee_many: 44,
        department_many: 45,
        object_reference_many: 46,
        casecade_select: 47, //级联单选
        what_data: 48,
        what_apiname: 49,
        out_department: 50, // 外部部门
        data_visibility_range: 51 //公共对象数据范围
    };

    const widthConfig = (() => {
        const fieldWidthConfig = {
            crm_tag__c: 240,
            text: 220,
            rich_text: 220,
            html_rich_text: 220,
            big_text: 220,
            auto_number: 100,
            comment: 220,
            long_text: 220,
            number: 100,
            currency: 100,
            date_time: 112,
            true_or_false: 100,
            country: 100,
            province: 100,
            city: 100,
            district: 100,
            town: 100,
            village: 100,
            record_type: 112,
            select: 112,
            select_one: 112,
            select_many: 112,
            checkbox: 112,
            employee: 112,
            department: 112,
            image: 112,
            date: 100,
            multi_level_select_one: 112,
            file_attachment: 112,
            big_file_attachment: 112,
            time: 100,
            phone_number: 112,
            email: 150,
            percentile: 100,
            url: 220,
            formula: 100,
            count: 100,
            signature: 100,
            object_reference: 200,
            master_detail: 150,
            location: 220,
            quote: 112,
            what_list_data: 200,
            dimension: 112,
            out_employee: 112,
            employee_many: 112,
            department_many: 112,
            object_reference_many: 112,
            casecade_select: 220,
            what_data: 220,
            what_apiname: 200,
            out_department: 112,
            data_visibility_range: 112
        };

        const scaledFields = ['text', 'rich_text', 'html_rich_text', 'big_text', 'comment', 'long_text', 'file_attachment', 'big_file_attachment', 'location'];
        const isPhraseLang = ['zh-CN', 'zh-TW', 'ja-JP', 'ko-KR'].includes(Fx.userLanguage);
        if (!isPhraseLang) {
            _.each(scaledFields, (name) => {
                const width = fieldWidthConfig[name];
                if (width) {
                    fieldWidthConfig[name] = Math.ceil(width * 1.4);
                }
            });
        }

        Object.entries(fieldWidthConfig).forEach(([name, width]) => {
            const dataType = fieldTypeMap[name];
            if (dataType) {
                fieldWidthConfig[dataType] = width;
            }
        });

        return fieldWidthConfig;
    })();
    
    var helper = {

        typeMap: fieldTypeMap,

        compare: [{
            value: 1,
            name: $t('等于')
        }, {
            value: 2,
            name: $t('不等于')
        }, {
            value: 3,
            name: $t('大于'),
            dname: $t('晚于'),
            optionTip: $t('crm_filter_tips2_1'),
            dtoptionTip: $t('crm_filter_tips2')
        }, {
            value: 4,
            name: $t('大于等于'),
            dname: $t('晚于等于'),
            optionTip: $t('crm_filter_tips4_1'),
            dtoptionTip: $t('crm_filter_tips4')
        }, {
            value: 5,
            name: $t('小于'),
            dname: $t('早于'),
            optionTip: $t('crm_filter_tips1_1'),
            dtoptionTip: $t('crm_filter_tips6')
        }, {
            value: 6,
            name: $t('小于等于'),
            dname: $t('早于等于'),
            optionTip: $t('crm_filter_tips3_1'),
            dtoptionTip: $t('crm_filter_tips8')
        }, {
            value: 7,
            name: $t('包含')
        }, {
            value: 8,
            name: $t('不包含')
        }, {
            value: 9,
            name: $t('为空（未填写）')
        }, {
            value: 10,
            name: $t('不为空')
        }, {
            value: 11,
            name: $t('开始于')
        }, {
            value: 12,
            name: $t('结束于')
        }, {
            value: 13,
            name: $t('属于')
        }, {
            value: 14,
            name: $t('不属于')
        }, {
            value: 15,
            name: $t('是')
        }, {
            value: 16,
            name: $t('不是')
        }, {
            value: 17,
            name: $t('时间段'),
            cname: $t('介于')
        }, {
            value: 18,
            name: $t('过去N天内(不含当天)'), // 不含当天/周/月
            optionTip: $t('crm_filter_tips9')
        }, {
            value: 19,
            name: $t('未来N天内(不含当天)'),
            optionTip: $t('crm_filter_tips10')
        }, {
            value: 20,
            name: $t('过去N月内(不含当月)'),
            optionTip: $t('crm_filter_tips11')
        }, {
            value: 21,
            name: $t('未来N月内(不含当月)'),
            optionTip: $t('crm_filter_tips12')
        }, { // 文本类型的属于
            value: 22,
            name: $t('属于'),
            oname: $t('包含')
        }, {
            value: 23, // 文本类型的不属于
            name: $t('不属于'),
            oname: $t('不包含')
        }, {
            value: 24, // 日期类型等于
            name: $t('等于')
        }, {
            value: 25,
            name: $t('过去N周内(不含当周)'),
            optionTip: $t('crm_filter_tips13')
        }, {
            value: 26,
            name: $t('未来N周内(不含当周)'),
            optionTip: $t('crm_filter_tips14')
        }, {
            value: 27,
            name: $t('过去N天内(含当天)'),
            optionTip: $t('crm_filter_tips15')
        }, {
            value: 28,
            name: $t('未来N天内(含当天)'),
            optionTip: $t('crm_filter_tips16')
        }, {
            value: 29,
            name: $t('过去N周内(含当周)'),
            optionTip: $t('crm_filter_tips17')
        }, {
            value: 30,
            name: $t('未来N周内(含当周)'),
            optionTip: $t('crm_filter_tips18')
        }, {
            value: 31,
            name: $t('过去N月内(含当月)'),
            optionTip: $t('crm_filter_tips19')
        }, {
            value: 32,
            name: $t('未来N月内(含当月)'),
            optionTip: $t('crm_filter_tips20')
        }, {
            value: 33,
            name: $t('N天前'),
            optionTip: $t('crm_filter_tips21')
        }, {
            value: 34,
            name: $t('N天后'),
            optionTip: $t('crm_filter_tips22')
        }, {
            value: 35,
            name: $t('N周前'),
            optionTip: $t('crm_filter_tips23')
        }, {
            value: 36,
            name: $t('N周后'),
            optionTip: $t('crm_filter_tips24')
        }, {
            value: 37, // 定位字段不等于
            name: $t('不等于')
        }, {
            value: 38, // 布尔字段属于
            name: $t('属于')
        }, {
            value: 39, // 布尔字段不属于
            name: $t('不属于')
        }],

        /**
         * @desc 根据类型获取比较条件
         * @param type 字段类型
         */
        getCompare: function(type) {
            var result = [],
                enums = null;
            type = type * 1;
            switch (type) {
                case 1:
                    enums = [7, 8, 22, 23, 1, 2, 9, 10, 11, 12];
                    break;
                case 101:
                case 31:
                case 32:
                case 34:
                case 35:
                case 41:
                    enums = [1, 2, 7, 8, 22, 23, 9, 10, 11, 12];
                    break;
                case 38:
                    enums = [7, 8, 22, 23, 1, 2, 9, 10, 11, 12];
                    break;
                case 39:
                    enums = [7, 8, 9, 10, 11, 12, 1, 2];
                    break;
                case 2:
                case 3:
                case 33:
                case 36:
                    enums = [1, 2, 3, 4, 5, 6, 17, 9, 10];
                    break;
                case 5:
                    enums = [1, 2, 9, 10, 38, 39];
                    break;
                case 22:
                case 6:
                    enums = [1, 2, 13, 14, 9, 10];
                    break;
                case 7:
                    enums = [1, 2, 7, 8, 13, 14, 9, 10];
                    break;
                case 8:
                case helper.typeMap.out_department:
                    enums = [22, 23, 9, 10];
                    break;
                case 43: // 外部人员
                    enums = [22, 23, 13, 14, 9, 10];
                    break;
                case 30: // 时间类型
                    enums = [1, 2, 5, 3, 6, 4, 9, 10, 17];
                    break;
                case 4: // 日期时间
                case 10: // 日期
                    enums = [1, 2, 5, 3, 4, 6, 9, 10, 17, 18, 19, 20, 21,
                        25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36
                    ];
                    break;
                case 9:
                case 17:
                case 37:
                    enums = [9, 10];
                    break;
                case 42:
                case 44:
                case 45:
                case 51:
                    enums = [13, 14, 9, 10];
                    break;
                case 46:
                    enums = [13, 14, 7, 8, 9, 10];
                    break;
                case 47:
                    enums = [13, 14];
                    break;
                case 48:
                    enums = [1];
                    break;
                case 49:
                    enums = [7, 8, 22, 23, 1, 2, 9, 10];
                    break;
                default:
                    enums = [1];
            }
            return _.map(enums, function(item) {
                var option = _.extend({}, helper.compare[item - 1]);
                if (type == 4 || type == 10 || type == 30) { // 时间 日期时间 小时类型
                    option.name = option.dname || option.name;
                } else if (type == 2 || type == 3 || type == 33 || type == 36) { // 数字介于
                    option.name = option.cname || option.name;
                    option.optionTip = undefined;
                    option.dtoptionTip = undefined;
                } else if (type == 43) { // 外部人员
                    option.name = option.oname || option.name;
                } else {
                    option.optionTip = undefined;
                    option.dtoptionTip = undefined;
                }
                return option;
            });
        },

        /**
         * @desc 根据类型获取比较条件（用于夸对象筛选， 是普通筛选的子集）
         * @param type 字段类型
         */
        getCrossFilterCompare: function(type) {
            type = type * 1;
            
            let enums;
            switch (type) {
                case 1:
                    enums = [7, 8, 22, 23, 1, 2, 9, 10, 11, 12];
                    break;
                case helper.typeMap.object_reference:
                    enums = [7, 8, 1, 2, 9, 10, 11, 12]; //22, 23
                    break;
                case 101:
                case 31:
                case 32:
                case 34:
                case 35:
                case 41:
                    enums = [1, 2, 7, 8, 22, 23, 9, 10, 11, 12];
                    break;
                case 39:
                    enums = [7, 8, 9, 10, 11, 12, 1, 2];
                    break;
                case 2:
                case 3:
                case 33:
                case 36:
                    enums = [1, 2, 3, 4, 5, 6, 17, 9, 10];
                    break;
                case 5:
                    enums = [1, 2, 9, 10, 38, 39];
                    break;
                case 22:
                case 6:
                    enums = [1, 2, 13, 14, 9, 10];
                    break;
                case 7:
                    enums = [7, 8, 13, 14, 9, 10];
                    break;
                case 8:
                case 43:
                case helper.typeMap.out_department:
                    enums = [22, 23, 9, 10];
                    break;
                case 30: // 时间类型
                    enums = [1, 2, 5, 3, 6, 4, 9, 10, 17];
                    break;
                case 4: // 日期时间
                case 10: // 日期
                    enums = [1, 2, 5, 3, 4, 6, 9, 10, 17, 18, 19, 20, 21,
                        25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36
                    ];
                    break;
                case 9:
                case 17:
                case 37:
                    enums = [9, 10];
                    break;
                case 42:
                case 44:
                case 45:
                case 51:
                    enums = [13, 14, 9, 10];
                    break;
                case helper.typeMap.object_reference_many:
                    enums = [9, 10]; // 跨对象不支持 7, 8, 13, 14
                    break;
                case 47:
                    enums = [13, 14];
                    break;
                case 48:
                    enums = [1];
                    break;
                case 49:
                    enums = [7, 8, 22, 23, 1, 2, 9, 10];
                    break;
                default:
                    enums = [1];
            }

            return enums;
        },


        getFieldTypes: function() {
            return helper.typeMap;
        },

        generateTagHtml: function(data, mode, color) {
            if (_.contains(['dark', 'plain'], mode)) {
                const tt = color || '#0c6cff';
                const ss = mode === 'dark' ? `background:${tt}` : `color:${tt}`;
                return `
                    <span style="${ss};display:inline-block;border-color:${tt}" class="crm-g-tag crm-g-tag--${mode} crm-g-max-width-ellipsis">${data}</span>
                `;
            }

            return data;
        },

        /**
         * @desc 格式化整形和小数类型
         * def 默认值
         * dataType：2
         */
        numFormat: function(num, column, def, hightKey) {
            if (column.editable) {
                if (num && column.is_show_thousands && !_.isNaN(+num)) {
                    return util.toMoney(num);
                }
                return this._newHighligh(num, hightKey);
            }

            if (!_.isNaN(+num)) {
                num = util.formatNumberForRegion(num, {useGrouping: column.is_show_thousands});
            }

            return this._newHighligh(num, hightKey);
        },

        /**
         * @desc 格式化金额类型
         * 千分位 并保留2位小数
         * def 默认值
         * dataType：3
         */
        moneyFormat: function(num, column, def, rowData) {
            if (_.isNaN(+num) || num === '') {
                return def;
            }
            if (column.toUpper) {
                return util.moneyUp(num);
            }

            if (column.editable || column.__isEdit || !rowData) {
                return util.toMoney(num);
            }

            if(rowData[column.data + '__r']) return _.escape(rowData[column.data + '__r']);

            var __p = column.__originType === 'currency' && rowData['mc_currency'];
            __p = __p ? (__p + '  ') : '';

            return __p + util.formatNumberForRegion(num, {useGrouping: true});
        },

        /**
         * @desc 时间日期类型格式化
         * dataType：4  日期时间类型
         * dataType：10 日期类型
         */
        timeFormat: function(time, formatType, def, column) {
            if (time === '') return def;
            if (time == '0') {
                time = 10;
            }
            if (formatType == 'date' && time) {
                time = FS.util.convertTimestampFromTZ8(time);
                let reg = {
                    'yyyy-QQQ': 'YYYY-[Q]Q',
                    'yyyy-MM': 'YYYY-MM',
                    'yyyy': 'YYYY'
                }[column && column.date_format];
                if (reg) return util.moment.unix(time / 1000).format(reg);
            } else if (column && column.not_use_multitime_zone && time) {
                time = FS.util.convertTimestampFromTZ8(time);
            }

            if (formatType === 'time' && column.date_format === 'yyyy-MM-dd') formatType = 'date';

            return util.formatTime(time, formatType, def);
        },

        /**
         * @desc 格式化布尔值
         * dataType：5
         */
        booleanFormat: function(val, column, def, hightKey) {
            if (val === void 0 || val === '' || val === null) return def;

            val = {
                'true': true,
                'false': false,
                '0': false,
                '1': true
            }[val.toString().toLowerCase()];
            if (val === void 0) return def;

            let fv = val ? $t('是') : $t('否');

            if (column.options && column.options.length) {
                var temp = _.findWhere(column.options, {
                    ItemCode: val
                });

                if (!temp) {
                    return def;
                }

                fv = this._newHighligh(_.escape(temp.ItemName), hightKey);
            }

            if (column && !column.editable && _.contains(['dark', 'plain'], column.display_mode) && _.contains(column.display_mode_page, 'list')) {
                return this.generateTagHtml(fv, column.display_mode);
            }

            return fv;
        },

        /**
         * @desc 时间类型格式化
         */
        hourMinFormat: function(val, def) {
            if (val == '0') {
                val = 1;
            }
            return val ? util.moment.unix(val / 1000, true).format('HH:mm') : def;
        },

        /**
         * @desc 百分数格式化
         * dataType: 33
         */
        percentFormat: function(data, def, hightKey, column) {
            if(data === '' || data === null) return def;

            if(column && !column.editable && column.display_mode === 'progress' && _.contains(column.display_mode_page, 'list')) {
                return `
                    <div class="crm-g-progress">
                        <span class="progerss-label">${this._newHighligh(data, hightKey)}%</span>
                        <div class="progress-bar">
                            <div class="bar-item" style="width:${data < 0 ? 0 : data > 100 ? 100 : data}%"></div>
                        </div>
                    </div>
                `
            }
            
            return `${this._newHighligh(data, hightKey)}%`;
        },

        /**
         * @desc 网址类型
         * dataType: 34
         */
        interFormat: function(data, def, souceType, hightKey) {
            var reg = /^http[s]?:\/\/.*/;
            var link = reg.test(data) ? data : 'http://' + data;
            if (souceType == 'result') {
                return data || def;
            }
            data = hightKey ? helper.highligh(data, hightKey) : data;
            return data ? '<a rel="noopener noreferrer" href="' + link + '" target="_blank" class="icon-link-out j-stop-propagation">' + data + '</a>' : def;
        },

        /**
         * @desc lookup类型
         * dataType: 38
         * 仅处理过滤结果的数据
         */
        lookFormat: function(data, column, def, souceType, rowData, hightKey) {
            var result = data;
            var _r = column.data + '__r';
            if (column.lookUpDataIsText) { // 老对象兼容
                data = column.data.replace('__r', '');
                data = rowData ? rowData[data] : '';
            } else {
                result = rowData ? (rowData[column.data] && rowData[_r]) || data || def : data
            }
            if (data && (rowData && !rowData[_r])) result = '--';
            result = helper._replaceSpace(_.escape(result));
            if (souceType == 'result') {
                return _.escape(column[_r]) || result || def;
            }
            result = hightKey ? helper.highligh(result, hightKey) : result;
            if (data && !column.isEdit && !column.showLookupText) {
                return `<a href="javascript:;" ${column.is_support_tree_view ? 'title=""' : ''} class="j-show-lookup${column.is_support_tree_view ? ' crm-ui-treetitle': ''}" data-id="${data}" data-apiname="${column.target_api_name}">${result}</a>${CRM.util.getDetailTabUrl(column.target_api_name, data)}`
            }
            return result;
        },

        objectreferenceManyFormat: function(data, column, rowData, hightKey) {
            var datas = (data && rowData && rowData[column.data + '__r']) || [];
            if (!column.isEdit && !column.showLookupText) {
                return _.map(datas, (a) => {
                    var n = _.escape(a.name || '--');
                    var nn = this._newHighligh(n, hightKey);
                    return `<a href="javascript:;" title="${n}" class="j-show-lookup${column.is_support_tree_view ? ' crm-ui-treetitle': ''}" data-id="${a._id}" data-apiname="${column.target_api_name}">${nn}</a>`;
                }).join(',')
            }
            return _.map(datas, (a) => {
                return a.name ? this._newHighligh(_.escape(a.name), hightKey) : '--'
            }).join(',');
        },

        whatlistFormat: function(data, column, def, souceType, rowData, hightKey) {
            if (!data || !data.length) return def;
            return _.map(data, function(item) {
                var result = _.escape(item.name);
                if (souceType !== 'result' && hightKey) {
                    result = helper.highligh(result, hightKey)
                }

                return column.showLookupText ? result : '<a href="javascript:;" class="j-show-lookup" data-id="' + item.id + '" data-apiname="' + item.describe_api_name + '">' + (result || def) + '</a>'
            }).join(',')
        },

        /**
         * @desc 定位类型处理
         * dataType: 39
         */
        locationFormat: function(data, column, def, souceType, hightKey) {
            if (data) {
                data = data.split('#%$');
                if (souceType == 'result') {
                    return data[0];
                }
                let str =  this._newHighligh(_.escape(data[data.length - 1]), hightKey);
                return '<div class="location-box">' +
                    '<div class="location-text"><span>' + str + '</span></div>' +
                    '<span class="ico-location"></span>' +
                    '</div>';
            }
            return def;
        },

        /**
         * @desc 单选类型格式化
         * dataType： 6
         */
        singSelectFormat: function(val, column, def, rowData, noColor, hightKey) {
            if (val === void 0 || val === '' || val === null) return def;

            // 是否展示标签
            var tagged = !noColor && column
                && _.contains(['dark', 'plain'], column.display_mode)
                && _.contains(column.display_mode_page, 'list');

            var option = _.find(column._options || [], function(item) {
                return item.ItemCode == val || (item.value && item.value == val);
            });
            

            if (!option) {
                var rr = rowData && rowData[column.data + '__r'];
                rr && (rr = _.escape(rr));

                if (tagged && !column.editable && rr) {
                    return this.generateTagHtml(this._newHighligh(rr, hightKey), column.display_mode);
                }

                return rr ? this._newHighligh(rr, hightKey) : (!column.editable && !noColor ? DELETESPAN : def);
            }

            var strs;
            if (rowData && rowData[column.data + '__ov']) {
                strs = rowData[column.data + '__ov'];
            } else {
                var ol = (option.isCustom || column.isMyObject) && rowData && rowData[column.data + '__o'];
                strs = (option.ItemName || option.name || option.label) + (ol ? '：' + ol : '');
            }


            strs = this._newHighligh(_.escape(strs), hightKey);

            // 他的优先级高
            if (tagged && !column.editable) {
                return this.generateTagHtml(strs, column.display_mode, option.font_color);
            }

            if (!column.editable && option.font_color && !noColor) { //非编辑态 正常显示颜色
                strs = '<em style="color:' + option.font_color + '">' + strs + '</em>';
            }

            return strs;
        },

        /**
         * @desc 多选类型格式化
         * dataType： 7
         */
        mulSelectFormat: function(val, column, def, rowData, noColor, hightKey) {
            if (!val || !val.length) return def;

            _.isString(val) && (val = val.split('|'));

            var strs = [];
            var editable = column.editable; //是否是编辑态 原则上编辑态不支持颜色
            var otherLabel = rowData && rowData[column.data + '__o'];
            var options = column._options;
            var deletedStrs = !editable ? DELETESPAN : '';

            _.each(val, (code) => {
                var item = _.find(options, function(a) {
                    return (a.ItemCode || a.value) == code;
                })
                if (!item && column.data === 'related_api_names') {
                    var tt = CRM.util.getObjByApiName(code);
                    tt && tt.displayName && (item = {
                        label: tt.displayName
                    });
                }
                if (item) {
                    var label = item.ItemName || item.label || item.name;
                    if (code === 'other') {
                        if (rowData && rowData[column.data + '__ov']) {
                            label = rowData[column.data + '__ov'];
                        } else if (otherLabel) {
                            label = label + '：' + otherLabel;
                        }
                    }

                    label = this._newHighligh(_.escape(label), hightKey);
                    strs.push(item.font_color && !column.editable && !noColor ? '<em style="color:' + item.font_color + '">' + label + '</em>' : label);
                } else {
                    deletedStrs && !noColor && strs.push(deletedStrs)
                }
            })

            return strs.join(',') || def;
        },

        /**
         * @desc 获取图片列下的所有路径
         * 解析path 路径
         */
        getImgListOnNewPath(path, isMyObject, tmpPath) {
            var imgList = [];

            if (!path) {
                return imgList;
            }

            const originDimensions = {width: 0, height: 0};
            const smallDimensions = {width: 80, height: 0};

            const imagePaths = path.split('|');
            _.each(imagePaths, function(imagePath, index) {
                const tmp = tmpPath && tmpPath[index] || {};
                const imageExt = CRM.util.getFileExtText(imagePath);
                const baseName = CRM.util.getFileNamePath(imagePath);
                const downloadPath = imageExt ? `${baseName}1.${imageExt}` : baseName;

                let smallPath, bigPath;
                let isHttps, originDownUrl;
                if(tmp.signedUrl) {
                    smallPath = tmp.signedUrl + '&size=80*0&crmsignedurl';
                    originUrl = bigPath = tmp.signedUrl;
                    originDownUrl = tmp.signedUrl + '&acModel=attachment';
                } else if (/^TN|TC/.test(imagePath)) {
                    smallPath = bigPath = cUtil.getFscTempFileViewUrl(imagePath);
                } else if (/^http(s)?/.test(imagePath)) {
                    smallPath = bigPath = imagePath;
                    isHttps = true;
                } else if (CRM.util.isBigFile(imagePath)) {
                    bigPath = Fx.file?.getImgPath(imagePath, {
                        width: 0,
                        height: 0,
                        ext: imageExt || CRM.util.getFileExtText(tmp?.filename || '')
                    });
                } else {
                    bigPath = FS.util.getNewImgPath(baseName, originDimensions);
					if (imageExt) {
						smallPath = FS.util.getNewImgPath(baseName, smallDimensions);
					} else {
						smallPath = bigPath;
					}
                }

                imgList.push({
                    smallUrl: smallPath,
                    bigUrl: bigPath,
                    originUrl: bigPath,
                    originDownUrl: originDownUrl || (isHttps ? imagePath : util.getFscLink(
                        downloadPath, (tmp && tmp.filename) || bigPath, true
                    )),
                    is_recapture: tmp && (tmp.is_recapture || tmp.recapture),
                    scene_error: tmp && tmp.scene_error
                });
            });

            return imgList;
        },

        /**
         * @desc 获取图片列下的所有路径
         * 解析path 路径 3小图 2中图 1大图
         */
        getImgLists: function(path, isMyObject, tmpPath) {
            if (useNewImagePath) {
                return this.getImgListOnNewPath(path, isMyObject, tmpPath);
            }

            var list = [];
		
            if (!path) {
                return list;
            }
            
            path = path.split('|');
            _.each(path, function(item, index) {
				let baseName = CRM.util.getFileNamePath(item);
				let ext = CRM.util.getFileExtText(item);
				let originPath = ext ? baseName + '1.' + ext : baseName;
				let smallPath, bigPath;
                let tmp = tmpPath && tmpPath[index];
                let isHttps;

                if (/^TN|TC/.test(item)) {
                    smallPath = bigPath = cUtil.getFscTempFileViewUrl(item);
                } else if (/^http(s)?/.test(item)) {
                    smallPath = bigPath = item;
                    isHttps = true;
                } else if (CRM.util.isBigFile(item)) {
                    smallPath = bigPath = Fx.file?.getImgPath(item, {
                        width: 0,
                        height: 0,
                        ext: ext || CRM.util.getFileExtText(tmp?.filename || '')
                    });
                }
                else {
					bigPath = cUtil.getFscLinkByOpt({
                        id: originPath,
                        webp: true,
                        name: tmp && tmp.filename
                    })

					if(ext) {
						smallPath = cUtil.getFscLinkByOpt({
							id: baseName + '3.' + ext,
							webp: true
						})
					} else {
						smallPath = bigPath;
					}
                }

                let originDownUrl;
                if(tmp && tmp.signedUrl) {
                    smallPath = tmp.signedUrl + '&size=80*0&crmsignedurl';
                    bigPath = tmp.signedUrl;
                    originDownUrl = tmp.signedUrl + '&acModel=attachment';
                }

                list.push({
                    smallUrl: smallPath,
                    bigUrl: bigPath,
                    originUrl: bigPath,
                    originDownUrl: originDownUrl || (isHttps ? item : util.getFscLink(originPath, (tmp && tmp.filename) || bigPath, true)),
                    is_recapture: tmp && (tmp.is_recapture || tmp.recapture),
                    scene_error: tmp && tmp.scene_error
                });
            });

            return list;
        },

        /**
         * @desc 图片类型格式化
         * dataType 9
         * @param {{Array | String}}
         * [{path:'',ext:''}..]  |  'N_path|N_path2'
         * 自定义对象和预设对象处理不一致
         */
        imgFormat: function(path, column, def) {
            var me = this;
            var isMyObject = column.isMyObject;
            var tmpPath;
            if (_.isArray(path)) {
                tmpPath = path;
                var paths = [];
                _.each(path, function(item) {
                    // 下发的path带有ext
                    item.path || (item.path = '');
                    if (item.path.split('.').length > 1) {
                        paths.push(item.path);
                    } else {
                        paths.push(item.path + '.' + item.ext);
                    }
                });
                path = paths.join('|');
            }
            return imgTpl({
                path: path,
                pathList: me.getImgLists(path, isMyObject, tmpPath),
                column: column,
                def: def
            });
        },

        /**
         * @desc 格式化path
         * @path {{ String }} 后端下发图片路径
         * @type {{ Number }} 3小图 2中图 1大图
         */
        imgPathFromat: function(path, type) {
            var ps = path.split('.');
            var fp = ps[0] + type;
            if (ps[1]) {
                fp += '.' + ps[1];
            }
            return fp;
        },

        /**
         * @desc 签名类型
         * dataType 37
         */
        signatureFormat: function(path, column, def) {
            var me = this;
            path = path[0];
            path = path ? path.path + '.' + path.ext : '';
            var data = me.getImgLists(path, column.isMyObject);
            return signatureTpl({
                path: path,
                data: data[0] || null,
                column: column,
                def: def
            });
        },

        /**
         * @desc 计算类型
         * dataType 35
         */
        calculationFormat: function(data, column, souceType, rowData, hightKey) {
            var me = this;
            if (data == CRM.config.NA) {
                return data;
            }
            if (column.returnType) {
                let rt = column.returnType;

                //避免死循环
                if (rt == 35 || rt == 36) {
                    return data;
                }

                return me.getFormatVal(data, _.extend({}, column, {dataType: rt}), souceType, rowData, hightKey);
            }
            return data;
        },

        /**
         * @desc 附件类型
         * dataType 17
         */
        attachFormat: function(list, column, def) {
            var me = this;
            return attachTpl({
                getFileIco: cUtil.getFileIco,
                list: list || [],
                column: column,
                def: def
            });
        },

        /**
         * @desc 二级级联选项
         * dataType 11
         */
        cascadeSelectFormat: function(val, options, def) {
            var firstStr = '',
                secondStr = '',
                flag;
            for (var i = 0, len = options.length; i < len; i++) {
                var obj = options[i];
                _.each(obj.Children, function(item) {
                    if (item.ItemCode == val) {
                        flag = true;
                        secondStr = item.ItemName;
                    }
                });
                if (flag) {
                    firstStr = obj.ItemName;
                    break;
                }
            }
            return flag ? _.escape(firstStr + '/' + secondStr) : def;
        },

        /**
         * @desc 替换字符串中的非法值
         */
        replaceRegExp: function(str) {
            return CRM.util.replaceRegExp(str);
        },

        _newHighligh(data, key) {
            if (!key || !data) return data;

            data = data + '';
            
            if (data.indexOf('ww-open-data') != -1) {
                return data;
            } // 企业微信

            try {
                let od = _.unescape(data).replace(/&nbsp;/g, ' ');
                let Reg = new RegExp(CRM.util.replaceRegExp(key), 'g');
                if (od.search(Reg) != -1) {
                    let str = `<span class="dt-mark">${key}</span>`;
                    data =  _.map(od.replace(
                        Reg,
                        str
                    ).split(str), item => _.escape(item)).join(`<span class="dt-mark">${_.escape(key)}</span>`);
                }
            } catch(e) {}
            
            return data;
        },

        /**
         * @desc 搜搜高亮处理数据
         */
        highligh: function(data, key) {
            if (!CRM.util.getUserAttribute('grayCrmTableNoHighligh')) {
                return helper._newHighligh(data, key);
            }
            if (!key) return data;
            var keyWord = _.escape(key),
                reg = new RegExp(helper.replaceRegExp(keyWord), 'g');
            if (keyWord) {
                data = data + '';
                if (data.indexOf('ww-open-data') != -1) {
                    return data;
                } // 企业微信
                return _.map(data.split('&nbsp;'), function(a) {
                    if (!a) return '&nbsp;';
                    return a.replace(reg, function(item) {
                        return '<span class="dt-mark">' + item + '</span>';
                    });
                }).join('&nbsp;');
            }
            return data;
        },

        /**
         * @desc 根据column类型返回值
         * @param {{String}} souceType 区分来源的格式化 result过滤结果
         * @param {{String}} hightKey  高亮的字符
         * 默认空 所有数据的格式
         */
        getFormatVal: function(data, column, souceType, rowData, hightKey, isEm6) {
            var type = column.dataType,
                def = column.defaultValue;

            if (data == CRM.config.TEXT_HIDE || data == CRM.config.NUM_MIN || data == CRM.config.NUM_TIME_MIN || data == CRM.config.Min_MONEY) {
                return CRM.config.TEXT_HIDE;
            }

            if (rowData && rowData[column.data + '__s']) { //先取掩码
                return rowData[column.data + '__s'];
            }

            if (data === null) {
                if (_.contains([9, 17, 37], type)) { //图片 附件 签名的字段类型不能处理空
                    data = [];
                } else {
                    return def;
                }
            };

            var originData = data;

            switch (type) {
                case 1:
                case 101:
                    if (column.type === 'html_rich_text'  || column.type === 'big_text' || (column.type === 'rich_text' && rowData[column.data + '__o'] !== void 0)) {
                        if(column.type === 'big_text') {
                            data = this._newHighligh(_.escape(rowData[column.data + '__o'] || (rowData[column.data] || '').slice(0, 100)), hightKey);
                        } else {
                            data = this._newHighligh(_.escape(rowData[column.data + '__o']), hightKey);
                        }
                    } else {
                        if (window.FS_SOURCE == 'wechat') {
                            data = (_.isString(data) && !/^<ww-open-data/i.test(data)) ? helper._replaceSpace(_.escape(data || def)) : data;
                        } else {
                            if (column.enable_multi_lang) {
                                var mlData = rowData[column.data + '__r'];
                                if (mlData && _.isString(mlData)) {
                                    data = mlData;
                                }
                            }
                            data = _.isString(data) ? helper._replaceSpace(_.escape(data || def)) : data;
                        }
                        data = hightKey ? helper.highligh(data, hightKey) : data;
                    }
                    break;
                case 2:
                    data = helper.numFormat(data, column, def, hightKey);
                    break;
                case 3:
                    data = helper.moneyFormat(data, column, def, rowData);
                    break;
                case 4:
                    data = helper.timeFormat(data, 'time', def, column);
                    break;
                case 5:
                    data = helper.booleanFormat(data, column, def, hightKey);
                    break;
                case 6:
                case 22:
                    data = helper.singSelectFormat(data, column, def, rowData, false, hightKey);
                    break;
                case 7:
                    data = helper.mulSelectFormat(data, column, def, rowData, false, hightKey);
                    break;
                case 8:
                    data = helper.empCircleFormat(data, column, rowData, souceType, isEm6, hightKey);
                    break;
                case 9:
                    data = helper.imgFormat(data, column, def);
                    break;
                case 10:
                    data = helper.timeFormat(data, 'date', def, column);
                    break;
                case 11:
                    data = helper.cascadeSelectFormat(data, column._options, def);
                    break;
                case 17:
                    data = helper.attachFormat(data, column, def);
                    break;
                case 30:
                    data = helper.hourMinFormat(data, def);
                    break;
                case 31:
                    data = helper.phoneFormat(data, column, def, rowData);
                    data = hightKey ? helper.highligh(data, hightKey) : data;
                    break;
                case 32:
                    data = helper.emailFormat(data, column, def, rowData);
                    data = hightKey ? helper.highligh(data, hightKey) : data;
                    break;
                case 33:
                    data = helper.percentFormat(data, def, hightKey, column);
                    break;
                case 34:
                    data = helper.interFormat(data, def, souceType, hightKey);
                    break;
                case 35:
                case 36:
                    data = helper.calculationFormat(data, column, souceType, rowData, hightKey);
                    break;
                case 37:
                    data = helper.signatureFormat(data, column, def);
                    break;
                case 38:
                    data = helper.lookFormat(data, column, def, souceType, rowData, hightKey);
                    break;
                case 39:
                    data = helper.locationFormat(data, column, def, souceType, hightKey);
                    break;
                case 40:
                    data = helper.quoteFormat(data, column, def, rowData, hightKey);
                    // data = hightKey ? helper.highligh(data, hightKey) : data;
                    break;
                case 41:
                    data = helper.whatlistFormat(data, column, def, souceType, rowData, hightKey);
                    break;
                case 42:
                    data = helper.dimensionFormat(data, column, rowData, souceType, hightKey);
                    break;
                case 43:
                    data = helper.outEmployeeFormat(data, column, rowData, souceType, hightKey);
                    break;
                case 44:
                case 45:
                    data = helper.empCirclesFormat(data, column, rowData, hightKey) || def;
                    break;
                case 46:
                    data = helper.objectreferenceManyFormat(data, column, rowData, hightKey);
                    break;
                case 51: 
                    data = helper.dataVisibilityRangeFormat(data, column, rowData, hightKey);
                    break;
                case helper.typeMap.out_department:
                    data = helper.outDepartmentFormat(data, column, rowData, hightKey);
                    break;
                case 'operate':
                    data = helper.operateFormat(data, column, rowData);
                    break;
                case 'columnset':
                    data = helper.columnsetFormat(data, column, rowData);
                    break;
                default:
                    if (window.FS_SOURCE == 'wechat') {
                        data = (_.isString(data) && !/^<ww-open-data/i.test(data)) ? helper._replaceSpace(_.escape(data)) : data;
                    } else {
                        data = _.isString(data) ? helper._replaceSpace(_.escape(data)) : data;
                    }
                    data = hightKey ? helper.highligh(data, hightKey) : data;

                    break;
            }
            if (!column.editable && column.font_color && (_.isBoolean(originData) || (originData && _.isNumber(originData * 1)) || originData === 0)) {
                return `<span style="color:${column.font_color}">${data}</span>`;
            }
            return data === 0 || data !== '' ? data : data || def;
        },

        _replaceSpace(data) {
            return data && _.isString(data) ? data.replace(/\x20/g, '&nbsp;') : data;
        },

        /**
         * 操作列格式化
         */
        operateFormat: function(data, column, rowData) {
            if (rowData.__newLayout) {
                return operateTpl20({
                    id: rowData._id,
                    buttons: rowData.operate || []
                })
            }
            var options = rowData.operate || [];
            var buttons = [];
            var foldButtons = [];

            _.each(options, function(item) {
                if (item.render_type === 'not_fold') {
                    buttons.push(item);
                } else {
                    foldButtons.push(item);
                }
            });

            return operateTpl({
                options: options,
                buttons: buttons,
                foldButtons: foldButtons
            });
        },

        /**
         * 设置列格式化
         */
        columnsetFormat: function(data, column, rowData) {
            if (column.operateColumn) {
                return helper.operateFormat(data, column, rowData);
            } else {
                return '&nbsp;';
            }
        },

        /**
         * @desc 员工部门数据格式化
         */
        empCircleFormat: function(data, column, rowData, souceType, isEm6, hightKey) {
            var lData = rowData && rowData[column.data + '__l'];
            if (lData && data && data.length && helper._diffEmpDatas(lData, data, column)) {
                if (!isEm6 && lData.length === 1 && column.referRule == 'Employee' && rowData[column.data] != CRM.curEmpId && rowData[column.data] < Math.pow(10, 8) && rowData[column.data] != -10000) {
                    return '<a target="_blank" data-cardid="' + rowData[column.data] + '" class="j-open-profile" href="/XV/UI/Home#profile/=/empid-' + rowData[column.data] + '">' + this._newHighligh(lData[0].name, hightKey) + '</a>';
                }
                if (!isEm6 && lData.length === 1 && column.referRule != 'Employee') {
                    return '<span data-departmentid="' + rowData[column.data] + '">' + this._newHighligh(_.escape(lData[0].name || lData[0].deptName), hightKey) + '</span>';
                }
                return _.map(lData, (a) => {
                    return this._newHighligh(a.name || a.deptName, hightKey);
                }).join(',')
            }

            if (column.referRule == 'All' && souceType == 'result' && data != '') {
                var result = [];
                data = util.parseJson(data);
                _.each(data, function(item) {
                    _.each(item, function(cItem) {
                        result.push(cItem.name);
                    })
                });
                return result.join(',');
            };

            if (!column.isId) { // 老对象
                return _.isString(data) ? _.escape(data) : data;
            }
            if (_.isArray(data) && data.length > 1) { // 多个值
                return this.empCirclesFormat(data, column);
            }

            data = _.isArray(data) ? data.join('') : data; // 单个值
            if (column.referRule == 'Employee') {
                data = data == '-10000' ? {
                    name: $t('系统'),
                    id: data
                } : FS.crmUtil.getEmployeeById(data, {
                    includeStop: true
                });
                return data ? this._newHighligh(data.name, hightKey) : '';
            } else {
                data = FS.contacts.getCircleById(data);
                return data ? this._newHighligh(data.name, hightKey) : '';
            }
        },

        /**
         * @desc 多维度数据格式化
         */
        dimensionFormat: function(data, column, rowData, souceType, hightKey) {
            if (!data || !data.length) return '';
            return _.map(rowData && rowData[column.data + '__r'] || [], (a) => {
                return `<span data-dimensionid="${a.id}">${this._newHighligh(_.escape(a.name), hightKey)}</span>`;
            }).join(',');
        },

        /**
         * @desc 外部人员
         */
        outEmployeeFormat: function(data, column, rowData, souceType, hightKey) {
            if (!data || !data.length) return '';
            return _.map(rowData && rowData[column.data + '__l'] || [], (a) => {
                return this._newHighligh(_.escape(a.name), hightKey);
            }).join(',');
        },

        /**
         * @desc 外部部门
         */
        outDepartmentFormat: function(data, column, rowData, souceType, hightKey) {
            if (!data || !data.length) return '';
            return _.map(rowData && rowData[column.data + '__l'] || [], (a) => {
                return this._newHighligh(_.escape(a.name || a.deptName), hightKey);
            }).join(',');
        },

        empCirclesFormat: function(data, column, rowData, hightKey) {
            if (!data || !data.length) return column.defaultValue;
            var lData = rowData && rowData[column.data + '__l'];
            if (lData && helper._diffEmpDatas(lData, data)) {
                return _.map(lData, (a) => {
                    return this._newHighligh(_.escape(a.name || a.deptName), hightKey);
                }).join(',')
            }

            var fn = column.referRule == 'Employee' ? 'getEmployeesByIds' : 'getCirclesByIds';

            return _.map(data, (id) => {
                let obj = _.find(lData || [], a => a.id == id || a.deptId == id);
                if(!obj) {//前端兜底，理论上读取lData即可，如果lData没有，则大概率前端也获取不到值
                    let arr = util[fn]([id], {includeStop: true});
                    if(arr) {
                        obj = arr[0];
                    }
                }
                return obj ? this._newHighligh(_.escape(obj.name || obj.deptName), hightKey) : '--';
            }).join(',');
        },

        _diffEmpDatas: function(ld, data, column) {
            if (ld.length !== data.length) return;

            return !_.find(ld, function(a) {
                var _id = a.id || a.deptId;
                if (!_id) return false;
                return !_.find(data, function(id) {
                    return _id == id
                })
            })
        },

        /**
         * @desc 引用数据格式化
         */
        quoteFormat: function(data, column, def, rowData, hightKey) {
            if (data === void 0 || data === '' || data === null) return def;
            var qt = column.quote_field_type;
            var isSelectOne = qt === 'select_one';
            if (isSelectOne || qt === 'select_many') {
                var vv = rowData[column.api_name + '__r'];
                var ov = rowData[column.api_name + '__v'];
                var otherLabel = rowData[column.api_name + '__o'];
                if (ov && _.contains(isSelectOne ? [ov] : ov, 'other') && !otherLabel) {
                    otherLabel = vv.split && vv.split(',');
                    otherLabel = otherLabel && _.last(otherLabel);
                    if (otherLabel) {
                        rowData = _.extend({}, rowData);
                        rowData[column.api_name + '__ov'] = otherLabel
                    }
                }
                return column.options && column.options.length && ov !== void 0 ? helper[isSelectOne ? 'singSelectFormat' : 'mulSelectFormat'](ov, column, def, rowData) : this._newHighligh(vv ? _.escape(vv).replace(/#%\$/g, DELETESPAN) : _.escape(data), hightKey);
            }

            if (qt === 'location') {
                return helper.locationFormat(data, column, def, hightKey);
            }

            if (qt === 'image') {
                return helper.imgFormat(data, column, def);
            }

            if (qt === 'big_file_attachment' || qt === 'file_attachment') {
                return helper.attachFormat(data, column, def);
            }

            if(qt === 'currency') {
                return helper.moneyFormat(data, column, def, rowData);
            }

            if (qt === 'number') {
                return helper.numFormat(data, column, def, rowData, hightKey);
            }


            return  this._newHighligh(_.escape(data), hightKey);
        },

        phoneFormat: function(data, column, def, rowData) {
            return (rowData && rowData[column.data + '__s']) || data || def; //先取掩码再取原码
        },

        emailFormat: function(data, column, def, rowData) {
            return (rowData && rowData[column.data + '__s']) || data || def; //先取掩码再取原码
        },

        richTextFormat: function(data, column, def, rowData) {
            return (rowData && rowData[column.data + '__o']) || def;
        },

        /**
         * 拖动
         * @param {String}  String 选择器
         * @param {Object}  Object 拖动事件 start 开始 drag移动  stop停止
         */
        drag: function(selector, opts, context) {
            function dragHandle(e) {
                var me = this,
                    doc = document;
                opts.start && opts.start(e); // 开始
                if (opts.drag) {
                    doc.onmousemove = opts.drag;
                } // 拖拽
                doc.onmouseup = function() { // 结束
                    opts.stop && opts.stop(e);
                    doc.onmousemove = null;
                    doc.onmouseup = null;
                }
            }
            if (context) {
                $(context).on('mousedown', selector, dragHandle);
            } else {
                $(selector).on('mousedown', dragHandle);
            }
        },

        getTdWidth: function(dataType) {
            return widthConfig[dataType] || 100;
        },

        _empFormat: function(fieldName, val, data, fn) {
            if (!val || !val.length) return '';
            var lData = (data && (data[fieldName + '__l']) || data[fieldName + '__r']) || [];
            var ids = [];
            var strs = _.map(_.isArray(val) ? val : [], function(id, index) {
                var tt = _.find(lData, function(a) {
                    return a.id == id || a.deptId == id || a._id == id
                });
                return tt ? tt : (ids.push(id), {
                    id: id
                });
            })

            if (ids.length && fn) {
                _.each(FS.crmUtil[fn](ids, {
                    includeStop: true
                }), function(obj) {
                    var tc = obj && _.find(strs, function(a) {
                        return a.id == obj.id
                    });
                    tc && (tc.name = obj.name)
                });
            }

            var arr = [];
            _.each(strs, function(a) {
                var n = a.name || a.deptName;
                n && arr.push(n);
            })

            return arr.join(',');
        },

        dataVisibilityRangeFormat(val, column, data, hightKey) {
            let datas = data ? data[column.api_name + '__l'] : [];
            return _.map(datas, (a) => {
                let n = _.escape(a.name || '--');
                let nn = this._newHighligh(n, hightKey);
                return nn;
            }).join(',');
        },

        createDataTips: function(columns, data) {
            var dataTips = {};
            try {
                _.each(columns, function(column) {
                    dataTips[column.data] = helper.createDataTip(column, data[column.data], data);
                })
            } catch (e) {
                console.error(e);
            }
            return dataTips;
        },

        createDataTip: function(column, val, data) {
            var fieldName = column.data;
            var type = column.returnType || column.dataType;
            var ot = column.type;
            var title;
            
            if(typeof column.renderTips === 'function') return column.renderTips(column, val, data);

            if (fieldName === 'relevant_team') return _.escape(data.relevant_team__r || ''); //相关团队
            if (ot === 'html_rich_text' ||(ot === 'rich_text' && data[fieldName + '__o'])) return _.escape(data[fieldName + '__o']);
            if(ot === 'big_text') {
                return _.escape(data[fieldName + '__o'] || (data[fieldName] || '').slice(0, 100));
            }
            if (column._rendered) return '';
            if (column.render_type === 'quote') {
                return column.quote_field_type === 'text' || column.quote_field_type === 'long_text' ? _.escape(val || '') : '';
            }
            if (!val || data[fieldName + '__s']) return ''; //掩码和无值的返回空
            if (type == 1 || type == 101 || ot === 'text') {
                if (column.enable_multi_lang) {
                    var mlVal = data[fieldName + '__r'];
                    if (mlVal && _.isString(mlVal)) {
                        val = mlVal;
                    }
                }
                title = _.escape(val);
            } else if (type == 2) {
                title = helper.numFormat(val, column);
            } else if (type == 3) { 
                title = helper.moneyFormat(val, column, '', data);
            } else if (_.contains([31, 32, 33, 34], type)) {
                title = val;
            } else if (type == 38) {
                title = _.escape(data[fieldName + '__r']);
            } else if (type == 39 && val && val.split) {
                title = _.escape(val.split('#%$')[2]);
            } else if ((ot === 'employee' && type == 8) || type == 44) {
                title = _.escape(helper._empFormat(fieldName, val, data, 'getEmployeesByIds'));
            } else if ((ot === 'department' && type == 8)) {
                //title = _.escape(helper._empFormat(fieldName, val, data, 'getCirclesByIds'));
            } else if (type == 45) {
                title = _.escape(helper._empFormat(fieldName, val, data, 'getCirclesByIds'));
            } else if (type == 46) {
                title = _.escape(helper._empFormat(fieldName, val, data));
            } else if (ot === 'select_many' && type == 7) {
                title = helper.mulSelectFormat(val, column, '', data, true);
            } else if (ot === 'select_one' && type == 6) {
                title = helper.singSelectFormat(val, column, '', data, true);
            } else if (type == 4) {
                title = helper.timeFormat(val, 'time', '', column);
            } else if (type == 51) {
                title = helper.dataVisibilityRangeFormat(val, column, data);
            }

            return title
        }
    };


    module.exports = helper;
});
