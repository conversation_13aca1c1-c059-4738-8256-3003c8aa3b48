/**
 * @desc 入口文件
 * 表格分为三部分 1、头部  2、检索批量区域 3、表格主体
 * 此文件负责 构建三部分的布局
 * 负责表格主体渲染
 * @param columns 列配置
 * [{
     id:        '',       // 表示列的id
     data:      'Name',   // 读取数据的字段名称
     dataType:  '',       // 字段类型 3 金额  4 时间 10 日期 6 单选 7 多选 9 图片 11 二级级联
     formatStr: '',       // 用于时间的格式字符串 默认YYYY-MM-DD HH：mm
     options:   [],       // 多选单选的枚举项
     title:     '姓名',   // 表头名称
     width:     '',       // 宽度
     className: '',       // 名称
     isHidden:  true,     // 是否隐藏  // 默认不隐藏 隐藏的会在更多列表中展示
     fixed:     true,     // 是否固定
     fixedIndex:1,        // 固定列顺序
     isFilter: true,      // 支持筛选
     isOrderBy: false,    // 是否允许排序
     orderValues: ['asc', desc'],  // 排序类型值升降序
     defaultValue:  '--',          // 为空时的默认值
     placeHolder: '',              // 默认值
     render: function(data, type, fulldata) { // 自定义渲染方式
         data     // 当前单元格的值
         type     // fixed or column
         fulldata // 正行数据
     },
     noSupportBatchEdit:false //不支持批量编辑，默认false
   }]
 * @param fixedColumns 同columns
 *
 * 方法
 * doStaticData 处理静态数据
 * setParam
 * getParam
 * getParamByKey
 * setParamByKey
 * start
 * resize
 * getCheckedData
 * refresh
 * getRemberData
 * clearRemberData
 * addRemberData
 * reduceRemberData
 *
 * 事件
 * saveColumns 保存更多列事件 参数 所有数据
 * term.change 检索触发事件   参数 id 元素 事件对象
 * otherbtn.change 点击其他按钮时触发的事件 参数 元素 事件对象
 * dt.search  搜索事件
 * checkbox.click 复选框点击事件
 * 注释修改
 */
define(function (require, exports, module) {
	var doc = document,
		util = CRM.util,
		Select = require('crm-assets/widget/select/select'),
		makeTreeTable = require('./treetable'),
		Pagination = require('./pagination/pagination'),
		TermBatch = require('./termbatch/termbatch'),
		Operate = require('./operate/operate'),
		Search = require('./search/search'),
		Edit = require('./edit/edit'),
		BatchEdit = require('./batchedit/batchedit'),
		helper = require('./common/helper'),
		filterRule = require('./common/filterrule'), // 过滤规则
		measure = require('./measure'),
		imgEdit = require('./edit/src/img'),
		attachEdit = require('./edit/src/attach'),
		layoutTpl = require('./template/layout-html'),
		selectTpl = require('./template/select-html'),
		mainTpl = require('./template/main-html'),
		tableTrTpl = require('./template/tabletr-html'),
		tableTdTpl = require('./template/tabletd-html'),
		operateTpl = require('./template/operate-html'),
		TableSetting = require('./tablesetting/tablesetting'),
		errorMsgTpl = require('./template/errorMsg-html');

	var FI = 10;
	var Table = CRM.Widget.extend(_.extend({

		// 样式命名空间
		_cls: 'crm-w-table crm-w-table20',

		options: function () {
			return {
				$el: $('body'), // 默认添加到body中区
				custom_className: '',           // 用户自定义表格class
				className: 'crm-table crm-table-noborder crm-table-open',
				zIndex: 1000, // 层级
				autoHeight: false, // 是否自适应高度 * 不做高度限制  和 数据同高
				maxHeight: 'none',  //  最大高度 {{string}}
				showRequiredTip: false, // 显示必填*项标示

				api_name: '',
				showMask: false,
				title: '',     // 头部标题
				searchTip: '', // 搜索的提示文案

				termBatchPos: 'T',  // 搜索 批量位置
				showTermBatch: true,    // 是否显示检索区域
				showFilerBtn: false, // 是否显示过滤按钮
				hideFilterBtn: false, // 隐藏过滤按钮，不影响渲染逻辑
				showTagBtn: false,    // 是否显示标签筛选
				filterOr: false, // 是否支持搜索或关系
				showMoreBtn: false, // 是否显示更多列按钮
				showMultiple: false, // 是否显示复选框
				showBatchBtns: false, // 批量区域始终展现
				showSize: true, // 显示表格尺寸配置
				single: false, // 是否为单选
				arrowResetSingle: false, // 单选是否允许切换
				lineCheck: false, // 点击行选中
				colResize: true, // 允许改变列宽度
				size: 2, // 表格大小  暂未支持 目前默认2
				isMyObject: false, // 是否为自定义对象 业务参数 慎用

				sortFields: 'sortFields',// 高级排序字段的key
				sortField: 'SortField', // 排序字段的key   如： SortField： 'Name' 多层点好拼接
				sortType: 'SortType',   // 排序字段值的key 如：SortType： 'desc'   多层点好拼接
				orderColumn: null,      // 排序列 {name: '', orderby: 'asc'}
				orderColumns: null,      // 排序列 [{name: '', orderby: 'asc'}]
				addOrderColumn: null,   // 新建的默认排序
				columns: [],            // 所有列配置信息

				filterAloneColumns: null, // 单独筛选的列
				filterColumns: null,      // 所有筛选条件的列
				filterDatas: null,      // 默认筛选的数据 {aloneValues: {}, filters: []}

				addColumns: null,    // 新建时的所有的列

				alwaysShowTermBatch: false, // 始终显示检索区域 由于扩展配置条件
				searchTerm: null, // 表格检索配置项  参数说明 查看termbatch文件
				batchBtns: [], // 批量操作按钮    参数说明 查看termbatch文件
				otherBtns: [], // 其他按钮位置在批量操作后 参数说明 查看termbatch文件
				operate: null, // 操作按钮对象    参数说明 查看operate文件
				search: null, // 关键字搜索对象  参数说明 查看search文件
				hideSearch: false, // 隐藏搜索，不影响渲染逻辑

				showPage: true, // 是否显示分页配置
				noAlwaysShowPage: false, // 默认总显示分页
				pageType: 'pageNumber', // 分页类型 pageNumber or pageTime
				pageTimeKey: 'PageTime',

				url: '', // 数据请求url
				requestType: 'api', // 请求类型 FHHApi  api define
				method: 'get',
				requestCodeKey: 'errorCode',

				checked: null,
				// 配置checked 选项会打开check的记忆功能，分页时会记录上一页的选择
				// {idKey: '', data: [{}..]}
				// idKey 表示 表示数据中唯一的值的key data 可配置以选中的数据

				disabledcfg: null,
				// 不允许选中
				// {idKey: '', data: [{}..]}
				// idKey 表示 表示数据中唯一的值的key data 可配置以选中的数据

				scrollLoad: false,  // 滚动加载
				scrollLoadY: false, // 开启Y轴加载

				openStart: false, // 开启后不会直接请求数据需要手动调用start方法开始请求
				doStatic: false,  // 开启处理静态数据
				trHandle: true,   // tr上面是否显示小手
				colMinWidth: 145, // 默认最小列宽度
				noAllowedWrap: true, // 不允许换行 有固定列时无效
				noDataTip: $t('没有数据'), // 无数据提示
				defaultValue: '--', // 列为空时的默认显示全局
				rowCallBack: null, // 行的回调函数
				initComplete: null, // 表格渲染完成函数
				termBatchComplete: null, // 筛选和批量区域完成函数
				formatData: function (data) { // 格式化数据
					return {
						totalCount: data.totalCount,
						data: data.Items
					}
				},
				requestResult: null, // 请求表格数据结果回调方法 ( fun )
				errorAlertModel: 2, //  ajax 请求是否弹出提示框 ，默认弹出
				isShowAllChecked: true,		// 是否显示全选复选框
				isAddPlusSign: false,		// 是否显示加号。配合描述使用
				editable: false,            // 标记当前表格是否是编辑类型的
				refreshDetail: null, // lookup字段编辑，刷新监听事件
				noSupportLock: null, //不支持列锁定 如果你的表格业务对列的需求改造较多，建议传入true
				showWaterMask: false, //是否显示水印
				// noDataStyle: 'big',           // 无数据时提示样式；big/small
				visiblePageNums: 7, // 分页显示个数
				showHelpTip: false,
				isPreObjBatchEdit: false, //是否只是批量编辑
				forbidKeydown: function () {
					return false
				}, // 禁止键盘事件编辑单元格
				keydownEventExt: null, // fn 键盘按钮事件复写
				showAllCheckDatas: false, //显示已选择的所有数据
				separateCheckCount: false, // 渲染选中数量，不和批量按钮强绑定

				refreshCallBack: '',
				forceTrWrap: false,//强制换行，如果是true，行高自适应，没有前后固定列

				hideIconSet: false,//隐藏表格设置图标

				hideTermNum: false,// 不显示场景总数据量

				// [{
                //     FieldName: "name",
                //     disabled: true,
                //     Comparison: 1, //1等于 2不等于 3大于|晚于 4大于等于|不早于
                //     FilterValue: '111',
                //     onlyOr: [1, 2]
                // }, {
                //     FieldName: 'create_time'
                // }]
				customOutFilters: null,//默认的外露筛选条件,

				// 中间部分最小宽度
				// 当拖拽固定列宽时，如果中间可视宽度小于该值时，不应许改变宽度
				// <= 0 不做限制
				minContentWidth: 100,

				supportMultiFieldSort: false, // 是否支持多字段排序
				noCancelChecked: false,	 	// 点击行不触发 checkbox反选
				ignoreBatchOperate: false,	// 忽略统计已选区域
				resetFixDomPos: false 		// 如果遇到表格数据刷新后，复选框展示不全的问题，传 true 试一下
			}
		},

		isEm6:  false,   // 是否为下游

		tableStatus: 1, //  表格加载状态   1：开始加载    2：销毁表格   3：加载表格和分页完成

		// 记录请求次数
		requestCount: 0,

		// 保存默认参数
		defaultParam: null,

		// 保存当前数据
		curData: null,

		// 窗口resize的timer
		_rzTimer: null,

		// 记录全局搜索时的keyWord
		_keyword: '',

		// 记录选中的项
		_remberData: [],

		/**
		 * @desc 初始化
		 * 函数条用顺序 不要改变
		 * 计算高度要在头部和检索区域完成后
		 * 为了能够通过元素把检索筛选模块和主体模块关联起来
		 */
		setup: function () {
			// this.options = _.extend({}, _.result(Table.prototype, 'options'), this.options);
			var me = this,
				opts = this.options;
			me.helper = helper;

			me._addTagColumn();

			if(_.find(opts.columns, a => a.auto_wrap_line && !a.isHidden)) {
				opts.forceTrWrap = true;
				_.each(opts.columns, a => {
					a.auto_wrap_line || (a.className = [a.className || '', 'td-line-nowrap'].join(' '));
				})
			}

			if(opts.disabledTrWrap) {
				opts.forceTrWrap = false;
			}

			//强制换行
			opts.forceTrWrap && (opts.noAllowedWrap = false, opts.noSupportLock = true);

			me.__startTime = new Date().getTime();
			_.extend(Table, _.clone(filterRule));
			Table.clearRule();
			me.$el.addClass(me._cls);
			me.$el.addClass(me.options.custom_className);
			me.$el.addClass('crm-w-table-size' + opts.size); // 表格大小 TODO 优化掉
			opts.hideTermSoftly && me.$el.addClass('crm-w-table-term-soft-hidden'); // 通过UI先隐藏场景
			opts.creationSource && me.$el.addClass(`creation-source__${opts.creationSource}`);
			opts.showFilerBtn && opts.isMyObject && me.$el.addClass('crm-w-table-cellflex');
			opts.searchTerm = opts.searchTerm ? _.extend({pos: 'T'}, opts.searchTerm) : null;
			opts.sizeType || (opts.sizeType = CRM.getLocal('table-size') || 'md');
			opts.page = opts.page || {};
			opts.page.pageSize = opts.page.pageSize * 1 || CRM.getLocal('table-pagesize') * 1 || 20;
			opts.page.pageNumber = opts.page.pageNumber || 1;
			opts.page.pageSizeOption = opts.page.pageSizeOption || [20, 50, 100];
			opts.postData = opts.postData || {};


			!me.__hasEdit() && me.$el.addClass('crm-w-table-noedit');

			//竖向分割线逻辑
			opts.tdLineStatus = CRM.getLocal('table-tdline-status') === 'border'
			if(opts.tdLineStatus) {
				me.$el.addClass('crm-w-table-td-border');
			}

			me.$el.addClass('crm-table-' + opts.sizeType); // 表格大小
			opts.noSupportLock && me.$el.addClass('crm-table-nolockcolumn');
			me._isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
			// opts.scrollLoadY = !me._isMac;  // mac 不开启Y轴加载
			opts.scrollLoadY = true;

			if(opts.layoutTpl){
				me.$el.html(opts.layoutTpl(opts)); // 把结构添加到页面中去
			}else {
				me.$el.html(layoutTpl(opts)); // 把结构添加到页面中去
			}
			opts.$el.html(me.$el);     // 添加完成依赖到页面中去
			me._setOperateColumn();    // 设置操作列
			me._setCheckBoxColumn();   // 设置复选框
			me._setSernialnumberColumn();
			me.filterStatus = false;   // 是否在搜索状态
			me._remberColumnsOrder(opts.columns);
			me._getCountryOptions(function () {
				if (me.__destroyed) {
					return;
				}
				me._formatFilterAndAddColumns();
				me.formatColumns(); // 格式化列数据
				me._setNoWrap(); // 设置不换行
				me._countWidth(); // 计算宽度
				me._initDtTermBatch(); // 检索
				me._initOperate(); // 初始化化操作按钮
				me._supElement();
				me._initSearch();  // 初始化化搜索条
				me._countHeight();
				me._renderMain();
				me._countScroolLoad();  // 动态加载相关属性计算
				me._startStatus = opts.openStart; // 开启openStart后不会再请求数据
				me._renderList(true);
				me._bindEvents();
				me._bindScrollEvents();
				console.log('table init耗时:' + (new Date().getTime() - me.__startTime));
				CRM.Widget.prototype.setup.apply(me, arguments);
				
				//简单判断下升级提示条件 860上线两周后删除此逻辑 记得删除table.less里为引导写的特殊样式
				// if(me._termBatch && FS.store && FS.store.getLocal) {
				// 	try {
				// 		let guideKey = 'crm.table.header.guide00000';
				// 		if(FS.store.getLocal(guideKey)) return;
				// 		FS.store.setLocal(guideKey, 1);
				// 		let tt = _.findWhere(opts.fixedColumns, {isFilter: true}) || _.findWhere(opts.columns, {isFilter: true});
				// 		if(!tt) return;

				// 		let $guideTarget = me.$(`.header .th-${tt.data.toLowerCase()}`);
				// 		if(!$guideTarget.length) return;

				// 		require.async('base-modules/guide/guide', Guide => {
				// 			let pos = $guideTarget.offset();
				// 			if(!pos.top || pos.top < 1) return;

				// 			new Guide({
				// 				key: guideKey,
				// 				type: 'onetip',
				// 				data: [{
				// 					$target: $guideTarget,
				// 					pos:     'right',
				// 					top: -12,
				// 					//appendBody: true,
				// 					//删除逻辑后 记得去删除多语
				// 					text:    $t('crm.table.header.guide')
				// 				}]
				// 			})
				// 		})
				// 	} catch(e) {}
				// }
			});

			util.fetchObjectDownloadPermission(opts.objectApiname).then(function(flag) {
				if(me.__noDownLoadPermission = !flag) {
					me.$el.addClass('crm-w-table__nodownright');
				}
			})

			//埋个表格带border使用量的点
			opts.objectApiname && util.uploadLog('PaaSobj', 'List', {
				operationId: opts.tdLineStatus ? 'crmtablegridline' : 'crmtablenogridline',
				object_api_name: opts.objectApiname
			});
		},

		prepareColumns: function(columns) {
			return columns;
		},

		/**
		 * @desc 设置操作列
		 */
		_setOperateColumn: function () {
			var me = this,
				opts = me.options;
			var operate = _.findWhere(opts.columns, {dataType: 'operate'});
			if (!operate) {
				return;
			}
			operate = _.extend(operate, {
				data: null,
				title: $t('操作'),
				className: 'column-operate',
				width: operate.width || 64,
				isFilter: false,
				lastFixed: true,
				lastFixedIndex: 10000
			});
		},

		//------------------- 自定义标签字 段逻辑 start -------------------//
		//追加标签列
		_addTagColumn() {
			let {searchTerm, columns, showTagBtn, filterColumns} = this.options;
			if(searchTerm && searchTerm.options) {
				let s = _.findWhere(searchTerm.options, {id: searchTerm.defaultValue});
				if(!s || s.isHide) {
					s = _.find(searchTerm.options, a => a.isdef && !a.isHide);
				}

				if(!s) {
					s = _.find(searchTerm.options, a => !a.isHide);
				}

				if(s && s.show_tag) {
					let moreItem = _.findWhere(columns, {isSysDefined: true});
					columns.unshift({
						api_name: 'crm_tag__c',
						data: 'crm_tag__c',
						width: s.tag_width || helper.getTdWidth('crm_tag__c') || 240,
						title: $t('标签'),
						disableSet: true,
						fixed: this.options.forceTrWrap ? false : true,
						fixedIndex: moreItem ? 3 : -8,
						colResize: true,
						isFilter:  showTagBtn && filterColumns,
						noSupportLock: true,
						auto_wrap_line: s.tag_auto_wrap_line,
						className: s.tag_auto_wrap_line ? 'crm_tag_wrap_line' : '',
						render(a, b, trData) {
							let str = '--';
							if(trData.crm_tag__c && trData.crm_tag__c.length) {
								let content = _.map(trData.crm_tag__c, (tag) => {
									const sa = tag.tag_active === false ? `(${$t('已禁用')})` : '';
									return `<span>${_.escape(tag.tag_name)}${sa}</span>`;
								}).join('');
								str = `<div data-pos="left-top" data-title="crm_element_self__tip" class="crm-table-tag-column crm-ui-title">${content}</div>`;
							}
	
							return str;
						}
					});
				}
			}
		},
		//------------------- 自定义标签字 段逻辑 end -------------------//



		//------------------- 序号列 相关逻辑 start -------------------//
		_getSernialnumberColumn(pos) {
			return {
				_data: 'sernialnumber',
				data: null,
				_isCommonFixedColumn: true,
				width: 40,
				title: '#',
				fixed: this.options.forceTrWrap ? false : true,
				fixedIndex: -9,
				nocolResize: true,
				noSupportLock: true,
				render() {
					return '<div class="__columm_sernialnumber"" style="color:#91959E;text-align">' + (arguments[pos] + 1) + '</div>';
				}
			}
		},

		//追加序列号列
		_setSernialnumberColumn() {
			if(this.getSernialnumberStatus()) {
				let {columns, showMultiple} = this.options;
				columns.splice(showMultiple ? 1 : 0, 0 , this._getSernialnumberColumn(4));
			}
		},

		//显示隐藏序号列
		toggleSerNialNumberColumn(flag) {
			if(!this._isShowSernialnumber()) return;
			let {allColumns, objectApiname, showMultiple} = this.options;
			if(flag) {
				allColumns.splice(showMultiple ? 1 : 0, 0, this._getSernialnumberColumn(3))
			} else {
				let column = _.findWhere(allColumns, {_data: 'sernialnumber'});
				column && allColumns.splice(_.indexOf(allColumns, column), 1);
			}

			CRM.setLocal('table-sernialnumber-status' + objectApiname, flag);

			this.renderByColumns(this.options.allColumns, null, true, true);
		},

		_isShowSernialnumber() {
			let {forbidSernialnumber, objectApiname} = this.options;
			return !(forbidSernialnumber || !objectApiname);
		},

		getSernialnumberStatus() {
			if(!this._isShowSernialnumber()) return;

			let {objectApiname} = this.options;
			let vv = CRM.getLocal('table-sernialnumber-status' + objectApiname);

			// 910 改为默认全部放开
			// 930 恢复位910之前的逻辑
			// if (vv === void 0 || vv === null) {
			// 	return true;
			// }

			return !!vv;
		},

		_getSernialnumberConfig() {
			return {
				showSerNialNumber: this._isShowSernialnumber(),
				serNiaNumberValue: this.getSernialnumberStatus()
			}
		},

		_setSernialnumberValue($tr, index) {
			let {allColumns} = this.options;
			if(!_.findWhere(allColumns, {_data: 'sernialnumber'})) return;
			$tr.find('.__columm_sernialnumber').text(index);
		},
		//------------------- 序号列 相关逻辑 end -------------------//

		/**
		 * @desc 记住列的顺序
		 */
		_remberColumnsOrder: function(columns) {
			var _columnsOrder = [];
			var length = 0;
			_.each(columns || this.options.allColumns, function(a, index) {
				if(a.data && a.fixed && !a.isHidden) {
					if(++length > 10) {
						if(a.customFixed) {
							a.fixed = a.customFixed = void 0;
						}
					}
				}
				_columnsOrder.push(a.data);
				a.__posorderindex = index;
			})

			this._columnsOrder = _columnsOrder;
		},

		//
		// 设置滚动条列
		// 补一个滚动条距离
		//
		// _setScrollColumn: function () {
		// if (this.options.noAllowedWrap) {
		// this.options.columns.push({
		// data: null,
		// className: 'column-scroll',
		// width: 10,
		// lastFixed: true,
		// lastFixedIndex: 10002,
		// render: function () {
		// return '&nbsp;'
		// }
		// });
		// }
		// },

		/**
		 * @desc 设置复选款列配置_setCheckBoxColumn
		 * 复选款始终为固定列
		 * 根据是否记忆选中的项 渲染复选框
		 */
		_setCheckBoxColumn: function () {
			var me = this,
				opts = me.options;
			if (opts.showMultiple) {
				me._remberData = opts.checked ? opts.checked.data || [] : [];
				opts.columns.unshift({
					data: null,
					_isCommonFixedColumn: true,
					width: 46,
					title: opts.single || !opts.isShowAllChecked ? '&nbsp;' : '<em class="checkbox-item j-all-checkbox"></em>',
					className: 'column-checkbox',
					fixed: true,
					fixedIndex: -10,
					render: function (data, type, full) {
						// 禁用的样式
						var cls = '';
						var cfg = opts.disabledcfg;
						if (cfg) {
							if(cfg.filterOr) {
								cls = _.find(cfg.data, a => {
									return _.find(a, (v, k) => full[k] === v)
								})
							} else {
								cls = {};
								cls[cfg.idKey] = full[cfg.idKey];
								cls = _.findWhere(cfg.data, cls);
							}
							
							cls = cls ? 'checkbox-item-disabled' : '';
						}
						// 记录选中
						if (!opts.checked) {
							return '<em class="checkbox-item ' + cls + '"></em>';
						} else {
							var o = {};
							type = opts.checked.idKey;
							o[type] = full[type];
							data = _.findWhere(me._remberData, o);
							if (data) {
								_.extend(data, full);
							} // 补全记忆数据
							data = 'checkbox-item' + (!data ? '' : ' tb-checkbox-selected');
							data = cls ? (data + ' ' + cls) : data;
							return '<em class="' + data + '"></em>';
						}
					}
				});
			}
		},

		//
		//
		//
		_formatFilterAndAddColumns: function () {
			var me = this;
			if (me.options.addColumns) {
				me.options.addColumns = me._formatSimpleColumns(me.options.addColumns);
			}
			if (me.options.filterColumns) {
				me.options.filterColumns = me._formatSimpleColumns(me.options.filterColumns);
			}
		},

		/**
		 * Format a single column's data type and options
		 */
		_formatSimpleColumn: function(column) {
			if (!column) return column;
			
			column.dataType = helper.typeMap[column.dataType] || column.dataType;
			
			if (column.dataType == 35 || column.dataType == 36 || column.dataType == 40) {  // 计算和统计
				column.returnType = helper.typeMap[column.returnType] || column.returnType;
			} else {
				column.returnType = '';
			}
			
			if (column.dataType == 5 || column.returnType == 5) { // 补充全boolean类型
				column.options = column.options && column.options.length ? column.options : [{
					ItemName: $t('是'),
					ItemCode: true
				}, {
					ItemName: $t('否'), 
					ItemCode: false
				}];
			}
			
			return column;
		},

		/**
		 * Format multiple columns' data types and options
		 */
		_formatSimpleColumns: function(columns) {
			if (!columns) return;
			
			_.each(columns, column => {
				this._formatSimpleColumn(column);
			});
			
			return columns;
		},

		formatSimpleColumn: function(column) {
			return this._formatSimpleColumn(column);
		},

		/**
		 * @desc 格式化列数据
		 * 重新分配一次
		 * columns:      [],     所有非隐藏的列 非固定的列 表格
		 * fixedColumns: [],     头部固定列信息
		 * lastFixedColumns: [], 尾部固定列信息
		 * allColumns: [],       所有列信息包含隐藏显示 不包含data: null的列
		 */
		formatColumns: function () {
			var me = this,
				opts = me.options,
				columns = [],
				fixedColumns = [],
				lastFixedColumns = [],
				hideColumn = [],
				editable = opts.editable;
			_.each(this.prepareColumns(opts.columns), function (item) {
				item._options = item.options || [];
				if (item.width === void 0 && opts.isMyObject) {
					me._initMeasureWidth(item);
					item.width = item.measuredWidth || helper.getTdWidth(item.dataType) || 100;
				}
				item = _.extend({
					data: null,
					width: opts.colMinWidth,
					defaultValue: opts.defaultValue,
					isMyObject: opts.isMyObject,
					placeHolder: '',
					range: item.dataType == 2 ? [18, 0] : [10, 6], // 数字类型的位数
					isEdit: false,
					isRequired: false,
					dataType: 1,
					editable: editable,
					fixedIndex: item.customFixed ? ++FI : void 0
				}, item);
				item.dataType = helper.typeMap[item.dataType] || item.dataType;
				item.returnType = helper.typeMap[item.returnType] || item.returnType;
				if(item.dataType == 33 && (!item.range || !item.range.length)) {
					item.range = [10, 9];
				}
				// 业务类型 计算字段 引用不允许编辑
				item.isEdit = [22, 35, 36, 40].indexOf(item.dataType) != -1 ? false : item.isEdit;
				item.isEdit = !item.dataType || !item.data ? false : item.isEdit;
				if (item.dataType == 5 || item.returnType == 5) { // 补充全boolean类型
					item.options = item.options && item.options.length ? item.options : [{
						ItemName: $t('是'),
						ItemCode: true
					}, {
						ItemName: $t('否'),
						ItemCode: false
					}];
				}
				if (item.dataType != 35 && item.dataType != 36 && item.dataType != 40) {  // 计算和统计
					item.returnType = '';
				}
				if (item.default_is_expression) {
					item.expression = item.placeHolder;
					item.placeHolder = '';
				}
				item._rendered = !_.isUndefined(item._rendered) ? item._rendered : !!item.render; // 是否自定义了render
				item._realyRender = item._realyRender || item.render || function (data) {
					return data;
				}
				item._className = item.className || '';
				item.className = (item.data ? item.data.toLowerCase() : 'null') + (item._className ? ' ' + item._className : '');
				item.className += item.dataType == 9 || item.dataType == 37 ? ' column-img' : ''; // 图片列处理
				item.width = item.lastThTrueWidth || item.width;
				item.options = _.filter(item.options || [], function (o) { // 过滤掉点选多选中删除的值
					return !o.IsDeleted;
				});
				item.render = function (data, type, full, index) {

					// 高亮处理
					var name = item.data;
					var search = opts.search || {};
					var key;
					if(search.highFieldName === '#$%all_fields#$%') {
						key = me._keyWord || '';
					} else  {
						key = name == search.fieldName || name == search.highFieldName || (search.highOtherFieldName && search.highOtherFieldName.includes(name)) ? me._keyWord : '';
					}

					if(opts.hightKeyWord) {
						key = opts.hightKeyWord;
					}

					data = (!data && data != 0) ? '' : data;
					data = helper.getFormatVal(data, item, 'default', full, key, me.isEm6); // 格式化数据
					data = item._realyRender(data, type, full, helper, index, item, me);

					return me.beforeRender(data, this, full);
				}
				item = me.setColumnsOrderBy(item);

				item.showRequiredTip = opts.showRequiredTip && item.isRequired; // 是否显示必填提示符号*
				item.isFilter = item.isFilter && opts.showFilerBtn; // 是否显示筛选按钮
				item.colResize = opts.colResize && item.data;
				item.fixedIndex === void 0 && (item.fixedIndex = 5);
				opts.forceTrWrap && (item.fixed = item.lastFixed = false);
				if (!item.isHidden) {
					if (item.fixed) {
						fixedColumns.push(_.extend({
							fixedIndex: fixedColumns.length,
							__posorderindex: -1
						}, item));
					} else if (item.lastFixed) {
						lastFixedColumns.push(_.extend({
							lastFixedIndex: lastFixedColumns.length,
							__posorderindex: 10000
						}, item));
					} else {
						columns.push(item);
					}
				} else {
					hideColumn.push(item);
				}


			});
			opts.columns = columns;
			opts.fixedColumns = me._hackCustomColumnPos(_.sortBy(fixedColumns, 'fixedIndex'));
			opts.lastFixedColumns = _.sortBy(lastFixedColumns, 'lastFixedIndex');
			opts.allColumns = opts.fixedColumns.concat(opts.columns, opts.lastFixedColumns, hideColumn);
			me.__tableTypeNumber = 1 + (opts.fixedColumns.length ? 1 : 0) + (opts.lastFixedColumns.length ? 1 : 0);
			me.__startNoFixedColumns = !fixedColumns.length;
		},

		simplyFormatColumns: function() {
			var me = this,
				opts = me.options,
				columns = [],
				fixedColumns = [],
				lastFixedColumns = [],
				hideColumn = [];
			_.each(opts.columns, function (item) {
				if (item.lastThTrueWidth) {
					item.width = item.lastThTrueWidth;
				} else {
					me._ensureColumnMeasuredWidthIfNeeded(item);
					item.width = item.measuredWidth || item.width;
				}

				item = me.setColumnsOrderBy(item);

				if (!item.isHidden) {
					if (item.fixed) {
						fixedColumns.push(_.extend({
							fixedIndex: fixedColumns.length
						}, item));
					} else if (item.lastFixed) {
						lastFixedColumns.push(_.extend({
							lastFixedIndex: lastFixedColumns.length
						}, item));
					} else {
						columns.push(item);
					}
				} else {
					hideColumn.push(item);
				}
			});

			if(opts.forceTrWrap) {//排下序 避免之前排在前面的固定列被放到
				columns = _.sortBy(columns, a => {
					let fixedIndex = 0;
					if(a.fixedIndex !== 0) {
						fixedIndex = a.fixedIndex || a.lastFixedIndex || 100;
					}
					return fixedIndex;
				});
			}

			opts.columns = columns;
			opts.fixedColumns = me._hackCustomColumnPos(_.sortBy(fixedColumns, 'fixedIndex'));
			opts.lastFixedColumns = _.sortBy(lastFixedColumns, 'lastFixedIndex');
			opts.allColumns = opts.fixedColumns.concat(opts.columns, opts.lastFixedColumns, hideColumn);
			me.__tableTypeNumber = 1 + (opts.fixedColumns.length ? 1 : 0) + (opts.lastFixedColumns.length ? 1 : 0);
		},

		//设置自定义icon列的位置，不固定在第一列
		_hackCustomColumnPos(columns) {
			let key = CRM.util.getUserAttribute('crmIconColumnPos');
			if(key === false) return columns;

			if(key || CRM.ea === 'fs') {
				_.find(columns, (a, index) => {
					if(a._isCommonFixedColumn) return;

					if(a.isCustomIconColumn && columns[index + 1]) {
						columns[index] = columns[index + 1];
						columns[index + 1] = a;
						return true;
					}

					return true;
				})
			}

			return columns;
		},

		/**
		 * @desc render 之前，自定义处理数据
		 * @param data
		 * @param column
		 */
		beforeRender: function (data, column, rowData) {
			return data
		},

		/**
		 * @desc  统一设置所有列是否支持排序
		 */
		setColumnsOrderBy: function (column) {
			if (column.data && !_.isUndefined(this.options.isOrderBy_allColumn)) {
				column.isOrderBy = this.options.isOrderBy_allColumn;
			}
			return column;
		},

		/**
		 * @desc  获取列格式化后的值
		 * @param {{Mixed}} 原始值
		 * @param {{列名称}} 原始值
		 */
		getFormatVal: function (data, columnName) {
			var column = _.findWhere(this.options.columns, {
				data: columnName
			});
			return column ? helper.getFormatVal(data, column, 'default') : '';
		},

		/**
		 * @desc 获取国家省市区列
		 */
		_getCountryColumns: function () {
			var me = this,
				result = [],
				types = ['city', 'country', 'district', 'province'];
			_.each(me.options.columns, function (item) {
				if (item.type && types.indexOf(item.type) != -1) {
					result.push(item);
				}
			})
			_.each(me.options.addColumns || [], function (item) {
				if (item.type && types.indexOf(item.type) != -1) {
					result.push(item);
				}
			})
			_.each(me.options.filterColumns || [], function (item) {
				if (item.type && types.indexOf(item.type) != -1) {
					result.push(item);
				}
			})
			return result;
		},

		/**
		 * @desc 获取国家省市区的枚举项
		 * 补充国家省市区
		 */
		_getCountryOptions: function (cb) {
			var me = this,
				columns = me._getCountryColumns();
			if (columns.length == 0) {
				cb && cb();
				return;
			}

			if(util.getUserAttribute('crmNewArea') || util.getUserAttribute('crmAreaV3')) {
                _.each(columns, column => {
                    if (column.dataType != 40 && column.dataType != 'quote') {
                        column.dataType = 6; // 重置为单选单选类型
                    }
                })
                cb && cb();
                return;
            }

			//不能删除 业务组有错误用法
			var co = CRM.get('country_area_options');
			if (co) {
				me._doCountryColumns(columns, co);
				cb && cb();
				return;
			}

			me._countryAjax && me._countryAjax.abort();
			me._countryAjax = CRM.util.fetchCountryAreaOptions();
			me._countryAjax.then(res => {
				me._countryAjax = null;
				if(res && res.country) {
					me._doCountryColumns(columns, res);
				}
				cb && cb();
			}, () => {
				cb && cb();
			})
		},

		/**
		 * @desc 处理国家省市区列
		 */
		_doCountryColumns: function (columns, data) {
			_.each(columns, function (column) {
				if (column.dataType != 40 && column.dataType != 'quote') {
					column.dataType = 6; // 单选类型
				}
				column.options = _.map(data[column.type].options, function (o) {
					var depend = _.findWhere(columns, {
						cascade_parent_api_name: column.data
					});
					var child_options = o.child_options || [];
					if (depend && child_options.length > 0) {
						var dependData = {};
						var mapping = {
							country: 'province',
							province: 'city',
							city: 'district'
						};
						var dependKey = mapping[column.type];
						dependData[depend.data] = dependKey ? child_options[0][dependKey] : null;
						child_options = [dependData];
					}
					return {
						ItemCode: o.value,
						ItemName: o.label,
						Children: child_options
					};
				});
				column._options = column.options;
			});
		},

		/**
		 * @desc 设置内容不换行
		 * 条件：无固定列
		 */
		_setNoWrap: function () {
			var me = this,
				opts = me.options;
			if (opts.fixedColumns.length > 0 || opts.lastFixedColumns.length > 0 || opts.noAllowedWrap) {
				me.$el.addClass('crm-table-nowrap');
			}
			if (opts.fixedColumns.length > 0) { // 用于判读第一行元素
				me.$el.addClass('crm-table-fixed');
			} else {
				me.$el.addClass('crm-table-nofixed');
			}
		},

		//
		// 获取最后一列
		//
		_getLastColumn: function () {
			var columns = this.options.columns;
			var length = columns.length - 1;
			var last = null;
			do {
				last = columns[length];
				if (last && last.width !== 0) break;
				length--;
			} while (length >= 0)
			return last;
		},

		/**
		 * @desc 计算宽度
		 * mw 整个表格区域宽度
		 * tw 表格主体宽度
		 * fw 固定区域宽度
		 * pw 尾部固定列的宽度
		 * @return 表格宽度
		 */
		_countWidth: function () {
			var opts = this.options,
				mw = opts.$el.width(),
				lcolumn = this._getLastColumn(),
				lw = lcolumn ? lcolumn.width : 0,
				tw = 0,
				fw = 0,
				cw = 0,
				pw = 0;

			opts.lastThTrueWidth = lw; // 最后一行真实的宽度

			_.each(opts.fixedColumns, function (item, index) {
				fw += item.width + 1; // 加1位border的宽度
			});

			_.each(opts.lastFixedColumns, function (item, index) {
				pw += item.width + 1;
			});

			_.each(opts.columns, function (item, index) {
				tw += item.width + 1;
			});

			// 实际宽度大于列设置的宽度
			// 最后一列变更
			// if (mw - fw - pw > tw) {
			// tw = mw - fw - pw;
			// }

			if (mw - fw - pw - tw > 0) {
				lw += mw - fw - pw - tw - 1; // 补充到最后一列
				tw = mw - fw - pw + 1;
			}
			opts.fixedWidth = fw;
			opts.tableWidth = tw + fw + pw - 1;
			opts.lastFixedWidth = pw;
			opts.lastThWidth = lw;
			opts.lcolumn = lcolumn;
			if (lcolumn) {
				lcolumn.lastThTrueWidth = opts.lastThTrueWidth;
				// lcolumn.width = lw;
			}
			return opts.tableWidth;
		},

		/**
		 * @desc 计算表格高度
		 * 设置了高度直接返回
		 * 否则获取容器的高度 - 表头 - 检索批量区域
		 * 否则分页高度
		 * 最小高度 4 * 40 显示4行数据
		 * @return 表格高度
		 */
		_countHeight: function () {
			var me = this, opts = me.options;

			if (!opts || !opts.$el) {return;}

			var minHeight = 4 * 40,
				eh = opts.$el.height(),
				ch = $('.dt-caption', me.$el).outerHeight(), // 表头高度
				// 检索区域高度
				th = opts.termBatchPos == 'C' ? 0 : $('.dt-term-batch', me.$el).outerHeight(),
				ph = $('.dt-page', me.$el).outerHeight(); // 分页高度

			th = th || 0;

			// 容器设置了高度
			// 减去40表格表头头高度
			eh = (eh > minHeight && !opts.autoHeight && opts.height != 'auto') ? (eh - ch - th - ph - [35, 40, 40][opts.size - 1]) + 'px' : 'auto';
			eh = opts.height && opts.height != 'auto' ? parseInt(opts.height) + 'px' : eh;

			if(opts.showOutFilter || opts.crossFilterService) {
				var outFilterHeight = me._termBatch && me._termBatch.getOutFilterHeight();
				if(outFilterHeight && /px$/.test(eh)) {
					eh = (parseInt(eh) - outFilterHeight) + 'px'
				}
			}
			if(me._termBatch && !me.options.ignoreBatchOperate && me._termBatch.getBatchOperateHeight()) {
				eh = (parseInt(eh) - me._termBatch.getBatchOperateHeight()) + 'px';
			}

			opts.tableHeight = eh;
			return eh;
		},

		//
		//
		// 滚动加载用的相关属性
		// 默认第一次加载
		//
		_countScroolLoad: function () {
			if(this.options.forceTrWrap) return;

			var me = this, opts = me.options;
			if (!opts.scrollLoad) {
				return;
			}

			var w = 0, mw = opts.$el.width();
			mw = mw - opts.fixedWidth - opts.lastFixedWidth;

			var colspan = opts.columns.length;
			colspan += opts.fixedColumns.length > 0 ? 1 : 0;
			colspan += opts.lastFixedColumns.length > 0 ? 1 : 0;
			me._colspan = colspan; // 列的总数

			// 可视区域显示的行数
			me.showRowsNum = Math.ceil(parseInt(me.options.tableHeight) / 33) + 5;

			// 可视区域默认的列
			_.some(opts.columns, function (item, index) {
				w += item.width;
				me.showColsNum = index + 1;
				return w > mw;
			});
		},

		/**
		 * @desc 初始化检索和批量区域
		 * 检索区域 （筛选：全部 我的客户 高级筛选 标签管理）
		 * 批量按钮区域
		 * 其他按钮区域（列表视图 地图视图）
		 * 打开表格的筛选按钮
		 * @edit by hgl , 极简订单做扩展属性
		 */
		__getDtTermBatchOpts:function(){
			var me = this,
				opts = me.options;
			return {
				el: me.$('.dt-term-batch'),
				$termWrap: me.$('.term-item'),
				$table: me.$el,
				tableContext: me,
				$outFilter: me.$('.dt-out-filter'),
				$batchOpereate: me.$('.dt-batchoperate-wrapper'),
				alwaysShowTermBatch: opts.alwaysShowTermBatch,
				showSize: opts.showSize,
				shwoPage: opts.showPage,
				searchTerm: opts.searchTerm,
				separateCheckCount: opts.separateCheckCount,
				showBatchBtns: opts.showBatchBtns,
				batchBtns: opts.batchBtns,
				otherBtns: opts.otherBtns,
				showFilerBtn: opts.showFilerBtn,
				showTagBtn: opts.showTagBtn,
				filterOr: opts.filterOr,
				showMoreBtn: opts.showMoreBtn, // 设置列
				columns: _.sortBy(opts.allColumns, function(a) {
					return a.__posorderindex;
				}),
				orderColumn: opts.orderColumn,
				orderColumns: opts.orderColumns,

				filterAloneColumns: opts.filterAloneColumns,
				filterColumns: opts.filterColumns,
				filterDatas: opts.filterDatas,

				addColumns: opts.addColumns,
				addOrderColumn: opts.addOrderColumn,

				termBatchComplete: opts.termBatchComplete,
				zIndex: opts.zIndex,
				objectApiname: opts.objectApiname,

				showOutFilter: opts.showOutFilter,
				hideAloneFilter: opts.hideAloneFilter,

				isListLayout: opts.isListLayout,
				quickFilterField: opts.quickFilterField,
				sceneRenderType: opts.sceneRenderType,
				isTerm20: opts.isTemr20,
				showAllCheckDatas: opts.showAllCheckDatas,
				refreshCallBack: opts.refreshCallBack,
				customOutFilters: opts.customOutFilters,
				enableLiveFiltering: opts.enableLiveFiltering,
				supportMultiFieldSort: opts.supportMultiFieldSort,
				
				thirdAddRecordType: opts.thirdAddRecordType,

				aloneValueChangeCallBack: opts.aloneValueChangeCallBack,

				hideIconSet: opts.hideIconSet,

				hideTermNum: opts.hideTermNum,

				crossFilterService: opts.crossFilterService,
				crossFilterEndpoint: opts.crossFilterEndpoint,
			}
		},

		_initDtTermBatch: function () {
			if (!this.options.showTermBatch) {
				return
			}
			var me = this,
				opts = me.options;
			me._termBatch = new TermBatch(this.__getDtTermBatchOpts());
			me._termBatch.on('term.change', function (id, term) { // 切换检索项
				me.filterStatus = false;
				// 保证先trigger
				me.trigger('term.change', id, term);
				if (!term.isNotRequest) {
					me.setParamByKey(opts.searchTerm.type, id);
					Table.clearRule();
					//
					// 处理搜索带过去
					if (opts.search && opts.search.fieldName && me._keyWord) {
						me._doFilterData([{
							FieldName: opts.search.fieldName,
							Comparison: 7,
							FilterValue: me._keyWord
						}], true, 'advance');
					} else {
						me._doFilterData([], true, 'advance');
					}
				}
			});
			me.trigger('term.modify', function (data) {
				opts.searchTerm.options = data;
			});
			me._termBatch.on('term.advance', function (data, aloneValues, isInnerFilter) { // 筛选
				if (me.options.beforeTermAdvance && !me.options.beforeTermAdvance(data, aloneValues)) {
					return;
				} // 筛选前的逻辑
				Table.clearRule();
				me.filterStatus = true;
				me.setParamByKey('_aloneValuesKey', aloneValues);
				me._doFilterData(data, true, 'advance');
				me.trigger('term.advance', data, aloneValues, isInnerFilter);
				me._curFilterData = data;
				me._lightFilterColumns(me._curFilterData);
			});
			me._termBatch.on('filter.show', function () { // 筛选显示
				me.trigger('filter.show');
			});
			me._termBatch.on('otherbtn.change', function ($item, e) { // 其他按钮点击
				me.trigger('otherbtn.change', $item, e);
			});
			me._termBatch.on('term.bactchHide', function () { // 关闭批量term.bactchHide'
				me._clearChecked();
				me.trigger('term.bactchHide');
			});
			me._termBatch.on('columns.change', function (data) {
				me._remberColumnsOrder(data);
				me.renderByColumns(data, null, true, true);
				me.trigger('tb.savecolumns', data);
				me._lightFilterColumns(me._curFilterData || []);
			});
			me._termBatch.on('columns.reset', function () {
				me.trigger('tb.resetcolumns');
			});
			me._termBatch.on('batchbtn.change', function (item) {
				me.trigger('batchbtn.change', item);
			})
			me._termBatch.on('tag.change', function(data) {
				me.setParamByKey('tag_operator', data.tag_operator);
				me.setParamByKey('tags', data.tags);
				me._doFilterData([], true);
				me.trigger('tag.change', data);
			})
			me._termBatch.on('height.change', function(options) {
				if(!me.options.isListLayout) {
					me._fixTableHeight();
				};

				if (options && options.resizeTable) {
					me.resize();
				}

				me.trigger('height.change');
			});
			me._termBatch.on('term.showallcheck', function(callback) {
				var datas = me.getRemberData();
				if(!datas.length) {
					datas = me.getCheckedData();
				}
				datas.length && callback(datas, me.getSetColumnsPreVisible(6, true));
			})
			me._termBatch.on('multi.field.sort.change', function(orderFields) {
				me._handleMultiFieldSort(orderFields);
			})
		},

		getSetColumnsPreVisible: function(num, isCaintainsName) {
			return this._termBatch.getSetColumnsPreVisible(num, isCaintainsName);
		},

		updateTermBatchColumnStatus: function(column) {
			this._termBatch && this._termBatch.updateColumnStatus(column);
		},

		getFiltersExtraData: function() {
			return this._termBatch && this._termBatch.getFiltersExtraData();
		},

		_fixTableHeight: function() {
			var th = parseInt(this._countHeight());
			var $m = this.getMainWrapper().find('>.main');
			var $fir = $m.find('>.fix-start-b')
			var $last = $m.find('>.fix-end-b');
			$m.height(th);
			$fir.height(th - 10);
			$last.height(th - 10);
		},

		//
		// 点亮当前筛选的项
		//
		_lightFilterColumns: function (data) {
			var me = this;
			data = data || [];
			var $main = me.$('>.dt-main');
			$main.find('.filtercur').removeClass('filtercur').find('.j-th-filter').removeClass('cur');
			_.each(data, function (item) {  // 点亮标题筛序图标
				let fieldName = item.FieldName;
				if(fieldName === 'tag') {
					fieldName = 'crm_tag__c';
				}
				$main.find('.th-' + fieldName.toLowerCase() + ' .j-th-filter').addClass('cur').parent().addClass('filtercur');
			});
		},

		//
		// 获取当前场景
		//
		getCurTerm: function () {
			var me = this;
			if (me._termBatch && me._termBatch._term) {
				return me._termBatch._term.getCurTerm();
			}
			return null;
		},

		//
		// 设置筛选场景中独立字段的值
		//
		setFilterAloneColumnsValues: function (data) {
			var me = this;
			if(!me._termBatch) return;
			data && data.record_type && me._termBatch.updateRecordType(data && data.record_type);
			me._termBatch.filter && me._termBatch.filter.setAloneValues(data);
		},

		_createOperate: function (operate) {
			var me = this;
			me._operate = new Operate(_.extend({
				normalTriggerOperate: me.options.normalTriggerOperate,
				isListLayout: me.options.isListLayout,
				$el: me.$el,
				pos: 'C',
				btns: []
			}, operate));
			me._operate.on('click', function (event) {
				me.trigger('operate.btnclick', event);
			});
		},

		/**
		 * @desc 初始化操作按钮区域
		 * 如 新建 导入 导出 等
		 */
		_initOperate: function () {
			var me = this,
				opts = me.options,
				operate = opts.operate;
			if (operate) {
				me._createOperate(operate);
				// me._operate = new Operate(_.extend({
				//     $el: me.$el,
				//     pos: 'C',
				//     btns: []
				// }, operate));
			}
		},

		//
		// 更新操作
		// @param {{Array Object}} 新的按钮
		//
		upDataOperate: function (btns) {
			var me = this,
				operate = me.options.operate;

			if (operate) {
				me._operate && me._operate.destroy();
				me._createOperate({
					pos: operate.pos || 'C',
					btns: btns
				});
				// me._operate = new Operate({
				//     $el: me.$el,
				//     pos: operate.pos || 'C', xQhV98hsd
				//     btns: btns
				// });
			}
		},

		//一些场景补充元素
		_supElement: function() {
			var opts = this.options;
			var arr = [];
			var $opBox = this.$('.dt-op-box');
			if(opts.search && !this.$('.j-dt-sc-box').length) {
				var $div = '<div class="dt-sc-box j-dt-sc-box dt-sc-box__new"></div>';
				if($opBox.length) {
					$opBox.after($div);
				} else {
					var $dc = this.$('.dt-caption');
					$dc.length ? $dc.append($div) : this.$('.dt-term-batch').append('<div class="batch-term batch-term-operate crm-clearfix">' + $div + '</div>');
				}
			}
			if((opts.showPage && opts.showSize) || opts.refreshCallBack) {
				if(!this.$('.dt-control-btns').length) {
					arr.push(`<div class="dt-control-btns dt-control-btns__new">`);
					!opts.hideIconSet && opts.showSize && arr.push(`<span data-pos="top" data-title="${$t('设置')}" class="crm-ui-title fx-icon-set j-dt-icon-tableconfig"></span>`);
					opts.refreshCallBack && arr.push(`<span style="margin-left:12px;" data-pos="top" data-title="${$t('刷新')}" class="crm-ui-title fx-icon-refresh j-dt-icon-refresh"></span>`);
					arr.push('</div>');
					if(!$opBox.length) {
						this.$('.dt-page-right').prepend(arr.join(''));
					} else {
						$opBox.after(arr.join(''));
					}

				}
			}

			if($opBox.length && $opBox.css('position') === 'absolute') {
				var $tt = $opBox.parent().find('.dt-control-btns');
				if ($tt.length) {
					var tx = $opBox.width() + 20;
					var $other = $tt.next('.other-item');
					$tt.css('transform', `translateX(-${tx}px)`);
					$other.length && $other.css('transform', `translateX(-${tx}px)`);
				}
			}
		},
		/**
		 * @desc 初始化搜索区域
		 */
		_initSearch: function () {
			var me = this,
				opts = me.options,
				search = opts.search;
			me._searchkeydown = 0;
			if (search) {
				var $wrap = me.$('.j-dt-sc-box');
				me._search = new Search(_.extend({
					el: $wrap,
					pos: 'C',
					placeHolder: $t("请输入搜索文本"),
					filterColumns: search.showFilterField ? (opts.filterColumns || opts.allColumns) : null,
					objectApiName: opts.objectApiname,
					cacheSearchField: opts.cacheSearchField,
				}, search, {
					searchDefaultKeyword: opts.searchDefaultKeyword || search.searchDefaultKeyword
				}));
				me._search.on('search', function (keyWord, field) {
					me._doSearch(keyWord, field);
				});

				me._search.on('keydown', function (code, isSearching) {
					if (isSearching) return;
					var $tr = me.$('.main .tr-hover');
					$tr.removeClass('tr-hover');

					switch (code) {
						case 13:
							if (me._searchkeydown > 0) {
								$('.column-checkbox', $tr).trigger('click');
							}
							break;

						case 38: // up
							$tr = $tr[0] ? $tr.prev() : me.$('.main .tr:last');
							$tr = !$tr[0] ? me.$('.main .tr:last') : $tr;
							$tr = me.$('.main .tr[data-index=' + $tr.data('index') + ']'); // 所有tr
							me.scrollToTr($tr);
							me._searchkeydown += 1;
							break;

						case 40: // down
							$tr = $tr[0] ? $tr.next() : me.$('.main .tr:first');
							$tr = !$tr[0] ? me.$('.main .tr:first') : $tr;
							$tr = me.$('.main .tr[data-index=' + $tr.data('index') + ']');
							me.scrollToTr($tr);
							me._searchkeydown += 1;
							break;
					}
					$tr.addClass('tr-hover');
				});
			}
		},

		//hack 控制全字段的显示隐藏 后期需要优化 仅 crm-modules/page/list/list.js下有调用
		_toggerAllField: function(flag) {
			this._search._toggerAllField(flag);
		},

		getMainScroll() {
			return this.$('.main .main-scroll:first');
		},

        //
        // 滚动表格到指定位置
        //
        scrollTo: function(x, y, relative) {
            var oscroll = this.$('.main-scroll')[0];
            if (!oscroll) {return;}
            if (relative) {
                oscroll.scrollBy(x, y);
                return;
            }
            oscroll.scrollTo(x, y);
        },

		// 滚动到指定的行
		//
		scrollToTr: function ($tr) {
			if (!$tr[0]) {
				return;
			}
			var me = this;
			var opts = this.options;
			var $scroll = me.$('.main .main-scroll');
			var scrollTop = $tr.offset().top - $scroll.offset().top;
			var trHeight = $tr.outerHeight();
			var tableHeight = parseInt(opts.tableHeight);

			if (scrollTop > tableHeight - 20) {
				$scroll.scrollTop($scroll.scrollTop() + trHeight + scrollTop - tableHeight + 8);
			} else if (scrollTop < 0) {
				$scroll.scrollTop($scroll.scrollTop() - trHeight + scrollTop);
			}
		},

		scrollToTr20: function(pos) {
			var $scroll = this.$('.main .main-scroll');
			$scroll.scrollTop((pos - 1) * 34);
		},

		//
		// 滚动到指定的列
		//
		scrollToTd: function (name) {
			var me = this;
			var $th = me.$('.header .th-' + name);
			if (!$th[0]) {
				return;
			}

			if ($th[0].timer) {
				clearTimeout($th[0].timer);
			}

			$th[0].timer = setTimeout(function () {
				var opts = this.options;
				var $scroll = me.$('.header .tb-b');
				var offsetleft = $th.offset().left - $scroll.offset().left;
				var thWidth = $th.outerWidth();
				var tableWidth = $scroll.width();
				if (offsetleft > tableWidth - thWidth) {
					$scroll.scrollLeft($scroll.scrollLeft() + thWidth + offsetleft - tableWidth);
				} else if (offsetleft < 0) {
					$scroll.scrollLeft($scroll.scrollLeft() - thWidth + offsetleft);
				}
			}, 80);
		},

		scrollToTd20: function(name) {
			var columns = this.getAllColumns();
			var column = _.findWhere(columns, {data: name});
			if(!column || column.fixed) return;
			var $scroll = this.$('.header .tb-b');
			var sw = $scroll.width();
			var w = 0;
			var fw = 0;
			_.find(columns, function(a) {
				if(a.fixed) {
					fw += a.width + 1;
				}
				w += a.width + 1;
				return column === a;
			})

			if(w > sw) {
				$scroll.scrollLeft(w - sw + column.width);
			} else {
				var scrollLeft = $scroll.scrollLeft();
				if(w - column.width - fw < scrollLeft) {
					$scroll.scrollLeft(w - column.width - fw);
				}
			}
		},

		_getMainTmp(){
			return mainTpl;
		},

		/**
		 * @desc 渲染主体结构
		 */
		_renderMain: function (flag) {
			var me = this;
			var opts = me.options;
			if (me.__destroyed) {
				return;
			}
			let mt = this._getMainTmp();
			$('.dt-main', me.$el).html(mt(opts));
			me._lightSortFields();
			// me._initScrollBar();
			if (!flag) {
				me._initCompleteTimer = setTimeout(function () { // 调用表格方法时报错兼容
					!me.__destroyed && opts.initComplete && opts.initComplete(me.$el); // 渲染主体完毕调用回调函数
				}, 60);
			}
			opts.showWaterMask && this.renderWaterMask();
			opts.afterRenderMain && opts.afterRenderMain();
		},

		/**
		 * @desc 渲染水印
		 */
		renderWaterMask: function() {
		 	FS.util.addWatermark && FS.util.addWatermark(this.$('>.dt-main')[0]);
		},

		// 是否为多字段高级排序
		isMultiFieldSortMode: function() {
			const options = this.options;

			if (!options.supportMultiFieldSort) {
				return false;
			}

			return !!this.getParamByKey(options.sortFields);
		},

		_lightSortFields() {
			const options = this.options;
			const orderColumns = options.orderColumns;
			if (options.supportMultiFieldSort && (orderColumns && orderColumns.length > 1)) {
				this._lightMultiFieldSortBtn();
			} else {
				this._lightOrderColumn();
			}
		},

		/**
		 * 点亮高级排序按钮
		 */
		_lightMultiFieldSortBtn: function(columns) {
			columns = columns || this.options.orderColumns;
			if (!columns || !columns.length) {
				return;
			}

			const sortFields = columns.map((column) => {
				return {
					name: column.name,
					type: {'asc': 1, 'desc': 2}[column.orderby],
				}
			});
			this.setParamByKey(this.options.sortFields, sortFields);
			this._termBatch?.toggleMsStatus(true);
		},

		/**
		 * @desc 点亮排序的列
		 * @param {Object} column 列 {name:  '', orderby: 'asc'}
		 */
		_lightOrderColumn: function (column) {
			column = column || this.options.orderColumn;
			if (!column || !column.name || !column.orderby) {
				return;
			}
			var name = column.name.toLowerCase();
			this.$('.header .th-' + name).addClass('th-sort-' + column.orderby);
			this.setParamByKey(this.options.sortField, column.name);
			this.setParamByKey(this.options.sortType, {'asc': 1, 'desc': 2}[column.orderby]);
		},

		/**
		 * @desc 根据新的列重新渲染表格
		 * @param columns {{Array Object}}
		 * @param params {{Object}} 参数 是否重新请求数据
		 * @param setFix {{bollean}} 设置固定列
		 */
		renderByColumns: function (columns, params, setFix, noSearch) {
			var me = this,
				opts = me.options;
			opts.columns = me.prepareColumns(columns);
			if (!setFix) {
				if (opts.operateColumn) {
					opts.columns.push(opts.operateColumn);
				}
				me._setCheckBoxColumn();
				// me._setScrollColumn();
			}
			me.simplyFormatColumns();
			me._countWidth();
			me._countHeight();
			if(!this.__retainPaginationStatus) {
				me._pagination && me._pagination.destroy();
				me._pagination = null;
			}
			me._pageTimeRender = false;
			me._renderMain(true);
			me._countScroolLoad();
			// Table.clearRule(); //  重置检索条件
			_.each(Table.getRule(), function (item) {
				me.setParamByKey(item.type, item.value);
			});
			if (opts.search && !noSearch) { // 搜索参数
				if (opts.search.type) {
					me.setParamByKey(opts.search.type, '');
				} else if (me._keyWord && opts.search.fieldName) {
					Table.delRule(opts.search.fieldName);
					var rules = Table.getRule();
					_.each(rules, function (item) {
						me.setParamByKey(item.type, item.value);
					});
				}

				me.$('.dt-ipt').val('');
				me._keyWord = '';
			}
			me._termBatch && me._termBatch.set('columns', opts.allColumns);
			me._drawPage(me.getCurData(), me._totalCount);
			me.trigger('renderListComplete');
			me.$('.main-scroll').off();
			me._bindScrollEvents();
		},

		/**
		 * @desc 渲染表格列表
		 */
		_renderList: function (init) {
			var me = this;
			let _st = new Date().getTime();
			me._getData(function (res) {
				let _et = new Date().getTime();
				me.trigger('getDataComplete', init);
				me._drawPage(res.data || [], res.totalCount);
				me.trigger('renderListComplete', init, {
					getDataCostTime: _et - _st,
					dataRenderCostTime: new Date().getTime() - _et
				});
			});
		},

		/**
		 * @desc 根据数据绘制页面
		 * @param {{Array Object}} 要处理的数
		 * @param {{Number}}       总条数
		 * @param {{Boolean}}      保持滚动条位置不变
		 * 1、创建固定列
		 * 2、创建表格主体列
		 * 3、TODO 创建操作设置列
		 * 循环测试太多 考虑 性能问题 行数 * 列数
		 */
		_drawPage: function (data, totalCount, keepScroll) {
			var me = this,
				opts = me.options;
			if(opts.resetFixDomPos) me._fixDomPos = 'X';
			data = data || [];
			if (data.length > 0) {
				this.addRow(data, false, 2);
			} else {
				me._showNodata(me._keyWord ? $t('没有找到') + '“' + _.escape(me._keyWord) + '”' : opts.noDataTip);
			}

			if(!opts.columns.length && (!opts.fixedColumns.length || (opts.fixedColumns.length === 1 && !opts.fixedColumns[0].data))) {
				me._showNodata($t('暂无可显示的列'));
			}

			!keepScroll && $('.main .main-scroll', me.$el).scrollTop(0); // 滚动到顶部

			if(me.options.isListLayout && !totalCount) { //修正前后固定列的高度
				var $main = this.getMainWrapper().find('>.main');
				$main.find('.fix-start-b,.fix-end-b').height($main.height() - 10);
			}

			// $('.header', me.$el).removeClass('header-shadow');
			me._renderPage(totalCount || 0);
			me.$el.toggleClass('crm-table-nodata', data.length == 0);
			//找到的项才执行removeClass,不需要多余的执行
			if (me._remberData && me._remberData.length) {
				me.$('.j-header-fixed .checkbox-item').removeClass('tb-checkbox-half-selected tb-checkbox-selected');
			}
			me._renderRemberData();
			me._edit && me._edit.destroy();

			me.options.drawPageAfter?.(data, totalCount);

			//防止表格数据与表头对不齐，未找到具体原因 先resize修复
			//行业订单全屏模式表格上移问题：加上keepScroll（目前只有行业订单入参为true）控制：不执行resize，表格就不会上移
			!keepScroll && me.resize();
		},

		// 滚动加载处理
		// 避免大量dom，只会展示可视区域的dom
		// 滚动出可视区域的dom会销毁做占位
		_doScrollLoad: function (scrollY, scrollX, flag) {
			//折行的时候，不支持滚动加载。
			if (!this.curData || this.options.forceTrWrap) return;

			var me = this, mainPos = {}, bounds = {}, data = me.curData.data, opts = me.options;


			// Y轴
			if (opts.scrollLoadY && (me._scrollY != scrollY || flag)) { // Mac只处理x轴
				me._doScrollLoadY(data, scrollY);
			}

			// X轴
			if (me._scrollX != scrollX || flag) {
				var w = 0, mw = opts.$el.width() - opts.fixedWidth - opts.lastFixedWidth;
				var viewColIndex = [];
				_.each(opts.columns, function (item, index) {
					mainPos.right = scrollX + mw;
					mainPos.left = scrollX;
					bounds.left = w;
					w += item.width + 1;
					bounds.right = w;
					if (!(mainPos.right < bounds.left || mainPos.left > bounds.right)) {
						viewColIndex.push(index);
					}
				});
				me._viewColIndex = [viewColIndex[0], viewColIndex[viewColIndex.length - 1]];
				me.$('.main .tb .tr[data-ld="loaded"]').each(function (index, item) {
					index = $(item).attr('data-index') * 1;
					$(item).html(me._createTableTd(data[index], index));
				});
				me._scrollX = scrollX;
			}
		},

		getTrHeight() {
			let sizeType = this.options && this.options.sizeType;
			return !this.__hasEdit() ? {sm: 30, md: 34, lg: 42}[sizeType] || 34 :  {sm: 35, md: 42, lg: 51}[sizeType] || 42;
		},

		_doScrollLoadY: function (data, scrollY) {
			var me = this;
			var toIndex = 0;
			var $main = me.$('.main');
			var $h = $main.height() + 200;
			var ceilNum = 0;
			var isFirst;
			var trHeight = me.getTrHeight();
			function _isBound(index, $item) {
				if(isFirst && ceilNum) {
					toIndex = index + 1;
					ceilNum--;
					return true;
				}
				if(index === toIndex) {
					var pos = $item.position();
					if(pos.top < -200) {
						toIndex += (Math.floor(((0 - pos.top) - 200) / trHeight)) || 1;
					} else if(pos.top > $h) {
						toIndex = -1;
					} else {
						toIndex = index + 1;
						isFirst || (ceilNum = 15);//从现在起至少接下来的15条数据不用计算位置
						isFirst = true;
					}
					return pos.top > -200 && pos.top < $h
				}
			}

			$main.find('.tb .tr').each(function (index, item) {
				var $item = $(item), ld = $item.attr('data-ld');
				// 出现在视图中
				if (_isBound(index, $item)) {
					if (ld == 'loaded') {
						return;
					}
					$item.html(me._createTableTd(data[index], index));
					$item.attr('data-ld', 'loaded');
				} else if(ld) {
					$item.html('<td colspan="' + me._colspan + '"><div class="tb-cell"></div></td>');
					$item.attr('data-ld', '');
				}
			});

			me._scrollY = scrollY;
		},


		//
		// 创建滚动加载的td
		// index 当前行索引
		//
		_createTableTd: function (data, index) {
			var opts = this.options;
			var viewIndex = this._viewColIndex || [0, this.showColsNum];
			var columns = opts.columns.slice(viewIndex[0], viewIndex[1] + 1);
			var firstColSpan = viewIndex[0] != 0 ? viewIndex[0] : 0;
			var lastColSpan = opts.columns.length - viewIndex[1] - 1;
			return tableTdTpl({
				lastFixed: opts.lastFixedWidth != 0,
				fixed: opts.fixedWidth != 0,
				index: index,
				data: data,
				columns: columns,
				firstColSpan: firstColSpan,
				lastColSpan: lastColSpan,
				scrollLoad: opts.scrollLoad,
				dataTips: helper.createDataTips(columns, data),
				getTdClassName(column) {
					return (column.width === 0 ? ' td-width-0' : '') + (opts.getTdClassName ? ' '  + (opts.getTdClassName(data, column) || '') : '');
				},
				getTdId(column) {
					return opts.getTdId ? `id="${opts.getTdId(data, column)}"` : '';
				},
				getTdCss(column) {
					return opts.getTdCss && opts.getTdCss(data, column);
				}
			});
		},

		/**
		 * @desc 创建一行表格的列
		 * type  columns | fixedColumns | lastFixedColumns
		 */
		_createTableColumn: function (data, index, type) {
			var opts = this.options;
			var columns = opts[type];
			var colsNumspan = 0;
			var isScrollLoad = opts.scrollLoadY && opts.scrollLoad && type == 'columns';

			if (isScrollLoad && this.showColsNum !== void 0) {
				colsNumspan = opts[type].length - this.showColsNum;
				columns = columns.slice(0, this.showColsNum);
			}

			return tableTrTpl({
				loaded: !(isScrollLoad && index > this.showRowsNum), // 滚动相关
				showColsNum: this.showColsNum, // 滚动相关
				colsNumspan: colsNumspan,      // 滚动相关
				scrollLoad: isScrollLoad,     // 滚动相关
				colspan: this._colspan,    // 滚动相关

				isFixedColumn: type != 'columns',
				lastFixed: type == 'columns' && opts.lastFixedWidth != 0,
				fixed: type == 'columns' && opts.fixedWidth != 0,
				index: index,
				data: data,
				columns: columns,
				dataTips: helper.createDataTips(columns, data),
				className: opts.getTrClassName ? opts.getTrClassName(data, type) || '' : '',
				getTdClassName(column) {
					return (column.width === 0 ? ' td-width-0' : '') + (opts.getTdClassName ? ' '  + (opts.getTdClassName(data, column) || '') : '');
				},
				getTdId(column) {
					return opts.getTdId ? `id="${opts.getTdId(data, column)}"` : '';
				},
				getTrCss() {
					return opts.getTrCss && opts.getTrCss(data);
				},
				getTdCss(column) {
					return opts.getTdCss && opts.getTdCss(data, column);
				}
			})
		},

		/**
		 * @desc  添加一行数据
		 * @param data {{Object | Array Object}} 行数据
		 * @param prog {{Number}} 1 递增 2替换
		 * @param extendDefault {{boolen}} 是否用默认值补充数据
		 * @param pos 指定位置
		 * TODO 乱了 19年3月28号
		 */
		addRow: function (data, extendDefault, prog, pos, isBefore) {
			var me = this,
				rowData = me.getDefaultRowData(),
				cIndex = 0,
				method = 'append',
				fixed = '', //doc.createDocumentFragment()
				lastFixed = '', //doc.createDocumentFragment()
				table = '';//doc.createDocumentFragment()
			data = data || rowData;
			data = !_.isArray(data) ? [data] : data;
			prog = prog || 1;
			cIndex = prog == 1 ? (me.curData ? me.curData.data.length : 0) : cIndex;
			method = prog == 1 ? 'append' : 'html';

			_.each(data, function (item, index) {
				if (extendDefault) {
					item = _.defaults(item, rowData);
				}
				var $fix = me._createTableColumn(item, index + cIndex, 'fixedColumns'),
					$tr = me._createTableColumn(item, index + cIndex, 'columns'),
					$lastFix = me._createTableColumn(item, index + cIndex, 'lastFixedColumns');
				if (me.options.rowCallBack) {
					var $$tr = $($tr);
					var $$fix = $($fix);
					var $$lastFix = $($lastFix);
					me.options.rowCallBack($$tr, item, $$fix, $$lastFix); // 渲染完一行的回调函数
					$tr = $('<div></div>').append($$tr).html();
					$fix = $('<div></div>').append($$fix).html();
					$lastFix = $('<div></div>').append($$lastFix).html();
				}
				fixed += $fix//.appendChild($fix);
				table += $tr//.appendChild($tr);
				lastFixed += $lastFix //.appendChild($lastFix);
			});

			me.$('.dt-no-data').hide();
			me.$('.j-no-data').remove();

			var $tbody = me.$('.main .tb tbody');
			var $fstbody = me.$('.main .fix-start-b tbody');
			var $fetbody = me.$('.main .fix-end-b tbody');

			if (!_.isUndefined(pos) && prog == 1) {
				var $tr = $tbody.find('.tr').eq(pos);
				method = $tr[0] ? 'after' : 'html';
				if(isBefore) method = 'before';
				$tr[method](table);
				$fstbody.find('.tr').eq(pos)[method](fixed);
				$fetbody.find('.tr').eq(pos)[method](lastFixed);
			} else {
				$tbody[method](table);
				$fstbody[method](fixed);
				$fetbody[method](lastFixed);
			}

			me.curData = me.curData || {
				data: []
			};

			var td = me.curData.data;
			if (prog == 1) {
				if (!_.isUndefined(pos)) {
					if(isBefore) {
						pos--;
					}
					var startIndex = pos * 1;
					_.each(data, function (item) {
						td.splice(++pos, 0, item);
					})

					//插入时，重新排一下data-index;
					var $trs = me.findTrs(_.keys(td).slice(startIndex + 1));//$trs是三个表格拼起来的
					var count = $trs.length / me.__tableTypeNumber;
					var num = 0;
					$trs.each(function () {
						num++;
						$(this).attr('data-index', startIndex + num);
						me._setSernialnumberValue($(this), startIndex + num + 1);
						if (num == count) {
							num = 0;
						}
					})
				} else {
					if(isBefore) {
						[].unshift.apply(td, data);
					} else {
						[].push.apply(td, data);
					}
				}
			}

			me.upDataScroll();
			me.trigger('completeRender');


		},

        //
        // 处理滚动条
        //
        upDataScroll: function() {
			if(this.options.forceTrWrap) return;

            var me = this;
            var $tb = null;
            var $scroll = me.$('.main-con');               // 带滚动条
            var $con    = me.$('.j-count-scroll-width');   // 实际占有宽度
            var sWidth  = $scroll.width();
            var cHeight = $con.height();                   // 内容高度
            var xWidth  = $scroll.height() - cHeight;      // x轴滚动条宽度
            var yWidth  = sWidth  - $con.width();          // y轴滚动条宽度
            if (me._fixDomPos != 'Y') {                    // me._fixDomPos mac 下改变dom位置
                me.$('.main .fix-start-b').height(cHeight);
                me.$('.main .fix-end-b').height(cHeight);
                me.$('.fix-end-b').css('right', yWidth);
            }
            me.$('.j-tb-header').css('margin-right', yWidth);
            me.$('.dt-no-data').css('bottom', xWidth);
            if (yWidth > 0) { // 修复横向滚动条不应该出现的情况
                $tb = me.$('.tb');
                if ($tb.width() <= sWidth) {
                   me.$('.j-last-th').width(me.options.lastThWidth - yWidth);
                   $tb.width($tb.width() - yWidth);
                }
            }
            if (me.options && me.options.scrollLoad) {
                var st = $scroll.scrollTop(), sl = $scroll.scrollLeft();
                me._doScrollLoad(st, sl, true);
            }

			me.__trueHeight = cHeight;
			me.__scrollBarWidth = yWidth;
		},


		/**
		 * @desc  指定位置插入行
		 * @param data {{Object | Array Object}} 行数据
		 * @param opstions
		 *        pos:           0,
		 *        extendDefault: false
		 */
		insertRow: function (data, opts) {
			var me = this;
			opts = _.extend({
				pos: me.curData.data.length - 1, // 默认最后面插入行
				extendDefault: false             // 不用默认数据填充
			}, opts || {});

			me.addRow(data, opts.extendDefault, 1, opts.pos, opts.isBefore);
		},

		/**
		 * @desc 删除一行
		 * @param index {{Array | Number}} 删除行的索引
		 */
		delRow: function (index) {
			var me = this;
			if (!_.isUndefined(index)) {
				index = !_.isArray(index) ? [index] : index;
				_.each(index, function (item) {
					me.$('.tr[data-index=' + item + ']').remove();
					me.curData.data[item] = null;
				});
				var newData = _.filter(me.curData.data, function(o) {
					return !!o;
				});
				me.curData.data.splice(0, me.curData.data.length); //清空之前的值
				[].push.apply(me.curData.data, newData);
				me._setTrIndex();
			} else {
				me.$('.main tbody').html('');
				me.curData.data && me.curData.data.splice(0, me.curData.data.length);
			}

			// if (me.curData.data.length == 0) {
			// me.upDataScroll();
			// me._showNodata(me.get('noDataTip'));
			// }

			me._updataCheckNum();
			me.upDataScroll();
		},

		delRowByIndex:function(index){
			this.delRow(index);
			if(!this.curData) return;
			if(!this.curData.data.length) this.doStaticData([]);
		},

		//
		//
		//
		_updataCheckNum: function () {
			var data = this.getCheckedData() || [];
			this._termBatch && this._termBatch.setCheckNum(data.length);
		},

		/**
		 * @desc 删除选中的行
		 */
		delCheckedRow: function () {
			var me = this,
				delNum = [];
			me.$('.main .checkbox-item').each(function (index, item) {
				if ($(item).hasClass('tb-checkbox-selected')) {
					delNum.push(index);
				}
			});
			me.$('.j-all-checkbox').removeClass('tb-checkbox-half-selected tb-checkbox-selected');
			me.delRow(delNum);
			if(!this.curData.data.length) this.doStaticData([]);
		},

		/**
		 * @desc 获取表格当前的全部数据
		 */
		getCurData: function () {
			if(!this.curData) return null;
			return this.curData.data || null;
		},

		/**
		 * @desc 编辑整列数据
		 */
		_batchEditHandle: function (e) {
			var $target = $(e.currentTarget);
			var name = $target.closest('th').attr('data-name');
			if(!name) return false;

			let {beforeBatchEdit} = this.options;
			if(beforeBatchEdit && beforeBatchEdit(name) === false) return false;

			this.batchEditColumn(name, true, true, {
				ignoreEdit: true,
				upDown: true
			});
			return false;
		},

		/**
		 * @desc 批量编辑所有列
		 */
		batchEditColumns: function (flag, local, options) {
			var me = this;

			options = _.extend({
				upDown: true // 是否支持上调
			}, options || {});

			var filterColumns = me.needFilterColumnsForBatchEdit(me) || [];
			var columns = this.options.isPreObjBatchEdit ? _.filter(me.get('allColumns'), function (column) {
					return _.contains([2, 3, 33], column.dataType * 1) && column.isEdit && !filterColumns.includes(column.data)
				}) : _.filter(me.get('allColumns'), function (column) {
				return column.isEdit && column.width !== 0 && !column.disabledEdit && !column.noSupportBatchEdit && !filterColumns.includes(column.data);
			})
			if(!columns.length) {
				util.alert($t("无可编辑列！"));
				return;
			}
			var batch = new BatchEdit({
				columns: columns,
				isAllColumns: true,
				title: $t('批量编辑'),
				upDown: true,
				zIndex: me.get('zIndex') * 1 + 10,
				table: me,
				getFieldFilterOptions: this.options.getFieldFilterOptions,
				fieldIsReadonly: this.options.fieldIsReadonly,
				getMasterData: this.options.getMasterData,
				trigger_info: this.options.trigger_info,
				showContinue: options.showContinue,
				objectApiName: this.options.objectApiname
			});

			batch.show();

			batch.on('enter', function (val, type, cval, column, isContinue) {
				var data = me.getCheckedData() || me.getCurData();
				data = me.options.checked ? me.getRemberData() : data;
				me._bactchChangeColumnsData(column, data, val, type, flag, local, null, isContinue);
			});


		},

		needFilterColumnsForBatchEdit: $.noop,

		/**
		 * @desc 批量编辑指定列
		 * @param {{String}}  列名
		 * @param {{Boolean}} 不回填表格数据 默认回填
		 * @param {{Boolean}} 本地计算
		 * @param {{Object}}  其他扩展项
		 */
		batchEditColumn: function (name, flag, local, options) {
			var me = this;
			var column = _.findWhere(me.get('allColumns'), {
				data: name
			});
			var data = me.getCheckedData() || me.getCurData();
			data = me.options.checked ? me.getRemberData() : data;
			options = _.extend({
				ignoreEdit: false, // 忽略是否可编辑
				upDown: false // 是否支持上调
			}, options || {});

			if (!column) {
				util.alert($t("无此列"));
				return;
			}
			if (column.is_readonly || column.isReadOnly) {
				util.alert($t("您无权限执行此操作"));
				return;
			}
			var filterColumns = me.needFilterColumnsForBatchEdit(me) || [];
			if ((!options.ignoreEdit && !column.isEdit) || filterColumns.includes(name)) {
				util.alert($t("paas.crm.table.column_not_support_bulkedit", null, "此列不支持批量编辑"));
				return;
			}
			// if (!_.contains([1, 2, 3, 31, 32, 33, 34, 36], column.dataType * 1)) {
			// 	util.alert($t("该类型暂不支持编辑！"));
			// 	return;
			// }
			if (!data || !data.length) {
				util.alert($t("无可编辑数据"));
				return;
			}

			var batch = new BatchEdit({
				column: column,
				title: $t('批量编辑'),
				upDown: options.upDown,
				zIndex: me.get('zIndex') * 1 + 10,
				allColumns: me.get('allColumns'),
				getFieldFilterOptions: this.options.getFieldFilterOptions,
				getMasterData: this.options.getMasterData,
				fieldIsReadonly: this.options.fieldIsReadonly,
				trigger_info: this.options.trigger_info,
				objectApiName: this.options.objectApiname
			});

			batch.show();

			batch.on('enter', function (val, type, cval, column) {
				me._bactchChangeColumnsData(column, data, val, type, flag, local, options.callBack);
				options.success && options.success();
			});
		},

		//
		// 批量变更列的数据
		// 要变更的列
		// 要变更的数据
		// 变更的值
		// 变更的类型
		// 是否更新数据
		// 是否计算默认值
		//
		_bactchChangeColumnsData: function (column, list, val, type, flag, local, cb, isContinue) {
			var me = this;
			var exp;
			var isNum = _.contains([2, 3, 33], column.dataType);
			if(isNum) { //数字型
				exp = type == 1 ? 1 * val : -1 * val;
			}

			var noSetBatchEdit = this.options.noSetBatchEdit;
			var tt = [];

			var isMuti = _.isArray(column);
			var _setVal = function(item, fieldName, val) {
				var isobj = !_.isArray(val) && _.isObject(val);
				var cellStatus = item._cellStatus;
				if(cellStatus && cellStatus[fieldName] && (cellStatus[fieldName].hide || cellStatus[fieldName].readonly)) return; //只读或者是隐藏的不设置值
				if(!_.isArray(val) && _.isObject(val)) {
					noSetBatchEdit ? tt.push(_.extend({}, item, val)) : _.extend(item, val);
				} else {
					if(noSetBatchEdit) {
						var att = _.extend({}, item);
						att[fieldName] = val;
						tt.push(att);
					} else {
						item[fieldName] = val;
					}
				}
			}
			list = me.parseListForBatchEdit(list, column);
			_.each(list, function (item) {
				if (!isMuti && !me.canChange(item, column)) {
					return
				}

				if(isNum) {
					val = type == 0 ? val : ((item[column.data] || 0) * 1 + exp).toFixed(column.range[1]) * 1;
					_setVal(item, column.data, val);
				} else {
					if(isMuti) {
						_.each(column, function(a) {
							var f = a.data;
							_setVal(item, f, _.pick(val, [f, f + '__r', f + '__o']))
						})
					} else {
						_setVal(item, column.data, val);
					}
				}

				me.trigger('afterBatchChangeColumnsData', item, column, val, me);
			});

			var expColumn = me.getExpDepend(column);
			if (!local && expColumn.length) {
				me._getServerFormulaData(column, list, function () {
					me._triggerBatchChange(val, column, list, type, isContinue);
					!flag && me.doStaticData(me.getCurData());
					cb && cb();
				});
				return;
			}

			me._triggerBatchChange(val, column, noSetBatchEdit ? tt : list, type, isContinue);
			if (!flag) {
				me.doStaticData(me.getCurData());
				cb && cb();
			}
		},

		parseListForBatchEdit: function (list, column) {
			return list
		},

		/**
		 * @desc 校验是否可更改该行数据
		 * @returns {boolean}
		 */
		canChange: function (data, column) {
			return true
		},

		afterChangeData: $.noop,

		_triggerBatchChange: function (val, column, data, type, isContinue) {
			var me = this;
			me.trigger('batch.cell.change', {
				type: type || 0,  // 修改的类型 0  1上调   2下调
				val: val,         // 修改的值
				column: column,   // 修改的列
				changeData: data,  // 修改的数据
				table: me,
				isContinue
			});
		},

		/**
		 * @desc 查找表达式中依赖当前字段的字段
		 * 默认值 和 计算字段
		 * 表达式中类型
		 * 字符 数值 金额 百分数 汇总 计算 引用 lookup
		 */
		getExpDepend: function (column) {
			var me = this,
				result = [];
			if ([1, 2, 3, 33, 35, 36, 38, 40].indexOf(column.dataType) == -1) {
				return result;
			}
			_.each(me.get('allColumns'), function (item) {
				var exp = item.expression;
				if (exp && item.data != column.data && exp.indexOf(column.data) != -1) {
					result.push(item);
				}
			});
			return result;
		},

		/**
		 * @desc 通过server计算 计算类型的值和默认值
		 * TODO 业务逻辑去除
		 */
		_getServerFormulaData: function (column, data, cb) {
			var me = this,
				apiName = me.get('api_name'),
				api_name_list = [],
				objectDataList = {};

			me.showLoading();

			_.each(me.getExpDepend(column), function (item) {
				api_name_list.push(item.data);
			});

			_.each(data, function (item, index) {
				objectDataList[index] = item;
			});

			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/calculate/service/batchDataExpressionCalculate',
				data: {
					calculate_data_list: CRM.util.stringify([{
						api_name_list: api_name_list,
						objectDataList: objectDataList,
						objectDescribeApiName: apiName
					}])
				},
				success: function (res) {
					if (res.Result.StatusCode == 0) {
						var aoo = res.Value.value_list[apiName] || {};
						_.each(data, function (item, index) {
							_.extend(item, aoo[index] || {});
						});
						cb && cb(data);
						me.hideLoading();
					} else {
						cb && cb(data);
						me.hideLoading();
					}
				}
			}, {
				errorAlertModel: 1
			})
		},

		// 重新设置行顺序
		_setTrIndex: function () {
			var me = this;
			me.$('.tb .tr').each(function (i, item) {
				$(item).attr('data-index', i);
			});
			me.$('.fix-start-b tbody .tr').each(function (i, item) {
				me._setSernialnumberValue($(item).attr('data-index', i), i + 1);
			});
			me.$('.fix-end-b tbody .tr').each(function (i, item) {
				$(item).attr('data-index', i);
			});
		},

		/**
		 * @desc 获取一条默认数据
		 * @param {{Object}}用于处理里面的引用类型数据
		 */
		getDefaultRowData: function (quoteData) {
			var me = this,
				data = {};
			_.each(me.options.allColumns, function (item) {
				if (item.data && item.data != 'null') {
					var datType = item.dataType * 1;
					var val = [8, 9, 17, 37].indexOf(datType) != -1 ? [] : '';
					val = [2, 3].indexOf(datType) != -1 ? null : val;
					data[item.data] = item.placeHolder || val;
					// 引用类型
					if (datType == 40 && quoteData && item.quote_field && quoteData[item.quote_field.split('.')[0]]) {
						console.log(item)
						var fieldName = item.quote_field.split('.')[1];
						data[item.data] = quoteData[fieldName + '__r'] || quoteData[fieldName + '__tpd'] || quoteData[fieldName] || data[item.data];
					}
				}
			});
			return data;
		},

		/** 处理筛选条件
		 * 并搜索
		 * flag 是否刷新到第一页  true 刷新到第一页
		 * type advance or filter 来自高级检索或者过滤
		 */
		_doFilterData: function (data, flag, type) {
			var me = this,
				opts = me.options,
				rules = null;
			type = type || 'filter';
			_.each(data, function (item) {
				var metadata = item.metadata;
				var column = metadata?.isCorssMode ? metadata.field : _.findWhere(opts.filterColumns || opts.allColumns, {
					data: item.FieldName
				});
				if (column) {
					Table.setRule(column, item.FilterValue, item.Comparison, type, item);
				}
			});
			rules = Table.getRule();
			_.each(rules, function (item) {
				me.setParamByKey(item.type, item.value);
			});
			me.setParam({}, true, flag);
		},

		/**
		 * @desc 处理搜索
		 */
		_doSearch: function (keyWord, field) {
			var me = this,
				opts = me.options,
				search = opts.search,
				fieldName = search.fieldName,
				type = search.type || 'keyWord',
				rules = null,
				column = null;

			me._keyWord = keyWord;
			search.highFieldName = field ? field.value : fieldName;
			me.trigger('dt.search.before', keyWord, field);

			if (!fieldName) {
				rules = Table.getRule() || []; //  重置检索条件
		
				me.filterStatus = rules[0].rules.length > 0;
				me.setParamByKey(type, keyWord);
				me.setParam({}, true, true);
			} else {
				Table.delRule(fieldName);
				fieldName = field ? field.value : fieldName;
				me._doFilterData([{
					FieldName: fieldName,
					Comparison: 7,
					FilterValue: keyWord
				}], true, 'filter');
				search.fieldName = fieldName;
			}

			me.trigger('dt.search', keyWord, field);
		},

		// 清楚筛选结果时清楚和搜索有关的信息
		_delSearchInfo: function (fieldName) {
			var search = this.options.search;
			if (search && (search.fieldName == fieldName)) {
				this._keyWord = '';
				this.$('.dt-ipt').val('');
			}
		},

		/**
		 * @desc 窗口改变时监听事件
		 * 改变宽度高度
		 * 改变列宽度监听
		 */
		_bindEvents: function () {
			var me = this;
			$(window).on('resize.dt' + me.cid, function () {
				if (me._rzTimer) {
					clearTimeout(me._rzTimer);
				}
				me._rzTimer = setTimeout(function () {
					me.resize();
				}, 30);
				me.trigger('window.resize');
			});

			var selector = me.options.getLineSelector ? me.options.getLineSelector() : '>.dt-main .resize-line';

			helper.drag(selector, { // 改变列宽度
				start: $.proxy(me._onResizeStart, me),
				drag: $.proxy(me._onResizeDrag, me),
				stop: $.proxy(me._onResizeStop, me)
			}, me.$el);
		},

		//
		// 滚动条相关事件
		//
		_bindScrollEvents: function () {
			var me = this;
			var $fixS = me.$('.main .fix-start-b');
			var $fixE = me.$('.main .fix-end-b');
			var $fixSB = me.$('.fix-start-b');
			var $fixEB = me.$('.fix-end-b');
			var $headTb = me.$('.header .tb-b');
			var $scroll = me.$('.main-scroll');

			me._scrollY = 0;
			me._scrollX = 0;
			me._scrolling = false;
			me._fixDomPos = 'X'; // 只对mac生效， 记录Dom的位置， 滚动时改变dom位置, 解决mac滚动条不同步

			var posY = 0;
			var posX = 0;


			// 防抖动执行一次scrollLoad
			var debounceScroll = _.debounce(function (scrollY, scrollX) {
				$fixSB.toggleClass('column-shadow', scrollX != 0);
				$fixEB.toggleClass('column-shadow', scrollX != 0);
				$headTb[0].scrollLeft = scrollX;
				if (me.options && me.options.scrollLoad) {
					me._doScrollLoad(scrollY, scrollX);
				}
				me.trigger('tb.scroll', scrollY, scrollX);
				me._scrolling = false;
			}, 65);

			//
			// mac卡顿处理函数
			//
			function changeFixPos(scrollX, scrollY) {
				if (me._fixDomPos == 'X') {
					$fixS.height(me.__trueHeight);
					$fixE.height(me.__trueHeight);
					$fixS.css('left', 0);
					$fixE.css('right', me.__scrollBarWidth);
					me.$('.main').append($fixS);
					me.$('.main').append($fixE);
					$fixS.scrollTop(scrollY);
					$fixE.scrollTop(scrollY);
				}
				if (me._fixDomPos == 'Y') {
					$fixS.height('auto');
					$fixE.height('auto');
					$fixS.css('left', scrollX);
					$fixE.css('right', 0 - scrollX);
					$scroll.append($fixS);
					$scroll.append($fixE);
				}
			}


			$scroll.on('scroll', function () {
				var scrollY = this.scrollTop;
				var scrollX = this.scrollLeft;

				me._scrolling = true;

				if (posY != scrollY) {
					if (me._isMac) {
						if (me._fixDomPos != 'Y') {
							me._fixDomPos = 'Y';
							changeFixPos(scrollX, scrollY);
						}
					} else {
						$fixS[0] && ($fixS[0].scrollTop = scrollY);
						$fixE[0] && ($fixE[0].scrollTop = scrollY);
					}
				}

				if (posX != scrollX) {
					if (me._isMac) {
						if (me._fixDomPos != 'X') {
							me._fixDomPos = 'X';
							changeFixPos(scrollX, scrollY);
						}
					}
					$headTb[0].scrollLeft = scrollX;
				}

				posY = scrollY;
				posX = scrollX;

				debounceScroll(scrollY, scrollX);
			});


			$scroll.on('mousewheel', wheelHandle);
			$fixS.on('mousewheel', wheelHandle);
			$fixE.on('mousewheel', wheelHandle);

			if ($fixS[0]) {
				$fixS[0].addEventListener('DOMMouseScroll', wheelHandle);
			} // firefox
			if ($fixE[0]) {
				$fixE[0].addEventListener('DOMMouseScroll', wheelHandle);
			} // firefox

			function wheelHandle(e) {
				if(me.options.isListLayout || me.options.stopWheelEvent) return;

				var oEvent = e.originalEvent || e;
				if (me._isMac) {
					if (oEvent.wheelDeltaX) {
						return true;
					}
					// mac为了触发位置变更
					if (me._fixDomPos == 'X' && !$(e.currentTarget).hasClass('main-scroll')) {
						posY -= 1;
						$scroll.trigger('scroll');
					}
					return true;
				}
				var direct = 0;
				var pos = 60;
				direct = oEvent.wheelDelta || oEvent.detail * -1;
				pos = pos * (direct < 0 ? 1 : -1);
				$scroll[0].scrollTop += pos;
				e.stopPropagation();
				e.preventDefault();
			}

			// 页面ctrl + F 搜索列定位
			var onheadscroll = _.throttle(function () {
				if (me._scrolling) {
					return;
				}
				var left = $headTb.scrollLeft();
				$scroll.scrollLeft(left);
			}, 20);
			$headTb.on('scroll', onheadscroll);
		},

		_showNodata: function (text) {
			var colspan = this.options.columns.length;
			$('.no-data-text', this.$el).html(text);
			$('.dt-no-data', this.$el).show();
			// 塞一个元素到表格中撑开一个高度
			$('.main tbody', this.$el).html('');
			$('.main .tb tbody', this.$el).html('<tr class="j-no-data" style="background:none;"><td style="height:160px; border-bottom: none;" colspan="' + colspan + '">&nbsp;</td></tr>');
		},

		/**
		 * @desc 无数据提示
		 * text {{String}} 提示文案
		 */
		// _showNodata: function (text) {
		// 	var colspan = this.options.columns.length;
		// 	var height = this.options.noDataStyle === 'big' ? '160px' : '40px';
		// 	var className = this.options.noDataStyle === 'big' ? 'table-nodata-big' : 'table-nodata-small';
		//
		// 	$('.no-data-text', this.$el).html(text);
		// 	$('.dt-no-data', this.$el).removeClass('table-nodata-big');
		// 	$('.dt-no-data', this.$el).removeClass('table-nodata-small');
		// 	$('.dt-no-data', this.$el).addClass(className);
		// 	$('.dt-no-data', this.$el).show();
		// 	// 塞一个元素到表格中撑开一个高度
		// 	$('.main tbody', this.$el).html('');
		//
		// 	$('.main .tb tbody', this.$el).html('<tr class="j-no-data" style="height: ' + height + ';background:none;"><td style="border-bottom: none;" colspan="' + colspan + '">&nbsp;</td></tr>');
		// },

		preparePagedData: function(data) {
			return data;
		},

		/**
		 * @desc 获取数据
		 * 记录请求次数
		 */
		_getData: function (callBack) {
			var me = this,
				opts = me.options;
			if (me._startStatus || opts.doStatic) {
				var param = me.getParam();
				me.trigger('staticparam.change', opts.paramFormat ? opts.paramFormat(param) : param);
				return;
			}
			me._api = opts.requestType == 'FHHApi' ? me._fhhRequest : me._apiRequest;
			$('.dt-no-data', me.$el).hide();
			me.showLoading();
			me.trigger('getDataStart');
			var _innerFn = function(nopts) {
				me._getXhr && me._getXhr.abort();
				me._getXhr = me._api({
					suc: function (data) {
						me.isEm6 = me._getXhr ? me._getXhr._isEm6 : false;
						if (me.pageType == 'pageTime') { // 按时间分页 达到最后一页并且数据为0
							data = me.preparePagedData(opts.formatData(data)).data;
							if (me._pageTimeQueue.length > 1 && data.length == 0) {
								me.$('.dt-page .j-next-page').addClass('disabled');
								return;
							}
						}

						me.curData = opts.formatData(data);
						opts.getDataBack && opts.getDataBack(data, me.$el);
						
						if(opts.formatDataAsync) {
							opts.formatDataAsync(me.curData).then(data => {
								me.curData = me.preparePagedData(data);
								callBack && callBack(me.curData);
							})
						} else {
							me.curData = me.preparePagedData(me.curData);
							callBack && callBack(me.curData);
						}
					},
					complete: function () {
						me.hideLoading();
						me._getXhr = null;
					},
					parseParam: nopts && nopts.parseParam
				});
			}

			if(opts.beforeRequestFHH) {
				opts.beforeRequestFHH(me.getParam()).then(function(nopts) {
					_innerFn(nopts);
				})
			} else {
				//保持异步状态
				setTimeout(() => _innerFn());
			}
		},

		abort: function() {
			this._getXhr && this._getXhr.abort();
			this._getXhr = null;
		},

		_updateColumnsTitle(titles) {
			let $header = this.getHeaderWrapper();
			let columns = this.getAllColumns();

			//改表头区域
			_.each(titles, (title, data) => {
				let tt = _.findWhere(columns, {data: data});
				if(tt) {
					tt.label = tt.title = title;
					$header.find(`.th-${data.toLowerCase()} .icon-title`).text(title).attr('title', _.escape(title));
				}
			})

			//改外露筛选区域
			this._termBatch && this._termBatch._updateColumnsTitle(titles);

			//改检索区域
			this._search && this._search._updateColumnsTitle(titles);
		},

		// 刷新loading位置
		updateLoadingPosition(){
			var $load = $('.dt-loading', this.$el);
			if(!$load.length) return;
			var offset = this.getHeaderWrapper().offset();
			var top = offset.top;
			var top1 = (top + 42 + (($('body').height() - 62 - top - 42) / 2)) - 13;
			if(top1 < top) {
				top1 = top + 56;
			}
			$load.css({
				position: 'fixed',
				top: top1
			})
		},

		showLoading: function (type) {
			if (this.options?.loadingService?.show) {
				this.options.loadingService.show();
				return;
			}

			var $load = $('.dt-loading', this.$el);
			$load.addClass(type || 'lg');
			if(this.options.isListLayout) {
				var offset = this.getHeaderWrapper().offset();
				var top = offset.top;
				var top1 = (top + 42 + (($('body').height() - 62 - top - 42) / 2)) - 13;
				if(top1 < top) {
					top1 = top + 56;
				}
				$load.css({
					position: 'fixed',
					top: top1
				})
			}

			$load.show();
		},

		hideLoading: function () {
			if (this.options?.loadingService?.close) {
				this.options.loadingService.close();
				return;
			}

			// if(this.options.isListLayout) {
			// 	util.waiting(false);
			// 	return;
			// }
			$('.dt-loading', this.$el).hide();
		},

		/**
		 * @desc 通过util api请求数据接口
		 * option.suc 成功回调函数
		 * option.complete 完成回调函数
		 */
		_apiRequest: function (option) {
			var me = this,
				opts = me.options;
			var param = me.getParam();
			param = opts.paramFormat ? opts.paramFormat(param) : param;
			return util.api({
				url: opts.url,
				data: param,
				type: opts.method,
				success: function (data) {
					if(!me.options) return;

					if(me.options.requestResultAsync) {
						me.options.requestResultAsync(data).then(function(res){
							_next(res || data);
						});
					}else {
						_next(data);
					}
					
					function _next(data) {
						me.options.requestResult && me.options.requestResult(data);
						if (opts.requestType == 'define' && data[opts.requestCodeKey] == 0) {
							option.suc && option.suc(data);
							return;
						}
						if (data.success) {
							option.suc && option.suc(data.value || {});
						}
						opts.getFullDataBack && opts.getFullDataBack(data);
					}
				},
				error: function () {
					me.options.requestResult && me.options.requestResult('error');
				},
				complete: option.complete
			}, {
				autoPrependPath: opts.requestType != 'define'
			});
		},

		/**
		 * @desc 列表深翻页增加参数
		 * @param {*} param 传参
		 * @returns param.search_query_info
		 */
		addSearchAfter: function(param) {
			var me = this;
			var curData = me.curData ? me.curData.data : undefined,
				defaultParam = me.defaultParam,
				sub = false,
				info = JSON.parse(param.search_query_info);
			if(curData && curData.length && defaultParam.pageNumber > 10000/curData.length) {
				var fieldName = info.orders && info.orders[0].fieldName
				if(me.fieldName) {
					if(me.fieldName !== fieldName) {
						me.fieldName = fieldName
						return JSON.stringify(info);
					}
				} else {
					me.fieldName = fieldName
				}
				if(me.pageNumber) {
					sub = me.pageNumber > defaultParam.pageNumber ? true : false;
					me.pageNumber = defaultParam.pageNumber;
				} else {
					me.pageNumber = defaultParam.pageNumber;
					sub = false;
				}
				if(sub) {
					info.searchAfter = {
						direction: 'forward',
						searchAfterId: me.curData.data[0].searchAfterId
					};
				} else {
					info.searchAfter = {
						direction: 'backward',
						searchAfterId: me.curData.data[me.curData.data.length-1].searchAfterId
					}
				}
			}
			return JSON.stringify(info);
		},

		/**
		 * @desc 通过util FHHApi请求数据接口
		 * option.suc 成功回调函数
		 * option.complete 完成回调函数
		 * @return 请求对象
		 */
		_fhhRequest: function (option) {
			var me = this,
				opts = me.options;
			var param = me.getParam();
			if (!opts) {return;} // TODO 临时修复线上报错；view是不是快速切换时导致opts设置为null了
			param = opts.paramFormat ? opts.paramFormat(param) : param;
			if(param.search_query_info) {
				param.search_query_info = opts.isMyObject ? me.addSearchAfter(param) : param.search_query_info
			}

			let data = option.parseParam ? option.parseParam(param) : param;
			if(opts.formatParamsBeforeRequest) {
				data = opts.formatParamsBeforeRequest(data);
			}

			let _t = _.extend({
				url: opts.url,
				data: data,
				success: function (data) {
					if(me.options.requestResultAsync) {
						me.options.requestResultAsync(data).then(function(res){
							_next(res || data);
						});
					}else {
						_next(data);
					}
					
					function _next(data) {
						if(!me.options) return;
						me.options.requestResult && me.options.requestResult(data);
						if (data.Result.StatusCode == 0) {
							option.suc && option.suc(data.Value || {});
						} else if(me.options.errorAlertModel == 1) {
							let msg = data.Result.FailureMessage || $t('暂时无法获取相关数据请稍后重试');
							FxUI ? FxUI.Message({
								isMiddler: true,
								duration: 3000,
								message: msg,
								type: 'error'
							}) : util.alert(msg);
						}

						opts.getFullDataBack && opts.getFullDataBack(data);
					}
				},
				error: function () {
					me.options.requestResult && me.options.requestResult('error');
				},
				requestStatus: function (data) {
					me.options.requestResult && me.options.requestResult(data);
				},
				complete: option.complete
			}, {headers: opts.reqHeaders || {}})

			if(opts.queryFirstData) {
				return opts.queryFirstData(_t);
			}
			return util.FHHApi(_t, {
				errorAlertModel: me.options.errorAlertModel
			});
		},

		/**
		 *  @desc 渲染分页
		 * 条数为0时不在显示
		 */
		_renderPage: function (totalCount) {
			if(this.__retainPaginationStatus) return;
			var me = this,
				opts = me.options,
				param = me.getParam(),
				pageCount = 0;
			if (me.tableStatus == 2) {      // 表格已销毁，不再加载分页
				return;
			}
			if (!opts.showPage) {
				me.tableStatus = 3;
				return;
			}
			if (opts.pageType == 'pageNumber') {
				if (opts.noAlwaysShowPage && totalCount <= opts.page.pageSize) {
					me.$('.dt-page').hide();
					return;
				}
				me.$('.dt-page').show();
				me._initPage(totalCount);
				pageCount = Math.ceil(totalCount / opts.page.pageSize) || 1;
				if (param.pageNumber > pageCount) { // 当请求页数大于当前页面
					param.pageNumber = 1;
					me._pagination.set('activePageNumber', 1);
				}
				me._pagination.show();
				me._pagination.setTotalSize(totalCount);
			} else {
				me._initPage(totalCount);
				me._renderTimePage();
			}
			me.tableStatus = 3;
			me._totalCount = totalCount;
		},

		/**
		 * @desc 渲染时间分页
		 * @return {{String}}
		 * noRender // 不在渲染
		 * noData   // 无数据
		 * firstPage // 第一页 并且 不存在下一页
		 * lastPage  // 最后一页
		 */
		_renderTimePage: function () {
			var me = this,
				curData = me.curData.data || [],
				timeQueue = me._pageTimeQueue,
				pageSize = me.options.page.pageSize,
				dLen = curData.length,
				tLen = timeQueue.length;
			me.$('.dt-time-page span').removeClass('disabled');
			if (dLen == 0 && tLen == 0) { // 无数据
				me.$('.dt-time-page span').addClass('disabled');
			} else {
				if (tLen == 1 || me.options.page.pageNumber == 1) { // 第一页
					me.$('.dt-page .j-pre-page').addClass('disabled');
				}
				if (dLen < pageSize) { // 最后一页
					me.$('.dt-page .j-next-page').addClass('disabled');
				}
			}
			if (me.options.noAlwaysShowPage) { // 不总显示分页
				if ((dLen == 0 && tLen == 0) || (tLen == 1 && dLen < pageSize)) {
					me.$('.dt-time-page').hide();
				} else {
					me.$('.dt-time-page').show();
				}
			}
		},

		/**
		 * @description 初始化分页
		 */
		_initPage: function () {
			var me = this,
				opts = me.options;
			if (opts.pageType == 'pageNumber') {
				if (!me._pagination) {
					me._pagination = new Pagination({
						el: $('.dt-page-right', me.$el),
						isMyObject: opts.isMyObject,
						pageSize: opts.page.pageSize,
						pageSizeOption: opts.page.pageSizeOption,
						zIndex: opts.zIndex,
						visiblePageNums: opts.visiblePageNums,
						pageSizeStopPropagation: opts.pageSizeStopPropagation
					});
					me._pagination.render();
					me._pagination.on('page', function (pageNumber) {
						me.setParam({
							pageNumber: pageNumber
						}, true);
						me.trigger('dt.page', pageNumber);
					});
					me._pagination.on('pagesize', function (pageSize) {
						me.handlePageSizeChange(pageSize);
					});
					opts.isListLayout && me._pagination.on('total:change', function(total) {
						me.updateTotal20(total);
					})
				}
			} else {
				if (!me._pageTimeRender) {
					me._pageTimeRender = true;
					$('.dt-page-right', me.$el).html('<div class="dt-time-page">' +
						'<span class="j-pre-page">' + $t('上一页') + '</span>' +
						'<span class="j-next-page">' + $t('下一页') + '</span>' +
						'</div>');
					me._pageTimeQueue = [0]; // pageTime队列维护上一页下一页参数
				}
			}
		},

		handlePageSizeChange: function(pageSize) {
			this.options.page.pageSize = pageSize;
			this._pagination.updatePageSize(pageSize);
			this.setParam({
				pageSize: pageSize
			}, true, true);
			if (pageSize <= 200) {
				CRM.setLocal('table-pagesize', pageSize);
			}
			this.trigger('dt.pagesize', pageSize);
		},

		setPaginationTotal: function(total) {
			this._pagination && this._pagination.setTotalSize(total);
		},

		/**
		 * @desc 时间分页的上下一页
		 */
		_onTimePage: function (e) {
			var me = this,
				data = me.curData.data,
				$target = $(e.currentTarget),
				pageTime = 0,
				page = me.options.page || {};
			if ($target.hasClass('disabled')) {
				return false;
			}
			page.pageNumber = page.pageNumber || 1; // 同时传递一个pageNumber过去
			if ($target.hasClass('j-next-page')) {
				if (data.length > 0) {
					page.pageNumber = page.pageNumber + 1;
					pageTime = data[data.length - 1][me.options.pageTimeKey];
					me._pageTimeQueue.push(pageTime);
				}
			} else {
				page.pageNumber = page.pageNumber - 1;
				pageTime = me._pageTimeQueue[me._pageTimeQueue.length - 2];
				if (me._pageTimeQueue.length > 1) {
					me._pageTimeQueue.pop(); // 删除最后一项
				}
			}
			me.setParam({
				pageNumber: page.pageNumber,
				pageTime: pageTime
			}, true);
			me.trigger('dt.page', pageTime, page.pageNumber);
		},

		/**
		 * @desc 渲染记录的数据
		 * 主要要为了点亮全选按钮和批量设置按钮
		 */
		_renderRemberData: function () {
			var me = this,
				len = 0,
				slen = 0,
				dlen = 0,
				data = null;
			if (!me.options.checked) {
				return;
			}
			len = me.$('.main .checkbox-item').length;
			slen = me.$('.main .tb-checkbox-selected').length;
			dlen = me.$('.main .checkbox-item-disabled').length;
			data = me.getRemberData() || [];
			if (slen > 0) {
				if (slen + dlen == len) {
					me.$('.j-all-checkbox').addClass('tb-checkbox-selected');
				} else {
					me.$('.j-all-checkbox').addClass('tb-checkbox-half-selected');
				}
				me.$('.main .tb-checkbox-selected').each(function (index, item) {
					index = $(item).closest('.tr').attr('data-index');
					me.$('.tr[data-index=' + index + ']').addClass('tr-selected');
				});
			}

			me._termBatch && me._termBatch.toggleBatchBtn(data.length);
			// if (slen > 0 || data.length > 0) {
			// 	me._termBatch && me._termBatch.showBatchBtn(data.length);
			// } else {
			// 	me._termBatch && me._termBatch.hideBatchBtn();
			// }
		},

		/**
		 * @desc 处理静态数据
		 * @param 数据 {{Array Object}}    [{}, {}, {}]
		 * @param {{Boolean}}      保持滚动条位置不变
		 */
		doStaticData: function (data, keepScroll) {
			data = data || (this.curData && this.curData.data) || [];
			this.curData = {
				data: data,
				totalCount: data.length
			};
			this._drawPage(data, data.length, keepScroll);
			this._updataCheckNum();
		},

		/**
		 * @desc 获取全部请求参数
		 * 第一次请求时把搜索信息 分页信息参数拼接进去
		 */
		getParam: function () {
			var me = this,
				opts = me.options,
				page = {};

			if(!opts) return {};

			if (me.requestCount > 0) {
				return _.extend(opts.postData, {_isfilter: me.filterStatus});
			}
			if (opts.showPage) { // 拼入分页参数
				page = opts.page;
				page.pageNumber = 1;
				if (opts.pageType != 'pageNumber') {
					page.pageTime = 0;
				}
			}
			if (opts.searchTerm) { // 拼入检索参数
				var filter = opts.searchTerm.options;
				var def = _.findWhere(filter, {id: opts.searchTerm.defaultValue || ''});
				def = def || _.findWhere(filter, {isdef: true});
				if (!def || def.isHide) {
					def = _.findWhere(filter, {isHide: false});
					def = def || filter[0];
				}
				me.setParamByKey(opts.searchTerm.type, def ? def.id : '');
			}
			if (opts.filterDatas) { // 写入默认条件
				me.filterStatus = true;
				if (opts.filterDatas.aloneValues) {
					me.setParamByKey('_aloneValuesKey', opts.filterDatas.aloneValues);
				}
				if (opts.filterDatas.filters) {
					_.each(opts.filterDatas.filters, function (item) {
						var column = _.findWhere(opts.filterColumns || opts.allColumns, {
							data: item.FieldName
						});
						Table.setRule(column, item.FilterValue, item.Comparison, 'advance', item);
					});
					_.each(Table.getRule(), function (item) {
						me.setParamByKey(item.type, item.value);
					});
				}
			}
			me.requestCount += 1;
			me.defaultParam = _.extend(opts.postData, page); // 记录默认参数
			// console.log(me.defaultParam)
			return _.extend(me.defaultParam, {_isfilter: me.filterStatus});
		},

		/**
		 * @desc 设置指定 key postData的值
		 * key {{String}} 参数的可以 支持 QueryInfo.FilterMainID 以逗号间隔
		 * value 值
		 */
		setParamByKey: function (key, value) {
			var postData = this.options.postData,
				len = 0;
			key = key.split('.');
			len = key.length;
			_.each(key, function (item, index) {
				if (index == len - 1) {
					postData[item] = value;
				} else {
					postData[item] = postData[item] || {};
					postData = postData[item];
				}
			});
		},

		/**
		 * @desc 获取指定key的 postData的值 内部使用
		 * key {{String}} 参数的可以 支持 QueryInfo.FilterMainID 以逗号间隔
		 */
		getParamByKey: function (key) {
			var me = this,
				opts = me.options.postData;
			key = key.split('.');
			_.each(key, function (item) {
				if (opts) {
					opts = opts[item] || null;
				}
			});
			return opts;
		},

		/**
		 * @desc 设置请求参数
		 * 对外调用
		 * config {{Object}} 要设置的参数
		 * status {{Boolean}}    为true时请求数据
		 * resetPage {{Boolean}} 为true是刷新为第一页
		 */
		setParam: function (config, status, resetPage) {
			var param = this.getParam(),
				data = this.getRemberData() || [];
			$.extend(true, param, config || {});
			if (status) {
				if (resetPage) {
					if (this.options.pageType == 'pageNumber') {
						param.pageNumber = 1;
						this._pagination && this._pagination.reset();
					} else {
						param.pageNumber = 1;
						param.pageTime = 0;
						this._pageTimeQueue = [0];
					}
				}
				if (!this.options.checked || data.length == 0) {
					this._termBatch && this._termBatch.hideBatchBtn();
				}
				this._renderList();
			}
		},

		/**
		 * @desc 启用请求
		 * 关闭start状态
		 */
		start: function (config) {
			this._startStatus = false;
			this.setParam(config || {}, true, true);
		},

		/**
		 * @desc 关闭请求
		 */
		closeStart: function () {
			this._startStatus = true;
		},

		getAllColumns: function() {
			return this.options.allColumns;
		},

		/**
		 * @desc 获取选中的复选按钮的数据
		 * @return {{Array Object}}
		 */
		getCheckedData: function () {
			var me = this,
				data = [];
			me.$('.main .tb-checkbox-selected').each(function () {
				var $tr = $(this).closest('.tr');
				data.push(me.getRowData($tr));
			});
			return data.length > 0 ? data : null;
		},

		/**
		 * @desc 获取选中的数据格式化后的数据
		 * @param {{Array Object}} 要格式化的数据 默认选中的数据
		 * @return {{Array Object}}
		 */
		getCheckedFormatData: function (data) {
			var me = this;
			data = data || me.getCheckedData();
			data = _.map(data, function (item) {
				return _.clone(item);
			});
			_.each(me.options.allColumns, function (item) {
				if (item.data && item.data != 'null' && [9, 17, 34, 37, 38, 39].indexOf(item.dataType) == -1) {
					_.each(data, function (dItem) {
						var val = dItem[item.data];
						(val = helper.getFormatVal(val, item, '', dItem, '', me.isEm6));
						val = val == me.options.defaultValue ? '' : val;
						dItem[item.data] = val;
					});
				}
			});
			return data;
		},

		/**
		 * @desc 获取行数据数据
		 * @param {JQuery} 行元素
		 * 不传返回所有数据
		 */
		getRowData: function ($tr) {
			var data = this.curData.data;

			if ($tr && $tr.length > 0 && $tr.hasClass('tr')) {
				var index = $tr.index();
				var cur = data[index];
				cur.__tbIndex = index;
				return cur;
			}
			return data;
		},

		/**
		 * @description 在记录选中的数据中增加选项
		 * 和开启记忆选择项有关
		 */
		addRemberData: function (data) {
			var me = this;
			var od = data;
			data = me._remberData.concat(data);
			me._remberData = _.uniq(data, function (item) {
				return item[me.options.checked.idKey];
			});
			me.trigger('addRememberData', od, me);
		},

		/**
		 *   @desc 设置勾选行
		 *   @param    idKey: 行中表示唯一的字段；
		 *   @param    data: 需要设置勾选的数据  [ { _id:'23232332' }, ]
		 *   @todo 全选样式修改，半选/全选
		 */
		setCheckedRow: function (idKey, data) {
			var me = this;
			if (this.options.checked) {
				this.options.checked.data = this.options.checked.data.concat(data)
			} else {
				this.options.checked = {
					idKey: idKey,
					data: data
				};
			}
			this.options.checked.data = _.uniq(this.options.checked.data, function (item) {
				return item[me.options.checked.idKey];
			});

			_.each(data, function (obj) {
				var id = obj[idKey];
				var index = util.findIndex(me.curData.data, function (item) {
					return item[idKey] === id;
				});
				if(util.hasValue(index)){
					var $tr = me.$el.find('.tr[data-index="' + index + '"]');
					var checkbox = $tr.find('.column-checkbox .checkbox-item');
					$tr.addClass('tr-selected');
					checkbox.addClass('tb-checkbox-selected');
					me._setRemberData(checkbox);
				}
			})
			// this.doStaticData(this.curData.data)
			// 非单选 联动全选
			if (!me.options.single) {
				var $checkedboxs = me.$('.main .tb-checkbox-selected');
				var $allcheckbox = me.$('.j-all-checkbox');
				if ($checkedboxs.length && $checkedboxs.length < me.curData.data.length) $allcheckbox.addClass('tb-checkbox-half-selected');
				if ($checkedboxs.length && $checkedboxs.length === me.curData.data.length) $allcheckbox.addClass('tb-checkbox-selected');
			}
		},

		setUncheckedRow: function(idKey, data) {
			var me = this;
			if (this.options.checked) {
				this.options.checked.data = _.filter(this.options.checked.data, function(item) {
					var _ids = _.pluck(data, idKey);
					return !_.contains(_ids, item[idKey]);
				})
			}
			_.each(data, function(obj) {
				var id = obj[idKey];
				var index = util.findIndex(me.curData.data, function (item) {
					return item[idKey] === id;
				});
				var checkbox = me.$el.find('.fix-start-b').find('.tr[data-index="' + index + '"]').find('.checkbox-item');
				// me.$el.find('.fix-start-b').find('.tr[data-index="' + index + '"]').removeClass('tr-selected');
				me.$el.find('.tr[data-index="' + index + '"]').removeClass('tr-selected');
				checkbox.removeClass('tb-checkbox-selected');
				me._setRemberData(checkbox);
			})
			// 非单选 联动全选
			if (!me.options.single) {
				var $checkedboxs = me.$('.main .tb-checkbox-selected');
				var $allcheckbox = me.$('.j-all-checkbox');
				if ($checkedboxs.length && $checkedboxs.length < me.curData.data.length) {
					$allcheckbox.removeClass('tb-checkbox-selected');
					$allcheckbox.addClass('tb-checkbox-half-selected');
				}
				if (!$checkedboxs.length) $allcheckbox.removeClass('tb-checkbox-selected tb-checkbox-half-selected');
			}
		},

		/**
		 *   @desc 设置行的复选框禁用
		 *   @param    idKey: 行中表示唯一的字段；
		 *   @param    data: 需要设置勾选的数据  [ { _id:'23232332' }, ]
		 */
		setCheckboxDisabled: function (idKey, data) {
			var me = this;
			if (this.options.disabledcfg) {
				this.options.disabledcfg.data = this.options.disabledcfg.data.concat(data)
			} else {
				this.options.disabledcfg = {
					idKey: idKey,
					data: data
				};
			}
			this.options.disabledcfg.data = _.uniq(this.options.disabledcfg.data, function (item) {
				return item[me.options.disabledcfg.idKey];
			});

			_.each(data, function (obj) {
				var id = obj[idKey];
				var index = util.findIndex(me.curData.data, function (item) {
					return item[idKey] === id;
				});
				var checkbox = me.$el.find('.fix-start-b').find('.tr[data-index="' + index + '"]').find('.checkbox-item');
				// me.$el.find('.fix-start-b').find('.tr[data-index="' + index + '"]').addClass('tr-selected');
				checkbox.addClass('checkbox-item-disabled');
				me._setRemberData(checkbox);
			})
			// this.doStaticData(this.curData.data)
		},

		/**
		 * @description 在记录选中的数据中减少选项
		 * 和开启记忆选择项有关
		 */
		reduceRemberData: function (data, noTrigger) {
			var me = this,
				idKey = me.options.checked.idKey;
			var od = data;
			data = _.pluck(data, idKey);
			me._remberData = _.filter(me._remberData, function (item) {
				return !_.contains(data, item[idKey]);
			});

			me._termBatch && me._termBatch.setCheckNum(me._remberData.length);
 			
			if(!noTrigger) me.trigger('reduceRememberData', od, me)
		},

		/**
		 * @desc 清空记录的数据
		 */
		getRemberData: function () {
			return this._remberData;
		},

		/**
		 * @desc 清空记录的数据
		 */
		clearRemberData: function () {
			this._remberData = [];
			this.trigger('clearRemberData');
		},

		/**
		 * @desc 变更高度宽度
		 */
		resize: function () {
			var me = this;
			if(!me.options) return;

			me.$('.main').height(me._countHeight());
			me.$('.tb').css('width', me._countWidth());
			me.resizeLast();
			me.$('.j-last-th').css('width', me.options.lastThWidth);
			me.upDataScroll();
			me.trigger('resize');
		},

		//
		// 重新确保最后一列， 有可能调用了hideColumn
		resizeLast: function () {
			var me = this, cn = '';
			if (me.options.lcolumn) {
				var $last = me.$('.j-last-th');
				cn = me.options.lcolumn.data;
				if (cn && !$last.hasClass('th-' + cn.toLowerCase())) {
					$last.removeClass('j-last-th');
					if ($last.width() != 0) {
						$last.removeAttr('style');
					}
					me.$('.th-' + cn.toLowerCase()).addClass('j-last-th');
				}
			}
		},

		/**
		 * @desc 切换成全局表格
		 */
		toggleFullHeight: function (flag, max) {
			var me = this;
			var maxHeight = flag ? 'none' : me.options.maxHeight;
			if (max !== undefined) maxHeight = max;
			me.options.autoHeight = !flag;
			me.options.height = flag ? undefined : me.options.height;
			$('.main, .main-scroll', me.$el).css({
				'max-height': maxHeight
			});
			me.resize();
			//me.upDataScroll();
		},

		// 绑定事件
		events: {
			'mouseenter .main .tr': '_onTrHover',      // 行划过事件
			'mouseleave >.dt-main': '_mainMouseLeaveHandle',//鼠标离开表格区域
			'click .insert-icon__sss': '_insertIconClickHandle',
			'click .main .tr': '_trClickHandle',  // 点击行事件
			'mousedown .main .tr': '_trMouseDownHandle',
			'mouseup .main .tr': '_trMouseUpHandle',
			'mouseenter .main .j-td': '_onTdHover', // 单元滑过事件
			'mouseleave .main .j-td': '_onTdLeave', // 单元离开事件
			'click .th-sort': '_onSortHandle',   // 排序事件处理

			'click .column-checkbox': '_onChecked',  // 复选框事件
			'click .dt-time-page span': '_onTimePage', // 时间分页
			'click .tb-cell': '_onClickCell', // 编辑单元格
			'click .td-cell-edit': '_onEditCell', // 编辑单元格
			'mouseenter .td-cell-edit': '_onHoverEditCell', // 编辑单元格
			'click .j-view-img': '_onViewImg',  // 查看图片
			'click .j-tb-addimg': '_onAddImg',   // 上传图片
			'click .j-edit-img': '_onEditImg',  // 编辑图片
			'click .j-tb-addattach': '_onAddAttach',         // 上传附件
			'click .j-view-attach': '_onViewAttach',        // 查看附件
			'click .j-show-lookup': '_onShowLookupDetail',  // 显示lookUp的详情
			'click [tb-action-type]': '_onAction',
			'click >.dt-main .j-th-filter': '_onColumnFilter',    // 列筛选
			'click .j-th-batchEdit': '_batchEditHandle', //列批量编辑
			'click .j-th-lock':  '_lockColumnHandle',
			'click .j-th-cusbtn':  '_cusBtnHandle',
			'click .tr-operate-wrap': '_onTrOperateClick',  // 自定义操作列
			'mousedown .tr-operate-wrap': '_onTrOperateMouseDown',  // 自定义操作列
			'mousedown .tr-operate-wrap20': '_onTrOperateMouseDown',  // 自定义操作列
			'hover .tr-operate-btn': '_onTrOperateHover',  // 自定义操作列
			'click .j-open-profile': '_onStopPropagation',  // 打开人员详情
			'click .j-icon-newtab': '_onStopPropagation',  // 打开人员详情
			'click .j-stop-propagation': '_onStopPropagation',  // 打开链接
			'click .j-sw-linestatus': '_onSwitchTdLine', //切换竖向分割线
			'click .j-row-operate-btn': '_onTrOperateClick20', //自定义操作列
			'mouseenter .j-row-operate-more': '_onTrOperateHover20', // 自定义操作列
			'mouseleave .j-row-operate-more': '_onLeaveTrOperateHover20', // 自定义操作列
			'contextmenu img': '_imageContextMenu',
			'click .j-attach__nitem': '_onAttachNItemHandle',
			'click .j-dt-icon-tableconfig': '_onTableConfig',
			'click .j-dt-icon-columnconfig': 'showColumnSetting',
			'click .j-tb-setcolumn': 'showColumnSetting',
			'click .j-dt-icon-refresh': '_onrefreshCallBack',
			'mouseenter td.td-status-hide': '_removeHideTdTitle', // 单元滑过事件
			'mouseleave  td.td-status-hide': '_addHideTdTitle' // 单元离开事件
		},

		_removeHideTdTitle(e) {
			let $target = $(e.target);
			let title = $target.attr('title');
			if(title) {
				$target.attr('title', '');
			}

			$target.attr('title_copy', title || '');
		},

		_addHideTdTitle(e) {
			let $target = $(e.target);
			let title = $target.attr('title_copy');
			if(title) {
				$target.attr('title') || $target.attr('title', title);
				$target.attr('title_copy', '');
			}
		},

		_onrefreshCallBack: function() {
			this.options.refreshCallBack && this.options.refreshCallBack();
			return false;
		},


		getAllSetColumns() {
			var columns = this._termBatch && this._termBatch._parseSetColumns();
			return columns || this.getAllColumns();
		},

		//表格相关设置
		_onTableConfig: function() {
			var opts = this.options;
			var columns = this._termBatch && this._termBatch._parseSetColumns();
			var curTerm = this._termBatch && this._termBatch.getCurTerm();

			var allColumns = this.getAllColumns();
			var param = {
				sizeConfig: opts.showPage && opts.showSize && !opts.hideSizeConfig && {
					value: opts.sizeType
				},
				tdLineConfig: opts.showPage && opts.showSize && !opts.hideTdLineConfig && {
					value: CRM.getLocal('table-tdline-status') === 'border'
				},
				pageConfig: opts.showPage && !opts.doStatic && {
					options: opts.page.pageSizeOption || [20, 50, 100],
					value: opts.page.pageSize || 20
				},
				showRecoverWidth: opts.showMoreBtn,
				// disableRecoverWidth: !_.find(allColumns, column => {
				// 	return column.data && column.api_name && !column.isHidden && Math.abs(column.width - helper.getTdWidth(column.dataType)) > 3;
				// }),
				cleanFieldListConfig: opts.cleanFieldListConfig,
				showSaveAs: curTerm && ((curTerm.apiname && curTerm.apiname.indexOf('All') != -1) || (curTerm.type && curTerm.type != 1)),
				showSave: !curTerm || curTerm.type != 3,
				term: curTerm,
				extendClassName: opts.settingClassName || '',
				...this._getSernialnumberConfig(),
				columns: _.map(columns || [], function(a) {
					let tt =_.findWhere(allColumns, {data: a.data});
					return {
						label: tt ? tt.title || tt.titleAlias : a.title || a.titleAlias,
						value: a.data,
						disableSet: !!a.disableSet, //开关置灰
						fixed: a.fixed, //固定位置
						isHidden: a.isHidden, //开关值
						isDrag: true //是否可拖拽
					}
				})
			}

			this.showSetting(param).on({
				serNialNumber: (flag) => this.toggleSerNialNumberColumn(flag),
				dividingLine: _.bind(this.handleSwitchTdLine, this),
				lineHeight: _.bind(this.handleSizeChange, this),
				pageRow: _.bind(this.handlePageSizeChange, this),
				recoverColWidth: _.bind(this.handleRecoverWidth, this),
				recoverField: () => {
					curTerm.forceUpdate = true;
					this._termBatch.trigger('term.change', curTerm.id, curTerm)
				},
				setColumn: (columns, isSaveAs) => {
					this.__retainPaginationStatus = true;
					setTimeout(() => delete this.__retainPaginationStatus);
					this._termBatch.hancColumnsChange(columns, isSaveAs, this._curFilterData);
				},

			})

			return false;
		},

		//显示列设置
		showColumnSetting: function() {
			var columns = this._termBatch && this._termBatch._parseSetColumns();
			var curTerm = this._termBatch && this._termBatch.getCurTerm();
			var allColumns = this.getAllColumns();
			this.showSetting({
				showSaveAs: curTerm && ((curTerm.apiname && curTerm.apiname.indexOf('All') != -1) || (curTerm.type && curTerm.type != 1)),
				showSave: !curTerm || curTerm.type != 3,
				columns: _.map(columns || [], function(a) {
					let tt =_.findWhere(allColumns, {data: a.data});
					return {
						label: tt ? tt.title : a.title,
						value: a.data,
						disableSet: !!a.disableSet, //开关置灰
						fixed: a.fixed, //固定位置
						isHidden: a.isHidden, //开关值
						isDrag: true //是否可拖拽
					}
				})
			}).on({
				setColumn: (columns, isSaveAs) => {
					this._termBatch.hancColumnsChange(columns, isSaveAs);
				}
			})

			// return false; //目前暂未发现表格内部再嵌入同样的表格，所以不用阻止冒泡
		},

		showSetting: function(opts) {
			const options = this.options;

			if(!this.tableSetting) {
				this.tableSetting = new TableSetting();
				this.tableSetting.on('hide', () => {
					this.$el.removeClass('crm-w-table__setting');
				})
			}
			this.tableSetting.off();
			this.$el.removeClass('crm-w-table__setting');
			this.tableSetting.render(opts);

			return this.tableSetting;
		},

		_trackCriticalUsage: function(data) {
			const options = this.options;
			const eventId = data?.eventId || data?.eventid;

			if (!eventId || !options) {
				return;
			}

			window.logger?.action({
				eventId: eventId,
				eventName: 'click',
				module: 'table',
				apiName: options.objectApiname,
				...(data || {})
			});
		},

		_imageContextMenu: function() {
			return !this.__noDownLoadPermission;
		},

		_onAttachNItemHandle: function(e) {
			e.stopPropagation();

			var obj = $(e.target).data();

			this._trackCriticalUsage(obj);

			if(!obj || !obj.path || obj.onlinedocapiname) return
	
			obj.filePath = obj.path;
			obj.fileName = obj.name;
			if(obj.bigfilename) { //大附件下载
				CRM.api.alioss_download({
					fileName: obj.bigfilename,
					path: obj.path
				})
			} else if(/\.(?:tif|eps|png|gif|jpeg|jpg|dwg|ai|cdr|bmp|webp)$/i.test(obj.fileName)) { //图片类型
                CRM.api.preview_image({
                    list: [{
		                bigUrl: obj.signed ? obj.signed : util.getFscLinkByOpt({
	                        id: obj.filePath,
	                        webp: true,
	                        name: obj.fileName,
													ext: obj.ext || util.getFileExtText(obj.fileName)
	                    }),
	                    originDownUrl: obj.signed ? obj.signed + '&acModel=attachment' : util.getFscLink(obj.filePath, obj.fileName, true)
	                }],
	                index: 0,
                    isNew: true,
                    zIndex: 15000,
                    hideDownAllLink: this.__noDownLoadPermission
                })
            } else if(CRM.util.getUploadType(obj.fileName || '') === 'video') {
				CRM.api.preview_video({
					fileName: obj.fileName,
                	filePath: obj.filePath
				})
            } else {
				require.async('base-modules/file-preview/index', function(FilePreview) {
					if(util.getFileExtText && !util.getFileExtText(obj.filePath)) {
		                var ext = util.getFileExtText(obj.fileName || '');
		                ext && (obj.filePath = obj.filePath + '.' + ext);
		            }
					
					if(obj.needhiddenprintbutton) {
						obj.disableDownload = true;
					}

	                FilePreview.preview(obj);
				});
			}

            e.preventDefault();
		},

		_onStopPropagation: function (e) {
			e.stopPropagation();
		},

		_onTrOperateClick: function (e) {
			if ($(e.target).hasClass('tr-operate-btn-item')) {
				this.trigger('operate.btnclick', e);
			}

			return false;
		},

		_onTrOperateMouseDown: function (e) {
			this.trigger('operate.btnMouseDown', e);
		},

		// 操作列 进入  滑出
		//
		// TODO 优化逻辑
		_onTrOperateHover: function (e) {
			if (this.options.onTrOperateHover) {
				this.options.onTrOperateHover(e);
				return;
			}

			var me = this,
				$target = $(e.currentTarget),
				index = $target.closest('.tr').data('index');

			if (e.type == 'mouseenter') {
				var data = me.getCurData()[index];
				if(!data) return;
				var offset = $target.offset();
				var $opr = $(operateTpl({
					index: index,
					options: data.operate || []
				}));

				$opr.on('mouseenter', function () {
					$(this).addClass('enter');
				});

				$opr.on('mouseleave', function () {
					$(this).remove();
				});

				$opr.on('click', 'li', function (e) {
					var $target = $(e.target),
						action = $target.data('action'),
						$opr = $target.closest('.j-tr-operate-list-wrap'),
						index = $opr.data('index'),
						data = null;
					if (action) {
						data = me.getCurData()[index];
						action = _.findWhere(data.operate, {action: action});
						me.trigger('tr.operate', action, data);
					}
					$opr.remove();
					e.stopPropagation();
					return false;
				});

				$('body').append($opr);
				console.log(offset.top)
				console.log($target.height())
				var top = offset.top + $target.height();
				var oH = $opr.height();

				if ($(window).height() + $(doc).scrollTop() - top - oH - 35 < 0) {
					top = offset.top - oH;
				}

				$opr.css({
					left: offset.left + $target.width() - 160,
					top: top,
					display: 'block'
				});
			} else {
				setTimeout(function () {
					var $opr = $('.j-tr-operate-list-wrap[data-index=' + index + ']');
					if (!$opr.hasClass('enter')) {
						$opr.remove();
					}
				}, 100);
			}
		},

		_onTrOperateClick20: function(e) {
			var obj = $(e.currentTarget).data();
			var data = _.findWhere(this.getCurData(), {_id: obj.id + ''});
			var action = _.findWhere(data.operate, {action: obj.action});
			this.trigger('tr.operate', action, data);

			return false;
		},

		_onTrOperateHover20: function(e) {
			if(this.__$operateList && this.__$operateList.myCurTarget === e.currentTarget) {//来回反复，则移除hide就行
				clearTimeout(this.__$operateList.hideTimer);
				return;
			}

			var me = this, $target = $(e.currentTarget);
			var obj = $target.data();
			var width = $target.width();
			var data = _.findWhere(me.getCurData(), {_id: obj.id});
			var index = _.indexOf(me.getCurData(), data);
			var offset = $target.offset();
			var $opr = $(operateTpl({
				index: index,
				options: _.filter(data.operate || [], function(a) {
					return !a.not_fold;
				})
			}));


			if(me.__$operateList) {//销毁之前的组件
				me.__$operateList.destroy();
			}

			$opr.myCurTarget = e.currentTarget;//记住当前的目标对象，方便判断是否是同一个元素触发

			$opr.on('mouseleave', function () { //元素移除，则一定销毁
				$opr.destroy();
			}).on('mouseenter', () => {//把隐藏的定时器清除
				clearTimeout($opr.hideTimer);
			});

			$opr.destroy = _.bind(function() {
				this.remove();
				clearTimeout(this.hideTimer);
				delete this.myCurTarget;
			}, $opr)

			$opr.on('click', 'li', function (e) {
				var action = $(e.target).data('action');
				action && me.trigger('tr.operate', _.findWhere(data.operate, {action: action}), data);

				$opr.destroy();
				e.stopPropagation();
				return false;
			});

			$('body').append($opr);
			var top = offset.top + $target.height() + 8
			var oH = $opr.height();
			if ($(window).height() + $(doc).scrollTop() - top - oH - 35 < 0) {
				top = offset.top - oH - 8;
				$opr.__posTop = true;
			}

			$opr.css({
				left: offset.left + width - 152,
				top: top,
				display: 'block'
			});

			me.__$operateList = $opr;
		},

		_onLeaveTrOperateHover20: function() {
			let $opr = this.__$operateList;
			if($opr) {
				$opr.hideTimer = setTimeout($opr.destroy, 500);
			}
		},

		// 列筛选
		_onColumnFilter: function (e) {
			var me = this,
				$target = $(e.target),
				name = $target.closest('th').data('name');

			me._termBatch.filter.show([{
				FieldName: name === 'crm_tag__c' ? 'tag' : name,
				Comparison: '',
				FilterValue: ''
			}], true);

			me._termBatch.model && me._termBatch.model.listLog('SiftIcon');

			// return false;  出现详情时关闭
		},

		/**
		 * @desc 调整表格尺寸
		 */
		handleSizeChange: function(type) {
			this.$el.removeClass('crm-table-lg crm-table-sm');
			if (type) {
				this.$el.addClass('crm-table-' + type);
			}
			CRM.setLocal('table-size', type);
			this.options.sizeType = type;
			this.upDataScroll();
			this.trigger('size.change', type);
		},

		/**
		 * @desc 调整表格竖向分割线
		 */
		handleSwitchTdLine: function(flag) {
			var status = flag ? 'border' : 'none';
			this.$el[flag ? 'addClass' : 'removeClass']('crm-w-table-td-border');
			CRM.setLocal('table-tdline-status', status);
			util.uploadLog('PaaSobj', 'List', {operationId: 'setGridLine', eventData: {status: status}});//埋点
		},

		/**
		 * @desc 恢复表格默认宽度
		 */
		handleRecoverWidth: function() {
			var changeColumns = [];
			var termWidth = {};
			_.each((this.getCurTerm() || {}).field_list, a => {
				termWidth[a.field_name] = a.width;
			})
			_.each(this.getAllColumns(), column => {
				if(column.data && !column.isHidden) {
					var tw = termWidth[column.data] || this._getColumnMeasuredWidth(column);
					if(Math.abs(column.width - tw) > 3) {
						var $th = this.$('.th-' + column.data.toLowerCase());
						$th.attr('width', column.width = tw);
						changeColumns.push(column);
					}
				}
			})
			if(!changeColumns.length) return;

			this._countWidth();
			this.$('.fix-start-b').css('width', this.options.fixedWidth);
			this.$('.j-sfixed-th').css('width', this.options.fixedWidth);
			this.$('.tb').css('width', this.options.tableWidth);
			this.resizeLast();
			this.$('.j-last-th').css('width', this.options.lastThWidth);
			this.upDataScroll();

			this.trigger('tb.columns.resize', changeColumns);
		},


		/**
		 * @desc 点击有[tb-action-type]属性的元素触发对应事件
		 * trigger 传递参数 类型  触发元素 事件对象
		 */
		_onAction: function (e) {
			var me = this,
				$target = $(e.currentTarget),
				type = $target.attr('tb-action-type');
			if (type) {
				me.trigger(type, $target, e);
			}
			e.stopPropagation();
			return false;
		},

		/**
		 * @desc 显示lookup详情
		 * TODO 抽离出业务逻辑
		 */
		_onShowLookupDetail: function (e) {
			var me = this,
				$target = $(e.currentTarget),
				id = $target.attr('data-id'),
				apiName = $target.attr('data-apiname');
			require.async('crm-components/showdetail/showdetail', function (Detail) {
				if (!me._tbLoopUpDetail || (me._tbLoopUpDetail && me._tbLoopUpDetail.options.apiName !== apiName)) {
					me._tbLoopUpDetail && me._tbLoopUpDetail.destroy && me._tbLoopUpDetail.destroy();
					me._tbLoopUpDetail = new Detail({
						apiName: apiName,
						showMask: me.get('showMask')
					});
					me._tbLoopUpDetail.on('refresh', function () {
						me._tb && me._tb.setParam({}, true);
						me.options.refreshDetail && me.options.refreshDetail(me);
					});
				}
				me._tbLoopUpDetail.setApiName(apiName);
				me._tbLoopUpDetail.show(id);
				me.trigger('show.lookup.detail');
			});

			e.stopPropagation();
		},

		getEffectiveKeyword: function() {
			return this._keyWord;
		},

		getRowSign: function ($tr) {
			return $tr.attr('data-index');
		},

		getRowDataBySign: function (sign) {
			return this.curData && this.curData.data && this.curData.data[sign];
		},

		getDataByTr:function($tr){
			return this.curData && this.curData.data[$tr.attr('data-index')];
		},

		setCellsValBySign: function (sign, copyData, column) {
			var td = {};
			td[sign] = {
				cells: [column.data],
				data: copyData
			};
			this.setCellsVal(td);
		},

		clearOtherRowEditStatus: function (sign) {
			this.$('.main tbody .tr').each(function (index, item) {
				if ($(item).attr('data-index') != sign) {
					$(item).removeClass('tr-edit');
				}
			})
		},

		Edit: Edit,

		/**
		 * @desc 当hover编辑的单元格，出现清除单元格值图标
		 */
		_onHoverEditCell: function(e) {
			var me = this;
			var $target = $(e.currentTarget);
			me.delCellValue($target, function() {
				var $tr = $target.closest('.tr');
				var rowSign = me.getRowSign($tr);
				var curData = me.getRowDataBySign(rowSign);
				var fieldName = $target.attr('data-name');
				var allColumns = me.options.allColumns;
				var column = _.findWhere(allColumns, {
					data: fieldName
				});

				return column && {
					data: curData || {},
					column: column,
					columns: allColumns,
					fieldName: fieldName,
					$tr: $tr,
					list: me.curData.data,
					beforeEditFill: me.options.beforeEditFill,
					beforeDelCellFn: me.options.beforeDelCellFn,
					rowSign: rowSign,
					$content: me
				}
			});

			return false;
		},

		//点击单元格
		_onClickCell(e) {
			//this.__hasEdit() && $(e.currentTarget).find('.td-cell-edit').click();
		},

		//清空单元格的值
		delCellValue: require('./edit/del'),
		/**
		 * @desc 编辑单元格
		 * 事件： type:
		 * depend  依赖类型 返回的数据为数字
		 * quote   引用类型的值变更(被动)
		 * default 默认值的变更(被动)
		 * calculate 计算类型的值变更(被动)
		 */
		_onEditCell: function (e) {
			var me = this,
				$target = $(e.currentTarget),
				columns = me.options.allColumns,
				$tr = $target.closest('.tr'),
				rowSign = me.getRowSign($tr),
				curData = me.getRowDataBySign(rowSign),
				column = null;
			column = _.findWhere(columns, {
				data: $target.attr('data-name')
			});

			this.__removeTrEditCls();

			if (column.clearOnly) {
				return false;
			}

			var copyData = JSON.parse(JSON.stringify(curData));

			if (column.beforeEdit && !column.beforeEdit(curData, column)) {
				return false;
			}

			var _editFn = function (flag) {
				if (flag === false) {
					return;
				};
				if (column && !column.disabledEdit && [9, 17].indexOf(column.dataType) == -1) {
					if ($target.hasClass('editing') || $target.closest('.td-status-readonly').length > 0) {
						return false;
					} // 正在编辑

					if (me._edit) {
						me._edit.destroy();
						me._edit = null;
					}

					me._clearOverlay();
					// if ($tr.find('.td-status-require').length > 0) {column.isRequired = true;}
					me._edit = new me.Edit({
						el: $target,
						$tr: $tr,
						$wrap: me.$el,
						column: column,
						columns: columns,
						data: curData,
						def: curData[column.data],
						zIndex: me.options.zIndex * 1 + 10,
						apiName: me.options.api_name,
						allData: me.curData.data,
						noCalculate: me.options.noCalculate,
						isAddPlusSign: me.options.isAddPlusSign,
						isNewCalculate: me.options.isNewCalculate,
						table: me
					});
					me._edit.on('destroy', function (data) {
						me._edit = null;
					});
					me._edit.on('change', function (data, column, type, isFront, compData, ignoreCalculateFields) {
						var innerFn = function (flag, cb) {
							if (flag === false) {//还原为之前的值
								setTimeout(function () {
									me.setCellsValBySign(rowSign, copyData, column)
								})
							}

							if (type == 'depend') { // 依赖字段
								var _cells = [];
								var _data = {};
								var extVals = data.extVals || {};
								_.each(column, function (item, index) {
									var __d = item.dataType == 6 || item.dataType == 7 ? '__o' : '__r';
									if(curData[item.data] === data[index] && !extVals[item.data + __d]) return;
									_data[item.data] = data[index];
									_cells.push(item.data);

								});
								_.extend(curData, extVals);
								var _td = {};
								_td[rowSign] = {
									cells: _cells,
									data: _data
								};
								me.setCellsVal(_td);
							} else {
								curData[column.data] = data;
								if (column[column.data + '__r']) { // lookup类型
									curData[column.data + '__r'] = column[column.data + '__r'];
								}
							}


							// isFront = me.checkPreExpField(column, curData);

							me.trigger('cell.change', data, column, type, {
								columns: columns,
								cellData: curData,
								$tr: $tr,
								$td: $target.closest('.td-edit'),
								curData: me.curData.data,
								compData: compData,
								ignoreCalculateFields: ignoreCalculateFields,
								flag: flag,
								oldData: copyData,
								cb: cb
							}, isFront);

							me._edit && me._edit.destroy();
						}
						var tmp = {
							data: data,
							column: column,
							columns: columns,
							cellData: curData,
							$tr: $tr,
							curData: me.curData.data,
							compData: compData
						}
						if (me.options.beforeEditFill) {
							me.options.beforeEditFill(tmp, innerFn);
						} else {
							innerFn();
						}
					});
					me._edit.on('hideError', function ($td, $tr) {
						me.removeCellsError($td, $tr, curData);
					})

					me._edit.on('showError', function ($td, $tr, msg) {
						me.addCellsError($td, $tr, msg);
					});

					me._edit.on('keydown', function (code, e) {
						if (me.options.forbidKeydown()) return;
						var tar = $(e.target);
						var $td = e.$td || $(e.target).closest('td');
						var $tr = $td.closest('.tr');
						var index = Number($tr.attr('data-index'));
						var inputMap = {
							1: true,
							2: true,
							3: true,
							101: true,//多行文本
							31: true,
							32: true,
							33: true,
							34: true
						};
						var pos;
						switch (code) {
							case 37: //←
								function _fn37() {
									$('.td-' + preColumn[preColumn.length - 1].data.toLowerCase() + ' .td-cell-edit', $tr).trigger('click');
									me.scrollToTd(preColumn[preColumn.length - 1].data);
								}
								pos = CRM.util.getCursorPos($(e.target)[0]);
								if (pos == 0) {
									_.some(columns, function (item, i) {
										var flag = item.data == column.data;
										if (flag) {
											index = i
										}
										return flag;
									});
									var preColumn = _.filter(columns, function (item, i) {
										return i < index && inputMap[item.dataType] && item.data;
									});
									if (preColumn[preColumn.length - 1]) {
										me.options.keydownEventExt ? me.options.keydownEventExt(37, index, _fn37, tar) : _fn37();
									}
								}
								break;

							case 38: // ↑
								function _fn38() {
									$tr = me.$('.main .tr[data-index=' + (index - 1) + ']');
									$tr = !$tr[0] ? me.$('.main .tr[data-index=' + (me.curData.data.length - 1) + ']') : $tr;
									$('.td-' + column.data.toLowerCase() + ' .td-cell-edit', $tr).trigger('click');
									$tr.addClass('tr-edit').siblings().removeClass('tr-hover tr-edit');
									me.scrollToTr($tr);
								}
								me.options.keydownEventExt ? me.options.keydownEventExt(38, index, _fn38, tar) : _fn38();
								break;

							case 39: // →
								function _fn39() {
									$('.td-' + nextColumn[0].data.toLowerCase() + ' .td-cell-edit', $tr).trigger('click');
									me.scrollToTd(nextColumn[0].data);
								}
								pos = CRM.util.getCursorPos($(e.target)[0]);
								if (pos == $(e.target).val().length) {
									_.some(columns, function (item, i) {
										var flag = item.data == column.data;
										if (flag) {
											index = i
										}
										return flag;
									});
									var nextColumn = _.filter(columns, function (item, i) {
										return i > index && inputMap[item.dataType] && item.data;
									});
									if (nextColumn[0]) {
										me.options.keydownEventExt ? me.options.keydownEventExt(39, index, _fn39, tar) : _fn39();
									}
								}
								break;

							case 40: // ↓
								function _fn40() {
									var $tr = me.$('.main .tr[data-index=' + (index + 1) + ']');
									$tr = !$tr[0] ? me.$('.main .tr[data-index=0]') : $tr;
									$('.td-' + column.data.toLowerCase() + ' .td-cell-edit', $tr).trigger('click');
									$tr.addClass('tr-edit').siblings().removeClass('tr-hover tr-edit');
									me.scrollToTr($tr);
								}
								me.options.keydownEventExt ? me.options.keydownEventExt(40, index, _fn40, tar) : _fn40();
								break;

						}

						$tr.addClass('tr-hover');
					});

					me.trigger('cell.edit.click', column, e);

					// 清空其他列的编辑状态
					//
					me.clearOtherRowEditStatus(rowSign);
				}
			}

			// 编辑前验证
			if (_.isFunction(me.options.beforeEditFn)) {
				me.options.beforeEditFn({
					column: column,
					data: curData,
					$tr: $tr,
					$target: $target,
					e: e
				}, _editFn)
			} else {
				_editFn();
			}

			e.stopPropagation();
		},

		//=====================校验反算结果是否正常，如果不正常，则走触发反算字段的正向计算===========================
		//todo:反算逻辑应该放到newmd/handle.js的cellChangeHandle方法中

		/**
		 * @desc 处理反算逻辑
		 * column {{Object}} 依赖此列
		 * 纯前端逻辑
		 * 目前销售订单产品中小计用到
		 */
		// checkPreExpField: function (column, curData) {
		// 	var me = this, field = column.pre_expression_key;
		// 	if (!field) return false;
		// 	field = _.findWhere(me.options.columns, {data: field});
		// 	if (!field) {
		// 		return false
		// 	}
		// 	return me._doFrontCount(field, column.pre_expression, curData);
		// },

		/**
		 * @desc 前端自己处理计算默认值字段
		 * @param 计算公式
		 * 前端计算公式
		 */
		// _doFrontCount: function (column, exp, curData) {
		// 	var me = this, val = '',
		// 		dataType = column.dataType,
		// 		data = curData;
		//
		// 	exp = exp.replace(/\$[a-zA-Z_0-9]*\$/g, function () {
		// 		var key = arguments[0].replace(/\$/g, '');
		// 		var field = _.findWhere(me.options.columns, {data: key});
		// 		var fieldType = field ? field.dataType : 3;
		// 		key = data[key];
		// 		// key = fieldType == 33 && key != '' && (field.data !== 'discount' || arguments[2].substr(arguments[1] + 10, 4) !== '/100')? key / 100 : key; //特殊逻辑 销售订单的折扣在公式里有可能除以100或者没除100
		// 		key = fieldType == 33 && key != '' ? key / 100 : key;
		// 		if (key || key == 0) {
		// 			return key;
		// 		}
		// 		return fieldType == 1 ? '' : ([2, 3, 35, 36].indexOf(fieldType) != -1 ? 0 : NaN);
		// 	});
		//
		// 	var isIifi;
		// 	if (dataType == 1) {
		// 		val = exp.replace(/&/g, '');
		// 	} else {
		// 		var myEval = eval;
		// 		try {
		// 			val = (myEval(exp) * 1);
		// 			if (Infinity == val || val === -Infinity) { //不合理的计算丢弃，保持原值不动 比如1000/0
		// 				isIifi = true;
		// 				return false;
		// 			}
		// 			val = column.dataType == 33 ? val * 100 : val;
		// 			val = val.toFixed(column.range[1]) * 1;
		// 		} catch (e) {
		// 			val = NaN;
		// 			return false
		// 		}
		// 	}
		// 	if (isIifi) {
		// 		return false;
		// 	}
		// 	return true;
		// },

		//====================================================================

		/**
		 * @desc 禁用列编辑
		 * 要禁用的列表
		 */
		closeColumnEdit: function (name) {
			var column = _.findWhere(this.options.allColumns, {
				data: name
			});

			if (column) {
				column.disabledEdit = true;
				this.$('.td-' + name.toLowerCase()).addClass('td-edit-close');
			}
		},

		/**
		 * @desc 打开列编辑禁用
		 */
		openColumnEdit: function (name) {
			var column = _.findWhere(this.options.allColumns, {
				data: name
			});

			if (column) {
				column.disabledEdit = false;
				this.$('.td-' + name.toLowerCase()).removeClass('td-edit-close');
			}
		},

		/**
		 * @desc 切换列编辑
		 */
		toggleColumnEdit: function (name, flag) {
			if (flag) {
				this.openColumnEdit(name);
			} else {
				this.closeColumnEdit(name);
			}
		},

		_lockColumnHandle: function(e) {
			var $target = $(e.currentTarget);
			var name = $target.closest('th').attr('data-name');
			if(!name) return;
			var fn = $target.closest('.j-header-fixed').length ? 'unlockColumn' : 'lockColumn';
			this[fn](name);
			util.uploadLog('page', 'table', {
                eventId: fn,
                eventType: 'cl',
                eventData: {
                    apiname: this.options.objectApiname || 'unknow',
                    field: name
                }
            });
			return false;
		},

		_lockTip: function(status) {
			FxUI.Message({
				isMiddler: true,
				duration: 1500,
				message: status ? $t('已将该列在前部固定') : $t('取消该列在前部固定'),
				type: 'success'
			})
		},
		/**
		 * @desc 锁定指定的列
		 */
		lockColumn: function(name) {
			if(!_.find(this.options.allColumns, function(a) {return !a.isHidden && !a.fixed && !a.lastFixed && a.data !== name})) {
				util.alert($t('非固定列至少保留一列'));
				return;
			}

			var lockNum = 0;
			_.each(this.options.allColumns, function(a) {
				if(a.data && a.fixed && !a.isHidden) {
					lockNum++;
				}
			})
			if(lockNum > 9) {//已经有7个锁定列
				util.alert($t('最多只能锁定{{num}}列', {num: 10}));
				return;
			}

			var $theader = this.$('.dt-main').find('>.header');
			var isValidSize = this._isValidColSize({
				distance: $theader.find('.th-' + name.toLowerCase()).width() || 0,
				fixedStartWidth: $theader.find('.fix-start-b').width() || 0,
				fixedEndWidth: $theader.find('.fix-end-b').width() || 0,
				headerWidth: $theader.width() || 0,
			});
			if (!isValidSize) {
				util.alert($t('crm.table.drag.tips', null, '操作失败，锁定区域过宽，会导致此表无法滑动'));
				return;
			}

			var t = _.findWhere(this.options.allColumns, {data: name});
			t.fixed = true;
			t.fixedIndex = ++FI;
			t.customFixed = true;

			this.updateTermBatchColumnStatus(t);

			if(!this.options.isListLayout && (this.options.scrollLoad || this.__startNoFixedColumns || !this.options.isMyObject)) {
				this.renderByColumns(this.options.allColumns, null, true);
				this.trigger('column.lockChange', t, true);
				this.resize();
				this._lockTip(true);
				return;
			}

			//处理表头
			var $main = this.$('.dt-main')
			var $header = $main.find('>.header');
			var $th = $header.find('.th-' + name.toLowerCase());
			var $fsb = $header.find('.fix-start-b');
			$th.find('.j-th-lock').attr('data-title', $t('解锁'));
			$fsb.find('.j-header-fixed tr').append($th);


			//处理数据
			var qstr = '[data-name="' + name + '"]';
			var $firstTable = $main.find('.main .fix-start-b');
			var $table = $main.find('.main .tb');
			$firstTable.find('thead tr').append($table.find('thead tr ' + qstr));

			var $tds = $table.find('tbody .td-' + name.toLowerCase());
			$firstTable.find('tbody tr').each(function(index) {
				$(this).append($tds.eq(index));
			});

			this.options.columns = this.options.allColumns;
			this.simplyFormatColumns();
			this._countWidth();
			this.$('.fix-start-b').css('width', this.options.fixedWidth);
			this.$('.j-sfixed-th').css('width', this.options.fixedWidth);
			this.$('.tb').css('width', this.options.tableWidth);
			this.resize();
			this.trigger('column.lockChange', t, true);
			this._lockTip(true);
		},
		/**
		 * @desc 解锁指定的列
		 */
		unlockColumn: function(name) {
			var allColumns = this.options.allColumns;
			var t = _.findWhere(allColumns, {data: name});
			if(!t && !t.customFixed) return;
			allColumns = _.filter(allColumns, function(a) {
				return a.data !== name;
			})
			var index = _.indexOf(this._columnsOrder, name);
			delete t.fixed;
			delete t.customFixed;
			delete t.fixedIndex;

			this.updateTermBatchColumnStatus(t);

			var tmp; //
			while(index) {//依照初始化的列顺序找出当前解锁列的前一列
				var tt = this._columnsOrder[--index];
				var t2 = _.findWhere(allColumns, {data: tt});
				if(t2 && !t2.fixed && !t2.isHidden) {
					tmp = tt;
					break;
				};
			}

			if(!tmp) { //如果没找到，则插入到第一个位置
				_.find(allColumns, function(a, index) {
					if(!a.fixed) {
						allColumns.splice(index, 0, t);
						return true;
					}
				})
			} else {
				_.find(allColumns, function(a, index) {
					if(a.data === tmp) {
						allColumns.splice(index + 1, 0, t);
						return true;
					}
				})
			}

			if(!this.options.isListLayout && (this.options.scrollLoad || this.__startNoFixedColumns || !this.options.isMyObject)) {
				this.renderByColumns(allColumns, null, true);
				this.trigger('column.lockChange', t);
				this.resize();
				this._lockTip();
				return;
			}
			//处理表头
			var $main = this.$('.dt-main')
			var $header = $main.find('>.header');
			var $th = $header.find('.fix-start-b .j-header-fixed tr th[data-name="' + name + '"]');
			var $tr = $main.find('.j-header-tb tr');
			$th.find('.j-th-lock').attr('data-title', $t('锁定'));
			$tr.find(tmp ? '[data-name="'+ tmp +'"]' : '.j-sfixed-th').after($th);

			//处理数据
			var $firstTable = $main.find('.main .fix-start-b');
			var $table = $main.find('.main .tb');
			var $tds = $firstTable.find('tbody .td-' + name.toLowerCase());
			$main.find('.main-con .tb thead tr').find(tmp ? '[data-name="'+ tmp +'"]' : '.j-sfixed-th').after($firstTable.find('thead .th-' + name.toLowerCase()));
			tmp && (tmp = tmp.toLowerCase());
			$table.find('tbody tr').each(function(index) {
				let $$tr = $(this).find(tmp ? '.td-' + tmp : 'td:first');
				let $$td = $tds.eq(index);
				if($$tr.length) {
					$$tr.after($tds.eq(index));
				} else {
					$$td.remove();
				}
			});

			this.options.columns = allColumns;
			this.simplyFormatColumns();
			this._countWidth();
			this.$('.fix-start-b').css('width', this.options.fixedWidth);
			this.$('.j-sfixed-th').css('width', this.options.fixedWidth);
			this.$('.tb').css('width', this.options.tableWidth);
			this.resize();
			this.trigger('column.lockChange', t);
			this._lockTip();
		},

		_cusBtnHandle(e) {
			var $target = $(e.currentTarget);
			var name = $target.closest('th').attr('data-name');
			let c = _.findWhere(this.options.allColumns, {data: name});
			let index = $target.data('index');
			let btn = c.customHeaderBtns && c.customHeaderBtns[index];
			btn && btn.callBack && btn.callBack(e);
			
			return false;
		},

		//设置操作列宽度
		countOperateColumnWidth: function(num, isMore) {
			var moreWidth = isMore ? 48 : 16;
			var width = num ? 68 * num + moreWidth  : 64;//默认宽度等于每个按钮所占宽度68 + 更多按钮宽度32 + 左右pading16

			this.updateOpereateWidth(width);
		},

		//更新操作列的宽度
		updateOpereateWidth(width) {
			//更新列里面的宽度
			var cc = _.findWhere(this.options.allColumns, {dataType: 'operate'});
			if(cc && cc.width == width) return;
			cc && (cc.width = width);
			//更新dom元素的宽宽
			var $main = this.getMainWrapper();
			$main.find('>.header>.fix-end-b,>.main>.fix-end-b').width(width).find('.column-operate').width(width);
			$main.find('>.header .j-efixed-th:first,>.main .j-efixed-th:first').width(width + 1);
		},

		//更新数据总数
		updateTotal20: function(total) {
			this.$('.j-crm-list-total').html($t('共{{num}}条', {num: '<em style="margin:0 4px;">'+ total + '</em>'}));
		},

		//把表头设为fixed
		fixedHeader: function(css) {
			this.__fixedWrapper(this.getHeaderWrapper(), css);
		},

		__fixedWrapper: function($wrapper, css) {
			if(!css) {
				css = {
					position: '',
					top: '',
					left: '',
					right: '',
					zIndex: '',
					bottom: '',
					paddingTop: '',
					background: ''
				}
			}
			$wrapper.css(_.extend({
				position: 'fixed'
			}, css));
		},

		//把分页操作区设为fixed
		fixedPageFooter: function(css) {
			this.__fixedWrapper(this.getPaginationWrapper(), css);
		},

		//把批量操作区域浮动
		fixedBatchOperate: function(css) {
			this.__fixedWrapper(this.getBatchOperateWrapper(), css);
		},

		getBatchOperateWrapper: function() {
			return this.$('>.dt-batchoperate-wrapper');
		},

		getHeaderWrapper: function() {
			return this.getMainWrapper().find('>.header');
		},

		getTermBatchWrapper: function() {
			return this.$('>.dt-term-batch');
		},

		getMainWrapper: function() {
			return this.$('>.dt-main');
		},

		getCaptionWrapper: function() {
			return this.$('>.dt-caption');
		},

		getPaginationWrapper: function() {
			return this.$('>.dt-page');
		},

		//把
		fixedTermBatch: function(css) {
			var $TermBatchWrapper = this.getTermBatchWrapper();
			var $bterm = $TermBatchWrapper.find('>.batch-term');
			if(!css) {
				$bterm.css('padding-bottom', '');
			} else {
				$bterm.css('padding-bottom', '4px');
			}

			this.__fixedWrapper($TermBatchWrapper, css);
		},

		//
		// doAllDependColumnExp: function (data) {
		// var me = this;
		// _.map(me.get('allColumns'), function (item) {
		// _.map(data, function (value, index) {
		// me.doDependColumnExp(item.data, index);
		// });
		// });
		// },

		/**
		 * @desc 处理依赖此列的计算
		 * @param {{String}} 列名称
		 * @param {{Number}} 要处理的行
		 */
		doDependColumnExp: function (name, index) {
			var me = this;
			var column = _.findWhere(me.options.allColumns, {
				data: name
			});
			if (column) {
				var curData = me.curData.data[index];
				var edit = new me.Edit({
					column: column,
					columns: me.options.allColumns,
					data: curData,
					apiName: me.options.api_name,
					allData: me.curData.data,
					$tr: me.$('.tr[data-index=' + index + ']')
				});
				edit._doExpField(column);
			}
		},

		/**
		 * @desc 设置指定单元格的值
		 * name  列名
		 * val   {{Mix}} 值
		 * rowNo {{Numver}} 行号
		 */
		setColumnVal: function (name, val, rowNo) {
			var me = this;
			var column = _.findWhere(me.options.allColumns, {
				data: name
			});
			if (column) {
				me.curData.data[rowNo][name] = val;
				var formatVal = helper.getFormatVal(val, column, null, me.curData.data[rowNo], '', me.isEm6);
				var $tr = me.$('.tr[data-index=' + rowNo + ']');
				if ([35, 40].indexOf(column.dataType) != -1 || !column.isEdit) { // 不允许编辑的字段
					$('.td-' + name.toLowerCase() + ' .tb-cell', $tr).html(formatVal);
				} else {
					var $td = $('.td-' + name.toLowerCase() + ' .td-cell-edit', $tr);
					$td.html(formatVal);

					me.removeCellsError($td, $tr);
				}
			}
		},

		/**
		 * @desc 修改列属性
		 * name 列名
		 * property {{Object}}
		 */
		setColumn: function (name, property) {
			var column = _.findWhere(this.options.columns, {
				data: name
			});
			if (column) {
				_.extend(column, property || {});
			}
		},

		/**
		 * @desc 表格编辑是否验证通过
		 * 表格编辑字段的验证
		 */
		editValid: function () {
			var me = this;
			// if (me._edit) {
			// return me._edit.valid();
			// }
			return me.$('.tr-error').length == 0;
		},

		// 获取表格验证错误信息
		//
		getEditValidInfo: function () {
			var $trs = this.$('.tb .tr-error');
			return {
				rowNo: $trs.length,
				cellNo: $trs.find('.td-status-error').length
			}
		},

		//
		// 当前行是否通过验证
		//
		editRowValid: function (pos) {
			return !this.findTrs(pos, true).hasClass('tr-error');
		},

		// 设置单元格的值
		// opts: {1: {cells: ['name', 'sex'], data: {name: 112, sex: 2}}}
		setCellsVal: function (opts) {
			var me = this;
			var columns = this.options.allColumns;
			var arr = [];
			var rowNos = [];
			_.each(opts, function(item, key) {
				arr[key] = item;
				rowNos.push(key);
			})
			var $tbStrs = me.findTrs(rowNos, true);
			var $firstStrs = me.findTrs(rowNos, null, true);
			var curData = me.curData;

			function _inner($trs) {
				var index = 0;
				_.each(arr, function (item, rowNo) {
					if(!item) return;
					var $tmp = $trs.eq(index);
					var tmp = item.data;
					_.each(item.cells, function (name) {
						tmp[name] === void 0 && (tmp[name] = '');
					})
					var data = _.extend(curData.data[rowNo], tmp);
					_.each(item.cells, function (name) {
						var column = _.findWhere(columns, {data: name});
						if (!column) return;

						var formatVal = helper.getFormatVal(data[name], column, null, data, '', me.isEm6);
						var $$td = $tmp.find(column.isEdit ? '[data-name="' + name + '"]' : '.td-' + name.toLowerCase() + ' .tb-cell');
						var $td = $$td.closest('.td-edit').removeClass('td-status-error');

						if(!$td.length) {
							$$td.parent().removeClass('td-status-error');
						}

						if(column.__reRenderTextFn) {
							column.__reRenderTextFn($$td, formatVal, data);
							return;
						}

						$$td.html(formatVal);

						// 重新渲染render，可能有自定义的组件
						if (column.__reRender) {
							$td = $tmp.find('.td-' + name.toLowerCase() + ' .tb-cell');
							$td.html(column.render(data[name], 'column', data, rowNo));
						}

						var title = '';
						try {
							title = helper.createDataTip(column, data[name], data);
							if(title) {
								title = _.unescape(title);
							}
						} catch(e) {}

						if($td.length) {
							$$td.attr('title', title);
							$td.attr('title', title);
						} else {
							$$td.parent().attr('title', title);
							$$td.attr('title', title);
						}
					})

					me.removeCellsError(null, $tmp);
					me.removeErrorMsg();
					index++;
				})
			}

			$tbStrs.length && _inner($tbStrs);
			$firstStrs.length && _inner($firstStrs);
		},

		/**
		 * @desc 设置单元格的值
		 * @param obj:{
		 *     1:{
		 *         name:11,
		 *         age:22
		 *     },
		 *     22:{
		 *         price:33
		 *     }
		 * }
		 */
		setCellsValue: function (obj) {
			let opts = {};
			_.each(obj, (val, key) => {
				opts[key] = {
					cells: _.keys(val),
					data: val
				}
			});
			this.setCellsVal(opts);

		},

		//适用于单纯的更新数据，不适用带验证的数据更新
		//opts: {1: {cells: ['name', 'sex'], data: {name: 112, sex: 2}}}
		setTrsVal: function (trNo, fields, data) {
			var $trs = this.findTrs(trNo);
			var columns = this.options.allColumns || this.options.columns;
			var dataTips = helper.createDataTips(columns, data);
			_.each(fields, function (name) {
				var column = _.findWhere(columns, {data: name});
				if (!column) return;
				var $td = $trs.find('[data-fieldname="' + name + '"]');
				$td.html(column.render(data[name], column, data, trNo));
				var type = column.returnType || column.dataType;
				$td.attr('title', dataTips[name]);
				if(!column.isEdit && !column._rendered && column.data === 'name') {
					let url = util.getDetailTabUrl(data.object_describe_api_name, data._id);
					if(url) {
						$td.append(url);
					}
				}
			})
		},


		__sortCellsStatusRowNos(rowNos) {
			return _.sortBy(rowNos, rowNo => rowNo * 1);
		},

		/**
		 * @desc 修改单元格状态
		 * @param name {{String}} 单元格名称
		 * @param pos {{String}} 行位置
		 * @param status {{opts}}   状态
		 show:
		 hide:
		 require:
		 notrequire:
		 readonly:
		 notreadonly:
		 */
		setCellsStatus: function (name, pos, status) {
			var tmp = (this._cacheCellsStatus || (this._cacheCellsStatus = {}));
			(tmp[pos] || (tmp[pos] = {}))[name] = status;

			if (!this._setCellsStatus) {
				this._setCellsStatus = _.debounce(function () {
					var me = this;
					var rowNos = me.__sortCellsStatusRowNos(_.keys(this._cacheCellsStatus));
					var tt = this.findGroupTrs(rowNos);
					var $tbStrs = tt.$tbStrs;
					var $firstStrs = tt.$firstStrs
					var result = {
						show: {},
						hide: {},
						require: {},
						notrequire: {},
						readonly: {},
						notreadonly: {}
					}

					var needRemoveErrorRows = [];
					var trIndex = {};
					_.each(rowNos, function (rowNo, index) {
						trIndex[rowNo] = index;
						var obj = me._cacheCellsStatus[rowNo];
						_.each(obj, function (status, name) {
							_.each(status, function (a, k) {
								(result[k][rowNo] || (result[k][rowNo] = [])).push(name);
								if (k === 'hide' || k === 'notrequire') {
									needRemoveErrorRows.push(rowNo * 1)
								}
							})
						})
					})

					var rcls = {
						show: 'td-status-hide',
						hide: 'td-status-require td-status-show',
						require: 'td-status-notrequire td-status-hide',
						notrequire: 'td-status-require',
						readonly: 'td-status-require td-status-notreadonly',
						notreadonly: 'td-status-readonly'
					}

					var _inner = function(fields, $trs, rowNo, status) {
						var $tds = me.findTds(fields, $trs.eq(trIndex[rowNo]));
						var ac = 'td-status-' + status;
						$tds.addClass(ac).removeClass(rcls[status]);
						if (status === 'require') {
							$tds.find('.tb-cell').addClass('td-cell-require');
						} else if (status === 'hide' || status === 'notrequire') {
							me.removeCellsError($tds);
						}
					}
					_.each(result, function (item, status) {
						_.each(item, function (fields, rowNo) {
							if (!fields || !fields.length) return;
							_inner(fields, $tbStrs, rowNo, status);
							_inner(fields, $firstStrs, rowNo, status);
						})
					})

					needRemoveErrorRows = _.union(needRemoveErrorRows);
					needRemoveErrorRows.length && me.removeCellsError(null, tt.$allStrs.filter(function () {
						return _.contains(needRemoveErrorRows, $(this).attr('data-index') * 1);
					}))

					me._addCellsStatusToData(this._cacheCellsStatus);

					this._cacheCellsStatus = null;
				}, 50)
			}

			this._setCellsStatus();
		},

		//追加单元格状态到数据上，方便后期操作
		_addCellsStatusToData: function(cellsStatus) {
			var data = this.curData && this.curData.data;
			data && _.each(cellsStatus, function(item, index) {
				var tt = data[index];
				if(!tt) return;
				var cs = tt._cellStatus;
				if(!cs) {
					tt._cellStatus = item;
					return;
				}
				_.each(item, function(status, field) {
					if(!cs[field]) {
						cs[field] = {};
					}
					
					let cf = _.extend(cs[field], status);
					
					if(status.hide && !status.show) cf.show = void 0;
					if(status.show && !status.hide) cf.hide = void 0;
					

					if(status.readonly && !status.notreadonly) cf.notreadonly = void 0;
					if(status.notreadonly && !status.readonly) cf.readonly = void 0;

					if(status.require && !status.notrequire) cf.notrequire = void 0;
					if(status.notrequire && !status.require) cf.require = void 0;
				})
			})
		},

		//查询符合条件的行
		findTrs: function (pos, isTb, isFirst) {
			var $trs;
			var selector = isTb ? '.main .tb .tr' : isFirst ? '.main .fix-start-b .tr' : '.main tbody >.tr';
			if (_.isArray(pos)) {
				pos = _.map(pos, function (index) {
					return index * 1
				})
				$trs = this.$(selector);
				return $trs.filter(function () {
					return _.contains(pos, $(this).attr('data-index') * 1);
				})
			} else {
				return this.$(selector + '[data-index=' + pos + ']');
			}
		},

		//查询返回分组的行
		findGroupTrs: function(pos) {
			var $trs = this.findTrs(pos);
			var num = $trs.length / this.__tableTypeNumber; //表格的列是三个表格拼接起来的
			return {
				$allStrs: $trs,
				$firstStrs: $trs.slice(0, num),
				$tbStrs: $trs.slice(num, num * 2),
				$lastStrs: $trs.slice(num * 2, $trs.length)
			}
		},

		//查询符合条件的单元格
		findTds: function (names, $tr) {
			return $tr.find(_.map(names, function (name) {
				return '.td-' + name.toLowerCase();
			}).join(','))
		},

		/**
		 * @desc 错误提示
		 * @param opts { 1: ['name','price]}
		 * @param isRemove
		 */
		toggleValidError: function (opts, isRemove) {
			var me = this;
			var fn = isRemove ? 'removeCellsError' : 'addCellsError';
			me.findTrs(_.keys(opts)).each(function (index) {
				var $tmp = $(this);
				var _idx = _.isUndefined($tmp.data("index")) ? index : $tmp.data("index");
				var $tds = me.findTds(opts[_idx], $tmp);
				$tds.length && me[fn]($tds, $tmp);
			})
		},

		addTrError: function (rowIndex) {
			var me = this;
			var arr = _.isArray(rowIndex) ? rowIndex : [rowIndex];
			_.each(arr, function (index) {
				me.findTrs(index).addClass('tr-error')
			});
			this.triggerTrError();
		},

		/**
		 * @desc 添加错误提示气泡
		 * @param rowIndex        行index
		 * @param field            字段
		 * @param msg            提示信息
		 * @param isRemove
		 */
		addErrorMsg: function (rowIndex, field, msg, isRemove) {
			var me = this;
			var fn = isRemove ? 'removeCellsError' : 'addCellsError';
			me.findTrs([rowIndex]).each(function (index) {
				var $tmp = $(this);
				var $tds = me.findTds([field], $tmp);
				$tds.length && me[fn]($tds, $tmp, msg);
			})
		},

		addCellsError: function ($td, $tr, msg) {
			$td.addClass('td-status-error');
			if (msg) {
				this.showErrorMsg($td, msg);
			}
			this.findTrs($tr.attr('data-index')).addClass('tr-error');
			this.triggerTrError();
		},

		/**
		 * @desc 显示错误提示
		 * @param $td
		 * @param msg
		 */
		showErrorMsg: function ($td, msg) {
			if (msg) {
				// this.removeErrorMsg();
				// $('body').append(errorMsgTpl({
				// 	msg: msg
				// }));
				// var p = $td.offset();
				// $('body').find('.table-errorMsg').css({
				// 	left: p.left + 10 + 'px',
				// 	top: p.top + 45 + 'px'
				// })

				this.removeErrorMsg();
				this.$el.append(errorMsgTpl({
					msg: msg
				}));
				var p = $td.offset();
				this.$('.table-errorMsg').css({
					left: p.left + 10 + 'px',
					top: p.top + 45 + 'px'
				})
			}
		},

		hideErrorMsg: function () {
			this.$('.table-errorMsg').remove();
			// $('body').find('.table-errorMsg').remove();
		},

		removeErrorMsg: function ($td) {
			this.$('.table-errorMsg').remove();
			// $('body').find('.table-errorMsg').remove();
		},

		removeCellsError: function ($td, $tr, rowData) {
			if ($td) {
				$td.removeClass('td-status-error');
				this.removeErrorMsg($td)
			}

			if ($tr && $tr.length) {
				var $str = this.findTrs($tr.attr('data-index'));
				$str.find('.td-status-error').length || $str.removeClass('tr-error');
			}

			this.triggerTrError(rowData);
		},

		triggerTrError: function (rowData) {
			if (!this._triggerTrError) {
				this._triggerTrError = _.debounce(function (rowData) {
					this.trigger('tr.error', this.getEditValidInfo(), rowData);
				}, 100)
			}

			this._triggerTrError(rowData);
		},

		//根据传入数据进行排序
		//适用于静态数据的前端自定义排序
		//datas必须是table上的原数据的副本，只不过顺序不同
		sortByDatas: function (datas) {
			if (!datas.length) return;
			var me = this;
			var $fixstart = this.$('.fix-start-b tbody');
			var $fixtrs = $fixstart.find('>.tr');
			var $mid = this.$('.tb tbody');
			var $midtrs = $mid.find('>.tr');
			var $fixend = this.$('.fix-end-b tbody')
			var $fixendtrs = $fixend.find('>.tr');
			var data = this.curData.data;
			_.each(datas, function (item, nindex) {
				var index = _.indexOf(data, item);
				let ss = $fixtrs.eq(index).clone().attr('data-index', nindex);
				me._setSernialnumberValue(ss, nindex + 1);
				$fixstart.append(ss);
				$mid.append($midtrs.eq(index).clone().attr('data-index', nindex));
				$fixend.append($fixendtrs.eq(index).clone().attr('data-index', nindex));
			})

			$fixtrs.remove();
			$midtrs.remove();
			$fixendtrs.remove();

			data.splice(0, data.length);
			[].push.apply(data, datas);
			this.options.scrollLoad && this.resize();
		},

		//交换两行的位置
		//index 需要交互的列 targetIndex 被交换的列
		// 暂无使用场景
		exTrs: function (index, targetIndex) {
			// var $trs = this.findTrs(index);
			// var $targetTrs = this.findTrs(targetIndex);
			// $trs.attr('data-index', targetIndex);
			// $targetTrs.attr('data-index', index);

			// $trs.eq(0).after($targetTrs.eq(0).clone(true));
			// $trs.eq(1).after($targetTrs.eq(1).clone(true));
			// $trs.eq(2).after($targetTrs.eq(2).clone(true));
			// $targetTrs.eq(0).after($trs.eq(0));
			// $targetTrs.eq(1).after($trs.eq(1));
			// $targetTrs.eq(2).after($trs.eq(2));
			// $targetTrs.remove();

			// var data = this.curData.data;
			// var a = data[index];
			// var b = data[targetIndex];
			// data[index] = b;
			// data[targetIndex] = a;
		},

		/**
		 * @desc 关闭验证编辑错误提示
		 */
		closeEditErrorTip: function () {
			// var me = this;
			// if (me._edit) {
			// me._edit.hideError();
			// if (!me._edit.valid()) {
			// me._edit.destroy();
			// me._edit = null;
			// }
			// }
		},

		/**
		 * @desc 过滤按钮事件
		 */
		_onFilter: function (e) {
			var me = this,
				$target = $(e.target);
			$target.toggleClass('cur');
			if ($target.hasClass('cur')) {
				var rule = Table.getRule()[0];
				me._fcolumns.show(rule.rules || rule.value);
			} else {
				me._fcolumns.hide();
			}
			me.resize();
		},

		_mainMouseLeaveHandle() {
			if(this.__hover$Trs) {
				this.__hover$Trs.removeClass('tr-hover tr-edit');
				this.__hover$Trs = null;
			}

			this.__showInsertIcon();
		},

		__hasEdit() {
			if(this.__isEditTable === void 0) {
				this.__isEditTable = this.options && !!_.findWhere(this.options.columns, {isEdit: true});
			}

			return this.__isEditTable;
		},

		__removeTrEditCls() {
			this.__hover$Trs && this.__hover$Trs.removeClass('tr-edit');
		},

		/**
		 * @desc 行滑过时显示插入图标 830新增
		 */
		__showInsertIcon(flag) {
			let insertMuenFn = this.options && this.options.insertMuenFn;
			if(!insertMuenFn) return;
		
			this.__$insertIcon && (this.__$insertIcon.remove(), this.__$insertIcon = null);

			if(!flag || !this.__hover$Trs) return;

			let trData = this.curData.data[this.__hover$Trs.attr('data-index')]
			let insertMuen = insertMuenFn(trData);
			if(!insertMuen || !insertMuen.length) return;

			let pos = this.__hover$Trs.offset();
			let $h = this.__hover$Trs.height();
			let $main = this.getMainWrapper();
			let more1 = insertMuen.length > 1;

			let $muen = more1 ? '<div class="s-menu"><ul>' + _.map(insertMuen, (a, index) => `<li data-index="${index}" class="insert-icon__sss">${a.label}</li>`).join('') + '</ul></div>' : '';

			this.__$insertIcon = $(`
				<div
					class="crm-table-insert-icon"
					style="top:${pos.top + $h}px;left:${$main.offset().left - 16}px;"
				>
					<div style="height:${$h + 6}px;" class="insert-icon"><span data-index="0" data-title="${insertMuen[0].label}" class="el-icon-circle-plus${more1 ? '' : ' crm-ui-title insert-icon__sss'}"></span></div>
					<div style="width:${$main.width()}px;" class="p-line"></div>
					${$muen}
				</div>
			`)

			this.getMainWrapper().append(this.__$insertIcon);

			insertMuen._data = trData;
			this.__insertMuen = insertMuen;
		},

		_insertIconClickHandle(e) {
			this.__insertMuen[$(e.currentTarget).attr('data-index')].callback(e, this.__insertMuen._data);
		},

		/**
		 * @desc 行划过事件
		 */
		_onTrHover: function (e) {
			var me = this;

			if (me._scrolling) {
				return;
			}

			if(!me._trHoverTimer) {
				me._trHoverTimer = _.debounce($event => {
					if(me._scrolling) return;
					if(!me.options) return;
					
					var $tr = $($event.currentTarget);
					var $trs = me.findTrs($tr.attr('data-index'));
					var cls = `tr-hover${me.__hasEdit() ? ' tr-edit': ''}`
					me.__hover$Trs && me.__hover$Trs.removeClass(cls);
					me.__hover$Trs = $trs.addClass(cls);

					me.__showInsertIcon(true);

					me.trigger('tr.hover', $trs);

					// if (me.$('.td-edit').length > 0) {
					// 	me.$('.main .tr').each(function (index, item) {
					// 		var $item = $(item);
					// 		$item.toggleClass('tr-edit', $item.find('.editing').length > 0);
					// 	});
					// 	$trs.toggleClass('tr-edit', !$trs.hasClass('tr-disabled') && ($trs.find('.td-edit').length > 0 && !$trs.find('.td-edit-close').length) || $trs.find('.editing').length > 0);
					// }
				}, 10)
			}

			me._trHoverTimer(e);

			// if (me._trHoverTimer) {
			// 	clearTimeout(me._trHoverTimer);
			// }
			// me._trHoverTimer = setTimeout(function () {
			// 	// var $tr = $(e.currentTarget), index = $tr.attr('data-index');

			// 	// var $trs = me.$('.main .tr[data-index=' + index + ']');
			// 	// me.$('.main .tr').removeClass('tr-hover');
			// 	// $trs.addClass('tr-hover');

			// 	// if (me.$('.td-edit').length > 0) {
			// 	// 	me.$('.main .tr').each(function (index, item) {
			// 	// 		var $item = $(item);
			// 	// 		$item.toggleClass('tr-edit', $item.find('.editing').length > 0);
			// 	// 	});
			// 	// 	$trs.toggleClass('tr-edit', !$trs.hasClass('tr-disabled') && ($trs.find('.td-edit').length > 0 && !$trs.find('.td-edit-close').length) || $trs.find('.editing').length > 0);
			// 	// }
			// }, 10)
			// me.trigger('tr.enter', me.getRowData($tr), $tr, $(e.target), $trs);
		},

		_onTdHover: function (e) {
			var me = this;
            if (!me.options.allColumns) {return;}
			me.trigger('td.hover', e, function () {
				var $target = $(e.target);
				var obj = $target.data();
				if(!me.options) return {
					field: {},
					data: {}
				};
				var rowData = me.getDataByKeyVal('_id', obj.id);
				return {
					field: _.findWhere(me.options.allColumns, {
						data: obj.fieldname
					}),
					data: rowData
				}
			});
		},

		_onTdLeave(e) {
			this.trigger('td.leave', e);
		},

		getDataByKeyVal: function (key, val) {
			if (!this.curData) return null;
			return util.getDataByKey(val, this.curData.data || [], key)
		},

		/**
		 * @desc 点击行
		 */
		_trClickHandle: function (e) {
			const $target = $(e.target);
			const {$tr, $trs, rowData} = this._getTrClickHandleParam(e);
			this._clearOverlay();
			if (this._termBatch) {
				this._termBatch._term && this._termBatch._term.hide();
				this._termBatch.filter && this._termBatch.filter.hide();
				this._termBatch._setColumn && this._termBatch._setColumn.hide();
			}

			const name = $target.data('fieldname') || $target.closest('.tb-cell').data('fieldname');
			const column = name && _.findWhere(this.getAllColumns(), {
				data: name
			});

			if (this.trClickBeforeHandle && this.trClickBeforeHandle({column, $target, $tr, rowData}) === false) {
				return false;
			}

			if (column && column.stopClickPropagation) {
				this.trigger('trclick', rowData, $tr, $target, $trs, !!this.__noreallly, {name});
				this.__noreallly = null;
				e.stopPropagation();
				return;
			}

			if(!this.options?.lineCheck) this.$('.tr').removeClass('tr-selected');
			if ($('.td-edit', $trs).length == 0) { // 编辑状态不选中
				$trs.addClass('tr-selected');
			}
			this.trigger('trclick', rowData, $tr, $(e.target), $trs, !!this.__noreallly, {name});

			if(this.options) {
				if (this.options.single || this.options.lineCheck) {
					let checkBox = $('.checkbox-item', $trs);
					if(!this.options.noCancelChecked || !checkBox.hasClass('tb-checkbox-selected')){
						checkBox.trigger('click');
					}
				}
			}

			this.__noreallly = null;
			e.stopPropagation();

		},

		_getTrClickHandleParam: function (e) {
			var $tr = $(e.currentTarget),
				index = $tr.attr('data-index'),
				$trs = this.$('.tr[data-index=' + index + ']');
			var rowData = this.getRowData($tr);
			return {
				$tr,
				$trs,
				rowData
			}
		},

		_trMouseDownHandle: function(e) {
			this.__noreallly = null;
			this.__clientX = e.clientX;
		},

		_trMouseUpHandle: function(e) {
			if(this.__clientX && Math.abs(e.clientX - this.__clientX) > 10) {
				this.__noreallly = true;
			} else {
				this.__noreallly = null;
			}
		},

		/**
		 * @desc 清除表格上面的覆盖物
		 */
		_clearOverlay: function () {
			if (this._edit) {
				this._edit.destroy();
				this._imgPanel && this._imgPanel.destroy();
				this._attachPanel && this._attachPanel.destroy();
				this._attachPanel = this._imgPanel = this._edit = null;
			}
		},

		/**
		 * @desc 取消选择
		 */
		_clearChecked: function () {
			var me = this;
			me.$('.checkbox-item').removeClass('tb-checkbox-half-selected tb-checkbox-selected');
			me.$('.tr').removeClass('tr-selected');
			me.clearRemberData();
		},

		clearTermChecked() {
			this.$('>.dt-batchoperate-wrapper').find('.dt-close-batch').click()
		},

		/**
		 * @desc 判断是否达到最大选中条数
		 * 只对记录数据有效
		 * @param {Jquery} 当前复选框
		 */
		_isCheckedMaxNum: function ($check) {
			var me = this,
				opts = me.options,
				checked = opts.checked,
				maxNum = 0,
				remberNum = 0;
			if (checked && checked.maxNum) {
				maxNum = checked.maxNum;
				remberNum = me._remberData.length;
				if (!$check.hasClass('tb-checkbox-selected')) {
					if ($check.hasClass('j-all-checkbox')) {
						me.$('.main .checkbox-item').each(function (index, item) {
							if (!$(item).hasClass('checkbox-item-disabled') && !$(item).hasClass('tb-checkbox-selected')) {
								remberNum += 1;
							}
						});
					} else {
						remberNum += 1;
					}
					return remberNum > maxNum;
				}
				return false;
			}
			return false;
		},

		/**
		 * @desc 单选时是否允许选中
		 * 有禁用的数据
		 * 并且默认选中的中的数据是禁用的
		 * 单选转态下 不允许在选择
		 */
		_isArrowCheck: function () {
			var me = this,
				opts = me.options,
				dData = [],
				rData = [];
			if (opts.single && opts.disabledcfg && opts.disabledcfg.data && opts.disabledcfg.data.length>0 && opts.checked) {
				dData = opts.disabledcfg.data;
				rData = opts.checked.data;
				return !_.some(dData, function (item) {
					var o = {};
					o[opts.checked.idKey] = item[opts.checked.idKey] || '';
					return _.findWhere(rData, o);
				});
			}
			return true;
		},

		/**
		 * @desc 点击复选框事件
		 *
		 * TODO 优化逻辑
		 */
		_onChecked: function (e, obj) {
			if(this.__forbiddenChecked) return;

			var me = this,
				opts = me.options,
				$column = $(e.currentTarget),
				$target = $('.checkbox-item', $column),
				$tr = $column.closest('.tr'),
				index = $tr.attr('data-index'),
				$trs = me.$('.tr[data-index=' + index + ']'),
				cls = 'tb-checkbox-selected',
				checkedLen = 0,
				disabledChecked = 0,                         // 默认禁用且勾选行数量
				$allChecks = me.$('.main .checkbox-item');

			var isSkipDisabled = obj && obj.isSkipDisabled;
			if (!isSkipDisabled && $target.hasClass('checkbox-item-disabled')) {
                this.trigger('checkbox.disabled');
				return false;
			}
			if (!isSkipDisabled && !me._isArrowCheck()) {
				me.trigger('notAllowCheck')
				return false;
			}
			if (me._isCheckedMaxNum($target)) {
				util.remind(3, $t("最大允许选中") + opts.checked.maxNum + $t("条数据"));
				return false;
			}

			if (me.options.checkIntercept) {
				me.options.checkIntercept($target, function(result) {
					result && doing();
				}, !$target.hasClass(cls))
			} else {
				doing();
			}

			function doing() {
				var isAll = $target.hasClass('j-all-checkbox');
				if (opts.single && !($target.hasClass(cls) && opts.arrowResetSingle)) {
					me.clearRemberData();
					$allChecks.removeClass(cls);
					me.$('.tr').removeClass('tr-selected');
				}

				$target.toggleClass(cls);
				$trs.toggleClass('tr-selected', $target.hasClass(cls)); // 选中行

				me.options.checkChangeAfterHandle?.($target, $target.hasClass(cls), index, opts.single, isAll);

				$allChecks.each(function (index, item) {
					$(item).hasClass('tb-checkbox-selected checkbox-item-disabled') && disabledChecked++
				});

				var $allCheckBox = isAll ? $target : me.$('.j-all-checkbox');
				if (isAll) { // 全选按钮
					let $ttrs = me.$('.tr');
					if(!$allChecks.length && !$ttrs.length) return;

					$allChecks.each(function (index, item) {
						if (!$(item).hasClass('checkbox-item-disabled')) { // 禁用选项不允许操作
							$(item).toggleClass(cls, $target.hasClass(cls));
						} else {
							$(item).hasClass('tb-checkbox-selected')
						}
					});
					$ttrs.toggleClass('tr-selected', $target.hasClass(cls)); // 选中行
				}

				var disableCheckboxLen = me.$('.main .checkbox-item-disabled').length;
				checkedLen = me.$('.main .tb-checkbox-selected').length - disabledChecked;

				$allCheckBox.toggleClass('tb-checkbox-half-selected', checkedLen + disableCheckboxLen > 0 && $allChecks.length != checkedLen + disableCheckboxLen);
				$allCheckBox.toggleClass(cls, $allChecks.length == checkedLen + disableCheckboxLen);
				me._setRemberData($target);
				if (opts.checked) { // 记录数据
					var checkData = me.getRemberData();
					checkedLen = checkData ? checkData.length : checkedLen;
				}
				me._termBatch && me._termBatch.toggleBatchBtn(checkedLen);
				me.trigger('checkbox.click', $target.hasClass(cls), $target, checkedLen, me.options.single, isAll, index);
				me._termBatch && me._termBatch.filter && me._termBatch.filter.hide(); // 隐藏筛选面板
			}
			e.stopPropagation();
		},

		//批量简单设置数据的选中/半选中/不选中，不触发任何事件
		//此方法有一定的局限性，不支持opts.checked为true的情况
		//pos 行号 status: checked选中  halfChecked半选中  unChecked不选中
		//obj {0: true, 1:false}
		simplySetCheckStatus(pos, status) {
			let $trs = this.findTrs(pos);
			let $checkBox = $trs.find('.checkbox-item');
			if(pos === '') {
				this.$('.j-all-checkbox').addClass('tb-checkbox-selected').removeClass('tb-checkbox-half-selected');
				return;
			}
			if(status === 'checked') {
				$trs.addClass('tr-selected');
				$checkBox.addClass('tb-checkbox-selected').removeClass('tb-checkbox-half-selected');
				return;
			}
			if(status === 'halfChecked') {
				$trs.removeClass('tr-selected');
				$checkBox.addClass('tb-checkbox-half-selected').removeClass('tb-checkbox-selected');
				return;
			}
			if(status === 'unChecked') {
				$trs.removeClass('tr-selected');
				$checkBox.removeClass('tb-checkbox-half-selected tb-checkbox-selected');
			}
		},

		//清除半选状态
		cleanHalfChecked(pos, $checkItem) {
			let cls = 'tb-checkbox-half-selected';
			if($checkItem) {
				$checkItem.removeClass(cls);
				return;
			}
			if(pos === void 0) {
				this.$('.j-all-checkbox').removeClass(cls);
			} else {
				this.findTrs(pos, false, true).find('.checkbox-item').removeClass(cls);
			}
		},

		//根据传入的数据id触发对数据的check
		setCheckStatusByIds: function(ids, isClean) {
			var $b = this.$(`.dt-main .${this.options.forceTrWrap ? 'tb' : 'fix-start-b'} tbody`);
			_.isString(ids) && (ids = [ids]);
			var checkData = this.options.checked ? this.getRemberData() : this.getCheckedData();
			_.each(ids, function(id) {
				var tmp = _.findWhere(checkData, {_id: id});
				if(isClean) {
					tmp = !!tmp;
				} else {
					tmp = !tmp;
				}
				tmp && $b.find('.tr[data-id="' + id +'"]').find('.column-checkbox').trigger('click');
			})
		},

		setCheckStatusByPos: function(pos, flag) {
			var $check = this.findTrs(pos, this.options.forceTrWrap, true).find('.column-checkbox .checkbox-item');
			$check.each(function() {
				var isSelect = $(this).hasClass('tb-checkbox-selected');
				((flag && isSelect) || (!flag && !isSelect)) && $(this).trigger('click', {isSkipDisabled: true});
			})
		},

		_toggleCheckDatas: function(ids, datas) {
			_.isString(ids) && (ids = [ids]);
			var isChecked = this.options.checked;
			var checkData = isChecked ? this.getRemberData() : this.getCheckedData();
			var tids = [];
			var elist = [];
			_.each(ids, id => {
				_.findWhere(checkData, {_id: id}) || elist.push(_.extend({_id: id}, _.findWhere(datas, {_id: id})));
			})
			_.each(checkData, item => {
				if(_.contains(ids, item._id)) return;
				tids.push(item._id);

			})
			if(tids.length) {
				var $b = this.$(`.dt-main .${this.options.forceTrWrap ? 'tb-b' : 'fix-start-b'} tbody`);
				_.each(tids, (id) => {
					var $tr = $b.find('.tr[data-id="' + id +'"]').find('.column-checkbox');
					if($tr.length) $tr.click();
					var tmp = _.findWhere(checkData, {_id: id});
					tmp && checkData.splice(_.indexOf(checkData, tmp), 1);
				})
			}
			[].push.apply(checkData, elist);
			this._termBatch.setCheckNum(checkData.length);
		},

		/**
		 * @description 设置行的复选框禁用状态
		 * @param datas 要设置的行数据
		 */
		toggleDisabledCheckColumn(datas, flag) {
			let $wrapper = this.options.forceTrWrap ? this.$el : this.$el.find('.fix-start-b');

			this.curData && this.curData.data && _.each(datas, item => {
				let index = _.indexOf(this.curData.data, item);
				if(index > -1) {
					$wrapper.find(`.tr[data-index="${index}"]`).find('.checkbox-item')[flag ? 'addClass' : 'removeClass']('checkbox-item-disabled');
				}
			})
		},

		/**
		 * @description 隐藏显示复选框列
		 */
		toggleCheckColumn(flag) {
			this.__forbiddenChecked = !!flag;
			this.$el[flag ? 'addClass' : 'removeClass']('crm-table__hideCheck');
		},

		/**
		 * @description 设置某些行checkbox的显示隐藏
		 */
		toggleCheckBoxByPos(pos, status) {
			this.findTrs(pos, false, true).find('.column-checkbox')[!status ? 'addClass' : 'removClass']('crm-table-checkbox-hide');
		},

		/**
		 * @description  记录选中的数据
		 * 全选按钮记录当前页面所有数据
		 * 和开启记忆选择项有关
		 */
		_setRemberData: function ($checkbox) {
			var me = this,
				data = [];
			if (!me.options.checked) {
				return;
			}
			if (me.options.single) {
				me.clearRemberData();
			}
			if ($checkbox.hasClass('j-all-checkbox')) {
				me.$('.main .checkbox-item').each(function (index, item) {
					if (!$(item).hasClass('checkbox-item-disabled')) {
						data.push(me.getRowData($(item).closest('.tr')));
					}
				});
			} else {
				data.push(me.getRowData($checkbox.closest('.tr')));
			}
			if ($checkbox.hasClass('tb-checkbox-selected')) {
				me.addRemberData(data);
			} else {
				me.reduceRemberData(data);
			}
		},

		/**
		 * @desc 普通排序
		 * 排序规则 sortField：字段名称, sortType 列的orderValues
		 * 设置默认排序
		 */
		_onSortHandle: function (e) {
			var opts = this.options,
				$target = $(e.currentTarget),
				type = $target.find('.ico-sort').attr('data-sortval').split(','),
				field = $target.attr('data-name'),
				curField = this.getParamByKey(opts.sortField),
				curType = curField != field ? '' : this.getParamByKey(opts.sortType),
				suffix = ['asc', 'desc'],
				index = curType == type[0] ? 1 : 0;

			// 禁止点击筛选按钮触犯排序
			if ($(e.target).hasClass('j-th-filter')) {
				return true
			}

			if(curType == 2 && opts.supportDefaultSort) {
				this.removeMultiSortFields();
				this.removeSortField();
				this.setParam({}, true);
				this.trigger('dt.sort', field);
				return;
			}

			$('.th-sort', this.$el).removeClass('th-sort-asc th-sort-desc');
			$target.addClass('th-sort-' + suffix[index]);
			this.removeMultiSortFields([{name: field, orderby: suffix[index]}]);
			this.setParamByKey(opts.sortField, field);
			this.setParamByKey(opts.sortType, type[index]);
			this.setParam({}, true);
			this.trigger('dt.sort', field, type[index]);
		},

		/**
		 * 多字段排序
		 */
		_handleMultiFieldSort: function(sortFields) {
			const mfsKey = this.options.sortFields;
			const orders = sortFields?.map((f) => ({
				name: f.name,
				type: {'asc': 1, 'desc': 2}[f.orderby],
			}));
	
			this.removeSortField();
			this.setParamByKey(mfsKey, orders);
			this.setParam({}, true);
			this.trigger('dt.multi.field.sort', sortFields);
		},

		removeSortField: function() {
			$('.th-sort', this.$el).removeClass('th-sort-asc th-sort-desc');
			this.setParamByKey(this.options.sortField, '');
			this.setParamByKey(this.options.sortType, '');
		},

		removeMultiSortFields: function(resetFields) {
			this.setParamByKey(this.options.sortFields, null);
			this._termBatch?.updateMsFields(resetFields || []);
			this._termBatch?.toggleMsStatus(false);
		},

		/**
		 * @desc 改变列宽度开始
		 */
		_onResizeStart: function (e) {
			var me = this,
				$target = $(e.currentTarget),
				offset = $target.offset(),
				opts = null,
				columnName = '';
			me._startPos = me._endPos = e.clientX;
			me.$resizeTh = $target.closest('th');
			columnName = me.$resizeTh.attr('data-name');
			me.$start = $('<div class="crm-btable-resize-start"></div>');
			me.$cover = $('<div class="crm-btable-resize-cover"></div>');
			me._startWidth = me.$resizeTh.width();
			opts = {
				height: me.$('.main-scroll').height() + 41,
				left: me._startPos,
				top: offset.top,
			};
			me.$start.css(opts);
			me.$cover.css(opts);
			$('body').append(me.$start);
			$('body').append(me.$cover);
			if (columnName) {
				me.$resizeTd = me.$('.td-' + columnName.toLowerCase());
				me.$resizeTh.addClass('th-resize');
				me.$resizeTd.addClass('td-resize');
			}

			me._updateColResizeInfo();

			e.preventDefault();
		},

		/**
		 * @desc 拖拽过程
		 */
		_onResizeDrag: function (e) {
			var me = this;
			me._endPos = e.clientX;
			
			me.$cover.css({
				left: me._endPos
			});

			me._toggleResizeColEffect();

			e.preventDefault();
		},

		//
		// 更改最后一列的宽度
		// pos 变更的宽度
		//

		_changeWidth: function (pos) {
			var me = this;
			var $tb = me.$('.tb');
			var $last = me.$('.tb .j-last-th');
			var mw = me.options.$el.width();
			var tw = $tb.width();
			var lw = $last.width();

			tw += pos;

			if (tw < mw) {
				lw += mw - tw;
				tw = mw + 1;
			} else {
				if (lw > me.options.lastThTrueWidth) {
					mw = lw;
					lw = Math.max(me.options.lastThTrueWidth, lw - pos);
					tw = tw - (mw - lw) + 1;
				}
			}

			$last.css('width', lw + 1);
			me.options.tableWidth = tw - 1;
			$tb.css('width', me.options.tableWidth);
		},

		//
		// 固定列变化
		//
		_changeFixedWidth: function (pos) {
			var me = this;
			var fw = me.options.fixedWidth;
			var mw = me.options.$el.width();
			var $last = me.$('.tb .j-last-th');
			var lw = $last.width();
			var tlw = lw;

			me.options.fixedWidth += pos;
			me.options.tableWidth += pos;

			me.$('.fix-start-b').css('width', me.options.fixedWidth);
			me.$('.j-sfixed-th').css('width', me.options.fixedWidth);

			if (me.options.tableWidth < mw) {
				$last.css('width', lw + mw - me.options.tableWidth);
				me.options.tableWidth = mw;
			} else {

				if (lw > me.options.lastThTrueWidth) {
					lw = Math.max(me.options.lastThTrueWidth, lw - pos);
					me.options.tableWidth = me.options.tableWidth - (tlw - lw);
					$last.css('width', lw + 1);
				}
			}

			me.$('.tb').css('width', me.options.tableWidth);
		},

		/**
		 * @desc 改变列宽度开始
		 */
		_onResizeStop: function (e) {
			var me = this,
				cw = me.options.colMinWidth, // 列最小宽度
				tw = me.options.tableWidth, // 表格最小宽度
				pos = me._endPos - me._startPos, // 变化的距离
				field = me.$resizeTh.attr('data-name'),
				column = null,
				$th = me.$('.th-' + field.toLowerCase()),
				thw = Math.max(me._startWidth + pos, 80);  // 移动后的宽度

			if (!me._canResizeColWidth()) {
				me._cleanColResizeInfo();
				return;
			}

			if ($th.closest('.fix-start-b').length > 0) {  // 固定列
				me._changeFixedWidth(thw - me._startWidth);
				column = _.findWhere(me.options.fixedColumns, {
					data: field
				});
			} else {
				me._changeWidth(thw - me._startWidth);
				column = _.findWhere(me.options.columns, {
					data: field
				});
			}

			$th.attr('width', thw);

			if (column) { // 记录宽度
				column.width = thw;
				me._cleanMeasureWidth(column);
				me.updateTermBatchColumnStatus(column);
				me.trigger('tb.resize.width', {
					column: column,
					width: thw
				});
			}

			me._cleanColResizeInfo();
			me.upDataScroll();
		},

		_toggleResizeColEffect() {
			const $cover = this.$cover;

			if (this._canResizeColWidth()) {
				if ($cover.hasClass('resize-disabled')) {
					$cover.removeClass('resize-disabled');
				}
			} else {
				if (!$cover.hasClass('resize-disabled')) {
					$cover.addClass('resize-disabled');
				}
			}
		},

		_cleanColResizeInfo() {
			var me = this;
			me.$start && me.$start.remove();
			me.$cover && me.$cover.remove();
			if (me.$resizeTd.length > 0) {
				me.$resizeTh.removeClass('th-resize');
				me.$resizeTd.removeClass('td-resize');
			}
			
			me.$start = me.$cover = null;
			me.__resizeInfo =  null;
		},

		/**
		 * 记录拖拽列时一些信息
		 */
		_updateColResizeInfo() {
			if (!this.$resizeTh) {
				this.__resizeInfo =  null;
				return;
			}

			const field = this.$resizeTh.attr('data-name');
			const $th = this.$('.th-' + field.toLowerCase());
			const $fixedStartContainer = $th.closest('.fix-start-b');
			const $header = $fixedStartContainer.parent();
			const $fixedEndContainer = $header.find('.fix-end-b');

			this.__resizeInfo = {
				isFixedColumn: $fixedStartContainer.length > 0,
				fixedStartWidth: $fixedStartContainer.width() || 0,
				fixedEndWidth: $fixedEndContainer.width() || 0,
				headerWidth: $header.width() || 0,
			};
		},

		/**
		 * 获取拖拽列信息
		 */
		_getColResizeInfo() {
			return this.__resizeInfo;
		},

		/**
		 * 列宽度是否可以增大
		 */
		_canResizeColWidth() {
			const resizeInfo = this._getColResizeInfo();

			// 非对固定列不做限制
			if (!resizeInfo || !resizeInfo.isFixedColumn) {
				return true;
			}

			// 变化的距离(拖拽)
			return this._isValidColSize({
				distance: this._endPos - this._startPos, // 变化的距离(拖拽)
				fixedStartWidth: resizeInfo.fixedStartWidth,
				fixedEndWidth: resizeInfo.fixedEndWidth,
				headerWidth: resizeInfo.headerWidth,
			});
		},

		_isValidColSize(size) {
			const {
				minContentWidth
			} = this.options;

			if (!size || minContentWidth <= 0) {
				return true;
			}

			const nextMainContentWidth = size.headerWidth - (
				size.fixedStartWidth + size.distance + size.fixedEndWidth
			);

			if (_.isNaN(nextMainContentWidth)) {
				return true;
			}

			return nextMainContentWidth > minContentWidth;
		},

		/**
		 * @desc 增加一个下拉选项
		 * {
		 *    $target:  me.$('.term-item')目标
		 *    pos:     'after'
		 *    label:    '标题',
		 *    className: '',
		 *    showLine: false,
              options: []
		 * }
		 */
		addSelect: function (config) {
			var me = this;
			config = _.extend({
				$target: me.$('.last-target-item'),
				pos: 'after',
				showLine: false,
				size: '1',
				className: '',
				label: '',
				width: 135
			}, config || {});
			var $wrap = $(selectTpl(config));
			if (config.pos == 'after') {
				config.$target.after($wrap);
			} else {
				config.$target.before($wrap);
			}
			var select = new Select(_.extend({
				$wrap: $('.item-con', $wrap),
				width: 180,
				options: [],
				zIndex: me.get('zIndex') * 1 + 10,
				defaultValue: ''
			}, config));
			me._widgets = me._widgets || [];
			me._widgets.push(select);
			return select;
		},

		createBatchtermElement(config) {
			var me = this;
			config = _.extend({
				$target: me.$('.last-target-item'),
				pos: 'after',
				showLine: false,
				size: '1',
				className: 'batchterm-slot',
				label: '',
				width: 0,
			}, config || {});
			var $wrap = $(selectTpl(config));
			if (config.pos == 'after') {
				config.$target.after($wrap);
			} else {
				config.$target.before($wrap);
			}

			return $wrap.find('.item-con')[0];
		},

		/**
		 * @desc 调整指定列的宽度
		 * @param cn 列名称
		 * @param w 宽度
		 */
		setWidth: function (cn, w) {
			var me = this,
				opts = me.options,
				column = null,
				cw = 0;
			column = _.findWhere(opts.allColumns, {
				data: cn
			}); // 列属性

			if (!column || column.lastFixed || (!_.isUndefined(column.width) && column.width == w)) {
				return;
			}
			cn = cn.toLowerCase();
			cw = me.$('.th-' + cn).width() + 1; // 列的实际宽度
			cw = cw < 0 ? 0 : cw;
			if (column.fixed) {
				var fw = me.$('.fix-start-b').width() - cw - 20 + w + 20; // 固定列的总宽度
				me.$('.fix-start-b').width(fw);
				me.$('.header').css('padding-left', fw);
				me.$('.main-con').css('padding-left', fw);

				//me.$('.scroll-x').css('left', fw);
				me.$('.th-' + cn).attr('width', w);
				me.$('.td-' + cn).attr('width', w);
				column._defaultWidth = column._defaultWidth ? column._defaultWidth : (column.width || opts.fixMinWidth);
			} else {
				me.$('.th-' + cn).attr('width', w);
				me.$('.tb').width(me.$('.tb').width() - cw + w);
				column._defaultWidth = column._defaultWidth ? column._defaultWidth : (column.width || opts.colMinWidth);
			}
			column.width = w;
			// me._mainScrollBarX.updata();
		},

		setWidth2: function (cn, w) {
			var me = this,
				opts = me.options,
				column = null,
				cw = 0;
			column = _.findWhere(opts.allColumns, {
				data: cn
			}); // 列属性

			if (!column || column.lastFixed || (!_.isUndefined(column.width) && column.width == w)) {
				return;
			}
			cn = cn.toLowerCase();
			cw = me.$('.th-' + cn).width() + 1; // 列的实际宽度
			cw = cw < 0 ? 0 : cw;
			if (column.fixed) {
				var fw = 0; 								// 固定列的总宽度
				if(w > 1){									// 显示列
					fw = me.$('.fix-start-b').width() + w ; // 固定列的总宽度
					me.$('.th-' + cn).show();
					me.$('.td-' + cn).show();
				}else{										// 隐藏列
					fw = me.$('.fix-start-b').width() - cw; // 固定列的总宽度
					me.$('.th-' + cn).hide();
					me.$('.td-' + cn).hide();
				}
				me.$('.fix-start-b').width(fw);
				me.$('.j-sfixed-th').width(fw);
				column._defaultWidth = column._defaultWidth ? column._defaultWidth : (column.width || opts.fixMinWidth);
			} else {
				me.$('.th-' + cn).attr('width', w);
				me.$('.tb').width(me.$('.tb').width() - cw + w);
				column._defaultWidth = column._defaultWidth ? column._defaultWidth : (column.width || opts.colMinWidth);
			}
			column.width = w;

			me.$('.td-' + cn)[w > 1 ? 'removeClass' : 'addClass']('td-width-0');
			// me._mainScrollBarX.updata();
		},

		/**
		 * @desc  隐藏指定列
		 * @param cn 列名称
		 */
		hideColumn: function (cn) {
			var me = this,
				$th = me.$('.th-' + cn.toLowerCase()),
				cw = $th.width() + 1;

			if (cw <= 1) {
				return;
			}

			if ($th.hasClass('j-last-th')) {
				var column = _.findWhere(me.options.allColumns, {
					data: cn
				});
				cw = column.lastThTrueWidth || cw;
			}

			me['_hide_' + cn + '_width'] = cw;
			me.setWidth(cn, 0);
		},


		/**
		 * @desc  隐藏指定列
		 * @param cn 列名称
		 */
		hideColumn_new: function (cn) {
			var me = this,
				$th = me.$('.th-' + cn.toLowerCase()),
				cw = $th.width() + 1;

			if (cw <= 1) {
				return;
			}

			if ($th.hasClass('j-last-th')) {
				var column = _.findWhere(me.options.allColumns, {
					data: cn
				});
				cw = column.lastThTrueWidth || cw;
			}

			me['_hide_' + cn + '_width'] = cw;
			me.setWidth2(cn, 0);
		},

		/**
		 * @desc 显示列
		 * @param cn 列名称
		 */
		showColumn: function (cn) {
			var me = this,
				w = 0;
			w = me['_hide_' + cn + '_width'] || 0;
			if (w == 0) {
				return;
			}
			me.setWidth(cn, w);
		},
		/**
		 * @desc 显示列
		 * @param cn 列名称
		 */
		showColumn_new: function (cn) {
			var me = this,
				w = 0;
			w = me['_hide_' + cn + '_width'] || 0;
			if (w == 0) {
				return;
			}
			me.setWidth2(cn, w);
		},


		/**
		 * @desc 切换
		 * @param cn 别名称
		 */
		toggleColumn: function (cn) {
			var me = this,
				cw = 0;
			cw = me.$('.th-' + cn.toLowerCase()).width();
			if (cw <= 0) {
				me.showColumn(cn);
			} else {
				me.hideColumn(cn);
			}
		},

		/**
		 * @desc 获取第一列
		 */
		getFirstColumn: function () {
			var me = this,
				opts = me.options,
				fixLen = opts.fixedColumns.length;
			if (opts.showMultiple && fixLen >= 1) {
				return opts.fixedColumns[1];
			}
			if (fixLen > 0) {
				return opts.fixedColumns[0];
			}
			return opts.columns[0];
		},

		/**
		 * @desc 设置表格第一列宽度
		 * 多选不算第一列
		 */
		setFirstWidth: function (w) {
			var me = this,
				column = me.getFirstColumn();
			if (column) {
				me.setWidth(column.data, w);
			}
		},

		/**
		 * @desc 改变表格显示宽度
		 */
		setViewWidth: function (w) {
			var me = this,
				opts = me.options;
			w = w || 240;
			w = w - 20;
			if (opts.showMultiple) {
				w = w - 46 - 1;
			}
			me.setFirstWidth(w);
			me.$el.addClass('view-width');
		},

		/**
		 * @des 设置为默认表格显示宽度
		 */
		setDefaultWidth: function () {
			var me = this,
				column = me.getFirstColumn();
			if (column && column._defaultWidth) {
				me.setFirstWidth(column._defaultWidth);
			}
			me.$el.removeClass('view-width');
		},

		/**
		 * @desc 刷新列表
		 * 检索默认状态选中第一项
		 */
		refresh: function (data) {
			var me = this,
				opts = me.options;
			opts.postData = $.extend(true, {}, me.defaultParam);
			if (opts.searchTerm) {
				var filter = opts.searchTerm.options,
					def = _.find(filter, function (item) {
						return !item.isHide && item.isdef;
					});
				def = def || filter[0];
				me.setParamByKey(opts.searchTerm.type, def ? def.id : '');
				me._termBatch && me._termBatch.refresh();
			}
			me.filterStatus = false;
			me.setParam(data || {}, true, true);
		},

		/**
		 * @desc 根据index更新行数据
		 * @param data
		 * @param index
		 */
		updateRowByIndex: function (data, index) {
			this.curData.data.splice(index, 1, data);
			this.doStaticData(this.curData.data)
		},

		/**
		 * @desc 添加 '+'
		 * @param val
		 * @param column
		 */
		addPlusSign: function (val, column) {
			var value = CRM.util.moneyToNum(val);
			if (value > 0 && column.extend_info && column.extend_info.show_positive_sign && this.options.isAddPlusSign) {
				val = '+' + CRM.util.toMoney(value);
			}
			return val;
		},

		/**
		 * @desc 校验表格必填项是否填写
		 * @param data
		 * @returns {boolean}
		 */
		validateTable: function (data) {
			var me = this;
			var list = data;
			if (data === undefined) {
				list = me.getCurData();
			}
			if (!list) return true;
			var errorTrs = {};
			_.each(list, function (data, pos) {
				var cellError = me.validateTrData(data, pos);
				if (cellError) {
					errorTrs[pos] = cellError;
				}
			});

			if (_.isEmpty(errorTrs)) return true;

			this.toggleValidError(errorTrs);
			return false;
		},

		//验证一行数据数据是否正确
		validateTrData: function (data, pos) {
			var rules = data.__rules;
			var columns = this.options.columns;
			var errorCells = [];
			_.each(columns, function (field) {
				var k = field.api_name;
				var vv = data[k];
				if (field.is_required || (rules && _.contains(rules.required_field, k))) { //验证必填
					(vv === void 0 || vv === '' || vv === null || (_.isArray(vv) && vv.length === 0)) && errorCells.push(k);
				}
			});

			return errorCells.length && errorCells;
		},

		setTitle: function (title) {
			$('.dt-tit ', this.$el).html(title);
		},

		/**
		 * @desc 获取某一列信息
		 * @param field
		 * @returns {number | never | bigint | T | T}
		 */
		getColumnByField: function (field) {
			return _.find(this.options.allColumns, function (item) {
				return item.data === field
			})
		},

		getDescribeByField: function (field) {
			return this.options.describe[field]
		},

		removeTrError: function () {
			this.$('.main  tbody .tr').removeClass('tr-error');
			this.triggerTrError();
		},

		clearError: function () {
			this.removeTrError();
			this.hideErrorMsg();
			this.$('.main  tbody .tr td').removeClass('td-status-error');
		},

		getTableAllData: function () {
			return (this.curData && this.curData.data) || [];
		},

		/**
		 * @desc 获取预置字段；
		 * @returns {*}
		 */
		getPresetFields: function () {
			var des = this.options.describe;
			var res = [];
			 _.map(des, function (item) {
				if (item.define_type !== 'custom' && item.api_name) {
					res.push(item.api_name) ;
				}
			});
			return res;
		},

		// 设置表格默认选中数据；
		setCheckedData:function(data){
			if(this.options.checked) this.options.checked.data = this._remberData = _.isArray(data) ? data : [data];
		},

		// 清空表格数据;
		clean:function(){
			this.doStaticData([]);
			this.clearRemberData();
			let allChecked = this.$el.find('.j-all-checkbox');
			if(allChecked.length) allChecked.removeClass('tb-checkbox-selected tb-checkbox-half-selected');
		},

		/**
		 * @desc 销毁页面中的元素
		 */
		destroy: function () {
			console.log('destroy....table')
			this.clearError();
			this.__showInsertIcon();
			this.tableStatus = 2;
			this.__destroyed = true;
			this.curData = null;
			Table.clearRule();
			this.filterStatus = false;
			this._getXhr && this._getXhr.abort();
			this._countryAjax && this._countryAjax.abort();
			// this._mainScrollBarX && this._mainScrollBarX.destroy();
			// this._mainScrollBarY && this._mainScrollBarY.destroy();
			this._tbLoopUpDetail && this._tbLoopUpDetail.destroy();
			this._tbLookUpDetailPath = '';
			this._search && this._search.destroy();
			this._pagination && this._pagination.destroy();
			this._edit && this._edit.destroy();
			this._termBatch && this._termBatch.destroy();
			this._moreColumns && this._moreColumns.destroy();
			this._imgPreview && this._imgPreview.destroy();
			this._operate && this._operate.destroy();
			this.tableSetting && (this.tableSetting.destroy(), this.tableSetting = null);
			_.each(this._widgets || [], function (item) {
				item.destroy && item.destroy();
			});
			clearTimeout(this._initCompleteTimer);
			this._initCompleteTimer = null;
			this._headertimer = null;
			this._widgets = null;
			this.options = null;
			this.__$operateList = null;
			$('.j-tr-operate-list-wrap').remove();
			$(window).off('resize.dt' + this.cid);
			CRM.Widget.prototype.destroy.apply(this, arguments);
		}
	}, imgEdit, attachEdit, measure), _.clone(filterRule));


	Table.helper = helper;
	Table.Edit = Edit;
	Table.imgEdit = imgEdit;
	Table.attachEdit = attachEdit;
	Table.makeTreeTable = makeTreeTable;
	Table.Condition = require('./condition/condition');
	Table.Conditions = require('./condition/conditions');
	Table.OutFilter = require('./termbatch/outfilter/outfilter');
	
	module.exports = Table;
});
