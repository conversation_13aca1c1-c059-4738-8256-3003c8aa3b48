/**
 * @desc
 * 下拉控件
 * options [{
       name:  '',
       value: '',
       isTitle: false        // 是否为标题
       isCustom: false       // 是否自定义值 选中此项弹出dialog 自己输入值 仅单选支持
       customValue: ''       // 自定义默认值
       customRequired: false // 自定义是否必填
       optionTip: ''         // 选项说明title
   }]
 * <AUTHOR>
 * TODO 模板改为可传 模板改为一个
 * TODO 多选支持筛选
 */

   define(function (require, exports, module) {

    var util      = CRM.util,
        Panel     = require('crm-assets/widget/panel/panel'),
        searchTpl = require('./template/search-html'),
        tpl       = require('./template/tpl-html'),
		selectTpl = require('./template/select-html'),
        listTpl   = require('./template/list-html');

    var Select2 = CRM.Widget.extend({

        // 默认样式
        _cls: 'crm-w-select',

        options: function() {
            return {
                $wrap:       null,
                width:       '',  // 面板宽度
                className:   '',
                size:        '2', // 1: 28px 2: 32px;
                placeHolder: '',

                input:       true,  // 输入筛选 TODO 多选支持

                appendBody:  true,
                space:       0,
                zIndex:      10000,
                scrollCount: 200,    // 选项大于200时 滚动加载剩余的项
                multiple:    'single',
                onlyoneline: false,  // 多选时仅展示一行
                allCheck:    false,   // 是否支持全选
                options:     null,
                defaultVal:  '',

                disabled:    false,
				stopPropagation: false,
                clearable: false //可清除
            }
        },

        // 滚动加载数据的位置
        // 性能优化
        __posNum: 0,

        // 初始化
        // 记录默认值
        // 设置当行样式
        // 设置头部内容
        setup: function(options) {
            //统一处理下传过来的defaultVal
            this._filterDefaultVal();
            const _options = this.get('options')
            var defaultVal = _options && _options[0] && !this.get('placeHolder') ? _options[0].value : '';
            this.$el.addClass(this._cls);
            this.$el.addClass(this._cls + '-' + this.get('size'));
            this.$el.toggleClass('disabled', !!this.get('disabled'));
            this.uid = _.uniqueId(this._cls + '_');
			this.$el.html(selectTpl({
				input: this.get('input'),
                clearable: this.get('clearable')
			}));
            this.get('$wrap').append(this.$el);
            this.$('.j-select-input').attr('disabled', this.get('disabled'));
            this.$('.j-select-input').attr('placeHolder', this.get('placeHolder'));
			if (this.get('multiple') == 'multiple') {
				this.set('defaultVal', this.get('defaultVal') || this.get('defaultValue') || []);
			}
            this.setValue(this.get('defaultVal') || this.get('defaultValue') || defaultVal);
            this._initPanel();
            this._setOneLineStyle();
            this._initEvents();

			this.on('change', _.bind(this._debounceChange, this));

            this.$el.on('click', '.clear-icon', () => {
                if(!this._valIsNull()) {
                    this.clear(true);
                }
                return false;
            })

        },

        _valIsNull() {
            var val = this.val;
            if(val === '' || val === null || val === void 0) return true;
            if(_.isArray(val)) {
                return _.find(val, a => {
                    return !(a === '' || a === null || a === void 0)
                }) === void 0;
            }
        },

        _debounceChange: function(value) {
            var me = this;
            if(!this._panelshow) {
                var vv = value || this.getValue();
                var item;
                if(_.isArray(vv)) {
                    item = _.map(vv, function(v) {
                        return me._getItemByVal(v) || {};
                    })
                } else {
                    item = this._getItemByVal(vv)
                }
                this.trigger('debounce.change', vv, item);
			}
        },

        /**
         * @desc 过滤掉那些在options里没有对应值得value, 比如禁用掉的选项
         */
        _filterDefaultVal: function() {
            var v1 = this.get('defaultVal');
            var value = v1 === false ? v1 : this.get('defaultValue');
            var options = this.get('options');
            if(!value && value !== false) return;

            var df = [];
            if(_.isArray(value)) {
                df = _.filter(value, function(code) {
                    return !!_.find(options, function(item) {
                        return item.value == code;
                    })
                })
            } else {
                var obj = _.find(options, function(item) {
                    return item.value == value;
                })
                obj && (df = [value]);
            }
            this.set('defaultVal', df);
        },

        /**
         * @desc 支持筛选
         */
        _initEvents: function() {
            var me = this;
            util.fixInput('.j-select-input', function() {
                me._panelshow || me._panel.show();
                me._search();
            }, me.$el);
        },

        /**
         * @desc 初始化面板
         */
        _initPanel: function() {
            var me = this;
            me._panel = new Panel({
                $target:   me.$('.select-tit'),
                $parent:   me.get('appendBody') ? $('body') : me.$el,
				        zIndex: me.get('appendBody') ? 10000 : CRM.util.getzIndex(me.get('zIndex')),
					      // zIndex: FxUI.Utils.getPopupZIndex(CRM.util.getzIndex(me.get('zIndex'))),
                width:     me.get('width') ? me.get('width') : me.$el.width() || 200,
                space:     me.get('space'),
                className: me._cls + (me.get('className') ? ' ' + me.get('className') : ''),
                content:   tpl({
                    allCheck: me.get('allCheck'),
                    options:  me.get('options').slice(me.__posNum, me.get('scrollCount'))
                }),
                registerBody: false,
                stopPropagation: me.get('stopPropagation')
            });

            $('body').on('click.wselect' + me.uid, function(e) {
                var $target = $(e.target);
                var cls     = me._panel._cls + '.' + me._panel.options.className.replace(' ', '.');
                if (me._panel._status == 'show' &&
                    $target.closest('.' + cls).length == 0  &&
                    !$.contains(me._panel.get('$target')[0], $target[0])) {
                        me.hide();
                }
            });

            me._panel.on('show', function() {
                me._setSelected();
                me._setCheckStatus();
                me.$('.tit-con input').focus();
                if (me.get('multiple') != 'single' && me.get('onlyoneline')) {
                    me.$el.removeClass('one-line');
                    me.$('.more-btn').hide();
                    me._panel.resetPos();
                }
                me._panelshow = true;
                me.trigger('panel.show', me._panel);
            });
            me._panel.on('hide', function() {
                me.$el.removeClass('select-show');
                if (me.get('input')) {
                    me.setValue(me.val);
                    me.$('.j-select-input').blur();
                    $('.list-menu', me._panel.$el).html($(tpl({
                        allCheck: me.get('allCheck'),
                        options: me.get('options').slice(me.__posNum, me.get('scrollCount'))
                    })).find('.list-menu').html());
                }
                me._setOneLineStyle();
                me.trigger('panel.hide');
                me._panelshow = null;
                me._debounceChange();
            });
            me._panel.on('render', function() {
                var $scroll = $('.crm-scroll', me._panel.$el);
                $scroll.on('scroll', function() {
                    me._doScroll(me._panel.$el, $scroll);
                });
            });
            me._panel.on('itemclick', function($target, e) {
                me.trigger('option.click', $target.data('value'));
                me._onItem($target, e);
            });

            me._panel.on('checkboxclick', function($target, e) {
                me._onChecked($target, e);
            });

            // 其他逻辑处理
            me._panel.$el.on('keypress', '.j-custom-ipt', function(e) {
                if (e.which == 13) {
                    me._handleCustomInput($(e.currentTarget));
                }
            });
            me._panel.$el.on('click', '.j-custom-ipt', function(e) {
                e.stopPropagation();
                return false;
            });
            me._panel.$el.on('blur', '.j-custom-ipt', function(e) {
                me._handleCustomInput($(e.currentTarget));
            });
        },

        getPanelWrap: function() {
            return this._panel.$el;
        },

        getOtherIptVal: function() {

        },

        //
        // 重新设置位置
        // 重新计算pannel宽度
        //
        resize: function() {
            var me = this;
            if (me._panel) {
                me._panel.$el.css('width', me.get('width') ? me.get('width') : me.$el.width() || 200);
                me._panel.resetPos();
            }
        },

		// 单选逻辑
        _handleCustomInput: function($input) {
            var me = this;
            var $item  = $input.closest('li');
            if (!$item.hasClass('selected')) {return false;}
            var val    = $item.data('value');
            var item   = me._getItemByVal(val);
            var inputVal = $.trim($input.val());
            item.customValue = inputVal;
            if (item.customRequired && inputVal == '') {
                $input.addClass('b-g-ipt-error');
                me.hide();
                return;
            }
            if (me.get('multiple') == 'single') {
                me.setValue(item.value);
                me.hide();
                me.trigger('change', val, item, me);
            } else {
                if (me.val.indexOf(val) == -1) {
                    me.val.push(item.value);
                    me.setValue(me.val);
                }
                me._panel && me._panel.resetPos();
                me._setCheckStatus();
                me.trigger('change', this.val, this.get('options'), this);
            }
        },
        /**
         * @desc 搜索选项
         */
        _search: function() {
            var me = this,
                val = $.trim(me.$('input').val()),
                list = [];

            me._setIptWidth(val);

            var reg;
            if(val) {
                try {
                  reg = new RegExp(CRM.util.replaceRegExp(val), 'i');
                  _.each(me.get('options'), function (item) {
                  	if (!item.isTitle && item.name.match(reg)) {
                  		list.push(item);
                  	}
                  });
                } catch (error) {
                  console.error(error)
                }
            } else {
                list = me.get('options')
            }

            $('.list-menu', me._panel.$el).html(searchTpl({
                list:  list,
                val:   val
            }));
            me._setSelected();
            me._panel && me._panel.resetPos();
        },

        //
        // 设置input的宽度
        //
        _setIptWidth: function() {
            var me = this;
            if (!me.$('.tit-con').hasClass('multiple')) {return;}

            var $target = me.$('.j-ipt-target');
            $target.html(me.$('.tit-con input').val());
            var width = Math.max(36, $target.width());
            width = Math.min(me.$('.tit-con').width(), width);
            me.$('.tit-con input').width(width)
        },

        //
        // 设置一行显示样式
        //
        //
        _setOneLineStyle: function() {
            var me = this;
            if (me.get('multiple') == 'single') {return;}
            var pos = me.$('.j-select-input').position();
            var isMore = (pos.top > 0);
            me.$('.more-btn').toggle(isMore);
            me.$el.toggleClass('one-line', me.get('onlyoneline') && isMore);
        },

        /**
         * @desc 性能优化
         * 大数据的dom节点只大家200条
         * 剩余滚动加载
         */
        _doScroll: function($panel, $scroll) {
            var me = this, num = me.get('scrollCount');
            if (me._stimer) {clearTimeout(me._stimer); me._stimer = null;}
            me._stimer = setTimeout(function() {
                var st = $scroll.scrollTop();
                var sh = $scroll[0].scrollHeight;
                var h  = $panel.height();
                var options = me.get('options');
                if (st + h + 100 > sh && options.length > (me.__posNum + 1) * num) {
                    me.__posNum += 1;
                    $('.list-menu', $scroll).append(listTpl({
                        list: options.slice(me.__posNum * num + 1, me.__posNum * num + num)
                    }));
                    me._setSelected();
                }
            }, 60);
        },

        /**
         * @desc 设置选中的项
         */
        _setSelected: function() {
            var me = this;
            $('li', me._panel.$el).removeClass('selected');
            me._setInputStatus($('.j-custom-ipt', me._panel.$el), true);
            _.each(me.val, function(item) {
                var $item = $('li[data-value="' + item +'"]', me._panel.$el);
                $item.addClass('selected');
                if ($item.hasClass('custom-item')) {
                    me._setInputStatus($('.j-custom-ipt', $item), false);
                }
            });
        },

        /**
         * @desc 显示
         */
        show: function() {
            this.$el.addClass('select-show');
            this._panel.show();
            this.trigger('show', this);
            this.resize();
        },

        /**
         * @desc 显示
         */
        hide: function(noValid) {
            if (!noValid && !this.validOther()) {return;}
            this._panel.hide();
            this.trigger('hide', this);
        },

        toggle: function() {
            if (this._panel._status == 'hide') {
                this.show();
            } else {
                this.hide();
            }
        },

        disable: function() {
            this.$el.addClass('disabled');
            this.$('input').attr('disabled', 'disabled');
            this.set('disabled', true);
            this.hide();
        },

        enable: function() {
            this.$el.removeClass('disabled');
            this.$('input').removeAttr('disabled');
            this.set('disabled', false);
            this.hide();
        },

        /**
         * @desc 切换禁用 不禁用
         */
        toggleDisable: function(flag) {
            var me = this;
            me.$el.toggleClass('disabled', flag);
            me.$('input').attr('disabled', flag);
            me.set('disabled', flag);
            me.hide();
        },

        // 兼容老的控件
        setDisable: function(flag) {
            this.toggleDisable(flag);
        },

        // 重置选项值
        resetOptions: function(options, noClear, isDisable, noTrigger) {
            var me = this;
            noClear || this.clear(!noTrigger);
            me.set('options', options);
            me._panel.setCon(tpl({
                allCheck: me.get('allCheck'),
                options: options
            }))
            isDisable ? me.disable() : (isDisable === false && me.enable());
        },

		removeOption: function (v) {
			var a;
			var list = _.filter(this.get('options'), function (item) {
				if (item.value === v) {
					a = item;
                    return false;
				}
				return true;
			})

			this.resetOptions(list, true);

			return a;
            // TODO
        },

		addOption: function (v, isUnShift) {
            var options = this.get('options');
            options[isUnShift ? 'unshift' : 'push'](v);
			this.resetOptions(options, true);
		},

		getOption: function (v) {
			return _.findWhere(this.get('options'), {value: v});
		},

        // 兼容老的控件
        resetData: function(options) {
            this.resetOptions(options);
        },

        //
        // 布尔值修复
        _fixBoolean: function(val) {
            if (_.isString(val)) {
                var t = val.toLowerCase();
                if (t == 'false') {
                    return false;
                }
                if (t == 'true') {
                    return true;
                }
            }
            return val;
        },

        /**
         * @desc  根据val 获取一项
         */
        _getItemByVal: function(val) {
            var me = this, options = me.get('options');
            val = me._fixBoolean(val);
            var item = _.find(options, function(o) {
                var value = me._fixBoolean(o.value);
                if (_.isBoolean(val) || _.isBoolean(value)) {
                    return value === val;
                }
                return value == val;
            });
            return item;
        },

        /**
         * @desc 获取选中项的html片段
         */
        _getSelectedHtml: function() {
            var me = this, options = me.get('options'), html = '';
            if (me.get('multiple') == 'single') {
                var item = me._getItemByVal(me.val[0]);
                if (item) {
                    var name = me.get('input') ? item.name : _.escape(item.name);
                    if (item.isCustom) {
                        name += item.customValue ? '：' + item.customValue : '';
                    }
                    return name;
                }
                return '';
            }
            _.each(me.val, function(item) {  // 多选气泡展示
                item = me._getItemByVal(item);
                if (item) {
                    var name = item.name;
                    if (item.isCustom) {
                        name += item.customValue ? '：' + item.customValue : '';
                    }
                    html += '<div class="bubble">' + _.escape(name) +
                            '<span class="del-item" data-val="' + item.value +
                            '">×</span></div>';
                }
            });
            return html;
        },

        //仅适用于单选
        getSelectedName: function() {
            return this.getNameByVal(this.getValue());
        },

        getNameByVal: function(val) {
            return (_.findWhere(this.get('options'), {value: val}) || {}).name || '';
        },

        /**
         * @desc 获取选中的值
         */
        getValue: function() {
            if (this.get('multiple') == 'single') {
                return this.val ?  _.isUndefined(this.val[0]) ? '' :  this.val[0] : '';
			}

            return _.filter(this.val, function(a) {
                return a !== '';
            });
        },

        //flag 是否触发ui事件
        setFirstValue: function(flag) {
            var options = this.get('options');
            options[0] && this.setValue(options[0].value, flag);
            return options[0];
        },

        /**
         * @desc 设置值
         * @param {{String | Number}} 选项值
         * @param 设置值的是否触发change事件
         */
		setValue: function (val, flag) {
			var me = this;
			var vals = _.pluck(this.get('options'), 'value');

			vals = _.map(vals, function (item) {
				return me._fixBoolean(item);
			});

			this.val = _.isArray(val) ? val : [val];

			if (val != '' && this.get('multiple') == 'single') {
				this.val = _.filter(this.val, function (item) {  // 过滤掉单选中不存在的值
					item = me._fixBoolean(item);
					var temp = _.find(vals, function (v) {
						return v == item;
					});
					return !_.isUndefined(temp);
				});
				if (this.val.length == 0) { return; }
			}

			this._setTitCon();
			if (flag) {
				var item = this.get('options');
				if (this.get('multiple') == 'single') {
					item = this._getItemByVal(val);
				}
				this.trigger('change', val, item, this);
			}
		},

        //
        // 设置头部内容
        //
        _setTitCon: function() {
            var me = this;
            var html = me._getSelectedHtml() + '';
            var selectHtml = html || me.get('placeHolder') || $t("请选择");
            if (me.get('input')) {
                if (me.get('multiple') == 'single') {
                    var item = me._getItemByVal(me.val && me.val[0]);
                    if((item && item.isShowPlaceHolder) || (me.get('placeHolder') && !item)) {
                        me.$('.tit-con input').val('').attr('placeHolder', selectHtml);
                    } else {
                        me.$('.tit-con input').val(selectHtml);
                    }
                } else {   // 多选存在
                    me.$('.tit-con .bubble').remove();
                    if (!html) {
                        me.$('.tit-con input').attr('placeHolder', selectHtml);
                        if (me.$('.tit-con').hasClass('multiple')) {
                            me.$('.tit-con').removeClass('multiple');
                            if (me._panel && me._panel._status == 'show') {
                                me.$('.tit-con input').focus();
                            }
                        } else {
                            me.$('.tit-con input').val('')
                        }
                    } else {
                        me.$('.tit-con input').attr('placeHolder', '');
                        me.$('.tit-con').addClass('multiple');
                        me.$('.tit-con input').before(selectHtml);
                        me.$('.tit-con input').val('');
                        if (me._panel && me._panel._status == 'show') {
                            me.$('.tit-con input').focus();
                        }
                    }
                    me.$('.j-ipt-target').html('');
                    me.$('.tit-con input').removeAttr('style');
                }
            } else {
                me.$('.tit-con').html(selectHtml);
            }
        },


		/**
		*  @desc 设置placeholder
		* */
		setPlaceHolder:function(val){
			this.$('input').attr('placeHolder', val)
		},

        /**
         * @desc 清空选中的值
         * flag 是否触发change
         */
        clear: function(flag) {
            this.val = this.get('multiple') == 'single' ? '' : [];
            this.setValue(this.val, flag);
            // this.$('.tit-con').html(this._getSelectedHtml());
            this.hide();
            this.$('.more-btn').hide();
        },

        // 兼容
        clean: function() {
            this.clear();
        },

        // 设置全选状态
        _setCheckStatus: function() {
            if (this.get('multiple') == 'single') {return;}
            var val = _.pluck(this.get('options'), 'value');
            var isChecked = val.length == this.val.length;
            if (this._panel) {
                $('.mn-checkbox-item', this._panel.$el).toggleClass('mn-selected', isChecked);
            }
        },

        events: {
            'click   .select-tit': '_onTit',
            'click   .del-item':   '_onDel',
            'keydown input':       '_onKeyDown',
            'focus input':         '_onFocus'
        },

        /**
         * @desc 全选
         */
        _onChecked: function($target, e) {
            $target.toggleClass('mn-selected');
            if ($target.hasClass('mn-selected')) {
                this.val = _.pluck(this.get('options'), 'value');
            } else {
                this.val = [];
            }
            this.setValue(this.val);
            this._setSelected();
            this._panel && this._panel.resetPos();
            this.trigger('change', this.val, this.get('options'), this);
            e.stopPropagation();
            return false;
        },

        /**
         * @desc 标题
         */
        _onTit: function(e) {
            if (!this.get('disabled')) {
                this.toggle();
            }
            if (this.get('stopPropagation')) {
                e.stopPropagation();
                return false;
            }
        },

        _onFocus: function(e) {
           // $(e.target).select();
           this.$('.j-select-input').select();
        },

        /**
         * @desc 键盘事件
         */
        _onKeyDown: function(e) {
            var me = this, code = e.keyCode, $li = null, cls = 'keydown-hover';

            // back
            if (code == 8) {
                var val = me.$('.tit-con input').val();
                if (!val && me.get('multiple') != 'single') {
                    var $bubble = me.$('.tit-con .bubble').last();
                    if ($bubble.length > 0) {
                        $('.del-item', $bubble).trigger('click');
                    }
                }
                return true;
            }

            if ([13, 38, 40].indexOf(code) === -1) { return; };

            $li = $('.' + cls, me._panel.$el);
            $li.removeClass(cls);
            switch (code) {

                // enter
                case 13:
                $li.trigger('click');
                break;

                // 上
                case 38:
                $li = $li[0] ? $li.prev() : $('li:last', me._panel.$el);
                $li = !$li[0] ? $('li:last', me._panel.$el) : $li;
                $li.addClass(cls);
                me._resetScroll($li);
                break;

                // 下
                case 40:
                $li = $li[0] ? $li.next() : $('li:first', me._panel.$el);
                $li = !$li[0] ? $('li:first', me._panel.$el) : $li;
                $li.addClass(cls);
                me._resetScroll($li);
                break;
            }
            e.preventDefault();
        },

        /**
         * @desc 重置滚动条
         */
        _resetScroll: function($item) {
            var me = this,
                itemHeight = $item.outerHeight(),
                itemOffset = $item.offset(),
                pannelHeight = me._panel.$el.outerHeight(),
                pannelOffset = me._panel.$el.offset();

            var bottom = itemOffset.top + itemHeight - pannelOffset.top - pannelHeight;
            var top    = itemOffset.top - pannelOffset.top;

            if (bottom > 0) {
                $('.crm-scroll', this._panel.$el)[0].scrollTop += bottom;
            }

            if (top < 0) {
                $('.crm-scroll', this._panel.$el)[0].scrollTop += top;
            }
        },

        /**
         * @desc 删除item
         */
        _onDel: function(e) {
        	if (this.get('disabled')) {
                return false;
            }
            var val = $(e.currentTarget).data('val');
            this.val = _.filter(this.val, function(item) {
                return item != val;
            });
            this.setValue(this.val);
            this._setSelected();
            if (this.get('multiple') != 'single') {
                this._setCheckStatus();
                this._panel && this._panel.resetPos();
            }
            this.trigger('change', this.val, this.get('options'), this);
			this.trigger('del', val, { e });
            this._setOneLineStyle();
            return false;
        },

        // 切换其他下input的状态
        _setInputStatus: function($input, flag) {
            $input.removeClass('b-g-ipt-error');
            $input.attr('disabled', flag);
            $input.toggleClass('b-g-ipt-disabled', flag);
        },

        /**
         * @desc 点击选项
         */
        _onItem: function($target, e) {
            if ($target.hasClass('item-disable')) {e.stopPropagation(); return false;}
            var sv = $target.data('value') + '';
            var val = _.find(this.get('options'), function(item) {
                return (item.value + '') === sv;
            });
            val = val ? val.value : '';
            var item = this._getItemByVal(val);
            var $input = $target.find('.j-custom-ipt');
            var rightClick = !item.isCustom || !item.customRequired || !!item.customValue;
            if (this.get('multiple') == 'single') {
                if ($target.hasClass('selected')) {this.hide();e.stopPropagation(); return false;}

                $target.addClass('selected').siblings().removeClass('selected');

                if (item.isCustom) {
                    this._setInputStatus($input, false);
                    if (!item.customRequired || !!item.customValue) {
                        this.setValue(val);
                        this.trigger('change', val, item, this);
                    }
                } else {
                    this.setValue(val);
                    this.trigger('change', val, item, this);
					this.hide();
                }
            } else {
                $target.toggleClass('selected')
                if (this.val.indexOf(val) != -1) {
                    this.val = _.filter(this.val, function(item) {
                        return item != val;
                    });
                } else {
                    if (rightClick) {
                        this.val.push(val);
                    }
                }
                if (item.isCustom) {
                    this._setInputStatus($input, !$target.hasClass('selected'));
                }
                this.setValue(this.val);
                this._panel && this._panel.resetPos();
                this._setCheckStatus();
                if (rightClick) {
                    this.trigger('change', this.val, this.get('options'), this);
                }
            }
            e.stopPropagation();
            return false;
        },

        // 验证其他项是否填写
        validOther: function() {
            var me = this, error = 0;
            $('li.selected', me._panel.$el).each(function(index, dom) {
                var $item = $(dom);
                var value = $item.data('value');
                value = !_.isBoolean(value) ? String(value) : value;
                var item = me._getItemByVal(value);
                if (item && item.isCustom && item.customRequired && !item.customValue) {
                    $item.find('.j-custom-ipt').addClass('b-g-ipt-error');
                    error += 1;
                }
            });
            return error == 0;
        },

        // 销毁
        destroy: function() {
            this._panel.destroy();
            this._stimer = null;
            this.__posNum = 0;
            $('body').off('click.wselect' + this.uid);
            CRM.Widget.prototype.destroy.apply(this, arguments);
        }
    });

    module.exports = Select2;
});
