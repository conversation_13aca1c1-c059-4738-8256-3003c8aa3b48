{"assets/js": "common", "assets/widget/select": "select", "assets/widget/dropdown": "dropdown", "connect": "connect", "assets/widget/table": "table", "assets/widget/treetable": "treetable", "assets/widget/treetable2": "treetable2", "assets/widget/panel": "panel", "assets/widget/scroll": "scroll", "assets/widget/selector": "selector", "assets/widget/transfer": "transfer", "assets/widget/dialog": "dialog", "assets/widget/base": "base", "assets/widget/setcolumn": "set<PERSON><PERSON><PERSON>", "frurl/salesorderobj": "salesorderobj", "modules/action/leadsobj": "<PERSON><PERSON><PERSON>", "modules/action/cluepool": "cluepool", "modules/action/common": "common", "modules/action/contactobj": "<PERSON><PERSON><PERSON>", "modules/action/contract": "contract", "modules/action/customer": "customer", "modules/action/gb": "gb", "modules/action/inventory": "inventory", "modules/action/invoice": "invoice", "modules/action/market": "market", "modules/action/opportunityobj": "<PERSON><PERSON><PERSON>", "modules/action/orderform": "orderform", "modules/action/productobj": "<PERSON><PERSON><PERSON>", "modules/action/returnorder": "returnorder", "modules/action/myobject": "myobject", "modules/action/field": "field", "modules/action/customeraccountobj": "customeraccountobj", "modules/action/accounttransactionflowobj": "accounttransactionflowobj", "modules/action/accountcheckruleobj": "accountcheckruleobj", "modules/action/rewardtaskobj": "rewardtaskobj", "modules/action/enterpriserelationobj": "enterpriserelationobj", "modules/action/publicemployeeobj": "publicemployeeobj", "modules/action/erdepartmentobj": "erdepartmentobj", "modules/action/deliverynoteobj": "deliverynoteobj", "modules/action/goodsreceivednoteobj": "goodsreceived<PERSON><PERSON><PERSON>", "modules/action/orderpaymentobj": "orderpaymentobj", "modules/action/partnerobj": "<PERSON><PERSON><PERSON>", "modules/action/paymentobj": "<PERSON><PERSON><PERSON>", "modules/action/paymentplanobj": "paymentplanobj", "modules/action/prepaydetailobj": "prepaydetailobj", "modules/action/pricebookobj": "pricebookobj", "modules/action/promotionobj": "promotionobj", "modules/action/matchnoteobj": "match<PERSON><PERSON>j", "modules/action/salesinvoiceobj": "salesinvoiceobj", "modules/action/quotelinesobj": "quote<PERSON><PERSON><PERSON>", "modules/action/quoteobj": "<PERSON><PERSON><PERSON>", "modules/action/rebateincomedetailobj": "rebateincomedetail<PERSON><PERSON>", "modules/action/feesettlementbillobj": "feesettlementbill<PERSON><PERSON>", "modules/action/feedetailobj": "feedetailobj", "modules/action/bpmrelation": "bpmrelation", "modules/action/object_accessoryconsumeobj__c": "object_accessoryconsumeobj__c", "modules/action/accessoryconsumedetailobj": "accessoryconsumedetailobj", "modules/action/receivematerialbillobj": "receive<PERSON><PERSON><PERSON><PERSON><PERSON>", "modules/action/refundmaterialbillobj": "refundmaterial<PERSON><PERSON><PERSON>", "modules/action/serviceknowledgeobj": "serviceknowledgeobj", "modules/action/serviceprojectobj": "serviceprojectobj", "modules/action/servicefaultcategoryobj": "servicefaultcategoryobj", "modules/action/servicefaultobj": "servicefaultobj", "modules/action/devicecomponentsobj": "devicecomponentsobj", "modules/action/casesaccessoryuseinfoobj": "casesaccessoryuseinfoobj", "modules/action/employeeskillobj": "employeeskillobj", "modules/action/preventivemaintenanceobj": "preventivemaintenanceobj", "modules/action/commonunitobj": "commonunitobj", "modules/action/enterpriseinfoobj": "enterpriseinfoobj", "modules/action/deviceplanobj": "deviceplanobj", "modules/action/attributeobj": "<PERSON><PERSON><PERSON>", "modules/action/availablerangeobj": "availablerangeobj", "modules/action/attributepricebookobj": "attributepricebookobj", "modules/action/aggregateruleobj": "aggregateruleobj", "modules/action/pricepolicylimitaccountobj": "pricepolicylimitaccountobj", "modules/action/pricepolicyobj": "pricepolicyobj", "modules/action/areamanageobj": "areamanageobj", "modules/action/successfulstorerangeobj": "successfulstorerangeobj", "modules/action/productcollectionobj": "productcollectionobj", "modules/action/tieredpricebookobj": "tieredpricebookobj", "modules/action/supplierobj": "<PERSON><PERSON><PERSON>", "modules/action/stockobj": "<PERSON><PERSON><PERSON>", "modules/action/stockchecknoteobj": "stockchecknoteobj", "modules/action/spuobj": "s<PERSON><PERSON><PERSON>", "modules/action/specificationobj": "<PERSON><PERSON><PERSON>", "modules/action/rival": "rival", "modules/action/requisitionnoteobj": "requisitionnoteobj", "modules/action/purchaseorderobj": "purchaseorderobj", "modules/action/productconstraintobj": "productconstraintobj", "modules/action/pricebookproductobj": "pricebookproductobj", "modules/action/personnelobj": "<PERSON><PERSON><PERSON>", "modules/action/internalsigncertifyobj": "internalsigncertifyobj", "modules/action/exchangereturnnoteobj": "exchangereturnnoteobj", "modules/action/casesobj": "<PERSON><PERSON><PERSON>", "modules/action/batchobj": "<PERSON><PERSON><PERSON>", "modules/action/accountsigncertifyobj": "accountsigncertifyobj", "modules/action/accountaddrobj": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modules/action/outbounddeliverynoteobj": "outbounddeliverynoteobj", "modules/action/employeewarehouseadjustmentnoteobj": "employeewarehouseadjustmentnoteobj", "modules/action/sparepartsapplicationobj": "sparepartsapplicationobj", "modules/action/deviceobj": "<PERSON><PERSON><PERSON>", "modules/action/sparepartsreturnobj": "sparepartsreturnobj", "modules/action/newopportunityobj": "newopportunityobj", "modules/action/interactionstrategyobj": "interactionstrategyobj", "modules/action/newopportunitylinesobj": "newopportunitylinesobj", "modules/action/myobjectcpq": "myobjectcpq", "modules/action/accessoryexchangeobj": "accessoryexchangeobj", "modules/action/salecontractobj": "salecontractobj", "modules/action/productcategoryobj": "productcategoryobj", "modules/action/marketingactivity": "marketingactivity", "modules/action/dealerreturnapplicationobj": "dealerreturnapplicationobj", "modules/action/purchasereturnnoteobj": "purchasereturnnoteobj", "modules/action/logisticsinfoobj": "logisticsinfoobj", "modules/action/couponplanobj": "couponplanobj", "modules/action/rebateobj": "rebateob<PERSON>", "modules/action/rebatepolicyobjcommon": "rebatepolicyobjcommon", "modules/action/rebatepolicyobj": "rebatepolicyobj", "modules/action/rebateruleobj": "rebateru<PERSON>bj", "modules/action/simplepricepolicyobj": "simplepricepolicyobj", "modules/action/oppforecastobj": "oppforecastobj", "modules/action/procurementruleobj": "procurementruleobj", "modules/action/biddingsubscriptionrulesobj": "biddingsubscriptionrules<PERSON>j", "modules/action/historicalbiddingimportobj": "historicalbiddingimportobj", "modules/action/competitorobj": "<PERSON><PERSON><PERSON>", "modules/action/rfmruleobj": "rfmruleobj", "modules/action/object_ow91q__c": "object_ow91q__c", "modules/action/object_o1eoa__c": "object_o1eoa__c", "modules/action/reconciliationplanobj": "reconciliation<PERSON><PERSON><PERSON><PERSON>", "modules/action/transactionstatementobj": "transactionstatementobj", "modules/action/accounttreerelationobj": "accounttreerelationobj", "modules/action/subaccounttreerelationobj": "subaccounttreerelationobj", "modules/action/advancedformulaobj": "advancedformulaobj", "modules/action/opportunitydecisionchainobj": "opportunitydecision<PERSON><PERSON>bj", "modules/action/opportunitydecisionchaindetailobj": "opportunitydecisionchaindetailobj", "modules/action/incentivemetricobj": "incentivemetricobj", "modules/action/incentivepolicyobj": "incentivepolicyobj", "modules/action/incentivepolicyruleobj": "incentivepolicyruleobj", "modules/action/extendedattributeobj": "extendedattributeobj", "modules/action/accountworkbenchobj": "accountworkbenchobj", "modules/action/projectbudgetobj": "projectbudgetobj", "modules/action/salaryruleobj": "salary<PERSON><PERSON><PERSON><PERSON>", "modules/api/snapshot": "snapshot", "modules/api/abolish": "abolish", "modules/api/accountobj": "<PERSON><PERSON><PERSON>", "modules/api/address": "address", "modules/api/addsaleslog": "addsaleslog", "modules/api/awkward": "awkward", "modules/api/bill": "bill", "modules/api/bpm": "bpm", "modules/api/businessquery": "businessquery", "modules/api/convertrule": "<PERSON><PERSON><PERSON>", "modules/api/importexport": "importexport", "modules/api/leadsobj": "<PERSON><PERSON><PERSON>", "modules/api/lock": "lock", "modules/api/mergeobject": "mergeobject", "modules/api/pickdata": "pickdata", "modules/api/relatecrmdata": "relatecrmdata", "modules/api/relatecrmobject": "relatecrmobject", "modules/api/showbaiduscore": "showbaiduscore", "modules/api/showcrmdetail": "showcrmdetail", "modules/api/simply": "simply", "modules/api/smartform": "smartform", "modules/api/team": "team", "modules/api/draft": "draft", "modules/api/activerecord": "activerecord", "modules/api/marketingactivity": "marketingactivity", "modules/api/alioss": "al<PERSON>s", "modules/common/checkflow": "checkflow", "modules/common/datatable": "datatable", "modules/common/filter": "filter", "modules/common/field": "field", "modules/common/highseas": "highseas", "modules/common/indexmanage": "indexmanage", "modules/common/selectbox": "selectbox", "modules/common/slide": "slide", "modules/common/ztree": "ztree", "modules/common/newDialog": "newDialog", "modules/common/loading": "loading", "modules/common/cluepool": "cluepool", "modules/common/scrollloading": "scrollloading", "modules/common/scrollloadingwidthbox": "scrollloadingwidthbox", "modules/common/dragwidth": "dragwidth", "modules/common/fx_tree_search": "fx_tree_search", "modules/common/search": "search", "modules/common/filtertabs": "filtertabs", "modules/common/cleanresult": "cleanresult", "modules/common/vscroll": "vscroll", "modules/common/draggablecontainer": "draggablecontainer", "modules/detail/approval": "approval", "modules/detail/checkinsobj": "checkinsobj", "modules/detail/detail": "detail", "modules/detail/deliverynoteobj": "deliverynoteobj", "modules/detail/casesobj": "<PERSON><PERSON><PERSON>", "modules/detail/flow": "flow", "modules/detail/inventory": "inventory", "modules/detail/invoice": "invoice", "modules/detail/myobject": "myobject", "modules/detail/opportunityobj": "<PERSON><PERSON><PERSON>", "modules/page/accountobj": "<PERSON><PERSON><PERSON>", "modules/page/servicerecordobj": "servicerecordobj", "modules/page/consultquestionrecordobj": "consultquestionrecordobj", "modules/page/mailobj": "<PERSON><PERSON><PERSON>", "modules/page/paychannelobj": "paychannelobj", "modules/page/enterpriserelationobj": "enterpriserelationobj", "modules/page/crmrival": "crmrival", "modules/page/common": "common", "modules/page/deliverynoteobj": "deliverynoteobj", "modules/page/sparepartsdeliveryobj": "sparepartsdeliveryobj", "modules/page/requisitionnoteobj": "requisitionnoteobj", "modules/page/goalvalueobj": "goalvalueobj", "modules/page/highseasobj": "highseasobj", "modules/page/index": "index", "modules/page/info": "info", "modules/page/inventoryobj": "<PERSON><PERSON><PERSON>", "modules/page/importrecords": "importrecords", "modules/page/list": "list", "modules/page/list_tree": "list_tree", "modules/page/newopportunityobj": "newopportunityobj", "modules/page/pivottableruleobj": "pivotta<PERSON><PERSON><PERSON><PERSON>", "modules/page/relationtemplateobj": "relationtemplateobj", "modules/page/interactionstrategyobj": "interactionstrategyobj", "modules/page/accountdepartmentobj": "accountdepartmentobj", "modules/page/operationsstrategyobj": "operationsstrategyobj", "modules/page/pivottableinstanceobj": "pivottableinstanceobj", "modules/page/opeflow": "opeflow", "modules/page/opportunityobj": "<PERSON><PERSON><PERSON>", "modules/page/productobj": "<PERSON><PERSON><PERSON>", "modules/page/pricebookproductobj": "pricebookproductobj", "modules/page/sparepartsreturnobj": "sparepartsreturnobj", "modules/page/service": "service", "modules/page/stockobj": "<PERSON><PERSON><PERSON>", "modules/page/stockdetailsobj": "stockdetailsobj", "modules/page/warehouseobj": "<PERSON><PERSON><PERSON>", "modules/page/visitingobj": "<PERSON><PERSON><PERSON>", "modules/page/object_nf6ic__c": "object_nf6ic__c", "modules/page/fee_release__c": "fee_release__c", "modules/page/list20": "list20", "modules/page/stockbi": "stockbi", "modules/page/spuobj": "s<PERSON><PERSON><PERSON>", "modules/page/promotionobj": "promotionobj", "modules/page/paymentobj": "<PERSON><PERSON><PERSON>", "modules/page/partnerobj": "<PERSON><PERSON><PERSON>", "modules/page/leadspoolobj": "leadspoolob<PERSON>", "modules/page/approvalflowmonitor": "approvalflowmonitor", "modules/page/marketingactivity": "marketingactivity", "modules/page/deviceobj": "<PERSON><PERSON><PERSON>", "modules/page/qrcodeobj": "qrcodeobj", "modules/page/productcategoryobj": "productcategoryobj", "modules/page/casesobj": "<PERSON><PERSON><PERSON>", "modules/page/forecastruleobj": "forecastruleobj", "modules/page/procurementruleobj": "procurementruleobj", "modules/page/biddingsubscriptionrulesobj": "biddingsubscriptionrules<PERSON>j", "modules/page/biddingsubscriptionobj": "biddingsubscriptionobj", "modules/page/historicalbiddingimportobj": "historicalbiddingimportobj", "modules/page/procurementsearchobj": "procurementsearchobj", "modules/page/bizquerysearchobj": "bizquerysearchobj", "modules/page/procurementinfoobj": "procurementinfoobj", "modules/page/sparepartsmaintenanceplanobj": "sparepartsmaintenanceplanobj", "modules/page/projecttaskobj": "projecttaskobj", "modules/page/newcustomeraccountobj": "<PERSON>cus<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modules/page/marketinsights": "marketinsights", "modules/remind/approvalbyme": "approvalbyme", "modules/remind/approvalpush": "approvalpush", "modules/remind/importhelper": "importhelper", "modules/remind/weakremind": "<PERSON><PERSON><PERSON>", "modules/remind/flowhandle": "flowhandle", "modules/remind/bpmcasesobj": "bpmcasesobj", "modules/remind/remindcard": "remindcard", "modules/remind/saleaction2": "saleaction2", "modules/remind/workflowinfo": "workflowinfo", "modules/setting/businessflow": "businessflow", "modules/setting/cleanunit": "cleanunit", "modules/setting/clue": "clue", "modules/setting/bizquerysearchmanange": "bizquerysearchmanange", "modules/setting/common/tags": "tags", "modules/setting/crmlog": "crmlog", "modules/setting/customer": "customer", "modules/setting/customerrule": "customerrule", "modules/setting/datapermissions": "datapermissions", "modules/setting/smartforms/detail": "detail", "modules/setting/index": "index", "modules/setting/myobject": "myobject", "modules/setting/menumanage": "menumanage", "modules/setting/objmap": "objmap", "modules/setting/openapi": "openapi", "modules/setting/order": "order", "modules/setting/orderrule": "<PERSON><PERSON><PERSON>", "modules/setting/rest": "rest", "modules/setting/rolemanage": "rolemanage", "modules/setting/rolemanageenterpriseinterconnect": "rolemanageenterpriseinterconnect", "modules/setting/saleaction": "saleaction", "modules/setting/salesbrief": "salesbrief", "modules/setting/salestype": "salestype", "modules/setting/manageconfignav": "manageconfignav", "modules/setting/servicetype": "servicetype", "modules/setting/casesobjconfig": "casesobjconfig", "modules/setting/servicesearch": "servicesearch", "modules/setting/sysemail": "sysemail", "modules/setting/sysobject": "sysobject", "modules/setting/bigobject": "bigobject", "modules/setting/dataarchiving": "dataarchiving", "modules/setting/objecterdiagram": "objecterdiagram", "modules/setting/taskmonitoring": "taskmonitoring", "modules/setting/templatemanage": "templatemanage", "modules/setting/approval": "approval", "modules/setting/workflow": "workflow", "modules/setting/acountmanage": "acountmanage", "modules/setting/pricebookmanage": "pricebookmanage", "modules/setting/partnerobj": "<PERSON><PERSON><PERSON>", "modules/setting/ppmanage": "ppmanage", "modules/setting/globalvar": "globalvar", "modules/setting/goalruleobj": "goalruleobj", "modules/setting/indexmanage": "indexmanage", "modules/setting/repeat": "repeat", "modules/setting/visitorder": "visitorder", "modules/setting/accountreceivable": "accountreceivable", "modules/setting/batchchangeapprover": "batchchangeapprover", "modules/setting/business": "business", "modules/setting/hospital": "hospital", "modules/setting/callcenter": "callcenter", "modules/setting/cpq": "cpq", "modules/setting/ddsreportdata": "ddsreportdata", "modules/setting/device": "device", "modules/setting/device-bound": "device-bound", "modules/setting/esignature": "esignature", "modules/setting/exchangegoods": "exchangegoods", "modules/setting/fastsale": "fastsale", "modules/setting/marketingattribution": "marketingattribution", "modules/setting/membermanage": "membermanage", "modules/setting/opportunity": "opportunity", "modules/setting/ownership": "ownership", "modules/setting/promotion": "promotion", "modules/setting/recycle": "recycle", "modules/setting/roleset": "roleset", "modules/setting/saleaction2": "saleaction2", "modules/setting/shiporder": "shiporder", "modules/setting/logistics": "logistics", "modules/setting/smartforms": "smartforms", "modules/setting/stage": "stage", "modules/setting/statement": "statement", "modules/setting/usergroup": "usergroup", "modules/setting/usergroup2": "usergroup2", "modules/setting/tpm": "tpm", "modules/setting/marketingactivity": "marketingactivity", "modules/setting/oppforecastobj": "oppforecastobj", "modules/setting/common/fieldmanage": "fieldmanage", "modules/setting/common/saleaction": "saleaction", "modules/setting/common/category": "category", "modules/setting/common/tabs": "tabs", "modules/setting/procurement": "procurement", "modules/setting/rfmrule": "rfmrule", "modules/setting/proresource": "proresource", "modules/setting/robot": "robot", "modules/setting/assetmanagement": "assetmanagement", "modules/setting/promotionrebate": "promotionrebate", "modules/setting/tradeconfigure": "tradeconfigure", "modules/setting/paymentconfigure": "paymentconfigure", "modules/setting/contractconfigure": "contractconfigure", "modules/setting/creditmanage": "creditmanage", "modules/setting/onlinedoc": "onlinedoc", "modules/setting/termbankobj": "termbankobj", "modules/setting/interactive-assistant-agent": "interactive-assistant-agent", "modules/setting/profile-agent": "profile-agent", "app.js": "", "modules/components/category": "category", "modules/components/category_tree": "category_tree", "modules/components/emailBatchSend": "emailBatchSend", "modules/components/emailDetailList": "emailDetailList", "modules/components/filter": "filter", "modules/components/formbox": "formbox", "modules/components/gqc": "gqc", "modules/components/importtool": "importtool", "modules/components/map": "map", "modules/components/objecttable": "objecttable", "modules/components/object_treetable": "object_treetable", "modules/components/objecttable_bom": "objecttable_bom", "modules/components/pickobject": "pickobject", "modules/components/pickselfobject_cpq": "pickselfobject_cpq", "modules/components/pickselfobject_pricebook": "pickselfobject_pricebook", "modules/components/pickselfobject_salesorderhistory": "pickselfobject_salesorderhistory", "modules/components/pickselfobject_cpq_ladderprice": "pickselfobject_cpq_ladderprice", "modules/components/cartesianproduct_cpq": "cartesianproduct_cpq", "modules/components/pickselfobject_classification": "pickselfobject_classification", "modules/components/pickselfobject_newpricebook": "pickselfobject_newpricebook", "modules/components/pickselfobject_cpq_npb": "pickselfobject_cpq_npb", "modules/components/pickself_bom": "pickself_bom", "modules/components/picksku": "picksku", "modules/components/salesflow": "salesflow", "modules/components/search": "search", "modules/components/searchmap": "searchmap", "modules/components/setleader": "setleader", "modules/components/steplist": "steplist", "modules/components/upload": "upload", "modules/components/old_upload": "old_upload", "modules/components/dialog_selectbom": "dialog_selectbom", "modules/components/helper": "helper", "modules/components/selectAndSearch": "selectAndSearch", "modules/components/timeselect": "timeselect", "modules/components/templatemanage": "templatemanage", "modules/components/flow": "flow", "modules/components/selectLadderPriceBook": "selectLadderPriceBook", "modules/components/slide_historyquotedata": "slide_historyquotedata", "modules/components/repeat_data": "repeat_data", "modules/components/showgroupdetail": "showgroupdetail", "modules/components/product_guide": "product_guide", "modules/components/manage_config_nav_guide": "manage_config_nav_guide", "modules/components/signature": "signature", "modules/components/relationtree": "relationtree", "modules/components/operatelog": "operatelog", "modules/components/object_treeTable_extend": "object_treeTable_extend", "modules/components/search_price_tools": "search_price_tools", "modules/components/multiple_select": "multiple_select", "modules/components/customerrule": "customerrule", "modules/components/duplicate": "duplicate", "modules/components/prmmanage": "prmmanage", "modules/components/sortmenu": "sortmenu", "modules/components/sortlayout": "sortlayout", "modules/components/cardlayout": "cardlayout", "modules/components/pickselfobject": "pickselfobject", "modules/components/category_attribute": "category_attribute", "modules/components/cartesianproduct": "cartesianproduct", "modules/components/timeglider": "timeglider", "modules/components/flexgroup": "flexgroup", "modules/components/simpletools": "simpletools", "modules/components/editpd": "editpd", "modules/components/upload_big_file": "upload_big_file", "modules/components/quotehistory": "quotehistory", "modules/components/newmap": "newmap", "modules/components/edittable": "edittable", "modules/components/versioncheckinfo": "versioncheckinfo", "modules/components/dialog_addgroup": "dialog_addgroup", "modules/common/math": "math", "modules/buscomponents/md_priceservice": "md_priceservice", "modules/buscomponents/pickselfobject_bom": "pickselfobject_bom", "modules/buscomponents/form_currency": "form_currency", "modules/buscomponents/promotion": "promotion", "modules/buscomponents/model_priceservice": "model_priceservice", "modules/buscomponents/md_business": "md_business", "modules/buscomponents/md_salecontract": "md_salecontract", "modules/buscomponents/md_mappickobject": "md_mappickobject", "modules/buscomponents/orderform_pb": "orderform_pb", "modules/buscomponents/orderform_partner": "orderform_partner", "modules/buscomponents/orderform_md": "orderform_md", "modules/buscomponents/pickselfobject_bomcore": "pickselfobject_bomcore", "modules/buscomponents/pickself_bom_util": "pickself_bom_util", "modules/buscomponents/pickselfobject_cpq_style2": "pickselfobject_cpq_style2", "modules/buscomponents/action_field": "index", "modules/buscomponents/action_field/components/customer_table/index.js": "policy_table"}