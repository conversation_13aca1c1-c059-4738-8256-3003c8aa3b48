
define(function (require, exports, module) {
    var util = CRM.util,
        Pagination = require('base-modules/pagination/pagination'),
        layout = require('./template/layout-html'),
        tpl = require('./template/tpl-html');

    var ImportHelper = Backbone.View.extend({

        pageNumber: 1,
        pageSize: 20,
        apiNameList: {},

        events: {
            'click ul': 'tabHandle',
            'click a': 'preventTran'
        },
        initialize: function (opts) {
            // console.log(type);
            if(!$(opts.wrapper).closest('.crm-module-wrap').length) {
                require.async(['crm-assets/style/all.css','crm-assets/style/remind.css']);
                var $inner = $('<div></div>').addClass('crm-module-wrap crm-scroll crm-remind crm-r-importhelper');
                var $wrap = $('<div></div>').attr('id', 'crm-layout').html($inner);
                $(opts.wrapper).html($wrap);
                opts.wrapper = $inner[0];
            }
            var me = this;
            me.setElement(opts.wrapper);
            me._getAllObject(function() {
                me.renderPage();
            });
            me.$el.html(layout({}));

        },

        preventTran: function (e) {
            e.stopPropagation();
        },

        tabHandle: function (e) {
            // todo 进入到导入或者导出
            var $tg = $(e.target);
            var $wrap = $tg.closest('li');
            var type = $wrap.length && $wrap.data('type');
            type = (type == '2' ? 'export' : 'import');
			window.open('#crm/iorecords/=/' + type)
            return false;
        },

        //获取所有对象
        _getAllObject: function(callback) {
            var me = this,
                account = FS.getAppStore('contactData');
            me._ajax && me._ajax.abort();
            me._ajax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/object_import/service/get_import_object_list',
                data: {
                    'ea': account ? account.enterpriseAccount : 'admin'
                },
                success: function (data) {
                    if (data.Result.StatusCode == 0) {
                        me.apiNameList = me._formatObject(data.Value.importObjectList);
                        callback && callback();
                        return;
                    }
                    util.alert(data.Result.FailureMessage || $t("服务器返回错误!"));
                }
            }, {
                errorAlertModel: 1
            });
        },

        _formatObject: function(list) {
            var apiNameList = {};
            _.each(list, function(item) {
                apiNameList[item.objectCode] = item;
            });
            return apiNameList;
        },

        renderPage: function (type) {
            var me = this,
                list = [];
            me.getImportList(function (data) {
                if (data.items && data.items.length > 0) {
                    var statusMap1 = {
                        2: "{{typeText}}完成",  //[ignore-i18n]
                        3: "{{typeText}}过程异常终止",  //[ignore-i18n]
                        4: "系统发生异常无法对数据进行{{typeText}}，错误码为",  //[ignore-i18n]
                        6: "已取消",  //[ignore-i18n]
                    }
                    var statusMap2 = {
                        2: "{{typeText}}完成",  //[ignore-i18n]
                        3: "{{typeText}}过程异常终止",  //[ignore-i18n]
                        4: "无法{{typeText}}",  //[ignore-i18n]
                        6: "已取消",  //[ignore-i18n]
                    }
                    _.each(data.items, function (item) {
                        var type = item.jobType;
                        var typeText = item.jobType === 2 ? '导出' : '导入';  //[ignore-i18n]
                        var _obj;
                        var displayName =  _.escape((_obj = me.apiNameList[item.apiName || item.queryParam.object_describe_api_name]) ? _obj.objectName : item.apiFullName);
                        // 导入导出文件处理
                        var fileName = $t('{{displayName}}对象{{typeText}}结果_{{moment}}', {
                            typeText: $t(typeText),
                            displayName: displayName,
                            moment: FS.moment.unix(item.startTime  / 1000).format('YYYYMMDD'),
                        });
                        var fileInfo = '';
                        if (item.result && item.result.expired) {
                            fileInfo += '<a style="color:#999999">'+$t("文件已失效")+'</a>';
                        } else if (item.result && item.result.url) {
                            if(!(/\.xlsx/g.test(item.result.url) || new RegExp(item.result.fileExt).test(item.result.url))) {
                                fileName += (item.result.fileExt) ? item.result.fileExt : 'xlsx';
                            }
                            var href;
                            if(item.result.exportType === 'export_file') {
                                href = '/FSC/EM/File/BatchDownloadByStream?token=' + item.result.url;
                            } else {
                                if(item.jobType === 2) {//名称由server下发
                                    let fileExt =  util.getFileExtText(item.result.fileName);
                                    if(!fileExt) {
                                        fileName = item.result.fileName + '.' + (item.result.fileExt || 'xlsx');
                                    } else {
                                        fileName = item.result.fileName;
                                    }
                                }
                                href = util.getFscTempFileUrl(item.result.url, fileName, true);
                            }
                            fileInfo += "<a style='color:#3487E2;' href=" + href + "  >"+$t("下载查看{{typeText}}结果", {typeText: $t(typeText)})+"</a>";
                        }
                        fileInfo += "<a style='color:#999'>"+$t("（文件只保留15天）")+"</a>";

                        // 导入导出详细文案拼接
                        var title = displayName;
                        var info = $t('于{{moment}}进行了{{displayName}}{{typeText}}', {
                            moment: FS.moment.unix(item.startTime  / 1000).format('YYYY-MM-DD HH:mm'),
                            displayName: displayName,
                            typeText: $t(typeText),
                        }) + $t("，");
                        var _status = $t(statusMap1[item.status], {typeText: $t(typeText)});
                        switch (item.status) {
                            case 2:  //完成
                                info += $t("{{typeText}}数据量{{count}}条", {
                                    count: item.result && item.result.totalCount ? item.result.totalCount : 0,
                                    typeText: $t(typeText)
                                }) + $t("，");
                                info += $t("{{typeText}}失败量{{count}}条", {
                                    count: item.result && item.result.failCount ? item.result.failCount : 0,
                                    typeText: $t(typeText)
                                });
                                //导入成功 导入失败均可下载
                                if (type === 1 &&  ! (item.result && item.result.expired) || type === 2) {
                                    info += $t("，") + fileInfo;
                                } else {
                                    info += $t('。');
                                }
                                break;
                            case 3:  //失败
                                info += _status;
                                if (type === 1) {
                                    info += $t("已经{{typeText}}约{{sucCount}}条数据，请重新尝试。",{
                                        sucCount: item.result && item.result.sucCount ? item.result.sucCount : 0,
                                        typeText: $t(typeText),
                                    });
                                } else {
                                    info += $t("，") + $t('点击进入查看详细结果') + $t('。');
                                }
                                // info += fileInfo;
                                title = $t("系统异常，") + displayName;
                                break;
                            case 4:  //异常
                                info += _status;
                                if (type === 1) {
                                    info += item.result && item.result.code;
                                } else {
                                    info += $t("，") + $t('点击进入查看详细结果') + $t('。');
                                }
                                title = $t("系统异常，") + displayName;
                                break;
                            case 6:  //已取消
                                info += _status;
                                if (item.result) {
                                    info += $t("{{typeText}}数据量{{count}}条", {
                                        count: item.result.totalCount || 0,
                                        typeText: $t(typeText)
                                    }) + $t("，");
                                    info += $t("{{typeText}}失败量{{count}}条", {
                                        count: item.result.failCount || 0,
                                        typeText: $t(typeText)
                                    });
                                    // todo 改为type
                                    if (type === 1 && item.result.failCount) {
                                        info += $t("，") + fileInfo;
                                    } else {
                                        info += $t('。');
                                    }
                                } else {
                                    info += '。';
                                }
                                title = displayName + $t(typeText);
                                break;
                            default:
                                break;
                        }

                        item = _.extend(item, {
                            title: title,
                            statusText: $t(statusMap2[item.status], {typeText: $t(typeText)}),
                            info: info,
                        })

                        list.push(item);
                    });
                    $('.list-box', me.$el).html(tpl({
                        list: list,
                        getYestodayText: util.getYestodayText
                    }));
                    var page = {};
                    page.PageNumber = data.pageIndex;
                    page.PageSize = data.pageSize;
                    page.TotalCount = data.totalCount;
                    page.PageCount = data.pageCount;
                    me.renderPagation(page);
                } else {
                    $('.list-box', me.$el).addClass('no-data');
                    $('.list-box', me.$el).html('<div class="no-data">'+ $t("暂无导入导出记录") +'</div>');
                    $('.page-box', me.$el).hide();
                }

            });
        },

        /**
         *  @desc渲染分页
         */
        renderPagation: function (page) {
            var me = this;
            this.initPagation();
            this.page.setTotalSize(page.TotalCount || 0);
            this.page.render();
            if (page.TotalCount <= this.pageSize) {
                this.pageNumber = 1;
                this.page.reset();
            }
            $('.page-box', this.$el).show();
        },

        // 初始化分页
        initPagation: function () {
            var me = this;
            if (!me.page) {
                me.page = new Pagination({
                    'element': me.$el.find('.page-box').length ? me.$el.find('.page-box') : me.$('.page-box'),
                    'pageSize': me.pageSize
                });
                me.page.on('page', function (pageNumber) {
                    me.pageNumber = pageNumber;
                    me.renderPage();
                });
            }
        },

        getImportList: function (callback) {
            var me = this;
            util.FHHApi({
                url: '/EM1HJobCenter/inputJobCenter/queryJobList',
                data: {
                    'pageNumber': me.pageNumber,
                    'pageSize': me.pageSize,
                    'fromAssistant': true,
                    'business': 'crm'
                },
                success: function (data) {
                    if (data.Result.StatusCode == 0) {
                        if (data.Value.code == -1) {
                            util.alert(data.Value.message);
                        } else {
                            callback && callback(data.Value);
                        }
                    }
                },
                error: function () {
                    util.alert($t("网络错误"))
                }
            });
        },

        destroy: function () {
            var me = this;
            me._ajax && me._ajax.abort();
            me.page && me.page.destroy();
        }

    });

    module.exports = ImportHelper;
})
