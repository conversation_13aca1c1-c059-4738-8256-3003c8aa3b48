/**
 * @desc 项目管理afei
 */
define(function (require, exports, module) {
    let List = require("../list/list");
    let ProjectTaskObj = require("./projecttaskobj/projecttaskobj");
    const util = CRM.util;
    module.exports = List.extend({
        render(param) {
            //获取跳转项目的_id
            this.current_pro_id = localStorage.getItem("current_pro_id");
            $(".crm-p-projectobj .cs-running-component.no-shadow")[param[2] ? 'hide' : 'show']();
            if(param[2]) {
                this.btn_type = param[2];
                this.isGk = true;
                util.waiting(true);
            }
            List.prototype.render.apply(this, arguments);
        },
        trclickHandle: function (...args) {
            const [data, $tr, $target, a, noRealllyClick] = args;
            if (data.project_detail_redirect === '1') {
              //下钻甘特图
              this.goTaskDetails("", data);
            } else {
              // 走默认详情逻辑
              List.prototype.trclickHandle.apply(this, args);
            }
        },
        displayTable (type) {
            this.$el.find(".projectobj-table")[type === "hide" ? "hide" : "show"]();
            this.options.wrapperContext && this.options.wrapperContext.scrollHandle();
            this.table.resize();
        },
        renderListCompleteHandle () {
            if ($(".crm-module-content1").length) {
                if (
                    !$(".crm-module-content1>.crm-table").siblings(
                        ".projecttaskobj_box"
                    ).length
                ) {
                    $(".crm-module-content1>.crm-table")
                        .addClass("projectobj-table")
                        .before('<div class="projecttaskobj_box"><div>');
                }
            } else {
                if (!$(".crm-table").siblings(".projecttaskobj_box").length) {
                    $(".crm-table")
                        .addClass("projectobj-table")
                        .before('<div class="projecttaskobj_box"><div>');
                }
            }
            this.isGk === true && this.renderGanOrKan();
        },
        async renderGanOrKan () {
            const { current_pro_id, btn_type } = this;
            const { lists, data } = await this.getLists(current_pro_id);
            this.goTaskDetails(btn_type, data, lists);
        },
        goTaskDetails (str, data, lists) {
            this.$el.find(".projectobj-table").hide();
            $(".projecttaskobj_box").show();
            this.projecttaskobj = new ProjectTaskObj({
                apiname: "ProjectTaskObj",
                el: this.$(".projecttaskobj_box"),
                isListLayout: true,
                data: { ...data, name: _.escape(data.name) },
                btnType: str,
                protableCurData: lists ? lists : this.table.getCurData(),
                projectobj: this,
            });
            this.projecttaskobj.render();
            this.isGk = false;
            util.waiting(false);
            $(".crm-p-projectobj .cs-running-component.no-shadow").show();
        },
        getLists (proId) {
            const apiname = this.options.apiname;
            return new Promise((resolve, reject) => {
                const params = {
                    limit: 10000,
                    offset: 0,
                    filters: [],
                    orders: [
                        {
                            fieldName: "last_modified_time",
                            isAsc: false,
                        },
                    ],
                };
                util.FHHApi(
                    {
                        url:
                            "/EM1HNCRM/API/v1/object/" +
                            apiname +
                            "/controller/List",
                        data: {
                            object_describe_api_name: apiname,
                            extractExtendInfo: true,
                            ignore_scene_record_type: false,
                            include_describe: false,
                            search_query_info: JSON.stringify(params),
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                console.log(res.Value.dataList);
                                const lists = res.Value.dataList || [];
                                const data = lists.find(
                                    (item) => item._id === proId
                                );
                                resolve({
                                    lists,
                                    data,
                                });
                            }
                        },
                        complete: function () { },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            });
        },
        excuteAction (action, param) {
            const me = this;
            param = param || {};
            param["projectobj"] = me;
            List.prototype.excuteAction.call(this, action, param);
        },
        //添加滚动监听事件
        layoutScroll (e) {
            e && this.projecttaskobj && this.projecttaskobj.layoutTaskScroll(e);
        }
    });
});
