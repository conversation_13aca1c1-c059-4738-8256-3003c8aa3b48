/**
 * @desc   任务列表
 * <AUTHOR>
 */
define(function (require, exports, module) {
	var List = require("../../list_tree/list_tree");
	var GanttView = require("./ganttview/ganttview");
	var Resource = require('../../projectresourceobj/projectresourceobj');
	var KbanView = require("./kbanview/kbanview");
	let ScrollBar = require("base-modules/ui/scrollbar/scrollbar");
	let util = require("./util");
	module.exports = List.extend({
		initialize: function (opts) {
			List.prototype.initialize.apply(this, arguments);
			if (opts.el) {
				this.setElement(opts.el);
				this.isHasStageData = false;
				this.isFull = false;//默认不全屏
				this.isEdit = false; //是否编辑了详情页字段
				this.timeRange = []; //日期范围
				this.opts = opts;
				this.wrapper_scroll_top = 0;
				this.gantt_allow_update_date = 1; //是否允许拖拽
				this.getGanttConfig();
				this.collaseStatus = JSON.parse(localStorage.getItem('collaseStatus')) || {};
				this.openGantt = JSON.parse(localStorage.getItem(`${CRM.ea}_gantt`));  //获取是否打开过新版甘特图配置
				!this.openGantt && this.initGanttDialog();  //如之前未打开过则打开
				this.storageLineWidth = JSON.parse(localStorage.getItem(`${CRM.ea}_${CRM.curEmpId}_line`));
				this.storagePlanActual = localStorage.getItem(`${CRM.ea}_${CRM.curEmpId}_plan_actual`);
			}
		},
		render() {
			this.fetchStageDescribe().then(() => {
				List.prototype.render.apply(this, arguments);
			});
		},
		my_events: {
			'click .task-drag': 'dragTaskFun',
			'click .icon-collase': 'changeCollase',
			'click .resource-btn': 'changeMode',
			'click .j-btn-refresh': 'refreshSize',
			'click .j-btn-legend': 'changeLegend',
			'click .page-task-back': 'goBackProObj',
			'click .btn-fullscreen': 'toggleScreen',
			'click .pro-btn': 'showStageDetail'
		},
		initGanttDialog() {
			const _this = this;
			const isExistDialog = $('body').find('.crm-gantt-legend-dialog');
			isExistDialog?.length && isExistDialog.remove();
			return FxUI.create({
				wrapper: $('body')[0],
				template: `<fx-dialog :title="title" ref="ganttDialog" class="crm-gantt-legend-dialog" :visible.sync="visible" size="normal" :append-to-body="true" @close="handleClose" :before-close="handleBeforeClose">
					<div class="gantt-wrapper">
						<div class="gantt-img"></div>
						<div class="gantt-info">
							<h3 class="title">{{$t('gantt_visual_update_notes')}}</h3>
							<div class="content">{{$t('gantt_visual_update_notes_info')}}</div>
						</div>
					</div>
					<span slot="footer" class="dialog-footer">
						<fx-button type="primary" @click="handleClose" size="small">{{$t('知道了')}}</fx-button>
					</div>
				</fx-dialog>`,
				data() {
					return {
						visible: true,
						closeType: '',
						title: $t('gantt_update_notes')
					}
				},
				mounted() {
					if (this.$refs.ganttDialog) {
						const header = this.$refs.ganttDialog.$el.querySelector('.el-dialog__header');
						const body = this.$refs.ganttDialog.$el.querySelector('.el-dialog__body');
						const titleElement = this.$refs.ganttDialog.$el.querySelector('.el-dialog__title');
						header.style.lineHeight = 0;
						body.style.padding = '24px';
						titleElement.style.fontSize = '16px';
						titleElement.style.fontWeight = '700';
					}
				},
				methods: {
					handleClose() {
						// 对话框关闭时的处理
						this.visible = false;
						if (this.closeType !== 'overlay') {
							localStorage.setItem(`${CRM.ea}_gantt`, true);
							_this.$('.btn-legend').find('.gantt-badge').show();
						}
						this.closeType = ''; // 重置 closeType
					},
					handleBeforeClose(done) {
						// 在对话框关闭前的处理
						this.closeType = event.target.className.includes('el-icon') ? 'close-button' : 'overlay';
						done && done();
					}
				}
			})
		},
		updateDataByCellEdit(obj, isNeedRefresh = null) {
			List.prototype.updateDataByCellEdit.apply(this, arguments);
			const _this = this;
			const old_scrollL = $('.project-gantt-container').scrollLeft();
			const isFix = this._fixHeader;
			if (!isNeedRefresh) {
				this.isEdit = true;
				if (arguments[0].data.tasktype === "stage") {
					this.setTreeList(this.stageList, arguments);
				} else {
					this.setTreeList(this.taskList, arguments);
				}
				
				this.initGanttView(this.$('.project-gantt-container'),this.initGanttTasks(),(ganttview) => {
					if (isFix) {
						_this._fixGanttHeader = null;
						_this._toggerFix();
					}
					_this.addScroll(old_scrollL);
				});
			}
		},
		//节点展开收起事件
		dropDownClick(rowData, allData, status) {
			const _this = this;
			this.ifFold = status;
			util.setMemberTextWidth(this.table.$el);
			this.initGanttView(this.$('.project-gantt-container'),this.filteGanttData(),(ganttview) => {
				_this.setDtRight();
				_this.addScroll();
			});
			
		},
		setTreeList(list, obj) {
			const _this = this;
			const {
				plan_start_date,
				plan_end_date,
				actual_start_date,
				actual_end_date,
				biz_status,
				_id,
			} = obj[0].data;
			list = list.map((item) => {
				if (item._id === _id) {
					item.plan_start_date = item.plan_start_date ? FS.moment.unix(plan_start_date / 1000).format("YYYY-MM-DD") : '';
					item.plan_end_date = item.plan_end_date ? FS.moment.unix(plan_end_date / 1000).format("YYYY-MM-DD") : '';
					item.actual_start_date = item.actual_start_date ? FS.moment.unix(actual_start_date / 1000).format("YYYY-MM-DD") : '';
					item.actual_end_date = item.actual_end_date ? FS.moment.unix(actual_end_date / 1000).format("YYYY-MM-DD") : '';
					if (item.actual_start_date || item.actual_end_date) {
						// item._has_actual_bar = true;
						item._is_actual_bar = true;
					}
					item.biz_status = _this.fields_biz_status.find(it => it.value === biz_status).label;
				}
				return item;
			});
		},
		set_dtmain_layout() {
			let box = this.$('.crm-table-pro-tasks');
			let dt_main = box.find('.dt-main');
			if (!box.find(".dt-main-box").length) {
				box.append(`<div class="dt-main-box"></div>`);
				box.find(".dt-main-box").append(dt_main).append('<div class="dt-right"></div>');
			}
			box.find('.dt-right').html('<div id="gantt-project" class="projectGantt" style="min-width:100%;width: auto;"><div class="project-gantt-container" style="overflow:auto hidden !important"></div></div>');
			new ScrollBar(box.find(".dt-right"));
			let theads = dt_main.find('.j-header-fixed');
			let tbodys = dt_main.find('tbody');
			theads.each((index, value) => {
				const trs = $(value).find('tr');
				trs.each((idx, tr) => {
					$(tr).css({ height: '61px', 'line-height': '61px' })
				})
			});
			tbodys.each((index, item) => {
				const trs = $(item).find('.tr');
				trs.each((idx, tr) => {
					$(tr).css({ height: '38px', 'line-height': '38px' });
				})
			});
		},
		//计划、实际日期区分
		set_actual_select() {
			const _this = this;
			const type = $(".crm-ui-title.j-btn-guide.cur").attr("data-type");
			if(type !== "gantt") return;
			const plan_actual_wrapper = this.$('.dt-plan-actual-btn');
			plan_actual_wrapper.length && plan_actual_wrapper.remove();
			this.$('.other-item').after('<div class="dt-plan-actual-btn">');
			this.selectPlanActual = FxUI.create({
				wrapper: this.$('.dt-plan-actual-btn')[0],
				template: `<fx-select
                    v-model="selectvalue"
                    :options="options"
                    size="mini"
                    default-first-option
                    @change="changeSlect"
                ></fx-select>`,
				data() {
					return {
						selectvalue: _this.storagePlanActual || 'plan_actual',
						options: [
							{
								value: 'plan_actual',
								label: $t('task_plan_actual')
							},
							{
								value: 'plan',
								label: $t('task_plan')
							},
							{
								value: 'actual',
								label: $t('task_actual')
							}
						]
					}
				},
				methods: {
					changeSlect(value) {
						const key = `${CRM.ea}_${CRM.curEmpId}_plan_actual`;
						localStorage.setItem(key, value);
						_this.ganttview.update_view_plan_actual(value);
					}
				}
			})
		},
		getOptions() {
			const _this = this;
			let options = List.prototype.getOptions.apply(this, arguments);
			let opt_btns = options.operate.btns || [];
			return _.extend(options, {
				custom_className: 'crm-table-pro-tasks',
				showMultiple: false,
				autoHeight: false,
				//去掉排序
				isOrderBy_allColumn: false,
                operate: {
                    btns: opt_btns.map(item => {
                        if(item.api_name === "ExportGanttChart_button_default") {
                            item.action = "ExportGanttChart";
                            item.attrs = "data-action=" + item.action;
                        };

						if(item.api_name === "GenerateProjectBudget_button_default") {
                            item.action = "GenerateProjectBudget";
                            item.attrs = "data-action=" + item.action;
                        };
                        return item
                    })
                },
				onTrHoverHandle: (index, e) => {
					const $tr = $(e.currentTarget);
					const trId = $tr.data().id;
					if (this.ganttview) {
						this.ganttview.add_grid_row_class(
							"gantt-row-hover",
							index,
							trId
						);
					}
				},
				refreshCallBack: function () {
					//资源视图
					if (_this.resource) return _this.resource.resourceTable.options.refreshCallBack();
					_this.refreshSize();
				}
			});
		},
		//父子级展开/收起
		setAllChildrenIsShow(show) {
			let btns = Array.from($(".table-dropdown-btn"));
			if (btns.some((item) => !$(item).hasClass("close"))) {
				this.table.setAllChildrenIsShow(show);
			}
		},
		parseColumns() {
			let columns = List.prototype.parseColumns.apply(this, arguments);
			let tablecolumns = _.filter(columns, function (column) {
				return column.dataType !== "operate";
			});
			tablecolumns.some((item) => {
				if (item.api_name === "name") {
					item.customFixed = true;
					item.fixed = true;
					item.disableSet = true;

					return true;
				}
			});

			return tablecolumns;
		},
		parseParam() {
			let param = List.prototype.parseParam.apply(this, arguments);
			let search_query_info = JSON.parse(param.search_query_info);
			let p = {
				field_name: "project_id",
				field_values: this.opts.data._id,
				operator: "EQ",
			};
			search_query_info.filters.push(p);
			search_query_info.limit = "2000";
			search_query_info.orders = [{ fieldName: "index", isAsc: true }];
			param.search_query_info = JSON.stringify(search_query_info);
			param.include_layout = true;
			param.include_describe = true;
			this.isAll = param.search_template_type === "default" ? true : false;
			this.isCancle = param.ignore_scene_filter ? true : false;
			//获取阶段信息
			return param;
		},
		//获取表格中的数据
		parseData(obj) {
			const _this = this;
			let stageId = [], noSatgeTasks = [], stageList = [];
			let dataObj = List.prototype.parseData.apply(this, arguments);
			console.log(dataObj);
			this.stageDatas = this.stageDatas.map(item => {
				item.children && delete item.children;
				return item;
			});
			let stages = dataObj.data.filter(item => item.stage_id && !item.task_no);
			stageList = this.stageDatas;
			if (stages.length) {
				if (!this.isAll) {
					//阶段存在则预设筛选场景
					stages.forEach(item => {
						item.stage_id && !stageId.includes(item.stage_id) && stageId.push(item.stage_id);
					});
					stageList = this.isCancle ? this.stageDatas : this.stageDatas.filter(item => !stageId.includes(item._id));
				}
			} else {
				//阶段不存在则为手动筛选\搜索情形
				dataObj.data.forEach(item => {
					item.stage_id && !stageId.includes(item.stage_id) && stageId.push(item.stage_id);
				});
				stageList = this.stageDatas.filter(item => stageId.includes(item._id));
			}
			//绘制甘特视图日期备用
			this.objectDescribe_dates = [
				obj.objectDescribe.fields.plan_start_date.description,
				obj.objectDescribe.fields.plan_end_date.description,
				obj.objectDescribe.fields.actual_start_date.description,
				obj.objectDescribe.fields.actual_end_date.description,
			];
			//保存状态字段
			this.fields_biz_status = obj.objectDescribe.fields.biz_status.options;
			//筛选数据所处阶段、父任务等合并
			dataObj.data = dataObj.data.filter(item => item._id);
			dataObj.data = stageList.concat(dataObj.data);
			//去重
			dataObj.data = dataObj.data.reduce((cur, next) => {
				obj[next._id] ? "" : obj[next._id] = true && cur.push(next);
				return cur;
			}, [])
			const newobj = JSON.parse(JSON.stringify(obj));
			newobj.dataList = newobj.dataList.filter(
				(item) => item.tasktype !== "stage"
			);
			this._listData = newobj;
			if (dataObj.data.length) {
				dataObj.data = dataObj.data.map(function (item) {
					if ("parent_task_id" in item && item["parent_task_id"]) {
						item["pid"] = item["parent_task_id"];
						item["tasktype"] = "son";
					}
					item["rowId"] = item["_id"];
					if (
						!("parent_task_id" in item) ||
						!item["parent_task_id"]
					) {
						if (!item.stage_id) {
							item["tasktype"] = "roottask";
						}
						if (item.stage_id && item.task_no) {
							item["pid"] = item["stage_id"];
							item["tasktype"] = "roottask";
							let biz_status = item["biz_status"];
							let item_stage = _this.stageDatas.find(
								(stage) => stage._id === item.stage_id
							);
							if (item.stage_id && _this.stageDatas && item_stage)
								switch (biz_status) {
									case "unstarted":
										item_stage["unstarted"] += 1;
										break;
									case "ongoing":
										item_stage["ongoing"] += 1;
										break;
									case "completed":
										item_stage["completed"] += 1;
										break;
									default:
										break;
								}
						}
					}
					return item;
				});
			};
			//无阶段任务
			noSatgeTasks = dataObj.data.filter(item => !item.stage_id);
			for (var i = 0; i < dataObj.data.length; i++) {
				if (!dataObj.data[i].stage_id) {
					dataObj.data.splice(i--, 1);
				}
			}
			noSatgeTasks.reverse().forEach(item => dataObj.data.unshift(item));
			this.isHasStageData && dataObj.data.unshift({
				name: $t("快速添加"),
				rowId: 123,
				isEmpty: true
			});
			dataObj.data = CRM.util.parseDataToTree(dataObj.data);
			dataObj.data.map((item) => {
				if (item.children) {
					item.project_stage_start_date =
						item.children[0].project_stage_start_date;
					item.project_stage_end_date =
						item.children[0].project_stage_end_date;
				}
				return item;
			});
			return dataObj;
		},
		//字段编辑
		getCellEditApiName(field, trData) {
			if (trData && trData.tasktype === "stage") {
				return "ProjectStageObj"
			}
			return "ProjectTaskObj"
		},
		addTaskData(data) {
			let list = [];

			data.forEach((item) => {
				list.push(item);

				if (item.tasktype) {
					if (item.actual_start_date || item.actual_end_date) {
						list.push(
							_.extend({}, item, {
								plan_start_date:
									item.actual_start_date ||
									item.actual_end_date,
								plan_end_date:
									item.actual_end_date ||
									item.actual_start_date,
								_is_actual_bar: true,
							})
						);

						item._has_actual_bar = true;
					}
				}
			});

			return list;
		},
		excuteAction(action, param) {
			const me = this;
			me.excuteActionFlag = true;
			if (!me.excuteActionFlag) {
				return;
			}
			setTimeout(() => {
				me.excuteActionFlag = false;
				param = _.extend(param || {}, {
					data: Object.assign({}, param ? param.data : {}),
					//保存任务后刷新列表页面
					success: function () {
						me.refreshSize();
					},
				});
				"data" in param ? "" : (param["data"] = {});
				param["data"] = Object.assign(param["data"], {
					project_id: me.opts.data["_id"],
					project_id__r: me.opts.data["name"],
				});
				List.prototype.excuteAction.call(this, action, param);
			}, 0);
		},
		options: _.extend({}, List.prototype.options, {
			// preSetButton: ['drag_row'],
		}),

		getColumns: function () {
			let columns = List.prototype.getColumns.apply(this, arguments);
			let task_ds = this.get('objectDescribe').fields;
			let task_type_opts = task_ds.record_type && task_ds.record_type.options;
			let stage_fields = this.stageValue && this.stageValue.objectDescribe.fields;
			let stage_type_opts = stage_fields && stage_fields.record_type && stage_fields.record_type.options;
			let stage_status_opts = stage_fields && stage_fields.biz_status && stage_fields.biz_status.options;
			_.each(columns, function (item) {
				if (item.api_name === 'name') {
					let field_name = '';
					item.render = function (data, type, full) {
						let taskNameConfig = function () {
							if (full.tasktype == "son") {
								return {
									class: "bg-task-son drag-child-task gl-task"
								};
							} else if (full.tasktype == "roottask") {
								return {
									class: "bg-task drag-task gl-task"
								};
							} else if (full.tasktype == "stage") {
								let name = $t('阶段');
								field_name = /^[\u4e00-\u9fa5]$/.test(name) ? name.charAt(0) : name.charAt(0).toUpperCase();
								return {
									class: "bg-task-milepost icon-stage crm-ui-title"
								};
							}
						};
						if (full.rowId === 123) {
							return '<div class="add-contianer" style="position:relative;width:-webkit-fill-available;"></div>';
						} else {
							return `<span class="tr-operate-wrap drag-row"></span><span class="icon-name ${taskNameConfig() ? taskNameConfig().class : ''
								}">${full.tasktype == "stage" ? field_name : ""}</span><span class="taskname">${data}</span><span class="icon-add icon-add-forname">+</span>`;
						}
					};
				}
				if (item.api_name === 'owner') {
					item.render = function (data, type, full) {
						const _id = full['_id'];
						const ownerObj = full[item.api_name] ? $.extend(true, {}, (FxUI.organization.getEmployeeById(full[item.api_name]) || {})) : {};
						return _id ? `<span title="${ownerObj.name}"><img class="avatar" src="${ownerObj.profileImage}"  alt="${ownerObj.name}" data-cardid="${ownerObj.id}"/>${ownerObj.name}</span>` : data;
					}
				}
				if (item.api_name === 'record_type') {
					item.render = function (data, type, full) {
						if (full._index === 0) return data;
						let record_type = full.record_type;
						let txt = util.setFieldStatus(full.tasktype === "stage" ? stage_type_opts : task_type_opts, record_type, 'label');
						return txt ? txt : `<span style="text-decoration: line-through">${$t('crm.选项已删除')}</span>`;
					};
				}
				if (item.api_name === 'biz_status') {
					item.render = function (data, type, full) {
						if (full._index === 0) return data;
						const statusObj = util.setFieldStatus(full.tasktype === "stage" ? stage_status_opts : item.options, full[item.api_name], 'color');
						const color = statusObj['font_color'] ? statusObj['font_color'] : 'var(--color-neutrals19)';
						return `<span class="field-status">
								<span class="bg-layer" style="background-color: ${color};opacity:0.1;"></span>
								<span class="text-layer" style="color: ${color};" title="${statusObj?.label}">${statusObj?.label}</span>
							</span>`;
					}
				}
			})
			return columns;
		},
		completeList() {
			this.set_dtmain_layout();
			this.set_actual_select();
			this.setDtRight();
			this.setFxTip();
			this.setArrowStyle();
			util.setMemberTextWidth(this.table.$el);
		},
		//获取后台配置
		getGanttConfig: function () {
			const _this = this;
			CRM.util.getConfigValues(['gantt_allow_update_date']).then((res) => {
				const data = (res || [])[0] || {};
				_this.gantt_allow_update_date = parseInt(data.value);
			}, (err) => {
				CRM.util.alert(err);
			});
		},
		addScroll(oldScrollL) {
			this._destroyScrollDiv();
			this.addMainScroll();
			this.addGanttScroll(oldScrollL);
		},
		setArrowStyle() {
			let wrapper = Array.from($(".gl-task"));
			_.each(wrapper, function (item, index) {
				if ($(item).prevAll(".treetable-leaf").css("display")) {
					$(item).css("padding", "0");
					if ($(item).hasClass("bg-task-son")) {
						$(item)
							.prevAll(".treetable-leaf")
							.css("margin-left", "3px");
					} else {
						$(item)
							.prevAll(".treetable-leaf")
							.css("margin-left", "2px");
					}
				}
			});
		},
		getOtherBtns() {
			let btnList = [];
			const btnType = this.options.btnType;
			let type = btnType ? btnType : this.getDefaultViewType();
			!type && (this.getDefaultViewType = function() {
				return "gantt";
			});
			btnList.push(
				{
					isDefault: type === "gantt" || !type,
					name: "gantt",
					text: $t("甘特视图"),
					attrs: 'data-type="gantt"',
					className: `j-btn-guide fx-icon-view-gantt os-gantt-btn ${type === "gantt" || !type ? " cur" : ""}`
				},
				{
					isDefault: type === "resource",
					name: "resource",
					text: $t("resource_view"),
					attrs: 'data-type="resource"',
					className: `j-btn-guide fx-icon-calendar os-resource-btn ${type === "resource" ? "cur" : ""}`
				},
				{
					isDefault: type === "kban",
					name: "kban",
					text: $t("看板视图"),
					attrs: 'data-type="kban"',
					className: `j-btn-guide fx-icon-jieduanshitu os-step-btn ${type === "kban" ? "cur" : ""}`
				}
			);
			this._viewBtns = btnList;
			return btnList;
		},
		//处理甘特图布局高度
		handleDtHeight() {
			const task_data = this._ganttTasks;
			let obj = {};
			let data = task_data.reduce((cur, next) => {
				obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
				return cur;
			}, []);
			return data;
		},
		//设置'.dt-right'布局
		setDtRight() {
			let len = 0;
			let columns = List.prototype.getColumns.apply(this, arguments);
			this.setTableWidthDrag(columns[0].width);
			const data = this.table.getCurData();
			const type = $(".crm-ui-title.j-btn-guide.cur").attr("data-type");
			const wrapper = this.$('.crm-table-pro-tasks');
			const main_box = wrapper.find('.dt-main-box');
			const $main = main_box.find('.dt-main');
			const $header_h = wrapper.find(".header.crm-clearfix").height();
			const $right = main_box.find(".dt-right");
			const $line = main_box.find(".dt-main-drag-line");
			data.length <= 1 && main_box.length && main_box.removeClass('dt-main-box-flex');
			data.length > 1 && main_box.addClass("dt-main-box-flex");
			const tr = main_box.find(".main.main-tr-handle .fix-start-b")
			const trs = $(tr[0]).find(".tr");
			trs.length && Array.from(trs).forEach(item => {
				if ($(item).css("display") !== "none") {
					len += 1;
				}
			})
			if (type === "gantt") {
				const tr_handle_h = len * 38 + 30;
				$main.height(tr_handle_h + $header_h);
				$main.find(".main.main-tr-handle").height(tr_handle_h);
				$right.height($main.height());
				$line.height($main.height());
				if (
					$(tr[0]).find(".tr")
						.length === 1
				) {
					$line.height(0);
					$right.height(0);
				}

			}
			wrapper.find(".dt-size").hide();
		},
		addScrollCommon($scrllEl, $el, $scrollDiv, oldScrollL, fw) {
			var noTrigger;
			var wh = $(window).height();
			var $mainScroll = $scrllEl === "ganttscroll" ? $el.find(".project-gantt-container") : $el.find(".main-scroll:first");
			var $ganttFixed = $(".gantt-fixed");
			var $ganttFixHeader = $(".gantt-fixed .gantt-header");
			var $page = $(
				$($(".main.main-tr-handle .fix-start-b")[0]).find(".tr")[
				$($(".main.main-tr-handle .fix-start-b")[0]).find(".tr")
					.length - 1
				]
			);
			if (!$page.length) return;
			if ($page.offset().top + 38 <= wh) {
				$('.project-gantt-container').css('overflow-x', 'auto');
				oldScrollL && $('.project-gantt-container').scrollLeft(oldScrollL);
				return;
			}
			$el.append($scrollDiv);
			$scrollDiv.scrollLeft(oldScrollL || $mainScroll.scrollLeft());
			$scrollDiv.on("scroll", function () {
				if (noTrigger) {
					noTrigger = null;
					return;
				}
				$mainScroll.scrollLeft($scrollDiv.scrollLeft());
				if ($ganttFixed.css("position") === "fixed") {
					$ganttFixHeader.css({ transform: `translateX(${-$scrollDiv.scrollLeft()}px)` });
				}
			});
			var fn = _.debounce(function () {
				noTrigger = true;
				var mainScrollLeft = $scrllEl === "ganttscroll" ? (oldScrollL || $mainScroll.scrollLeft()) : $mainScroll.scrollLeft();
				$scrollDiv.scrollLeft(mainScrollLeft);
				if ($ganttFixed.css("position") === "fixed") {
					$mainScroll.scrollLeft(mainScrollLeft);
					$ganttFixHeader.css({ transform: `translateX(${-mainScrollLeft}px)` });
				}else {
					$ganttFixHeader.css({ position: 'static', transform: 'none' });
				}
			}, 0);
			$mainScroll.on(`scroll.${$scrllEl}`, fn);
		
			$scrollDiv.$mainScroll = $mainScroll;
			this._scrollGanttDiv = $scrollDiv;
		},
		//动态添加模拟滚动条
		addMainScroll() {
			var $main = $(".projecttaskobj_box .dt-main");
			var fw = $main.find(".tb:first").width();
			var offset = $main.offset();
			var mw = $main.width();
			var $scollDiv = $(
				'<div class="crm-scroll fack-scroll table-scroll" style="position:fixed;left:' +
				offset.left +
				"px;bottom:0;width:" +
				mw +
				'px;overflow:auto;z-index:100"><div style="height:16px;width:' +
				fw +
				'px"></div></div>'
			);
			this.addScrollCommon('mainscroll', $main, $scollDiv, null, fw);
		},
		//甘特图滚动模拟条
		addGanttScroll(oldScrollL) {
			let $gantt = $(".projecttaskobj_box .projectGantt");
			var offset = $gantt.offset();
			var fw = $(".gantt-body").width();
			var mw = $gantt.width();
			if (mw == fw) return;
			var $scollGanttDiv = $(
				'<div class="crm-scroll fack-scroll gantt-scroll" style="position:fixed;left:' +
				offset?.left +
				"px;bottom:0px;width:" +
				mw +
				'px;overflow:auto;z-index:100"><div style="height:16px;width:' +
				fw +
				'px"></div></div>'
			);
			this.addScrollCommon('ganttscroll', $gantt, $scollGanttDiv, oldScrollL, fw);
		},
		//销毁滚动条
		_destroyScrollDiv: function () {
			const widget_div = [this._scrollDiv, this._scrollGanttDiv];
			const widget_el = [$('.fack-scroll.table-scroll'), $('.fack-scroll.gantt-scroll')];
			const scroll_name = ["scroll.listmain", "scroll.listgantt"];
			widget_div.forEach((item, index) => {
				item?.remove();
				widget_el[index]?.remove();
				item?.$mainScroll?.off(scroll_name[index]);
			});
		},
		refreshSize() {
			let type = $(".crm-ui-title.j-btn-guide.cur").attr("data-type");
			if (type === 'resource') return this.switchViewByType(type);
			this.fetchStageDescribe().then(() => {
				this.switchViewByType(type);
			});
		},
		bindResize(el, line, minWidth) {
			//初始化参数
			var els = el.style;
			//鼠标的 X 和 Y 轴坐标
			var x = 0;
			var y = 0;
			var _this = this;
			//邪恶的食指
			$(line).mousedown(function (e) {
				//按下元素后，计算当前鼠标与对象计算后的坐标
				(x = e.clientX - el.offsetWidth),
					(y = e.clientY - el.offsetHeight);
				//在支持 setCapture 做些东东
				el.setCapture //捕捉焦点
					? (el.setCapture(),
						//设置事件
						(line.onmousemove = function (ev) {
							mouseMove(ev || event);
						}),
						(line.onmouseup = mouseUp)) //绑定事件
					: $(document)
						.bind("mousemove", mouseMove)
						.bind("mouseup", mouseUp);
				//防止默认事件发生
				e.preventDefault();
			});
			//移动事件
			function mouseMove(e) {
				let arrow = $(".dt-main-drag-line .arrow");
				const box_width = _this.$el.width() * 0.9;
				//宇宙超级无敌运算中...
				if (e.clientX - x < minWidth || e.clientX - x > box_width) {
					return;
				}
				els.width = e.clientX - x + "px"; //改变宽度
				_this.addScroll();
				let mainWrapper = _this.table.getMainWrapper();
				let rec = mainWrapper[0].getBoundingClientRect();
				let dRec = document.body.getBoundingClientRect();
				if ($('.gantt-fixed').css('position') === 'fixed') {
					_this.table.fixedHeaderFn({
						left: mainWrapper.offset().left,
						right: dRec.right - rec.right,
						zIndex: 600
					});
					$('.gantt-fixed').css("left", $('.projectGantt').offset().left);
				}
				if (arrow.hasClass("rotate")) {
					arrow.removeClass("rotate");
				} else {
					return;
				}
			}
			//停止事件
			function mouseUp() {
				//在支持 releaseCapture 做些东东
				el.releaseCapture //释放焦点
					? (el.releaseCapture(),
						//移除事件
						(el.onmousemove = el.onmouseup = null)) //卸载事件
					: $(document)
						.unbind("mousemove", mouseMove)
						.unbind("mouseup", mouseUp);
				_this.addScroll();
				_this.storage(el.offsetWidth);
			}
		},
		//存储竖线的位置
		storage(width) {
			const key = `${CRM.ea}_${CRM.curEmpId}_line`
			localStorage.setItem(key, width);
		},
		//分割线初始化
		setTableWidthDrag(minWidth) {
			let me = this;
			let dtmain = $(".projecttaskobj_box .dt-main");
			let dtwidth = this.table.getCurData().length > 1 ? this.storageLineWidth ? `${this.storageLineWidth}px` : '500px' : 'auto';
			let line = $(
				'<div class="dt-main-drag-line"><div class="arrow"></div></div>'
			);
			this.setArrowPosition(line);
			dtmain.css("width", dtwidth);
			const table_line = $(".projecttaskobj_box .dt-main-drag-line").length;
			table_line && $(".projecttaskobj_box .dt-main-drag-line").remove();
			dtmain.append(line);

			line.on("click", ".arrow", function (e) {
				me.setTreeTableWidth(e);
			});
			dtmain.length && this.bindResize(dtmain[0], line[0], minWidth);
		},
		//拖拽箭头
		setArrowPosition(elment) {
			let arrowTop;
            // 获取视口的高度
            const viewport_height = window.innerHeight;
			const dtmain = $(".projecttaskobj_box .dt-main");
			// 获取 div 盒子顶部相对于视口的位置
            const main_top = dtmain[0].getBoundingClientRect().top;
			// 获取 div 盒子的高度
			const mainHeight = dtmain[0].getBoundingClientRect().height;
            // 计算 div 盒子顶部到视口底部的距离
            const distanceToViewportBottom = viewport_height - main_top;
			// 计算箭头的 top 位置
			if (mainHeight > distanceToViewportBottom) {
				// 如果 div 盒子的高度大于视口高度，使用距离视口底部的一半
				arrowTop = distanceToViewportBottom / 2;
			} else {
				// 如果 div 盒子的高度小于或等于视口高度，使用 div 盒子高度的一半
				arrowTop = mainHeight / 2;
			}
			elment.find('.arrow').css({'top': arrowTop + 'px','position': 'absolute'});
		},
		//分割线点击
		setTreeTableWidth(e) {
			let me = this;
			let arrow = $(e.target);
			let columns = me.getAllColumns();
			let dtmain = $(".projecttaskobj_box .dt-main");
			if (!arrow.hasClass("rotate")) {
				dtmain.css("width", columns[0].width + "px");
			} else {
				dtmain.css("width", "90%");
			}
			arrow.toggleClass("rotate");
			setTimeout(() => {
				me.addScroll();
				me.storage(dtmain.width())
			}, 0);
		},
		getStageQueryInfo: function () {
			return {
				object_describe_api_name: "ProjectStageObj",
				include_describe: true,
				include_layout: true,
				search_template_type: "default",
				ignore_scene_record_type: true,
				search_query_info: JSON.stringify({
					limit: 2000,
					offset: 0,
					filters: [
						{
							field_name: "project_id",
							field_values: this.opts.data._id,
							operator: "IN",
						}
					],
					orders: [{ fieldName: "index", isAsc: true }]
				}),
				pageSizeOption: [20, 50, 100],
			};
		},
		switchViewByType: function (type) {
			const _this = this;
			let needTable = type === "list" || type === "gantt";
			let table = this.table;
			this._destroyCurView(type);
			this._destroyListView(type);
			this.setElementTitle();
			//修改滚动条默认回滚
			$(".uipaas-running-layout-scroll").scrollTop(0);
			$(".uipaas-running-layout-scroll").css("overflow-x", "hidden");
			table.$el.toggleClass("crm-list-table__ohterview", !needTable).attr("view-type", type);
			if (needTable) {
				table.start();
				table.on("renderListComplete", function () {
					CRM.util.waiting(false);
					table.addRow();
					_this.completeList();
					_this.getFormatDataByListLayout();
				});
				$(".projecttaskobj_box .list-view__box").remove();
			} else {
				$(".crm-list-table__ohterview,.crm-list-table__ohterview .dt-main-box").css("cssText", "height:auto !important");
			}
			var $wrapper = $('<div class="list-view__box ' + type + '-box"></div>');
			this.$el.append($wrapper);
			if (type == "kban") {
				this.curView = this.renderKbanView($wrapper);
			} else if (type === "resource") {
				!$('.resource-btn').length && $(".projecttaskobj_box .dt-term-batch .batch-term").append(
					`<div class="crm-btn resource-btn term-filter-btn fx-icon-filter ${type !== 'resource' ? 'hide' : ''}">${$t('resource.setup')}</div>`
				);
				this.renderResourceView($wrapper);
			};
			$(".gantt-date-mode-wrapper").toggle(type === "gantt");
			this.curView && (this.curView.__$wrapper = $wrapper);
		},
		//切换图例
		changeLegend(e) {
			e.stopPropagation();
			this.$('.btn-legend').find('.gantt-badge').hide();
			$(".dt-term-legend").toggle();
			!$(".le-wrapper").length && FxUI.create({
				wrapper: $(".dt-term-legend")[0],
				template: `<div class="le-wrapper">
				<h3 class="le-title">${$t('图例')}</h3>
				<div class="content">
					<fx-row>
						<fx-col :span="4"><div class="col-content"></div></fx-col>
						 <fx-col :span="4" v-for="(item,index) in status"><div class="col-content">{{item.label}}</div></fx-col>
					</fx-row>
					<fx-row>
						 <fx-col :span="4"><div class="col-content" style="align-items: start;">${$t('计划')}${$t('项目阶段')}</div></fx-col>
						 <fx-col :span="4" v-for="(item,index) in status"><div class="col-content"><i :class="['img', item.value, 'plan']"></i></div></fx-col>
					</fx-row>
					<fx-row>
						 <fx-col :span="4"><div class="col-content" style="align-items: start;">${$t('实际')}${$t('项目阶段')}</div></fx-col>
						 <fx-col :span="4" v-for="(item,index) in status"><div class="col-content"><i :class="['img', item.value]"></i></div></fx-col>
					</fx-row>
					<fx-row v-for="n in 4" :class="['line', 'milepost_'+n]">
						<fx-col :span="4">
							<div class="col-content flex">
								<span>{{labels[n-1]}}</span>
								<span>{{subLabels[n-1]}}</span>
							</div>
						</fx-col>
						<fx-col :span="4" v-for="(item,index) in status">
							<div class="col-content">
								<i :class="cellClass(index)"><b :class="statusIcon(item.value, index)">{{index <= 1 ? index * 30 + '%' : ''}}</b>
							</div>
						</fx-col>
					</fx-row>
					<fx-row>
						<fx-col :span="4" style="height:220px;"><div class="col-content" style="border-bottom: none;align-items: start;">${$t('依赖关系')}</div></fx-col>
						<fx-col :span="20" class="dependency">
							<fx-row  v-for="rowIndex in 2">
								<fx-col :span="12" v-for="colIndex in 2">
									<div class="main">
										<span :class="dependencyStatusIcon(rowIndex, colIndex)"></span>
										<span class="info">{{dependencyStatusIcon(rowIndex, colIndex,'label')}}</span>
									</div>
								</fx-col>
							</fx-row>
						</fx-col>
					</fx-row>
				</div>
				
				</div>`,
				data() {
					return {
						labels: [$t('task_plan_date'), $t('task_actual_date'), $t('task_milestone_plan_date'), $t('task_milestone_actual_date')],
						subLabels: [$t('only_the_plann_date'), $t('only_the_actual_date'), $t('only_the_milestone_plann_date'), $t('only_the_milestone_actual_date')],
						dependencyStatus: [$t('结束后开始'), $t('结束后结束'), $t('开始后开始'), $t('开始后结束')],
						dependencyIcons: ['end_of_start', 'end_of_end', 'start_of_start', 'start_of_end'],
						status: [
							{
								label: $t('未开始'),
								value: 'unstart'
							},
							{
								label: $t('进行中'),
								value: 'going'
							},
							{
								label: $t('已完成'),
								value: 'complete'
							},
							{
								label: $t('已暂停'),
								value: 'pause'
							},
							{
								label: $t('已取消'),
								value: 'cancle'
							},
						]
					}
				},
				methods: {
					cellClass(index) {
						let additionalClass = '';

						if (index === 2) {
							additionalClass = 'all';
						} else if (index !== 0 && index !== 2) {
							additionalClass = 'yellow';
						}

						return ['rect', additionalClass];
					},
					statusIcon(value, index) {
						if (index <= 1) return '';
						return `icon ${value}_icon`;
					},
					dependencyStatusIcon(rowIndex, colIndex, label) {
						let statusClass = '', dependencyLabel = '';
						if (rowIndex === 2) {
							statusClass = this.dependencyIcons[colIndex + 1];
							dependencyLabel = this.dependencyStatus[colIndex + 1];
						} else {
							statusClass = this.dependencyIcons[colIndex - 1];
							dependencyLabel = this.dependencyStatus[colIndex - 1];
						}
						return label ? dependencyLabel : `icon ${statusClass}`;
					}
				}
			})
			$(document).on("click", function (e) {
				$(".dt-term-legend").hide();
			});
		},
		//切换全屏
		toggleScreen() {
			let $screen = this.$('.btn-fullscreen');
			const $screenTip = $('.crm-ui-title-tip');
			$screenTip.length && $('.crm-ui-title-tip').remove();
			$screen.toggleClass('exit');
			if (!$screen.hasClass('exit') && this.fulldialog) {
				$(this).html(
					`<i class="fx-icon-fullscreen screen"></i>`
				);
				$screen.attr('data-title', $t('全屏显示'));
				this._removeFullEl();
			} else {
				$(this).html(
					`<i class="fx-icon-fullscreen-exit screen"></i>`
				);
				$screen.attr('data-title', $t('退出全屏'));
				this.$fullEl = this.$el.closest('.uipaas-running-layout-wrapper');
				this.$parent = $(this.$el.closest('.uipaas-running-layout-wrapper')).parent();
				this.fulldialog = CRM.util.fullDialog({
					el: this.$fullEl,
					className: "crm-pr-fullscreenlayer crm-d-layout"
				});
				this.isFull = true;
			}
			//重置滚动条位置
			this.addScroll();
			//重置看板
			this.getDefaultViewType() === "kban" && this.curView.resize();
		},
		//销毁视图
		_destroyListView(type) {
			this.table.doStaticData([]); //把表格数据清空
			// this.table.closeStart();
			this.ganttview = null; //清空甘特视图容器
			this.resource = null; //清空资源视图容器
			this.selectPlanActual = null; //清空计划、实际视图容器
			this.$('.dt-plan-actual-btn').length && this.$('.dt-plan-actual-btn').remove();
			this.$('.resource-btn').length && this.$('.resource-btn').remove();

			_.each($(".projecttaskobj_box .list-view__box"), function (item) {
				if (item.className.indexOf(`${type}-box`)) {
					item.remove();
				}
			});
			$(".dt-main-box .dt-right").html("");
			$(".dt-main-box").removeClass("dt-main-box-flex");
			$(".dt-main-box .dt-main").css({width: 'auto',height:0});
		},
		//渲染资源视图
		renderResourceView($el) {
			const _this = this;
			this.resource = new Resource({
				wrapper: $el,
				autoHeight: true,
				isListLayout: true,
				projecttaskobj: _this
			});
			this.resource.render(["ProjectResourceObj"]);
		},
		//获取布局数据
		getFormatDataByListLayout() {
			if (!this.isHasStageData) return;
			const _this = this;
			this.addStageTip();
			CRM.util.getFormatDataByListLayout(
				_this.stageDatas,
				_this.stageValue,
				function (stageList) {
					_this.stageList = stageList;
					CRM.util.getFormatDataByListLayout(
						_this._listData.dataList,
						_this._listData,
						function (taskList) {
							_this.taskList = taskList;
							_this.initGanttView(_this.$('.project-gantt-container'),_this.initGanttTasks(),(ganttview) => {
								ganttview.set_scroll_position(new Date().getTime());
								//记忆收起
								if (_this.collaseStatus && _this.collaseStatus[_this.options.data._id] === 2) {
									_this.ifFold = false;
									_this.initGanttView(_this.$('.project-gantt-container'),_this.filteGanttData());
								} else {
									_this.ifFold = true;
								}
								_this.addScroll();
								_this.ganttview.update_view_plan_actual(_this.storagePlanActual || 'plan_actual');
							});
						}
					);
				}
			);
		},
		initGanttView(containerSelector, dataProvider, callback) {
			const _this = this;
			GanttView(containerSelector, dataProvider, {
			  taskList: this.taskList,
			  stageList: this.stageList,
			  projectTask: this,
			})
			.then(ganttview => {
				_this.ganttview = ganttview;
				callback && callback(ganttview);
			});
		},
		fetchStageDescribe() {
			const _this = this;
			this._fetchStageDescribeIng = new Promise((resolve, reject) => {
				util.getDataList(this.getStageQueryInfo()).then(
					(res) => {
						if (res.Result.StatusCode === 0) {
							//缓存项目阶段信息
							_this.isHasStageData = true;
							console.log(res.Value);
							let lists = res.Value.dataList;
							lists = lists.map((item) => {
								item.rowId = item._id;
								item.stage_id = item._id;
								item.stage_id__r = item.name;
								item.tasktype = "stage";
								item.unstarted = 0;
								item.ongoing = 0;
								item.completed = 0;
								return item;
							});
							_this.stageDatas = lists;
							_this.stageValue = res.Value;
							resolve();
						} else {
							_this.isHasStageData = false;
							_this.stageDatas = [];
							let msg = res.Result.FailureMessage;
							CRM.util.waiting(false);
							FxUI ? FxUI.Message({
								isMiddler: true,
								duration: 2000,
								message: msg,
								type: 'error'
							}) : CRM.util.remind(3, msg);
						}
					}
				);
			});

			return Promise.resolve(this._fetchStageDescribeIng);
		},
		//设置gantt数据
		setGanttData(item) {
			let start = item.plan_start_date;
			let end = item.plan_end_date;
			let percentage = Number(item.percentage_complete) || 0;
			let obj = this.actualPlanHandle(item);
			return {
				id: item._id,
				name: item.name,
				start: start ? FS.moment.unix(start / 1000).format("YYYY-MM-DD") : '',
				end: end ? FS.moment.unix(end / 1000).format("YYYY-MM-DD") : '',
				progress: percentage,
				priority: item.priority,
				biz_status: item.biz_status,
				actual_start_date: item.actual_start_date ? FS.moment.unix(item.actual_start_date / 1000).format("YYYY-MM-DD") : '',
				actual_end_date: item.actual_end_date ? FS.moment.unix(item.actual_end_date / 1000).format("YYYY-MM-DD") : '',
				dependencies:
					Object.keys(item.pre_task_id_depend_type_map || {}) || [],
				dependenciesType:
					Object.values(item.pre_task_id_depend_type_map || {}) || [],
				tasktype: item.tasktype,
				_has_actual_bar: !!item._has_actual_bar,
				_is_actual_bar: !!item._is_actual_bar,
				is_milestone: item.is_milestone,
				isActualEq: obj && obj.isActualEq,
				isPlanEq: obj && obj.isPlanEq
			};
		},
		// 处理日期
		actualPlanHandle(item) {
			let isActualEq, isPlanEq;
			if (!item.tasktype || item.tasktype === 'stage') return;
			if (item.actual_start_date && item.actual_end_date) {
				isActualEq = this.isSameDay(item.actual_start_date, item.actual_end_date) ? true : false;
			} else {
				isActualEq = (!item.actual_start_date && !item.actual_end_date) ? false : true;
			};
			if (item.plan_start_date && item.plan_end_date) {
				isPlanEq = this.isSameDay(item.plan_start_date, item.plan_end_date) ? true : false;
			} else {
				isPlanEq = (!item.plan_start_date && !item.plan_end_date) ? false : true;
			};
			return {
				isActualEq,
				isPlanEq
			}
		},
		//判断日期是否处在同一天
		isSameDay(startTime, endTime) {
			const startTimeMs = new Date(startTime).setHours(0, 0, 0, 0);
			const endTimeMs = new Date(endTime).setHours(0, 0, 0, 0);
			return startTimeMs === endTimeMs ? true : false
		},
		//treetable展开合起 后 甘特图数据处理
		filteGanttData() {
			const { ifFold } = this;
			let me = this;
			let d = CRM.util.parseTreeToNormal(this.table.getCurData());
			let g = [];
			if (ifFold) {
				//展开行时执行
				return this.initGanttTasks(d);
			}
			//收起行时执行
			d.forEach(function (item, index) {
				if (item["isShow"]) {
					if (index === 0) {
						g.push({
							name: item.name,
						});
					} else {
						g.push(me.setGanttData(item));
					}
				}
			});
			me._ganttTasks = [...g];

			// me.setDtRight(62 + g.length * 38 + "px");
			me.setDtRight();
			return g;
		},

		getStepOptions() {
			var me = this;
			return {
				options: me.options,
				// stageField: Columns,
				table: me.table,
				parseParam: function () {
					return JSON.parse(
						JSON.stringify(me.parseParam(me.table.getParam()))
					);
				},
			};
		},
		renderKbanView: function ($wrapper) {
			let me = this;
			return new KbanView(
				_.extend(
					{
						el: $wrapper,
						projecttaskobj: me,
					},
					me.getStepOptions()
				)
			);
		},

		//初始化甘特图所需任务数据
		initGanttTasks(data) {
			const me = this;
			let tasks = [];
			let curData = data ? data : CRM.util.parseTreeToNormal(this.table.getCurData());

			curData = this.addTaskData(curData);
			if (data) {
				curData.forEach((item) => {
					if (item["isShow"]) {
						if (item._id) {
							tasks.push(me.setGanttData(item));
						} else {
							tasks.push({
								name: item.name,
								...{
									isEmpty: item.isEmpty || false
								}
							});
						}
					}
				});
			} else {
				tasks = curData.map((item) => {
					if (item._id) {
						return me.setGanttData(item);
					} else {
						return {
							name: item.name,
							...{
								isEmpty: item.isEmpty || false
							}
						};
					}
				});
			}
			this._ganttTasks = [...tasks];
			return tasks;
		},

		//返回项目列表
		goBackProObj() {
			this.opts.projectobj.displayTable();
			$(".projecttaskobj_box").html("").hide();
			$(".dt-page").show();
			this.fulldialog && this._removeFullEl();
			this.destroy();
		},
		trclickHandle: function (data, $tr, $target, a, noRealllyClick) {
			let me = this;
			const $tip = $(".el-popover.el-popper.add-tip");
			if ($target.hasClass('j-tootip')) {
				return;
			} else {
				$tip.hide();
			}

			if (this.table._tbLoopUpDetail) {
				this.table._tbLoopUpDetail.destroy();
				this.table._tbLoopUpDetail = null;
			}
			if ($target.attr("class").indexOf("icon-add-forname") > -1) {
				//埋点
				CRM.util.sendLog(this.options.apiname, 'list', {
					operationId: 'addLineTask',
					eventType: 'cl'
				});
				let parma = {
					data: {
						stage_id: data.stage_id,
						stage_id__r: data.stage_id__r,
					},
					show_type: 'dialog'
				};
				if (data.tasktype !== "stage") {
					parma.data["parent_task_id"] = data["_id"];
					parma.data["parent_task_id__r"] = data["name"];
				}
				this.excuteAction("add", parma);
			}

			if ($target.attr("class").indexOf("icon-collase") > -1) {
				this.changeCollase();
			}
			if ($target.attr("class").indexOf("task-drag") > -1) {
				this.dragTaskFun();
			}
			if (
				!$target.parents(".add-contianer").length &&
				$target.attr("class").indexOf("icon-add-forname") <= -1
			) {
				noRealllyClick || me.showStageDetail(data);
			}
			//控制阶段任务弹框的层级
			if ($("body > .el-popover.el-popper.add-tip").length) {
				$tip.appendTo(".crm-con");
			}
			me.setDtRight();
		},
		//折叠/展开
		changeCollase() {
			const _this = this;
			const old_scrollL = $('.project-gantt-container').scrollLeft();
			this.isCollase = true;
			if ($(".icon-collase").attr('class').indexOf("fx-icon-list-expand") > -1) {
				$(".icon-collase").removeClass('fx-icon-list-expand').addClass('fx-icon-list-collapse');
				this.isCollase = this.ifFold = false;
			} else {
				$(".icon-collase").removeClass('fx-icon-list-collapse').addClass('fx-icon-list-expand');
				this.isCollase = this.ifFold = true;
			}
			this.table.setAllChildrenIsShow(this.isCollase);
			util.setMemberTextWidth(this.table.$el);
			this.collaseStatus[this.options.data._id] = this.isCollase ? 1 : 2;
			localStorage.setItem('collaseStatus', JSON.stringify(this.collaseStatus));
			this.initGanttView(this.$('.project-gantt-container'),this.filteGanttData(),(ganttview) => {
				_this.addScroll(old_scrollL);
			});
		},
		
		//显示阶段详情
		showStageDetail: function (data) {
			let _this = this;
			this.wrapper_scroll_top = $('.uipaas-running-layout-scroll').scrollTop();
			const obj = (typeof data === 'object' && 'type' in data && 'target' in data) ? this.opts.data : data;
			CRM.api.show_crm_detail({
				type: obj.object_describe_api_name,
				data: {
					crmId: obj._id,
				},
				callback: () => {
					_this.refreshSize();
					return;
				}
			});
		},
		//添加阶段提示框
		addStageTip() {
			let me = this;
			let text = me.stageValue.objectDescribe.display_name;
			let $taskMilpost = $(".bg-task-milepost");
			let wrapper = Array.from($taskMilpost);
			_.each(wrapper, function (item) {
				$(item).attr({
					"data-title": text,
					"data-pos": "top",
				});
			});
		},
		//维度显示/隐藏
		changeMode() {
			this.$('.resource-box').find('.dt-term-batch').toggleClass('hide');
		},
		//任务拖拽组件
		dragTaskFun() {
			const _this = this;
			$('.protask-drag-wrapper').length && $('.protask-drag-wrapper').remove();
			let treeData = this.getTableAllData().filter(item => item._id);
			const isFixed = $('.dt-main .fix-add').length;
			let lists = CRM.util.parseTreeToNormal(treeData).map(item => {
				item.id = item._id;
				item.showChild = true;
				return item;
			});
			const main_t = isFixed ? $('.dt-main .fix-add').offset().top : $('.dt-main-box').offset().top;
			const main_l = $('.dt-main-box').offset().left;
			const main_w = this.getAllColumns() && this.getAllColumns()[0].width || 274;
			const body_h = $(document.body).outerHeight(true);
			require.async('vcrm/sdk', function (SDK) {
				SDK.getComponent('ProjectTaskDrag').then((Comp) => {
					const tree = new Vue({
						render: h => h(Comp.default, {
							props: {
								projectId: _this.opts.data["_id"],
								data: lists,
								styles: {
									left: main_l + 'px',
									top: main_t + 'px',
									width: main_w + 'px',
									height: (body_h - main_t) + 'px',
									zIndex: CRM.util.getzIndex(FxUI.Utils.getPopupZIndex() || 3000, true)
								}
							},
							on: {
								refresh: _this.refreshSize.bind(_this)
							}
						})
					}).$mount();
					$("body").append(tree.$el);
				});
			});
		},
		//快速添加弹框
		setFxTip(wrapper) {
			let me = this;
			let isCollase = this.collaseStatus && Object.keys(this.collaseStatus).includes(this.options.data._id);
			let status = this.collaseStatus[this.options.data._id];
			let statusClass = status === 1 ? 'fx-icon-list-expand' : 'fx-icon-list-collapse';
			const elment = wrapper || $(".add-contianer");
			this.params_tip = {
				wrapper: elment[0],
				template: ` <fx-popover
			    placement="bottom-start"
			    width="132"
                ref="addTaskPopover"
                popper-class="add-tip"
                :visible-arrow="false"
			    trigger="click">
                <div><span @click="addStage">{{stageName}}</span><span @click="addTask">{{taskName}}</span></div>
                <p class="tip-btn add-tip-btn" slot="reference">
                    <span class="icon-add j-tootip">+</span>
                    <span class="add-stage-task j-tootip">${$t('快速添加')}</span>
                </p>
			  </fx-popover>`,
				data() {
					return {
						taskName:
							$t("添加") + (me._listData.objectDescribe.display_name || $t("任务")),
						stageName:
							$t("添加") + (me.stageDataList ? me.stageDataList.objectDescribe.display_name : $t("项目阶段"))
					};
				},
				methods: {
					//新建任务
					addTask() {
						//埋点
						CRM.util.sendLog(me.options.apiname, 'list', {
							operationId: 'addTask',
							eventType: 'cl',
						});
						me.excuteAction("add", { show_type: 'dialog' });
						$(".add-tip").hide();
					},
					//新建项目阶段
					addStage() {
						//埋点
						CRM.util.sendLog(me.options.apiname, 'list', {
							operationId: 'addSatge',
							eventType: 'cl',
						});
						CRM.api.add({
							apiname: "ProjectStageObj",
							displayName: $t("项目阶段"),
							show_type: 'dialog',
							data: {
								project_id: me.opts.data["_id"],
								project_id__r: me.opts.data["name"],
							},
							success: () => {
								me.refreshSize();
							},
							error: () => { },
						});
						$(".add-tip").hide();
					}
				},
			};
			FxUI.create(this.params_tip);
			!wrapper && $(".add-contianer").parents('td.td-name').css('padding-left', 0);
			elment.append(`<img class="task-drag" src="${FS.CRM_MODULE.ASSETS_PATH}/images/drag.svg"></img>`);
			elment.append(`<span class="${isCollase ? statusClass : 'fx-icon-list-expand'} icon-collase"></span>`);
			this.collaseStatus && isCollase && !wrapper && this.table.setAllChildrenIsShow(status === 1 ? true : false);
		},
		_renderTable: function () {
			var me = this;
			me.getTable(function (Table) {
				if (!Table) {
					return;
				}
				me.table && me.table.destroy();
				var options = me.getOptions() || {};
				Table.prototype.events = _.extend(Table.prototype.events, {
					"click .dt-main-box .dt-main .j-th-filter":
						"_onColumnFilter", // 列筛选
				});

				me.table = new Table(
					_.extend(
						{
							isMyObject: true, //默认都是自定义对象
							noAllowedWrap: true,
							editable: me.get("isEdit"),
							newStyle: true,
							getLineSelector: function () {
								return ">.dt-main-box .dt-main .resize-line";
							}
						},
						options
					)
				);
				//重置表格高度
				me.table.on("resize", () => {
					const view_type = me.getDefaultViewType() && me.getDefaultViewType();
					if(view_type !== 'gantt') return;
					const add_L = $('.add-contianer').length && $('.add-contianer').offset().left;
					$(".el-popover.el-popper.add-tip").css(
						"left",
						`${add_L}px`
					);
					me.setDtRight();
					!me.wrapper_scroll_top && me._toggerFix(true);
					//预置滚动位置
					me.wrapper_scroll_top && $('.uipaas-running-layout-scroll').scrollTop(me.wrapper_scroll_top);
				});
				me.table.getMainWrapper = function () {
					return this.$(".dt-main");
				};
				me.table.getTermBatchWrapper = function () {
					return this.$('.dt-term-batch');
				},
				me.table.fixedHeaderFn = function () {
					Table.prototype.fixedHeader.apply(this, arguments);
				};
				me.table.unlockColumn = function (name) {
					if (name === "name") {
						return;
					}
					return Table.prototype.unlockColumn.apply(this, arguments);
				};
				me._proxyTableFn(me.table);
				me.bindTableEvents(me.table);
				me.copyDtEvents(me.table);
				me.extendTableEvents(me.table);
			});
		},
		setElementTitle() {
			let len = $(".projecttaskobj_box .dt-term-batch .batch-term .btn-fullscreen").length;
			let pro_detail = $(".projecttaskobj_box .dt-term-batch .batch-term .pro-btn").length;
			//创建项目select、返回按钮
			if (!$(".projecttaskobj_box .page-task-back").length) {
				$(".projecttaskobj_box .dt-tit").html(this.opts.data.name);
				$(".projecttaskobj_box .dt-caption").prepend(
					`<div class="page-task-back"><i class="p-unicode">&#xe62e</i><span>${$t('返回')}</span></div>`
				);
			}

			if (!len) {
				$(".projecttaskobj_box .dt-term-batch .batch-term .j-dt-icon-refresh").before(
					`<span class="btn-fullscreen crm-ui-title" data-title="${$t('全屏显示')}" data-pos="top">
                    	<i class="fx-icon-fullscreen screen"></i>
                    </span>
                    <span class="btn-legend crm-ui-title j-btn-legend" data-title="${$t('图例')}" data-pos="top"><i class="gantt-badge">.</i></span>`
				);
				$(".projecttaskobj_box").find(".crm-table-open.crm-widget").append('<div class="dt-term-legend"></div>');
			}
			!pro_detail && $(".projecttaskobj_box .dt-term-batch .batch-term").append(
				`<div class="item crm-btn pro-btn">${$t('项目详情')}</div>`
			);
		},
		//移除全屏
		_removeFullEl() {
			let container = $('.crm-p-projectobj').find('.uicustom-running-object-list-container');
			container.append(this.$fullEl);
			this.fulldialog.destroy && this.fulldialog.destroy();
			this.fulldialog = null;
			this.isFull = false;
		},
		tdHoverHandle: function (e, getData) {
			if (!getData().data) {
				return;
			}
			List.prototype.tdHoverHandle.apply(this, arguments);
		},
        editFun(obj) {
            let version = Number(obj.version) + 1;
            return new Promise((resolve,reject) => {
                CRM.util.FHHApi(
                    {
                        url: "/EM1HNCRM/API/v1/object/ProjectTaskObj/action/Edit",
                        data: {
                            object_data: Object.assign(obj,{version: String(version)}),
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                //可以拖拽
                                resolve()
                            } else {
                                CRM.util.alert(res.Result.FailureMessage);
                                CRM.util.waiting(false);
                                reject();
                                return;
                            }
                        },
                        complete: function () {
                            CRM.util.waiting(false);
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            })
        },
		//处理已拖拽的数据排序组装参数
		setDataSort(data, newObj) {
			let me = this;
			for (let [index, item] of new Map(
				data.map((item, index) => [index, item])
			)) {
				// if (item._level !== 0) {
				// 	newObj[item._id] = index;
				// }
				newObj[item._id] = index;
				if (item.children && item.children.length > 0) {
					me.setDataSort(item.children, newObj);
				}
			}
			return newObj;
		},
		//监听纵向滚动事件
		layoutTaskScroll(e) {
			const type = $(".crm-ui-title.j-btn-guide.cur").attr("data-type");
			$('.el-popover.add-tip').length && $('.el-popover.add-tip').remove();
			//吸顶处理
			type === 'gantt' && this._toggerFix();
			type === 'resource' && this.resource.layoutScroll(e);
		},
		_toggerFix(resize) {
			if (!this.table) return;
			let mainWrapper = this.table.getMainWrapper();
			let offset = mainWrapper.offset();
			let rec = mainWrapper[0].getBoundingClientRect();
			let dRec = document.body.getBoundingClientRect();
			let $h = this.isFull ? 0 : window.Fx && window.Fx.theme == 'new' ? 41 : 56;//header高度
			let $hGantt = $('.gantt-fixed');
			if (offset.top < $h - 1) {
				if (!this._fixHeader || !this._fixGanttHeader) {
					!this._fixHeader && mainWrapper.prepend(this._fixHeader = $('<div style="height:62px"></div>'));
					$('.project-gantt-container').prepend(this._fixGanttHeader = $('<div style="height:62px;width:100%;"></div>'));
					this.table.fixedHeaderFn({
						top: $h,
						left: offset.left,
						right: dRec.right - rec.right,
						zIndex: 600
					});
				}
				if (resize) {
					this.table.fixedHeaderFn({
						top: $h,
						left: offset.left,
						right: dRec.right - rec.right,
						zIndex: 600
					});
				}
				$hGantt.css({ position: 'fixed', top: $h, overflowX: 'hidden', width: $('.projectGantt').width() + 'px' });
				$('.gantt-header').css({ position: 'absolute', width: $('.project-task').width() });
				!$('.fix-add').length && $('.dt-main-box .dt-main').append('<div class="fix-add" style="width:' + (this.getAllColumns()[0].width + 2) + 'px;left:' + offset.left + 'px;top:' + ($h + 60) + 'px;"></div>');
				!$('.fix-add .add-stage-task').length && this.setFxTip($('.fix-add'));
				resize && $('.fix-add').css('left', offset.left + 'px');
				$('.dt-main-drag-line .arrow').css({'position': 'sticky', 'top': '50%'});
			} else if (this._fixHeader && this._fixHeader.offset().top > $h - 2) {
				this.table.fixedHeaderFn();
				this._fixHeader.remove();
				this._fixGanttHeader.remove();
				this._fixHeader = null;
				this._fixGanttHeader = null;
				$('.fix-add').length && $('.fix-add').remove();
				$hGantt.css({ position: 'initial', overflowX: 'visible', top: 'auto'});
				$('.gantt-header').css({ position: 'initial', width: $('.project-task').width(), transform: 'none'});
				//拖拽箭头
				this.setArrowPosition($('.dt-main-drag-line'));
			}
			this.addScroll();
		}
	});
});
