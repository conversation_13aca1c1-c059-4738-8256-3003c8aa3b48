define(function (require, exports, module) {
    module.exports = {
        async render() {
            if (this.isOpenAttribute()) {
                try {
                    let attributes = await CRM.util.getAttributeByCategory('999999'); // 全部
                    let conditions = attributes.reduce((arr, item) => {
                        let {attribute, attributeValues} = item;
                        let defalut_checked = attributeValues.reduce((res, item) => {
                            item['selected_flag'] && res.push(item._id);
                            return res;
                        }, []);
                        defalut_checked.length && arr.push({
                            field_name: `attribute${attribute.field_num}`,
                            field_values: defalut_checked,
                            operator: 'HASANYOF'
                        })
                        return arr
                    }, [])
                    this._attrConditions = conditions;
                } catch (e) {
                    console.error(e);
                }
            }
        },
    }
})