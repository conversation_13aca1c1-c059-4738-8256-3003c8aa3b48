/**
 * @description 在线客服数据列表
 */
define(function (require, exports, module) {
	const MyObject = require('../list/list');
	const proto = MyObject.prototype;
	const util = CRM.util
	const CustServSessionUtil = require('./utils/utils')

	var Table = require('crm-assets/widget/table/table')  //crm-modules/components/objecttable/objecttable
	var TermBatch = require('crm-assets/widget/table/termbatch/termbatch')
	var Filter = require('crm-assets/widget/table/termbatch/filter/filter')
	var Condition = require('crm-assets/widget/table/condition/condition')

	var _conditionHandle
	var filterHandle

	var FilterExt = Filter.extend({

		// 不支持或关系
		//
		_initCondition: function () {
			var me = this;
			me._condition = new Condition({
				el: me.$('.conditon-box'),
				columns: me.options.columns,
				zIndex: me.get('zIndex') * 1 + 10,
				data: me._filterData,
				model: me.model
			});
			me._condition.on('key.enter', function () {
				me._onFilter();
			});
			me._condition.on('del', function (len) {
				me.$('.j-reset').toggleClass('b-g-btn-disabled', len == 0);
			});
			_conditionHandle = me._condition
		},

		_onFilter: function (e) {
			var me = this;
			if (!me._condition) return;
			var data = me._condition.getValue();
			if (me._condition.isValid(data, { noValidRequire: true })) {
				me._filterData = data;
				me._filterAloneValues = me.getAloneValues();
				me.trigger('filter', data, me._filterAloneValues);
				me.hide(null, true);
				me.model && (me.model.updateCacheFilterDatas(data), me.model.updateOutFilterValue());
				me.setTargetNum(data);
			}

			me.model && me.model.log('Sift');
		},
		_customFilter: function (external_account) {
			var me = this;
			me._condition && me._condition.clear()
			var data = [
				{
					"FieldName": "external_account",
					"FieldType": 1,
					"Comparison": 1,
					"FilterDetailID": "external_account",
					"FilterValue": external_account,
					"IsSys": false,
					"returnType": "",
					_id: `cache_${Math.random().toFixed(3) * 1000}`
				}
			]
			me._filterData = data;
			me._filterAloneValues = me.getAloneValues();
			me.trigger('filter', data, me._filterAloneValues);
			me.model && (me.model.updateCacheFilterDatas(data), me.model.updateOutFilterValue());
			me.setTargetNum(data);
			me.model && me.model.log('Sift');
		}
	})

	var TermBatchExt = TermBatch.extend({
		_initFilter: function () {
			var me = this, opts = me.options;
			var curTerm = me._term ? me._term.getCurTerm() : null;
			if (opts.showFilerBtn) {
				let FilterClass = opts.FilterClass ? opts.FilterClass(FilterExt) : FilterExt;
				me.filter = new FilterClass({
					$table: opts.$table,
					tableContext: opts.tableContext,
					filterOr: opts.filterOr,
					$target: me.$('.j-filter-btn'),
					trigger: 'click',
					stopPropagation: false,
					pos: 'bottom left',
					filterAloneColumns: opts.filterAloneColumns,
					filterDatas: opts.filterDatas,
					columns: opts.filterColumns || opts.columns || [],
					tableColumns: opts.columns || [], // 当前表格的列
					orderColumn: opts.orderColumn,
					addOrderColumn: opts.addOrderColumn,
					zIndex: me.getTermBatchIndex(),
					searchTerm: opts.searchTerm,
					model: me.model,
					hideAloneFilter: opts.hideAloneFilter,
					supportMultiFieldSort: opts.supportMultiFieldSort,
					aloneValueChangeCallBack: opts.aloneValueChangeCallBack,
				});
				me.filter.on('filter', function (data, aloneValues) {
					me.model.set('cacheFilterDatas', data)
					me.model.cacheFilter(data, curTerm);
					if (me.outFilter) {
						let sf = me.outFilter.getSysFilter();
						if (sf.length) {
							[].unshift.apply(data, sf);
						}
					}
					var td = _.filter(data, function (a) {
						return me.model.validateFilter(a);
						// return a.FilterValue !== '' || (a.Comparison == 9 || a.Comparison == 10);
					})
					me.trigger('term.advance', td, aloneValues, true);
					me.triggerOutFilterHeightChange();
					me.updateRecordType(aloneValues && aloneValues.record_type);
				});
				me.filter.on('term.change', function (id, term) {
					me._term && me._term.changeTerm(term);
				});
				me.filter.on('show', function () {
					me.trigger('filter.show');
				});
				me.filter.on('cleancache', function (term) {
					term && me.model.cleanCacheFilter(term.id);
				})
				if (curTerm) {
					me.filter.setTerm(curTerm);
					if (!opts.filterDatas) { // 不存在默认数据的时候
						me.changeFilterNum(curTerm);
					}
				}
				if (opts.filterDatas && opts.filterDatas.filters) {
					me.filter.setFilterData(opts.filterDatas.filters);
				}

				filterHandle = me.filter
			}
		},
	})

	var TableExt = Table.extend({
		_initDtTermBatch: function () {
			if (!this.options.showTermBatch) {
				return
			}
			var me = this,
				opts = me.options;
			me._termBatch = new TermBatchExt(this.__getDtTermBatchOpts());
			me._termBatch.on('term.change', function (id, term) { // 切换检索项
				me.filterStatus = false;
				// 保证先trigger
				me.trigger('term.change', id, term);
				if (!term.isNotRequest) {
					me.setParamByKey(opts.searchTerm.type, id);
					TableExt.clearRule();
					//
					// 处理搜索带过去
					if (opts.search && opts.search.fieldName && me._keyWord) {
						me._doFilterData([{
							FieldName: opts.search.fieldName,
							Comparison: 7,
							FilterValue: me._keyWord
						}], true, 'advance');
					} else {
						me._doFilterData([], true, 'advance');
					}
				}
			});
			me.trigger('term.modify', function (data) {
				opts.searchTerm.options = data;
			});
			me._termBatch.on('term.advance', function (data, aloneValues, isInnerFilter) { // 筛选
				if (me.options.beforeTermAdvance && !me.options.beforeTermAdvance(data, aloneValues)) {
					return;
				} // 筛选前的逻辑
				Table.clearRule();
				me.filterStatus = true;
				me.setParamByKey('_aloneValuesKey', aloneValues);
				me._doFilterData(data, true, 'advance');
				me.trigger('term.advance', data, aloneValues, isInnerFilter);
				me._curFilterData = data;
				me._lightFilterColumns(me._curFilterData);
			});
			me._termBatch.on('filter.show', function () { // 筛选显示
				me.trigger('filter.show');
			});
			me._termBatch.on('otherbtn.change', function ($item, e) { // 其他按钮点击
				me.trigger('otherbtn.change', $item, e);
			});
			me._termBatch.on('term.bactchHide', function () { // 关闭批量term.bactchHide'
				me._clearChecked();
				me.trigger('term.bactchHide');
			});
			me._termBatch.on('columns.change', function (data) {
				me._remberColumnsOrder(data);
				me.renderByColumns(data, null, true, true);
				me.trigger('tb.savecolumns', data);
				me._lightFilterColumns(me._curFilterData || []);
			});
			me._termBatch.on('columns.reset', function () {
				me.trigger('tb.resetcolumns');
			});
			me._termBatch.on('batchbtn.change', function (item) {
				me.trigger('batchbtn.change', item);
			})
			me._termBatch.on('tag.change', function (data) {
				me.setParamByKey('tag_operator', data.tag_operator);
				me.setParamByKey('tags', data.tags);
				me._doFilterData([], true);
				me.trigger('tag.change', data);
			})
			me._termBatch.on('height.change', function () {
				if (!me.options.isListLayout) {
					me._fixTableHeight();
				};
				me.trigger('height.change');
			})
			me._termBatch.on('term.showallcheck', function (callback) {
				var datas = me.getRemberData();
				if (!datas.length) {
					datas = me.getCheckedData();
				}
				datas.length && callback(datas, me.getSetColumnsPreVisible(6, true));
			})
			me._termBatch.on('multi.field.sort.change', function (orderFields) {
				me._handleMultiFieldSort(orderFields);
			})
		}
	})


	module.exports = MyObject.extend({

		initialize: function() {
			window.addEventListener('message', this.slideMailDetail);
            MyObject.prototype.initialize.apply(this, arguments);
        },
		
		getColumns: function () {
			var me = this;
			var columns = proto.getColumns.apply(this, arguments);

			columns.forEach(column => {
				if (["first_response_duration", "average_response_duration", "servicer_response_duration"].indexOf(column.api_name) !== -1) {
					column.render = function (data, type, full) {
						return me.formatTime(data);
						// return data;
					};
				}
			});

			return columns;
		},

		getTable: function (fn) {
			if (fn) {
				fn && fn(TableExt)
			}
		},

		_operateHandle: function (...args) {
			const [opts, data] = args;
			if (this['handle' + opts.action]) {
				return this['handle' + opts.action].apply(this, args);
			}
			return MyObject.prototype._operateHandle.apply(this, args);
		},
		excuteAction(action, params) {
			const me = this
			if (action === 'list_custom_action.QueryUser') {
				this.Handle_QueryUser_button_default()
				return
			}
			MyObject.prototype.excuteAction.apply(this, arguments);
		},

		slideMailDetail(data) {
			const propsData = data.data
			if (propsData.sender !== 'external-contacts') return
			if(propsData.eventName !== 'slideMailDetail') return
			const mailData = propsData.data
			CustServSessionUtil.slideMailDetail({
			  id: mailData.emailMessageId,
			  ea: (FS.contacts.getCurrentEmployee() || {}).enterpriseAccount,
			  userId: mailData.fsUserId,
			});
		},

		/* 转接 */
		handleTransferSession(opts, data) {
			const detailData = data;
			const me = this
			const session = { sessionId: data.session_id }
			const appId = data.session_app_id
			// CustServSessionUtil.handleTransferSession(detailData).then(res => {
			// 	me.refresh();
			// })
			FS.MEDIATOR.trigger('qx.sendPlugin.sendStandaloneServiceTransfer',{}, {session, appId, source: 'crm'})
		},
		/* 结束 */
		// handleEndSession(opts, data) {
		// 	const detailData = data;
		// 	const me = this
		// 	CustServSessionUtil.handleEndSession(detailData).then(res => {
		// 		//塞给过滤条件重新过滤
		// 		me.refresh();
		// 	})
		// },
		/* 查看会话记录 */
		handleViewChatRecord(opts, data) {
			const detailData = data;
			const me = this
			CustServSessionUtil.handleViewChatRecord(detailData).then(res => {
			})
		},
		/* 查看留言 */
		handleViewLeaveMessage(opts, data) {
			const detailData = data;
			const me = this
			CustServSessionUtil.handleViewLeaveMessage(detailData).then(res => {
			})
		},
		/* 用户查找 */
		Handle_QueryUser_button_default(opts, data) {
			CustServSessionUtil.handleClickCustomer().then(res => {
				filterHandle._customFilter(res)
			})
			// filterHandle._customFilter('82313.1000')
		},
		/**
		 * @description 时间格式化，毫秒转换
		 * 原则：最小单位为秒，最多展示两个单位
		 */
		formatTime(ms) {
			// 非数字不转换 undefined null '' (空字符串)
			if (ms === '' || isNaN(String(ms))) return ms;

			ms = Number(ms);
			let seconds = ms;
			// 1 分钟内
			if (seconds < 60) {
				return seconds + $t("秒");
			}
			// 1 小时内
			if (seconds < 3600) {
				const minutes = Math.floor(seconds / 60);
				seconds = seconds - (minutes * 60);
				return minutes + $t("分钟") + (seconds > 0 ? (seconds + $t("秒")) : '');
			}
			// 1 天内
			if (seconds < 86400) {
				const hours = Math.floor(seconds / 3600);
				let minutes = Math.round((seconds - (hours * 3600)) / 60);
				return hours + $t("小时") + (minutes > 0 ? (minutes + $t("分钟")) : '');
			} else {
				const days = Math.floor(seconds / 86400);
				let hours = Math.round((seconds - (days * 86400)) / 3600);
				return days + $t("天") + (hours > 0 ? (hours + $t("小时")) : '');
			}
		},
		destroy: function(){ 
			window.removeEventListener('message', this.slideMailDetail)
			MyObject.prototype.destroyed.apply(this, arguments);
		},
	});
});
