// [ignore-i18n-file]
define(function(require, exports, module){
    
    var moment = require("base-moment");
	var statusMap = [
		"等待中",
		"进行中",
		"完 成",
		"{{typeText}}过程异常终止",
		"无法{{typeText}}",
		"取消中",
		"已取消",
	];
	var getColumns = function(type) {
        var _map = {
            import: $t('导入'),
            export: $t('导出'),
        };
        var typeText = _map[type];
        return [{
            data: 'apiName',
            title: $t("{{typeText}}对象", {typeText: typeText}),
            dataType: 1,
            className: 'td-first',
            isOrderBy: true,
            orderValues: [1, 0],
            filterCompare: [11, 12],
            isFilter: true,
            render: function (data, index, full) {
                return full.apiFullName ? _.escape(full.apiFullName) : CRM.config.TEXT_DEFAULT;
            }
        }, {
            data: 'status',
            title: $t("状态"),
            isOrderBy: true,
            dataType: 6,
            options: [{
                ItemName: $t(statusMap[0], {typeText: typeText}),
                ItemCode: '0'
            }, {
                ItemName: $t(statusMap[1], {typeText: typeText}),
                ItemCode: '1'
            }, {
                ItemName: $t(statusMap[2], {typeText: typeText}),
                ItemCode: '2'
            }, {
                ItemName: $t(statusMap[3], {typeText: typeText}),
                ItemCode: '3'
            }, {
                ItemName: $t(statusMap[4], {typeText: typeText}),
                ItemCode: '4'
            }, {
                ItemName: $t(statusMap[5], {typeText: typeText}),
                ItemCode: '5'
            }, {
                ItemName: $t(statusMap[6], {typeText: typeText}),
                ItemCode: '6'
            }],
            orderValues: [1, 0],
            isFilter: true,
            render: function (data, type, full) {
                var index = parseInt(full.status, 10),
                    result = '',errorCode='',
                    minus = (full.endTime - full.startTime)/1000,
                    seconds = parseInt(minus%60, 10),
                    temp = (index == 2 && full.endTime)? '<span class ="timer" >'+ $t("耗时") + parseInt(minus/60, 10)+ $t("分") +seconds+ $t("秒")+'</span>':'',
                    tempCount = full && full.result &&full.result.sucCount;
                errorCode = full && full.result &&full.result.code;
                tempCount = tempCount || 0;
                var statusText = $t(statusMap[index], {typeText: typeText});
                switch(index){
                    case 0 : result = '<span class="cancel" >' + statusText + temp + '</span>';
                        break;
                    case 1 : result = '<span class="doing" >'  + statusText + temp + '</span>';
                        break;
                    case 3 : result = '<span class="error" >' + statusText +'</span>';
                        break;
                    case 4 : result = '<span class="error" >' + statusText +'</span>';
                        break;
                    case 2 :
                    case 5 :
                    case 6 : result = '<span class="other">' + statusText + temp + '</span>';
                        break;
                }
                if(index == 6 && full.errorMessage) {
                    var msg = _.escape(full.errorMessage);
                    result = result + '<span class="timer" title="' + msg + '">' + msg + '</span>'
                }
                return result;
            }
        },{
            data: 'totalCount',
            title: $t("{{typeText}}数据量", {typeText: typeText})+'('+$t("条")+')',
            dataType: 2,
            render: function (data, all, full) {
                if (full.jobType == 2) {
                    if (full.status < 2) {
                        return full.result && (full.result.sucCount ? full.result.sucCount : 0) + '/' + full.result.totalCount;
                    } else if (full.status == 3) {
                        return ''
                    }
                }
                return full.result && full.result.totalCount;
            }
        }, {
            data: 'failCount',
            title: $t("{{typeText}}失败量", {typeText: typeText})+'('+$t("条")+')',
            dataType: 2,
            render: function (data, all, full) {
                return full.result && full.result.failCount;
            }
        }, {
            data: 'Result',
            title: $t("{{typeText}}结果", {typeText: typeText}),
            dataType: 1,
            render: function (data, tip, full) {
                // 导出异常显示错误信息
                if (full.jobType == 2 && full.status == 3){
                    return full.errorMessage;
                } else if (full.status == 2 && full.result){
                    /* if (full.jobType == 1 && full.result.failCount === 0) {
                        // 导入全部成功
                        return $t('导入成功');
                    } else */
                        if (full.result.expired) {
                        // 导入导出下载文件失效
                        return "<a style='color:#999999'>"+$t('文件已失效')+"</a>";
                    } else if (full.result.url) {
                        // 导入导出文件下载
                        var fileName;
                        if(full.jobType === 2) {
                            let rf = full.result.fileName;
                            let fileExt =  CRM.util.getFileExtText(rf);
                            if(!fileExt) {
                                fileName = rf + '.' + (full.result.fileExt || 'xlsx');
                            } else {
                                fileName = rf;
                            }
                        } else {
                            fileName = $t('{{displayName}}对象{{typeText}}结果_{{moment}}', {
                                typeText: typeText,
                                displayName: full.apiFullName,
                                moment: FS.moment.unix(full.startTime  / 1000).format('YYYYMMDD'),
                            })
                            fileName += '.' + ((!full.result.fileExt) ? 'xlsx' : full.result.fileExt);
                        }
                        return "<a href=" + CRM.util.getFscLink(full.result.url, fileName, true) + ">"+$t('下载查看')+"<a>";
                    }
                }
            }
        }, {
            data: 'ImportType',
            title: $t("{{typeText}}类型", {typeText: typeText}),
            dataType: 1,
            // isOrderBy: true,
            // orderValues: [1, 0],
            // filterCompare: [11, 12],
            // isFilter: true,
            render: function (data, tip, full) {
                return full.queryParam && full.queryParam.ImportType ? $t("更新导入"): $t("新建导入")
            }
        }, {
            data: 'createrId',
            title: $t("{{typeText}}操作人", {typeText: typeText}),
            isOrderBy: true,
            dataType: 8,
            referRule: 'Employee',
            noGroup: true,//屏蔽部门
            // width: 150,
            orderValues: [1, 0],
            filterCompare: [11, 12],
            isFilter: true,
            isId: true,
        }, {
            data: 'startTime',
            title: $t("{{typeText}}时间", {typeText: typeText}),
            isOrderBy: true,
            dataType: 4,
            formatStr: 'YYYY-MM-DD HH:mm',
            width: 140,
            orderValues: [1, 0],
            filterCompare: [19, 21],
            isFilter: true,
            noShortCuts: true
        },{
            data: 'notifies',
            title: $t("通知方式"),
            width: 100,
            render: function (data, type, full) {
                var list = '';
                if (full.notifies != "--"){
                    _.each(full.notifies, function (item) {
                        list += "," + item.userName;
                    });
                    list = list.slice(1);
                }
                return '<span class="crmtip">'+ $t("CRM提醒")+'</span>';
            }
            }, 
            {
                data: 'fileName',
                title: $t("文件名称"),
                width: 100,
                render: function (data, type, full) {
                    let name = full.queryParam?.fileName || '--';
                    return `<span  title="${name}">${name}</span>`
                }
            }, 
		{
			data: "params",
			title: $t("参数"),
			width: 100,
			render: function (data, type, full) {
				
				return '<a class="detail-param" data-action="detail">'+ $t("详情")+'</a>';
			},
		},
		
		
		{
            data: 'id',
            title: $t("操作"),
            width: 80,
            lastFixed: true,
            lastFixedIndex: 100,
            render: function (data, type, full) {
                var index = parseInt(full.status, 10), temp = '';
                if (index <= 1) {
                    temp = $t("取 消");
                }
                return '<a  class="set-status cancel-dialog" href="javascript:void(0)" cancelId = ' + full.jobId + '>' + temp + '</a>';
            }
        }];
    }


	var getDetailColumns = function (data = {}) {
		let type = {
			1: $t("导入"),
			2: $t("导出"),
        };
        let queryParam = data.queryParam || {};
        let MatchingType = {
            1: $t("唯一性ID"),
            2: $t("主属性"),
            3: $t("唯一性规则"),
            4: $t("其他字段")
        }
        return [
			{
				content: data.apiFullName,
				value: data.apiName,
                title: $t("{{typeText}}对象", {typeText: type[data.jobType]}),
				key: "apiName",
			},
			{
				content: $t(statusMap[data.status], {typeText: type[data.jobType]}),
				value: data.status,
				title: $t("状态"),
				key: "status",
            },
            {
				content: queryParam.fileName||'--',
				value: queryParam.fileName,
				title: $t("文件名称"),
				key: "fileName",
            },
			{
				content: (data.result||{}).totalCount,
				value: (data.result||{}).totalCount,
				title:
					$t("{{typeText}}数据量", {
						typeText: type[data.jobType],
					}) +
					"(" +
					$t("条") +
					")",
				key: "totalCount",
			},
			{
				content: data.createrName,
				value: data.createrId,
				title: $t("{{typeText}}操作人", {
					typeText: type[data.jobType],
				}),
				key: "createrId",
			},
			{
				content: moment(data.startTime).format("YYYY-MM-DD HH:mm"),
				value: data.startTime,
				title: $t("{{typeText}}时间", {
					typeText: type[data.jobType],
				}),
				key: "startTime",
			},
			{
				content: (data.result||{}).failCount,
				value: (data.result||{}).failCount,
				title:
					$t("{{typeText}}失败量", {
						typeText: type[data.jobType],
					}) +
					"(" +
					$t("条") +
					")",
				key: "failCount",
			},
			{
				content: queryParam.ImportType?$t("更新导入"):$t("新建导入"),
				value: queryParam.ImportType,
				title: $t("{{typeText}}类型", {
					typeText: type[data.jobType],
				}),
				key: "importType",
			},
			{
				content: $t("CRM提醒"),
				value: data.notifies,
				title: $t("通知方式"),
				key: "notifies",
			},
			{
				content: typeof queryParam.IsEmptyValueToUpdate == 'boolean' ? queryParam.IsEmptyValueToUpdate ? $t("是") : $t("否") :'--',
				value: data.IsEmptyValueToUpdate,
				title: $t("导入数据的字段有空值时，是否更新现有字段数据"),
				key: "IsEmptyValueToUpdate",
            },
            {
				content: typeof queryParam.IsWorkFlowEnabled == 'boolean' ? queryParam.IsWorkFlowEnabled ? $t("是") : $t("否") :'--',
				value: queryParam.IsWorkFlowEnabled,
				title: queryParam.ImportType ? $t("是否触发工作流"):$t("是否触发工作流和阶段推进器"),  
				key: "IsWorkFlowEnabled",
			},
			{
				content: typeof queryParam.IsApprovalFlowEnabled == 'boolean' ? queryParam.IsApprovalFlowEnabled ? $t("是") : $t("否") :'--',
				value: queryParam.IsApprovalFlowEnabled,
				title: $t("是否触发审批流"),
				key: "IsApprovalFlowEnabled",
			},
			{
				content: typeof queryParam.IsCheckOutOwner== 'boolean' ? queryParam.IsCheckOutOwner ? $t("是") : $t("否"):'--',
				value: queryParam.IsCheckOutOwner,
				title: $t("是否校验外部负责人"),
				key: "IsCheckOutOwner",
			},
			{
				content: typeof queryParam.IsRemoveOutTeamMember =='boolean' ?  queryParam.IsRemoveOutTeamMember ? $t("是") : $t("否"): '--',
				value: queryParam.IsRemoveOutTeamMember,
				title: $t("是否移除外部相关团队成员"),
				key: "IsRemoveOutTeamMember",
			},
			{
				content: typeof queryParam.no_export_relevant_team == 'boolean' ?  queryParam.no_export_relevant_team ? $t("否"):$t("是") :'--',
				value: queryParam.no_export_relevant_team,
				title: $t("导出相关团队字段"),
				key: "no_export_relevant_team",
			},
			{
				content: queryParam.include_id ? $t("是") : $t("否"),
				value:  queryParam.include_id,
				title: $t("导出的表格中包含“唯一性ID”"),
				key: "include_id",
			},
			{
				content: queryParam.according_to_list ? $t("是") : $t("否"),
				value: queryParam.according_to_list,
				title: $t("根据列设置导出字段"),
				key: "according_to_list",
            },
            {
				content: MatchingType[queryParam.MatchingType] ,
				value: queryParam.MatchingType,
				title: $t("{{typeText}}识别方式", {
					typeText: type[data.jobType],
				}),
				key: "MatchingType",
            },
            {
				content: typeof queryParam.IsUpdateOwner =='boolean' ?  queryParam.IsUpdateOwner ? $t("是") : $t("否"): void 0 ,
				value: queryParam.IsUpdateOwner,
				title: $t("是否更新负责人"),
				key: "IsUpdateOwner",
            },
            {
				content: data.jobId,
				value: data.jobId,
				title: $t("任务ID"),
				key: "jobId",
			},
            {
                content: data.jobType == '1' ? (queryParam.importMethod && queryParam.importMethod == 'addAction' && !queryParam.ImportType ? $t('paas.object.import.newbuild.mode') : $t('paas.object.import.independent.mode')) : '',
                value: queryParam.importMethod || 'normal',
                title: $t("paas.object.import.mode"),
                key: "importMethod",
			}
		].filter(v=>v.content);
	};

    module.exports = {
        getColumns,
        getDetailColumns

    };
});
