/**
 *@describe 列表页地图视图
 *author wang<PERSON>an
 *2021/1/08
 */
define(function (require, exports, module) {
	var util = CRM.util;
	var api = CRM.api;
	return Backbone.View.extend({
		options: {
			apiname: '', //对象apiname
			locationField: '',
			columns: null,
			bubbleInfo: null,
			queryParam: null
		},
		events: {
			'click .j-card' : '_cardClickHandle',
			'click .j-object-detail': '_showObjectDetailHandle',
			'click .j-view-image': '_preViewImageHandle',
			'click .j-view-location': '_showLocationHandle',
			'click .j-view-attach': '_preAttachHandle',
			'click .j-show-detail': '_showDetailHandle',
			'click .j-num': '_fetchNumHandle',
			'click .map-item-info h5': '_infoTitleClickHandle',
			'click .card .change-geo': '_changeGeo',
			'click .j-checkbox': '_checkData',
			'click .j-checkbox-all': '_checkAll'
		},
	
		cardHtml: require('../template/card-html'),
		infoHtml(obj) {
			let strs = _.map(obj.contents, a => {
				return `<li><span>${_.escape(a.title)}</span><label>${a.content || '--'}</label></li>`;
			})
			let pre = '';
			if(this._lazyLoad) {
				let cls = this.__mapCheckedDatas && this.__mapCheckedDatas[obj.customerID] ? ' mn-selected' : '';
				pre = `<div style="margin-bottom:4px" class="mn-checkbox-box"><span data-id="${obj.customerID}" class="mn-checkbox-item j-checkbox j-map-checkbox${cls}"></span></div>`;
			}
			return `
				<div class="crm-map-item-info map-item-info">
					${pre}
					<h5 data-id="${obj.customerID}">${obj.name}</h5>
					<p>${obj.address}</p>
					${!strs.length ? '' : '<ul>' + strs.join('') + '</ul>'}
				</div>
			`
		},
		tipHtml: require('./template/tip-html'),

		initialize(options) {
			let maxMarkers = options.maxMarkers;
			if(maxMarkers) {
				this._lazyLoad = true;
				if(_.isNumber(maxMarkers)) {
					if(maxMarkers > 999) {
						this.$el.addClass('crm-list-mapview__2000');
					}
					if(maxMarkers >= 2000) {
						maxMarkers = 2000;
					} else if(maxMarkers < 1) {
						maxMarkers = '';
					}
				} else {
					maxMarkers = '';
				}

				this.options.maxMarkers = maxMarkers;
			}

			this.parseQueryParam(options.queryParam);
			
			this.render();
		},

		render: function() {
			var me = this;
			me.curItem = null;

			if(me._lazyLoad) {
				let strs = `
					<div class="crm-list-mapview crm-page-common_layout">
						<div style="display:flex;flex-direction:column;width:100%">
							<div class="xxx"></div>
							<div style="flex:1;overflow:hidden;display:flex">
								<div class="left-box common-left-box">
									<div class="t-sss">
										<div class="map-total"></div>
										<div class="mn-checkbox-box"><span style="margin-right:4px">${$t('全选')}</span><span class="mn-checkbox-item j-checkbox-all"></span></div>
									</div>
									<div class="map-list-box"></div>
									<div class="map-page-box"></div>
								</div>
								<div class="map-box common-right-box"></div>
							</div>
						</div>
					</div>
				`
				me.$el.html(strs);
				me.renderXXX(me.$('.xxx'));
				me.$checkAll = me.$('.j-checkbox-all');
			} else {
				me.$el.html('<div class="crm-list-mapview crm-page-common_layout"><div class="left-box common-left-box"><div class="map-list-box"></div><div class="map-page-box"></div><div class="map-total"></div></div><div class="map-box common-right-box"></div></div>');
			}
			
			me.$leffbox = me.$('.left-box');
			me.$listbox = me.$('.map-list-box');
			me.$pagebox = me.$('.map-page-box');
			me.$mapbox = me.$('.map-box');
			me.$total = me.$('.map-total');
			me._renderList();
			me.renderPagation();
		},

		fetchList: function() {
			var me = this;
			me._numAjax && (me._numAjax.abort(),  me._numAjax = null);
            return new Promise(function(resolve, reject) {
            	me.showLoading();
            	util.FHHApi({
	               url: '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/List',
	               data: me.queryParam,
	               success: function(res) {
	                   if (res.Result.StatusCode === 0) {
	                       resolve(res.Value);
	                   } else {
	                   	   resolve();
	                       util.alert(res.Result.FailureMessage || $t('暂时无法获取数据!'));
	                   }
	                   me.hideLoading();
	               }
	            }, {
	            	errorAlertModel: 1
	            })
            })
        },

        //获取精确数量
		_fetchNumHandle: function(e) {
			var me = this;
			if(me._numAjax) return;

			$(e.currentTarget).html($t('计算中'));
            me._numAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/'+ me.options.apiname +'/controller/List',
                data: _.extend({
                    find_explicit_total_num: true
                }, me.queryParam),
                success: function(res) {
                    if (res.Result.StatusCode === 0) {
                    	me.$total && me.setTotalStatus(me._trueTotal = res.Value.total);
                    } else {
                    	$(e.currentTarget).html($t('精确查询'));
                    }
                },
                complete: function() {
                	me._numAjax = null;
                }
            });
		},

		setTotalStatus: function(total) {
			var me = this;
			var str;
			if(me._trueTotal) {
				str = $t('共{{num}}条', {num: '<em style="margin:0 4px;">'+ me._trueTotal + '</em>'});
				me.$total.html(str);
				// me.$titTotal.html(str);
			} else {
				if(total >= 1000) {
					str = $t('约{{num}}条', {num:'1000+'});
					// me.$titTotal && me.$titTotal.html(str);
					me.$total.html(str + '<span style="margin-left:8px;" class="s5 j-num">' + $t('精确查询') + '</span>');
				} else {
					str = $t('共{{num}}条', {num: '<em style="margin:0 4px;">'+ total + '</em>'});
					// me.$titTotal && me.$titTotal.html(str);
					me.$total.html(total ? str : '');
				}
			}

			me.renderPagation(me._trueTotal || total || 0);
		},

		resize: function() {},

		parseQueryParam: function(queryParam) {
			var sq = JSON.parse(queryParam.search_query_info);
			sq.offset = 0;//从第一页开始
			this.pageSize = sq.limit || 20;
			if(this.options.maxMarkers) {
				sq.limit = this.pageSize = this.options.maxMarkers
			}
			this.queryParam = _.extend({}, queryParam, {
				search_query_info: JSON.stringify(sq)
			});
		},

		refresh: function(queryParam) {
			this._cleanAll();

			this.parseQueryParam(queryParam);
			this._trueTotal = null;
			if(this.pageNumber != 1) {
                this.pageNumber = 1;
                this.pagation.reset();
            }
			this._renderList();
		},

		_addMarker: function(list) {
            var me = this;
            var columns = me.options.columns;
            var locationField = me.options.locationField;
            require.async('crm-modules/action/field/field', function(field) {
				var result = [];
				_.each(list, function(item, index) {
	            	var tt = item[locationField];
	            	if(!tt || !tt.split) return;
	            	var pos = tt.split('#%$');
	            	if(!(Math.abs(pos[0] || 0) + Math.abs(pos[1] || 0))) return;
	            	var contents = _.map(columns, function(aa) {
                    	return {
                    		title: aa.label,
							content: me._formatData(item[aa.api_name], aa.quote_field_type || aa.type, aa, item, field.format)
                    	}
                    })

	            	result.push({
	            		lon: pos[0] === -1 ? '' : pos[0],
	                    lat: pos[1] === -1 ? '' : pos[1],
	                    index: index + 1,
	                    name: contents[0] ? (contents[0].content || '--') : '',
	                    color: me.getMarkColor(item),
	                    address: contents[1] ? contents[1].content || '--' : ' ',
	                    contents: contents.slice(2),
	                    customerID: item._id
	            	})
	            })

	            me.crmMap && me.crmMap.addMarker(result);
			});
		},

		_addLegend(dataList) {
			if(!this.options.legend) return;
			if(!this.$legendWrapper) {
				this.$legendWrapper = $('<div style="position:absolute;z-index:10000;right:0;top:40px"></div>');
				this.$mapbox.append(this.$legendWrapper);
			}

			this.options.legend(this.$legendWrapper[0], dataList);
		},

		_renderList: function() {
			var me = this;
			Promise.all([me.fetchList(), me.renderMap()]).then(res => {
				if(!me.options) return;//已销毁

				rv = res[0];
				me._total = rv.total;
				me.$leffbox.scrollTop(0);
				me.setTotalStatus(rv.total);

				let dataList = (rv && rv.dataList) || [];

				me.renderList(dataList);

				me._addMarker(dataList); //往地图上追加点
				me._addLegend(dataList);
				me.options.createPolygon && me.options.createPolygon(dataList, me.crmMap && me.crmMap.mapInstance);
			})
		},

		_formatData: function(vv, renderType, column, full, format) {
			if(full[column.api_name + '__s']) return full[column.api_name + '__s'];
			
			if (renderType !== 'html_rich_text' 
			&& renderType !== 'big_text' 
			&& renderType !== 'rich_text' 
			&& (vv === void 0 || vv === '' || vv === null || !renderType || (_.isArray(vv) && vv.length === 0))) return '';
            var strs, fieldName = column.api_name;
            if(renderType === 'location') {
                var arr = vv.split('#%$');
				var ltitle = _.escape(arr[2]);
                strs = '<span title="' + ltitle + '" data-lon="' + arr[0] + '" data-lat="' + arr[1] + '" class="s5 j-view-location">' + ltitle + '</span>';
			} else if(renderType === 'file_attachment' || renderType === 'big_file_attachment') {
				strs = '<span data-id="' + full._id + '" data-fieldname="' + fieldName + '" class="s5 j-view-attach">' + vv.length + $t('附件') +'</span>'
			} else if(renderType === 'image' || renderType === 'signature') {
				var images = format(column, vv);
				strs = _.map(images, function(a, index) {
					return index > 1 ? '' : '<img data-id="' + full._id + '" data-index="'+ index +'" data-fieldname="' + fieldName +'" class="j-view-image" src="' + _.escape(a.smallUrl) + '"/>'
				}).join('') + (images[2] ? ('<span data-index="2" data-fieldname="' + fieldName +'" class="s5 j-view-image">' +  $t('图片数量', {num: images.length}) + '></span>') : '');
				return '<span class="s3">' + strs + '</span>';
			} else if ((renderType === 'object_reference' || renderType === 'master_detail') && column.target_api_name) {
				strs = `<span data-id="${vv}" data-apiname="${column.target_api_name}" class="s5 j-object-detail${column.is_support_tree_view ? ' crm-ui-treetitle': ''}">${_.escape(full[column.api_name + '__r'] || '--')}</span>`;
			} else if(renderType === 'url') {
				strs = CRM.util.rStrUrl(vv, true);
			} else if(renderType === 'employee') {
				const isOutOwner = column.api_name === 'out_owner'; // 外部负责人不应该走人员的
				if(!isOutOwner && !CRM.util.isConnectApp() && vv && vv.length === 1 && vv != CRM.curEmpId && full[fieldName + '__l'] && full[fieldName + '__l'].length && vv != -10000) {
					strs = '<a target="_blank" data-cardid="'+ vv[0] +'" class="j-open-profile" href="/XV/UI/Home#profile/=/empid-'+ vv[0] +'">' + full[fieldName + '__l'][0].name + '</a>'
				} else {
					strs = format(column, vv, full);
				}
			} else if(renderType === 'phone_number') {
				strs = format(column, vv, full);
				var op = full[fieldName + '__p'];
				if(op && op.mobilePath) {
					strs = strs + '<span style="margin-left:4px;color:#92a3b7">' + op.mobilePath +'</span>';
				}
			} else if(renderType === 'relevant_team' || renderType === 'embedded_object_list') {
				strs = _.escape(full.relevant_team__r);
			} else if(renderType === 'object_reference_many') {
				strs = _.map(full[fieldName + '__r'], function(a) {
					return `<span data-id="${a._id}" data-apiname="${column.target_api_name}" class="s5 j-object-detail${column.is_support_tree_view ? ' crm-ui-treetitle': ''}">${_.escape(a.name || '--')}</span>`
				}).join(',');
			} else if(column.is_what && column.render) {
				strs = column.render('', '', full, 'j-object-detail');
			} else {
				strs = format(column, vv, full);
			}

			if(strs === '') strs = '--';

            return '<span class="s2">' + strs + '</span>'
        },

		renderList: function(list) {
			var me = this;
			me._dataList = list;
			var columns = me.options.columns;
			require.async('crm-modules/action/field/field', function(field) {
				let dataItems = _.map(me._dataList, function(a, index) {
					var contents = _.map(columns, function(b) {
						return {
							title: b.label,
							content: me._formatData(a[b.api_name], b.quote_field_type || b.type, b, a, field.format)
						}
					})

					var color =  me.getMarkColor(a);
					return {
						_id: a._id,
						name: a.name,
						preTitle: '<div style="background-color:' + color + '" class="crm-list-icon-location"><span>' + me.getIconIndex(index + 1) + '</span></div>',
						mainTitle: contents[0] ? (contents[0].content || '--') : ' ',
						color: color,
						contents: contents.slice(1),
						apiName: me.options.apiname,
						location: me.options.locationField,
						pos: a[me.options.locationField]
					}
				})
				if(me._lazyLoad) {
					me._renderScrollLoadList(dataItems);
				} else {
					me.$listbox.html(me.cardHtml({
						list: dataItems
					}))
				}
			})
		},

		_checkData(e) {
			let $target = $(e.target);
			let isCheck = !$target.hasClass('mn-selected');
			let id = $target.data('id')
			let item = _.findWhere(this._dataList, {_id: id});
			item && (item.__mapChecked = isCheck);
			this._updateCheckData([item]);

			let $check;
			if($target.hasClass('j-map-checkbox')) {//地图上选的 把左侧列表的也更新下
				$check = this.$listbox.find(`[data-id="${id}"]`);
			} else {
				$check = this.$('.j-map-checkbox');
			}

			$check[isCheck ? 'addClass' : 'removeClass']('mn-selected');
		},

		_checkAll(e) {
			let isCheck = !$(e.target).hasClass('mn-selected');
			_.each(this._dataList, a => a.__mapChecked = isCheck);
			
			this.$listbox.find('.mn-checkbox-item')[isCheck ? 'addClass' : 'removeClass']('mn-selected');
			this.$('.j-map-checkbox')[isCheck ? 'addClass' : 'removeClass']('mn-selected');

			this.$checkAll.removeClass('mn-half-selected');
			this._updateCheckData();
		},

		_cleanAll() {
			if(!this._lazyLoad) return;
			
			try {
				this.$('.mn-selected').removeClass('mn-selected');
				this.$checkAll.removeClass('mn-half-selected');
				_.each(this._dataList, a => a.__mapChecked = false);
				this.__mapCheckedDatas = null;
				this.xxxComp && (this.xxxComp.dCheckNum = 0);
			} catch(e) {}
		},

		_updateCheckData(list) {
			let s = this.__mapCheckedDatas || (this.__mapCheckedDatas = {});
			_.each(list || this._dataList, a => {
				if(a.__mapChecked) {
					s[a._id] = a;
				} else {
					delete s[a._id];
				}
			})
			this.xxxComp && (this.xxxComp.dCheckNum = _.keys(this.__mapCheckedDatas).length);
			if(!list) return;

			let index = 0;
			_.each(this._dataList, a => s[a._id] && index++);
			if(!index) {
				this.$checkAll.removeClass('mn-selected mn-half-selected')
			} else if(index < this._dataList.length) {
				this.$checkAll.removeClass('mn-selected').addClass('mn-half-selected');
			} else {
				this.$checkAll.removeClass('mn-half-selected').addClass('mn-selected');
			}
		},

		renderXXX(el) {
			let me = this;
			let {batchBtns, batchActionCallBack} = me.options;
			if(!batchBtns) return;

			me.xxxComp = FxUI.create({
				wrapper: el[0],
				template: `
					<div v-show="!!dCheckNum" class="crm-view-batchwrapper">
						<div class="btn-tit">
							<span v-html="cTitHtml"></span>
							<span @click="cleanAll" class="fx-icon-close"></span>
						</div>
						<div ref="bb" class="batchbtnwrapper"></div>
					</div>
				`,
				data() {
					return {
						dCheckNum: 0,
						dTitHtml: $t('已选择{{count}}条', {count: '<em style="color:var(--color-info06);margin: 0 4px">' + 100 + '</em>'})
					}
				},
				mounted() {
					this.$nextTick(() => {
						this.renderFlexGroup();
					})
				},
				computed: {
					cTitHtml() {
						return $t('已选择{{count}}条', {count: '<em style="color:var(--color-info06);margin: 0 4px">' + this.dCheckNum + '</em>'});
					}
				},
				methods: {
					renderFlexGroup() {
						require.async('crm-modules/components/flexgroup/flexgroup', Comp => {
							this._flexButton = new Comp({
								el: this.$refs.bb
							}).render(batchBtns);

							this._flexButton.on('change', (item) => {
								batchActionCallBack && batchActionCallBack(item);
							})
						})
					},
					cleanAll() {
						me._cleanAll();
					}
				},
				beforeDestroy() {
					this._flexButton && (this._flexButton.destroy(), this._flexButton = null);
				}
			})
		},

		//滚动到具体位置
		_scrollTo(id) {
		},

		//巨量数据的渲染
		_renderScrollLoadList(dataItems) {
			this.$checkAll.removeClass('mn-selected');

			this.$scroll && (this.$scroll.remove(), this.$scroll = null);
			if(!dataItems.length) {
				this.$listbox.html(`<div style="padding-top:25px;text-align:center;color:#999;">${$t('没有数据')}</div>`);
				return;
			}
			let $cardHeight = 16 + 40 + 1 + dataItems[0].contents.length * 20 //单张卡片高度 当前没有图片字段，如果有需要适配
			let $h = $cardHeight * dataItems.length //整体高度算出来
			let $scroll = $(`<div class="crm-page-common__card crm-scroll"><div style="height:${$h}px" class="inner"></div><div>`);
			let $inner = $scroll.find('.inner');
			this.$listbox.html('').append(this.$scroll = $scroll);

			let parseFn = (dataItems) => {
				_.each(dataItems, a => {
					let tmp = _.findWhere(this._dataList, {_id: a._id});
					a.afterSlot = `<div style="position:absolute;right:8px;top:4px;" class="mn-checkbox-box"><span data-id="${a._id}" class="mn-checkbox-item j-checkbox${tmp.__mapChecked ? ' mn-selected' : ''}"></span></div>`;
				})

				return dataItems;
			}

			//首次先插入25张卡片
			let loadNum = 25;
			let $cardHtml = this.cardHtml({list: parseFn(dataItems.slice(0, loadNum))});
			$inner.html($($cardHtml).html());
			let fn = _.debounce(() => {
				let num = Math.floor($scroll.scrollTop() / $cardHeight) //当前滚过了几个卡片
				$cardHtml = this.cardHtml({list: parseFn(dataItems.slice(num, num + loadNum))});
				$inner.html(`<div style="height:${num * $cardHeight}px"></div>` + $($cardHtml).html());
			}, 60)

			this.$scroll.on('scroll', fn)
		},

		getActionDatas() {
			return _.values(this.__mapCheckedDatas);
		},

		getMarkColor(data) {
			let {bubbleInfo, parseMarkerColor} = this.options;
			let markColor;
			if(parseMarkerColor) {
				markColor = parseMarkerColor(data);
			} else if(bubbleInfo) {
				markColor = (_.findWhere(bubbleInfo.options || [], {value: data[bubbleInfo.field]}) || {}).color;
			}

			return markColor || '#ff5730'
		},

		getIconIndex(index) {
			return index;
		},

		showLoading: function() {
			if(this.$load) return;

			this.$load = FxUI.create({
				wrapper: this.$el[0],
				template: '<div v-loading="true" style="position:absolute;top:0;height:100%;width:100%;z-index:10000;"></div>'
			})
		},

		hideLoading: function() {
			this.$load && (this.$load.destroy(), this.$load = null);
		},

		// 初始化分页
        renderPagation: function(total) {
            var me = this;
            if(!me.pagation) {
			me.pagation = FxUI.create({
	            	wrapper: me.$pagebox[0],
	            	template: '<fx-pagination v-show="!!total" @current-change="changeHandle" :current-page="pageNumber" small layout="prev, pager, next" :pager-count="5" :page-size="pageSize" :total="total"></fx-pagination>',
	            	data: function() {
	            		return {
	            			total: total || 0,
	            			pageSize: me.pageSize,
	            			pageNumber: 1
	            		}
	            	},
	            	methods: {
	            		changeHandle: function(pageNumber) {
							me.pageNumber = pageNumber;
							var search_query_info = JSON.parse(me.queryParam.search_query_info);
							search_query_info.offset = search_query_info.limit * (pageNumber - 1);
							me.queryParam.search_query_info = JSON.stringify(search_query_info);
							me._renderList();
	            		},
	            		reset: function() {
	            			this.pageNumber = 1;
	            		}
	            	}

	            })
            } else {
            	me.pagation.total = total;
            }
        },

        updateDataByCellEdit: function(obj) {
        	var tmp = _.pick(obj.data, obj.editFields);
        	var tt = _.findWhere(this._dataList, {_id: obj.data._id});
        	if(!tt) return;
        	_.extend(tt, tmp);
        },

		// 
		_getNewMapArgs: function(args){
            let me = this;
            return {
                el: me.$mapbox,
				mapBIData: args.data || [],
				xkcharts: args.xkcharts,
                infoWindow:{
                    infoTpl: _.bind(me.infoHtml, me),
                    tipTpl: me.tipHtml,
                    isShowClose: true,
                    filterareaTpl: args.filterareaTpl || function() {}
                },
                clickHandle: function(e) {
                    e = window.event || e;
                    var id = $(e.srcElement || e.target).attr('data-id');
                       id && CRM.api.show_detail({
                           apiName: me.options.apiname,
                           id: id
                    })
                },
                getTipPointFn: function(data, index){
                    return '<span style="background-color:' + data.color + '" data-id="' + data.customerID +'" class="new-tip-pointer"><span class="tip-index">' + index +'</span></span>';
                }
            }

        },

        renderMap: function() {
        	var me = this;
			var newMapArgs = me._getNewMapArgs({data: me.options.data, xkcharts: me.options.xkcharts, filterareaTpl: me.options.filterareaTpl})
			newMapArgs.apiName = me.options.apiname;
			if (newMapArgs.isConsume === void 0) {
				newMapArgs.isConsume = true;
			}
			newMapArgs.lazyLoad = me._lazyLoad;
			// _.extend(newMapArgs.infoWindow, {...newMapArgs.infoWindow, ...{filterareaTpl: me.options.filterareaTpl}})
        	return new Promise(function(resolve) {
        		if(me.crmMap) return resolve();
        		require.async('crm-modules/components/newmap/newmap', function(CrmMap) {
	    			if(!me.options) return;
	                me.crmMap = new CrmMap(newMapArgs);
	                me.crmMap.on('showInfoWindow', function(id) {
						if(me._lazyLoad) return me._scrollTo(id);

	                	me.$('.j-card').removeClass('card-selected');
	                	me.$('.j-card[data-id="' + id + '"]').addClass('card-selected');
	                });
	                me.crmMap.on('map.complete', function(id) {
	                	resolve();
	                });
					me.crmMap.on('biMap', function(data) {
						me.trigger('biMap', data);
					});
	                me.crmMap.render();
	            })
        	})
        },

		/** 
         * @desc 修改定位信息
         */
		 _changeGeo: function(e) {
            var me = this,
                $target = $(e.target),
                data = $target.closest('.card').data(),
                index = $target.index();

            var geoParam = {
                account_id: data.id,
                CustomerAddress: null
            };

			api.pick_data({
				apiName: 'AccountAddrObj',
				beforeRequest(param) {
					param.include_associated = true;
					return param;
				},
				extendParam: {
					mainData: {
						apiname: 'AccountObj',
						id: data.id,
						name: data.name,
					},
					relatedList: [{
						display_name: $t('客户地址'),
						api_name: 'AccountAddrObj',
						list_name: 'account_accountaddr_list',
					}],
					relatedIndex: 0,
				},
				methods: {
					select(res){
						me._upDataGeo(_.extend(geoParam, res.selected || {}));
					}
				},
			})

            e.stopPropagation();    
        },
        /**
         * @desc 更新定位
         */
        _upDataGeo: function(param) {
            var me = this;

            CRM.api.setmain_address({
                data: param,
                success: function(data) {
                    me._renderList();
                }
            });
        },

        _showInfoWindow: function(index, id) {
            let _id = id || this._dataList[index]._id;
            this.crmMap && this.crmMap.showInfoWindow(_id);
        },

        _cardClickHandle: function(e) {
			var $card = $(e.currentTarget);
			if($card.hasClass('card-selected')) return;
			if($(e.target).hasClass('mn-checkbox-item')) return;

			this.__cardItem && this.__cardItem.removeClass('card-selected');
			$card.addClass('card-selected');
			this.__cardItem = $card;

			this._showInfoWindow($card.index(), $card.attr('data-id'));
        },

		_showObjectDetailHandle(e) {
			var $data = $(e.currentTarget).data();
			$data.apiname && CRM.api.show_crm_detail({
                type: $data.apiname,
                data: {
                    crmId: $data.id
                }
            })
			e.stopPropagation();
		},

		_preViewImageHandle(e) {
			var $data = $(e.currentTarget).data();
			require.async('crm-modules/action/field/field', (field) => {
				var item = _.findWhere(this._dataList, {_id: $data.id});
				item && CRM.api.preview_image({
					objectPermissionApiName: item.object_describe_api_name,
					datas: field.format({render_type: 'image', api_name: $data.fieldname}, item[$data.fieldname]),
					index: $data.index * 1,
					isNew: true
				})
			})
			e.stopPropagation();
		},

		_preAttachHandle(e) {
			var $target = $(e.currentTarget);
			var $data = $target.data();
			var column = _.findWhere(this.options.columns, {api_name: $data.fieldname});
			var item = _.findWhere(this._dataList, {_id: $data.id});
			item && this.trigger('viewAttach', {
				$target: $target,
                data: _.clone(item[$data.fieldname]),
                limit: 10,
                zIndex: 10000,
                isMyObject: true,
				isBigFile: column && column.type === 'big_file_attachment'
			});
			return false;
		},

		_showLocationHandle(e) {
			CRM.api.show_map($(e.currentTarget).data());
			e.stopPropagation();
		},

		_showDetailHandle: function(e) {
			let me = this;
			if (me.options.detailFeatureDisabled) return;
			let $card = $(e.target).closest('.j-card');
			let apiname = me.options.apiname;
			
			CRM.api.show_crm_detail({
				type: apiname,
				apiName: apiname,
				id: $card.attr('data-id') || me._dataList[$card.index()]._id,
				callback: function() {
					me._trueTotal = null;
					me._renderList();
				}
			})
				
			return false;
		},

		_infoTitleClickHandle(e) {
			var id = $(e.currentTarget).data('id');
			id &&  CRM.api.show_crm_detail({
				type: this.options.apiname,
				apiName: this.options.apiname,
				id: id,
				callback: () => {
					this._trueTotal = null;
					this._renderList();
				}
		 	})
		},

		destroy: function () {
			this.hideLoading();
			this.$titTotal && this.$titTotal.remove();
			this.$legendWrapper && this.$legendWrapper.remove();
			this.pagation && this.pagation.destroy && this.pagation.destroy();
			this.crmMap && this.crmMap.destroy && this.crmMap.destroy();
			this.cardList && this.cardList.destroy();
			this.cardList = this.pagation = this.curItem = null;
			this.$leffbox = this.$listbox = this.$pagebox = this.$mapbox = this.$total = null;
			this.options = this.events = this.crmMap = null;
			this.off();
		}
	});
})
