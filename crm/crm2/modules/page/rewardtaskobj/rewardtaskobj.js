define(function (require, exports, module) {
	const List = require("../list/list");
	const objectApiName = "RewardTaskObj";

	module.exports = List.extend({
		options: _.extend({}, List.prototype.options, {
			apiname: objectApiName,
		}),

		getOptions() {
			let options = List.prototype.getOptions.apply(this, arguments);

			const formatDataAsync = options.formatDataAsync;

			options.formatDataAsync = async (obj) => {
				let { data } = obj;
				if (formatDataAsync) {
					obj = await formatDataAsync(obj);
				}
				let roleList = await CRM.util.getUserRoles2();
				_.each(data, (item) => {
					let role_range =  item.role_range ? JSON.parse(item.role_range) : [];
					const { red_packet_expiration_date, points_expiration_date } = item;
					data.red_packet_expiration_date = red_packet_expiration_date ? $t('tpm.days.text', { num: red_packet_expiration_date }) : $t('永久有效');
					data.points_expiration_date = points_expiration_date ? $t('tpm.days.text', { num: points_expiration_date }) : $t('永久有效');
					const roles = {}
					roleList.forEach(item => { roles[item.id] = item.name })
					item.role_range = role_range.map(id => roles[id]).join(',') || undefined;
				});
				return obj;
			};

			return options;
		},

		getColumns() {
			const columns = List.prototype.getColumns.apply(this, arguments);
			const name_column = columns.find(item => item.api_name === 'name');
			if(name_column) {
				name_column.render = (data, type, full) => {
					const icon = `<i class="fx-icon-f-jingshi rewardtaskobj-error-icon" data-id="${full._id}" data-message="${ full.__task_config_error_message || $t('tpm.rewardtaskobj.error_tip')}"></i>`;
					return `
						<div class="rewardtaskobj-custom-column-wrap">
							<a class='column-data' href="javascript:;" title="${full[name_column.api_name]}" data-name="${name_column.api_name}">${data}</a>
							${full.__task_config_status == 'error' ? icon : ''}
						</div>
						<a href="/XV/UI/Home#objdetail/${name_column.describe_api_name}/${full._id}" target="_blank" title="${$t('新页签打开')}" class="fx-icon-newtab j-icon-newtab"></a>
					`;
				};
			}
			return columns;
		},

		// 添加事件处理函数
		_handleHoverEvents(e) {
			const $target = $(e.target);
			const $icon = $target.closest('.rewardtaskobj-error-icon');

			// 检查是否悬停在tooltip上
			const tooltipId = $target.closest('[id^="rewardtaskobj-tooltip-"]').attr('id');
			const isOnTooltip = !!tooltipId;

			// 如果鼠标在错误图标区域内
			if ($icon.length) {
				// 获取图标相对于文档的绝对位置
				const iconOffset = $icon.offset();
				const id = $icon.data('id'), message = $icon.data('message');
				const tooltipSelector = `#rewardtaskobj-tooltip-${id}`;

				// 检查对应的tooltip是否已经存在
				if ($(tooltipSelector).length === 0) {
					// 如果不存在，创建一个新的tooltip添加到body
					$(`<div x-placement="top" class="el-tooltip__popper is-dark rewardtaskobj-tooltip" id="rewardtaskobj-tooltip-${id}">${message}<div class="popper__arrow"></div></div>`)
					.css({
						position: 'absolute',
						zIndex: 9999,
						left: iconOffset.left + 'px', // 居中显示
						top: iconOffset.top + 'px'    // 向上偏移以显示在图标上方
					})
					.appendTo('body');
				}
			} else if (!isOnTooltip) {
				// 如果既不在图标上也不在tooltip上，删除所有tooltip
				$('[id^="rewardtaskobj-tooltip-"]').remove();
			}
		},

		render() {
			// 调用父类的render方法
			List.prototype.render.apply(this, arguments);

			// 创建绑定后的事件处理函数（如果尚未创建）
			if (!this._boundHoverHandler) {
				this._boundHoverHandler = _.bind(this._handleHoverEvents, this);
			}

			// 移除之前可能存在的事件监听，防止重复绑定
			const $body = $(document.body);
			$body.off('mousemove.rewardtaskobj', this._boundHoverHandler);
			$body.on('mousemove.rewardtaskobj', this._boundHoverHandler);
		},

		destroy() {
			// 移除事件监听
			$(document.body).off('mousemove.rewardtaskobj', this._boundHoverHandler);

			// 调用父类的destroy方法
			List.prototype.destroy.apply(this, arguments);
		}
	});
});
