/**
 * @description 流程运维监控
 * <AUTHOR>
 */

define(function(require, exports, module) {

    const ApprovalflowMonitor = Backbone.View.extend({

        initialize: function(opts) {
            this.setElement(opts.wrapper);
        },
        render(){
            require.async(['paas-paasui/lib', ], (lib) => {
                lib.getModule('flowOpsMonitoring').then((cmpt) => {
                  this.inst = new Vue({
                    el: this.$el[0],
                    render: (h) => h(cmpt.default)
                  });
                });
            });
        },
        destroy: function() {

        }
    });

    module.exports = ApprovalflowMonitor;
});
