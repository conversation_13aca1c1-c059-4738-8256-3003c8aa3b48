/**
 * @desc  
 */
define(function (require, exports, module) {
	var List = require("../list/list");
	var roles = [];
	module.exports = List.extend({
		// 列处理
		getColumns: function () {
			var me = this;
			var columns = List.prototype.getColumns.apply(this, arguments);
			columns.forEach(e => {
				if (e.api_name == 'applicable_role_inte') {
					e.render = function (data, type, full) {
						if (!full.applicable_role_inte) return '--';
						if (roles.length) {
							var roleNames = roles.filter(item => full.applicable_role_inte.includes(item.id))
								.map(item => item.name)
								.join(',');
							return roleNames || '--';
						}
						// 先返回一个占位符，异步获取数据后再更新
						var cellId = 'role_cell_' + full.id_id;
						// 异步获取角色数据并更新单元格内容
						FS.contacts.getRoles().then(res => {
							roles = res;
							var roleNames = res.filter(item => full.applicable_role_inte.includes(item.id))
								.map(item => item.name)
								.join(',');
							var cellElement = document.getElementById(cellId);
							if (cellElement && cellElement.innerHTML === '') {
								cellElement.innerHTML = roleNames || '--';
							}
						});
						// 返回带有唯一ID的元素，用于后续更新
						return '<span id="' + cellId + '"></span>';
					}
				}
			})
			return columns;
		},
	});
});

