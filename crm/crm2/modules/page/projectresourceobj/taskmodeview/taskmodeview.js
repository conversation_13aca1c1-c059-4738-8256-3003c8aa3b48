define(function (require, exports, module) {
    let List = require("../../list_tree/list_tree");
    let ScrollBar = require("base-modules/ui/scrollbar/scrollbar");
    let GanttView = require('../resourceview/resourceview');
    let util = CRM.util;
    let api = require('../util');
    module.exports = List.extend({
        initialize: function(options) {
            this.getStageDescribe();
            this.options = options;
            this.resourceObj = options.resourceObj || {};
            this.resource_time_ranges = this.resourceObj.timeRange || [];
            this.resource_select_working = this.resourceObj.selectWorking || 'planed_resource';
            this.resource_select_selectDimension = this.resourceObj.selectDimension || 'task_mode';
            this.resource_projectId = this.resourceObj.project_id;
            this.resource_projectName = _.escape(this.resourceObj.project_name);
            this.projectTask = this.resourceObj.projectTask;
            this.storageLineWidth = JSON.parse(localStorage.getItem(`${CRM.ea}_${CRM.curEmpId}_task_line`));
            List.prototype.initialize.apply(this, arguments);
        },
        render() {
            List.prototype.render.apply(this, arguments);
        },
        getStageDescribe() {
            let _this = this;
            CRM.util.fetchDescribe("ProjectStageObj", {}, function (res) {
                _this.stageFields = res?.objectDescribe?.fields || {};
            });
        },
        _renderTable: async function () {
			var me = this;
            this.fetchTaskColunms(function(){
                me.getTable(function (Table) {
                    if (!Table) {
                        return;
                    }
                    var options = me.getOptions() || {};
                    me.table && me.table.destroy();
                    me.table = new Table(
                        _.extend(
                            {
                                isMyObject: true, //默认都是自定义对象
                                noAllowedWrap: true,
                                editable: true,
                                newStyle: true,
                                getLineSelector: function () {
                                    return ">.dt-task-main-box .dt-main .resize-line";
                                }
                            },
                            options
                        )
                    );
                    me.table.on('renderListComplete', function (init) {
                        me.renderListCompleteHandle(init);
                    });
                    me.table.getMainWrapper = function () {
                        return me.$(".dt-main");
                    };
                    me.table.getTermBatchWrapper = function () {
                        return me.$('.dt-term-batch');
                    },
                    me.table.fixedHeaderFn = function () {
                        Table.prototype.fixedHeader.apply(this, arguments);
                    };
                    me.table.unlockColumn = function (name) {
                        if (name === "name") {
                            return;
                        }
    
                        return Table.prototype.unlockColumn.apply(this, arguments);
                    };
                    me._proxyTableFn(me.table);
				    me.bindTableEvents(me.table);
                    me.extendTableEvents(me.table);
                });
            });
			
		},
        //获取列配置
        fetchTaskColunms: function(cb) {
            const _this = this;
            return util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/ProjectTaskObj/controller/ListHeader',
                data: {
                    include_layout: true,
                    apiname: 'ProjectTaskObj',
                    layout_type: "list"
                },
                success:function(res) {
                    if (res.Result.StatusCode === 0) {
                        _this.fetchColunmsSuccess(res, cb);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                }
            }, {
                errorAlertModel: 1
            })
        },
        parseColumns() {
			let columns = List.prototype.parseColumns.apply(this, arguments);
			let tablecolumns = _.filter(columns, function(column) {
				return column.dataType !== "operate";
			});
			tablecolumns.some((item) => {
				if (item.api_name === "name") {
					item.customFixed = true;
					item.fixed = true;
					item.disableSet = true;

					return true;
				}
			});

			return tablecolumns;
		},
		parseParam: function(...args) {
            let param = List.prototype.parseParam.apply(this, args);
            let search_query_info = JSON.parse(param.search_query_info);
            search_query_info.orders = [{ fieldName: "index", isAsc: true }];
            param.search_query_info = JSON.stringify(search_query_info);
            param.handler_parameter = {
                working_time_start: this.resource_time_ranges[0], // 甘特图数据的起始时间
                working_time_end: this.resource_time_ranges[1], 
                row_type: 128,
                column_type: 14, // 甘特图列标识
                resource_type: this.resource_select_working,
                view_mode: this.resource_select_selectDimension,
                project_id: this.resource_projectId
            };
            return param;
        },
        //获取表格中的数据
		parseData(obj) {
            const _this = this;
            let pro_tasks = [], noSatgeTasks = [];
            let dataObj = List.prototype.parseData.apply(this, arguments);
            console.log(dataObj);
            if (dataObj.data.length) {
                dataObj.data = dataObj.data.map(function (item) {
                    item["rowId"] = item["_id"];
                    if ("parent_task_id" in item && item["parent_task_id"]) {
                        item["pid"] = item["parent_task_id"];
                        item["tasktype"] = "son";
                    }else if(item.stage_id && item.task_no) {
                        item["pid"] = item["stage_id"];
                        item["tasktype"] = "roottask";
                    };

                    if(item.stage_no) {
                        item["tasktype"] = "stage";
                    }
                    if(_this.resource_select_working === 'planed_and_actual') {
                        // 设置 sum_actual_planed_working_hours_cost,actual_planed_working_hours 属性
                        const {working_hours_cost, working_hours} = api.setWorkingHours(item);
                        item.sum_actual_planed_working_hours_cost = working_hours_cost;
                        item.actual_planed_working_hours = working_hours;
                    }
                    return item;
                });
            };
            //无阶段任务
            noSatgeTasks = dataObj.data.filter(item => !item.stage_id);
            noSatgeTasks.forEach(item => dataObj.data.unshift(item));
            pro_tasks = dataObj.data.filter(item => item.stage_id);
            dataObj.data = api.sortDataByIndex(util.parseDataToTree(noSatgeTasks.concat(pro_tasks)));
            return dataObj;
        },
        getOptions: function() {
            let options = List.prototype.getOptions.apply(this, arguments);
            options = _.extend(options, {
                custom_className: 'crm-pr-task-work-table',
                height: 'auto',
                searchTerm: null,
                showTermBatch: false,
                showBatchBtns: false,
                showMultiple: false,
                showFilerBtn: false,
                showMoreBtn: false,
                showSize: false,
                showPage: false,
                alwaysShowTermBatch: false,
                isOrderBy_allColumn: false,
                search: null,
                scrollLoad: false,
                operate: null,
                title: '',
                noDataTip: $t('暂无数据'),
                onTrHoverHandle:(index,e) => {
                    const $tr = $(e.currentTarget);
					const trId = $tr.data().id;
                    if(this.ganttview){
                        this.ganttview.add_grid_row_class(
							"gantt-row-hover",
							index,
							trId
						);
                    }
                },
            });
            return options;
        },
        getColumns: function () {
            const _this = this;
			let columns = List.prototype.getColumns.apply(this, arguments);
            let stage_type_opts = this.stageFields?.record_type && this.stageFields?.record_type.options;
			let stage_status_opts = this.stageFields?.biz_status && this.stageFields?.biz_status.options;
            const select_working = this.resource_select_working === 'planed_and_actual';
            _.each(columns, function(item) {
				if(item.api_name === 'name') {
                    if(!_this.projectTask) {
                        item.title = '';
                        item.titleHtml = `<div class="crm-pr-thwrapper"><div class="crm-pr-searchwrapper"><div class="tag-box"><span class="tag-item add-project">${_this.resource_projectId ? _this.resource_projectName : $t('请选择')}</span><span class="fx-icon-arrow-down circle-icon"></span></div></div></div>`
                    }
                    
					item.render = function (data, type, full) {
                        let name = full.stage_no ? $t('阶段') : '';
                        field_name =  /^[\u4e00-\u9fa5]$/.test(name) ? name.charAt(0) : name.charAt(0).toUpperCase();
                        return `<span class="icon-name ${full.stage_no ? 'icon-stage' : ''} crm-ui-title">${full.stage_no ? field_name : ""}</span><span class="taskname ${!full.stage_id ? 'no-stage' : ''}">${data}</span>`;
                    };
				}
				if(item.api_name === 'owner') {
					item.render = function(data, type, full) {
						const _id = full['_id'];
						const ownerObj = full[item.api_name] ? $.extend(true,{},(FxUI.organization.getEmployeeById(full[item.api_name]) || {})) : {};
						return _id ? `<span style="position: relative;width: 200%;padding-left: 23px;"><img class="avatar" src="${ownerObj.profileImage}"  alt="${ownerObj.name}" data-cardid="${ownerObj.id}" />${ownerObj.name}</span>` : data;
					}
				}
                if(item.api_name === 'record_type') {
					item.render = function (data, type, full) {
						let record_type = full.record_type;
						let txt = api.setFieldStatus(full['stage_no'] ? stage_type_opts : item.options, record_type, 'label');
						return txt ? txt : `<span style="text-decoration: line-through">${$t('crm.选项已删除')}</span>`;
					};
				}
				if(item.api_name === 'biz_status') {
					item.render = function(data, type, full) {
						const statusObj = api.setFieldStatus(full['stage_no'] ? stage_status_opts : item.options, full[item.api_name], 'color');
						const color = statusObj['font_color'] ? statusObj['font_color'] : 'var(--color-neutrals19)';
						return `<span class="field-status">
								<span class="bg-layer" style="background-color: ${color};opacity:0.1;"></span>
								<span class="text-layer" style="color: ${color};">${statusObj?.label}</span>
							</span>`;
					}
				}
                if(item.api_name === 'actual_work_cost' || item.api_name === 'planed_work_cost') {
                    item.isHidden = true;
                }
			});
            const newColumns = columns.filter(item => !item.hasOwnProperty('new'));
            //处理不同工时条件
            const {sum_working_hours_label,working_hours_label,sum_working_hours_key, working_hours_key} = api.computedWorks(this.resource_select_working);
            //licenses
            CRM.api.get_licenses({
                key: 'project_management_professional_app',
                cb: licenses => {
                    const isNewLicenses = licenses['project_management_professional_app'];
                    if(isNewLicenses) {
                        newColumns.push({
                            new: true,
                            data: sum_working_hours_key,
                            title: '',
                            titleHtml: `<div class="work-hour-tips"><span class="work-label" title="${sum_working_hours_label}">${sum_working_hours_label}</span><div class="work-hour-content"></div></div>`,
                            width: 136,
                            lastFixed: true,
                            lastFixedIndex: 90,
                            render: function (data, type, fullData) {
                                const value = fullData[sum_working_hours_key];
                                const strs = select_working && value && value.split('/') || '';
                                const isOver = strs.length && Number(strs[0]) > Number(strs[1]);
                                return strs.length > 1 ? `<span title="${strs[0]}/${strs[1]}"><i class="${isOver ? 'over' : ''} working">${strs[0]}</i>/${strs[1]}</span>` : `<span title="${api.formatNumber(data)}">${api.formatNumber(data)}</span>`;
                            },
                        })
                    }
                }
            })
            // 新增两列
            newColumns.push({
                new: true,
				data: working_hours_key,
				title: '',
                titleHtml: `<div class="work-hour-tips"><span class="work-label" title="${working_hours_label}">${working_hours_label}</span><div class="work-hour-content"></div></div>`,
				width: 136,
				lastFixed: true,
				lastFixedIndex: 90,
				render: function (data, type, fullData) {
                    const value = fullData[working_hours_key];
                    const strs = select_working && value && value.split('/') || '';
                    const isOver = strs.length && Number(strs[0]) > Number(strs[1]);
					return strs.length > 1 ? `<span title="${strs[0]}/${strs[1]}"><i class="${isOver ? 'over' : ''} working">${strs[0]}</i>/${strs[1]}</span>` : `<span title="${api.formatNumber(data)}">${api.formatNumber(data)}</span>`;
				},
			});
			return newColumns;
		},
        trclickHandle: function (data, $tr, $target, a, noRealllyClick) {
            if (
				!$target.parents(".add-contianer").length &&
				$target.attr("class").indexOf("icon-add-forname") <= -1
			) {
				noRealllyClick || this.showDetail(data);
			}
        },
        dropDownClick(rowData, allData, status) {
           this.updateResource();
        },
        showDetail(data={}) {
            const _this = this;
            CRM.api.show_crm_detail({
				type: data.object_describe_api_name,
				data: {
					crmId: data._id,
				},
                callback: () => {
                    _this.refresh();
                    return;
                }
			});
        },
        renderListCompleteHandle(init) {
            const _this = this;
            if(init) {
                this.initRightLayout();
                this.table.on("resize", () => {
                    //列表替换icon
                    api.addReplaceRadio(_this.table.$el);
                    _this.setWorkHourTip();
                    api.setTaskMemberTextWidth($('.projecttask-box .taskname'));
                    _this.updateResource(true);
                    _this.resourceObj._toggleFixedHeader(true);
                });
            }
            //列表替换icon
            api.addReplaceRadio(this.table.$el);
            this.setWorkHourTip();
            api.setTaskMemberTextWidth($('.projecttask-box .taskname'));
            this.updateResource();
        },
        //资源视图布局初始化
        initRightLayout () {
            this.set_dtmain_layout();
            this.setTableWidthDrag();
        },
        set_dtmain_layout () {
            const data = this.table.getCurData();
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .crm-pr-task-work-table .dt-main") : $(".crm-pr-task-work-table .dt-main");
            let tbodys = dtmain.find('tbody');
            let tb = dtmain.find('table.tb');
            let theads = dtmain.find('.header thead');
            if (!$('.crm-pr-task-work-table .dt-task-main-box').length && data.length) {
                dtmain.after("<div class='dt-task-main-box'></div>");
                $('.crm-pr-task-work-table .dt-task-main-box').append(dtmain).append('<div class="dt-right"><div id="resource-gantt" class="taskGantt" style="min-width:100%;width: auto;"><div class="resource-gantt-container"></div></div></div>');
            }

            tb.width(dtmain.width());
            theads.each((index, value) => {
                const trs = $(value).find('tr');
                trs.each((idx,tr) => {
                    $(tr).css({height:'61px','line-height':'61px'});
                    $(tr).find('.tb-cell').css({height: '61px','line-height':'61px', 'padding-top':0});
                })
            });
            tbodys.each((index, item) => {
                const trs = $(item).find('.tr');
                trs.each((idx,tr) => {
                    $(tr).css({height:'38px','line-height':'38px'});
                })
            });
            $('.dt-task-main-box .dt-right').height(dtmain.height());
            new ScrollBar($(".dt-task-main-box .dt-right"));
        },
        //分割线
        setTableWidthDrag () {
            const _this = this;
            const columns = this.getColumns();
            let minTableWidth = api.setTableMinWidth(columns, 4);
            let dtwidth = this.getCurData().length >= 1 ? this.storageLineWidth ? `${this.storageLineWidth}px` : `${minTableWidth}px` : 'auto';
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .crm-pr-task-work-table .dt-main") : $(".crm-pr-task-work-table .dt-main");
            if (!dtmain.find(".dt-main-drag-line").length) {
                let line = $('<div class="dt-main-drag-line"><div class="arrow"></div></div>');
                dtmain.css("width", dtwidth);
                dtmain.append(line);
                line.on("click", ".arrow", function (e) {
                    _this.setTreeTableWidth(e);
                });
                this.bindResize(dtmain[0], dtmain.find(".dt-main-drag-line")[0], columns);
            }
        },
        //分割线点击
		setTreeTableWidth(e) {
			let me = this;
			let arrow = $(e.target);
			let dtmain = $(".crm-pr-task-work-table .dt-main");
            const columns = this.getColumns();
            let minTableWidth = api.setTableMinWidth(columns, 2);
			if (!arrow.hasClass("rotate")) {
				dtmain.css("width", minTableWidth + "px");
			} else {
				dtmain.css("width", "90%");
			}
			arrow.toggleClass("rotate");
			setTimeout(() => {
                me.addResourceScroll();
				me.storage(dtmain.width())
			}, 0);
		},
        setWorkHourTip() {
            const select_working = this.resource_select_working === 'planed_and_actual';
            const plan_and_actual_tip = [['actual_working_hours', 'actual_working_hours_cost'], ['allocated_working_hours','planed_working_hours_cost']];
            const plan_work_tip = ['allocated_working_hours','planed_working_hours_cost'];
            const actual_work_tip = ['actual_working_hours', 'actual_working_hours_cost'];
            const work_hour_content = this.resource_select_working === 'planed_resource' ? api.setWorkingHoursLanguage(plan_work_tip) : this.resource_select_working === 'actual_resource' ? api.setWorkingHoursLanguage(actual_work_tip) : api.setWorkingHoursLanguage(plan_and_actual_tip, 'actual_plan');
            const {sum_working_hours_key, working_hours_key} = api.computedWorks(this.resource_select_working);
            this.$('.work-hour-content').html('');
            [sum_working_hours_key, working_hours_key].forEach((item,index) => {
                const is_cost = item.includes('cost');
                this.workHourTip = FxUI.create({
                    wrapper: this.$('.work-hour-content')[index],
                    template: `<fx-tooltip placement="top" popper-class="work-hour-tip">
                        <div slot="content" class="${is_cost ? '' : 'no'}">
                            <div>${$t(`${item}_tip`)}</div>
                            <div style="display: ${select_working && !is_cost ? 'block' : 'none'};">
                                <template>
                                  <div class="text">${$t('actual_working_hours_tip')}</div>
                                  <div class="text">${$t('allocated_working_hours_tip')}</div>
                                </template>
                            </div>
                            <div>${work_hour_content}</div>
                        </div>
                        <span class="fx-icon-question f-title-help crm-ui-title"></span>
                    </fx-tooltip>`
                });
            })
        },
        //存储竖线的位置
		storage(width) {
			const key = `${CRM.ea}_${CRM.curEmpId}_task_line`
			localStorage.setItem(key, width);
		},
        bindResize (el, line, columns) {
            //初始化参数
            var els = el.style;
            //鼠标的 X 和 Y 轴坐标
            var x = 0;
            var y = 0;
            var _this = this;
            //邪恶的食指
            $(line).mousedown(function (e) {
                //按下元素后，计算当前鼠标与对象计算后的坐标
                e = e || window.e;
                (x = e.clientX - el.offsetWidth),
                    (y = e.clientY - el.offsetHeight);
                el.setCapture //捕捉焦点
                    ? (el.setCapture(),
                        //设置事件
                        (line.onmousemove = function (ev) {
                            mouseMove(ev || event);
                        }),
                        (line.onmouseup = mouseUp)) //绑定事件
                    : $(document)
                        .bind("mousemove", mouseMove)
                        .bind("mouseup", mouseUp);
                //防止默认事件发生
                e.preventDefault();
            });
            //移动事件
            function mouseMove (e) {
                let arrow = $(".dt-task-main-box .dt-main-drag-line .arrow");
                let min_width = api.setTableMinWidth(columns, 2);
                const box_width = _this.$el.width() * 0.9;
                //宇宙超级无敌运算中...
                if (e.clientX - x < min_width || e.clientX - x > box_width) {
                    return;
                }
                els.width = e.clientX - x + "px"; //改变宽度
                // _this._toggleFixedHeader(true);
                _this.addResourceScroll(false);
                if(arrow.hasClass("rotate")) {
                    arrow.removeClass("rotate");
                }else {
                    return;
                }
            }
            //停止事件
            function mouseUp () {
                //在支持 releaseCapture 做些东东
                el.releaseCapture //释放焦点
                    ? (el.releaseCapture(),
                        //移除事件
                        (el.onmousemove = el.onmouseup = null)) //卸载事件
                    : $(document)
                        .unbind("mousemove", mouseMove)
                        .unbind("mouseup", mouseUp);
                api.setMemberTextWidth(_this);
                _this.addResourceScroll();
                _this.storage(el.offsetWidth);
            }
        },
        updateResource(isResize) {
            const _this = this;
            if(!this.table.getCurData().length) return;
            this.setResourceH();
            GanttView(this.$('.resource-gantt-container'), _this.initGanttTasks(), {
                isTaskMode: true,
                dates: _this.resource_time_ranges,
                projectresourceobj: _this.options.resourceObj
            }).then(ganttview => {
                _this.ganttview = ganttview;
                _this.addResourceScroll(isResize);
            });
        },
        //设置资源视图高度
        setResourceH(){
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .crm-pr-task-work-table .dt-main") : $(".crm-pr-task-work-table .dt-main");
            if ($(".dt-task-main-box").length) {
                $('.dt-task-main-box .dt-right').height(dtmain.height());
            }
        },
        //初始化资源视图
        initGanttTasks () {
            let resources = [];
            let curData = this.table.getCurData();
            const timeRanges = this.resource_time_ranges || [];
            const working_hours_threshold = this.working_hours_threshold;
            let data = util.parseTreeToNormal(curData);
            data = api.addResourceData(data, timeRanges);
            data.forEach((item) => {
                if (item["isShow"]) {
                    if (item._id) {
                        resources.push(api.setResourceData(item, timeRanges, working_hours_threshold));
                    }
                }
            });
            this._ganttResources = [...resources];
            // console.log(resources);
            return resources;
        },
        //添加模拟滚动条
        addResourceScroll (isResize) {
            this._destroyScrollDiv();
            let $mainBox = $(".dt-task-main-box");
            let $gantt = $mainBox .find(".taskGantt");
            let $tr = $mainBox.find(".main.main-tr-handle .tr");
            let $page = $($tr[$tr.length-1]);
            let mw = $gantt.width();
            let wh = $(window).height();
            let fw = $gantt.find('.project-resource-gantt').width();
            let $mainScroll = $gantt.find(".resource-gantt-container");
            let _scrollx = $mainBox.find('.scroll-x.scroll-scrollx_visible');
            let isOverH = true; // 高度是否超出一屏
			if (!$page.length || ($page.length && ($page.offset().top + 38 < wh))) {
                //数据较少滚动条基于父容器绝对定位,反之固定定位
                isOverH = false;
			}
            if(isResize && _scrollx.length){_scrollx.hide();}
            if (mw == fw) {
				this._destroyScrollDiv();
				return;
			}
            this.setScrollEl('gantt', $gantt, mw, fw, $mainScroll,isOverH);
        },
        setScrollEl(el,$main,mw,fw,$mainScroll,isOverH){
			var noTrigger;
			var offset = $main.offset();
            var $scollDiv = $(
				`<div class="crm-scroll fack-scroll ${el}" style="position:${isOverH?'fixed':'absolute'};left:${isOverH?offset.left:'0'}px;bottom:0px;width:${mw}px;overflow:auto;z-index:100"><div style="height:16px;width:${fw}px"></div></div>`
			);
            $main.append($scollDiv);
			$scollDiv.scrollLeft($mainScroll.scrollLeft());
			$scollDiv.on("scroll", function () {
				if (noTrigger) {
					noTrigger = null;
					return;
				}
				$mainScroll.scrollLeft($scollDiv.scrollLeft());
                $main.find(".resource-fixed").length && $main.find(".project-resource-gantt").css({ left: -$scollDiv.scrollLeft() });
			});
			var fn = _.debounce(function () {
				if (
					Math.abs(
						$scollDiv.scrollLeft() - $mainScroll.scrollLeft()
					) > 100
				) {
					noTrigger = true;
					$scollDiv.scrollLeft($mainScroll.scrollLeft());
				}
			}, 100);
			$mainScroll.on(`scroll.${el}`, fn);

			$scollDiv.$mainScroll = $mainScroll;
            el === 'gantt' && (this._scrollGanttDiv = $scollDiv);
        },
        _destroyScrollDiv: function() {
            this._scrollGanttDiv && this._scrollGanttDiv.remove();
            this._scrollGanttDiv = null;
        },
        destroy: function() {
            this.table && this.table.destroy();
            List.prototype.destroy.apply(this, arguments);
        }
    });
});