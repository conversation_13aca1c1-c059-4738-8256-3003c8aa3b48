/**
 * @file CRM对象显示侧滑详情
 * <AUTHOR>
 */

define(function (require, exports, module) {
	var util = require('crm-modules/common/util');
    function ShowDetail(opts) {

			this.options = _.extend({
            apiName:  '',     // 对象apiName
            zIndex:   500,    // 模块层级
            showMask: false,  // 是否显示遮罩
            top:      0, // @2024年7.9修改为贴顶展示
            subtab:   ''      //默认展示导航 summary-摘要,board-详细信息,team-团队成员
        }, opts || {});
    }

    ShowDetail.prototype = _.extend({

        constructor: ShowDetail,

        // 记录详情路径
        _detailPath: '' ,

        /**
         * @desc 设置apiName
         * 用于多对象列表详情
         * 手动调用
         */
        setApiName: function(apiName) {
            this.options.apiName = apiName;
        },

        /**
         * @desc 判断是否为系统默认对象
         */
        _isSysObj: function() {
            var apiName = this.options.apiName;
            return !!CRM.config.objDes[apiName.toLowerCase()];
        },
				// Activity AI license
				getActivityAILicense(apiname) {
					let self = this

					return new Promise((resolve, reject) => {
						if (self.options.activityLicense !== undefined) {
							resolve()
							return
						}
						CRM.api.get_licenses({
							key: ['ai_interactive_assistant_app'],
							objectApiName: apiname,
							cb: licenses => {
								console.log('Activity AI licenses:=>', licenses)
								self.options.activityLicense = !!licenses?.ai_interactive_assistant_app
								resolve()
							}
						})
					})
				},
				// SFA Sales Record Approval config
				getSalesRecordApprovalConfig(apiname) {
						let self = this;
						return new Promise((resolve) => {
							if (self.options.sfaSalesRecordApprovalConfig !== undefined) {
									resolve();
									return;
							}
							// Assuming Fx.util.getBizConfig is available in this context
							Fx.util.getBizConfig('sfa_sales_record_approval')
							.then(configValue => {
									// console.log('ShowDetail: sfa_sales_record_approval config:=>', configValue);
									self.options.sfaSalesRecordApprovalConfig = !!configValue;
									resolve();
							})
							.catch(error => {
									console.error('ShowDetail: Error fetching sfa_sales_record_approval config:', error);
									self.options.sfaSalesRecordApprovalConfig = false; // Default to false on error
									resolve(); // Ensure promise chain continues
							});
						});
				},
				// 获取AI角色权限
        getActivityUserRole () {
					let self = this
          return new Promise((resolve) => {
						// 上下游传参不同
						let role_code = !CRM.util.isConnectApp() ? 'AIInteractiveAssistantRole' : 'ExternalAISalesAssistantRole'
            if (typeof self.options?.AIInteractiveAssistantRole !== 'undefined') {
                resolve();
                return;
            }
            CRM.util.FHHApi({
              url: '/EM1HNCRM/API/v1/object/activity_user/service/has_role',
              data: {
                role_code
              },
              success: ({ Result, Value }) => {
                // console.log('activity_user/service/has_role:=>', Result, Value)
                if (Result.StatusCode === 0) {
									// 变量其他地方有用到，命名需要统一
                  self.options.AIInteractiveAssistantRole = !!Value[role_code];
                  return resolve();
                }
                return resolve();
              }
            })
          })
        },

        getActivityRole (apiname) {
					return new Promise((resolve) => {
						this.getActivityAILicense(apiname).then(() => {
							if (this.options?.activityLicense) {
								this.getActivityUserRole().then(() => {
									resolve()
								})
							} else {
								resolve()
							}
						})
					})
          // return Promise.all([
          //   this.getActivityAILicense(),
          //   this.getActivityUserRole()
          // ])
        },
		/**
		 * @desc 显示
		 * @param {String} 数据id
		 */
		show(id, apiName, options) {
			var apiname = apiName || this.options.apiName;

			if (FS.util.getUserAttribute('feedObjs') && FS.util.getUserAttribute('feedObjs').indexOf(apiname) >= 0) {
				if (apiname === 'ActiveRecordObj') {
						this.getSalesRecordApprovalConfig(apiname).then(() => {
								if (this.options.sfaSalesRecordApprovalConfig) {
										this.showNewDetail(id, apiname, options);
								} else {
									this.getActivityRole(apiname).then(() => {
										if (this.options.activityLicense && this.options.AIInteractiveAssistantRole) {
											this.showNewDetail(id, apiname, options);
										} else {
											this.showFeed(id, apiname, options);
										}
									})
										// // SFA config is false (or failed to fetch), try AI license
										// this.getActivityAILicense(apiname).then(() => {
										// 		if (this.options.activityLicense) {
										// 				this.showNewDetail(id, apiname, options);
										// 		} else {
										// 				// Both configs are false (or failed), show feed
										// 				this.showFeed(id, apiname, options);
										// 		}
										// });
								}
						});
				}
				else { // Other feed objects
					this.showFeed(id, apiname, options);
				}
			} else if (CRM.util.getDetailVersion(apiname) !== 'v1') {
				this.showNewDetail(id, apiname, options);
			}
			else {
				this.showOldDetail(id, apiname, options);
			}
		},
		showFeed: function (id, apiName, param) {
			var me = this,
				apiname = apiName || me.options.apiName;
			var myIndex = param?.zIndex || param?.data?.zIndex || CRM.util.getzIndex();
			FS.MEDIATOR.trigger('fs.feed2019.slide', {
				url: 'FsFeed/getFeedByObjectId',
				data: {
					apiName: apiname,
					dataId: id
				},
				options: {
					zIndex: Math.max(myIndex, CRM.util.getzIndex()),
					success: function () {
						me.trigger('render.after')
					},
					beforeDestroy: function () {
						me.trigger('hide')
					}
				}
			})
		},
        showNewDetail: function(id, apiName, options) {
            options = options || {};

			if (!options._performance) {
				options._performance = { startTime: +new Date() };
			}

            var me    = this,
                apiname = apiName || me.options.apiName,
                path  = CRM.util.getCrmFilePath(apiname, 'detail'),
                param = Array.prototype.slice.call(arguments);

            console.log(path);
            if (!apiname) {return;}

            //埋点 
            if(apiname == 'SalesStatementsObj'){
                CRM.util.sendLog('statements', 'detail', {
                    operationId: 'click',
                    biz: 'FS-FMCG_SALES'
                });
            }

			if(window.auth && param.source !== 'custom_comp' && !param.disableOpenNewPage) {
				Fx.getBizAction('paasbiz', 'openSitePage', {
					pageType: 13,
					objectApiName: apiname,
					objectDataId: id
				})
				return;
			}


			// 订货通下的产品/商品特殊逻辑
			const isDHT = window.Fx && Fx.IS_DHT_CONNECTAPP;
			if (isDHT && (apiName === 'ProductObj' || apiName === 'SPUObj')) {
				const { masterData, objectData } = this.options;
				const options = {
					masterData,
					objectData,
				};
				CRM.api.show_crm_detail({
					type: apiname,
					data: {
						crmId: id
					},
					options,
				})
				return;
			}

            //FS.crmUtil.waiting();
			if( me._detail && me._detail.options && me._detail.options.apiName !== apiname){
				me._detail.destory();
				me._detail = null;
			}

            if (!me._detail) {

				function exec(Detail) {
					options._performance.loadAssetsEnd = +new Date();

					if (!Detail) {
						return;
					}

					me._detail = new Detail(me.options);

                    me._detail.on('refresh', function(type) {
                        me.trigger('refresh', type);
                    });

										me._detail.on('hide', function() {
											me.trigger('hide');
                    });

					me._detail.on('changeRow', function(index) {
                        me.trigger('changeRow', index);
                    });

                    me._detail.on('render.after', function() {
                        me.trigger('render.after');
                    });

                    me._detail.show(id, apiName, options);

				}
				// 用户报详情页白屏问题。目前排查可能问题，资源请求慢会生成多个详情页实例，但是接口请求是节流加载里面会出现白屏问题
				if (me.detailLock) {
					return;
				}
				me.detailLock = true;
				if (CRM.util.isNeedDev('detail', {apiname: apiname})) {
					var params = _.extend({ea: CRM.ea, action: 'detail'}, me.options, {apiname: apiname});
					CRM2K.loadComp(params, function(Detail) {
						exec(Detail);
						me.detailLock = false;
					})
				} else {
					require.async('vcrm/detail',function(Detail){
						exec(Detail);
						me.detailLock = false;
					})
				}

                return;
            }

			options._performance.loadAssetsEnd = +new Date();

			me._detail.show && me._detail.show(id, apiname, options);
        },

		showOldDetail:function(id, apiName, options){
			var me    = this,
				apiname = apiName || me.options.apiName,
				path  = CRM.util.getCrmFilePath(apiname, 'detail'),
				param = Array.prototype.slice.call(arguments);

			console.log(path);
			if (!apiname) {return;}

			function exec(Detail) {

				if (me._detailPath != path) {
					me._detail && me._detail.destroy();
					me._detail = null;
					me._detailPath = path;
				}

				if (!me._detail || ( me._detail && me._detail.options.apiName !== apiname)) {

					me._detail = new Detail(_.extend(me.options, {
						apiName: apiname
					}));

					me._detail.on('refresh', function(type) {
						me.trigger('refresh', type);
					});

					me._detail.on('hide', function() {
						me.trigger('hide');
					});

					me._detail.on('render.after', function() {
						me.trigger('render.after');
					});
				}

				if (me._isSysObj()) {
					me._detail.show.apply(me._detail, param);
				} else {
					me._detail.show(id, apiname);
				}
			}
			if (CRM.util.isNeedDev('detail', {apiname: apiname})) {
				var params = _.extend({ea: CRM.ea, action: 'detail'}, me.options, {apiname: apiname});
				CRM2K.loadComp(params, function(Detail) {
					exec(Detail);
				})
			} else {
				require.async(path, function(Detail) {
					exec(Detail);
				});
			}
		},

        hide: function() {
            this._detail && this._detail.hide();
        },

        /**
         * @desc 销毁对象
         */
        destroy: function() {
            var me = this;
            me._detail && me._detail.destroy();
            me._detail = null;
        }

    }, Backbone.Events);


    module.exports = ShowDetail;

});
