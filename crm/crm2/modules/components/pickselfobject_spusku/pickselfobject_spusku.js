/**
 *@desc 中台-选产品/商品组件
 *<AUTHOR>
 */
define(function (require, exports, module) {
	var PickSelfObject = require('crm-modules/components/pickselfobject_classification/pickselfobject_classification');
	const { getCategoryRequestExtra } = require('crm-modules/page/shopmall/utils');
	var widgetService = require('./service');
	var util = CRM.util;
	const shopcategory = require('./shopcategory');
	const EastCategory = require('./components/east-category');
	const VIEW_TYPES = {
		TABLE: 'table',
		CARD: 'card',
		SP: 'split',
	};

	var newPickSelfObject = function (opt) {
		const curERUpstreamEa = $.cookie('ERUpstreamEa');
		const cacheViewType = util.getCache('dht-product-view-type');

		let self = this;
		this.isSpu = window.$dht.config.sail.isSpuMode;
		this.$$vue = {};
		this.quantityTimerMap = {};
		this.cardView = null; // 卡片视图
		this.splitView = null; // 分屏视图
		this.commodityOptions = [];
		this.viewType = cacheViewType;
		this.cardListData = [];
		this.isFirstInit = true;
		// 易事特客开分类
		this.isEastCategory = ['east2020', '90327', '777375_sandbox'].includes(curERUpstreamEa);
		this.eastCategoryView = null;
		// 引导卡片
		this.guideCard = null;

		// 处理产品/商品分类组件的请求参数
		const categoryRequestExtra = getCategoryRequestExtra();
		let fBtnName = this.isSpu ? $t('商品分类') : $t('产品分类');
		shopcategory.isEnable && (fBtnName = $t('dht.self_object.shop_category'));

		this.options = _.extend({
			maxSize:true,
			isMultiple: true, // 不显示checkbox
			showMultiple: false,
			isChooseSpu: !!this.isSpu, // spu模式下，固定选spu，不读取配置
			openFloatCategory: { // 打开浮动分类
				floatFirst: this.viewType !== VIEW_TYPES.TABLE, // 优先显示浮动分类
				fixCategoryBtnVisibility: false,
				fBtnName,
				fClassName: this.isEastCategory ? 'dht-hide-category' : 'dht-category-float',
			},
			categoryRequestExtra,
      		_totalNumLimit: 1000, // 分页总数最大显示数量

			beforeRequest: (rq) => {
				const {
					master_data,
					tabName,
					collectionIds = [],
				} = this.options;
				rq.master_data = master_data;
				// 收藏商品需更新参数里的filter条件
				if (tabName && tabName.indexOf('dht_shopmall_collectionproduct') !== -1) {
					let sq = JSON.parse(rq.search_query_info);

					// 没有收藏商品时，重置参数给一个不存在的id，防止报错
					if (!collectionIds.length) {
						let filters = sq.filters || [];
						let field = _.find(
							filters,
							(a) => a.field_name === '_id'
						);
						field && (field.field_values = ['null']);
					}
					sq.limit = 500;
					rq.search_query_info = JSON.stringify(sq);
				}

				if (window.$dht.config.newAvailableRangeFilter?.isEnable) {
					const availableRangeFilterData = window.$dht.getMainFilterData();
					const { apiname, value } = availableRangeFilterData;
					if (apiname) {
						rq.master_data[apiname] = value;
						rq.object_data[apiname] = value;
					}
				}

				return rq;
			},

			async formatDataAsync(rq) {
				if (
					window.$dht.config.simpleCpq.isEnable &&
					window.$dht.config.sail.isSimpleCpqShowAdjustPrice
				) {
					// 显示固定搭配调整价
					const { BomService } = await widgetService.getService('bomService');
					let bomService = new BomService();
					await bomService.adjustSimpleCpqPrice(rq.data);
				}
				const { UnitService } = await widgetService.getService('unitService');
				let unitInstance = UnitService.getInstance();
				await unitInstance.formatDataAsync(rq);
				return rq;
			},

			formatColumns: (columns, apiname) => {
				const operateColumn = columns.find(column => column.id === 'operate_column');
				if (operateColumn) {
					operateColumn.isEdit = true;
					const isZd = ['744767_sandbox', 'zdylyp'].includes($.cookie('ERUpstreamEa'));
					if (isZd) {
						operateColumn.title = $t(
							'crm.order_quickly.zd_quantity'
						);
						operateColumn.width = 200;
					} else {
						operateColumn.title = $t('数量');
						operateColumn.width = 124;
					}

					operateColumn.className = 'column-operate';

					operateColumn.render = (data, column, fulldata) => {
						const id = `quantity-input-${fulldata._id}`;

						if (this.$$vue[fulldata._id]) {
							this.$$vue[fulldata._id].$destroy();
							delete this.$$vue[fulldata._id];
						}
						if (this.quantityTimerMap[id] != null) {
							clearTimeout(this.quantityTimerMap[id]);
						}
						const timer = setTimeout(() => {
							clearTimeout(timer);
							delete this.quantityTimerMap[id];
							widgetService.getWidgetApp('cartQuantityInput', {
								propsData: {
									product: fulldata,
								},
							}).then(instance => {
								this.$$vue[fulldata._id] = instance;
								$(`#${id}`).append(instance.$el);
								instance.$on('change', ({type, product}) => {
									if (type === 'unit') {
										this.table.setCellsValue({
											[product.__tbIndex]: product
										});
									}
								});

								instance.$on('focus', (product) => {
									// 聚焦的时候，展开属性
									if (product.attribute) {
										this.table.showAttribute(product);
										CRM.util.parseAttrData(false, product);
									}
								});
							});
						}, 0);
						this.quantityTimerMap[id] = timer;

						return `<div id="${id}" style="width: 100%;"></div>`;
					};
				}
				return columns;
			},

			formatOtherBtns: (btns = []) => {
				const isDefaultTable = this.viewType === VIEW_TYPES.TABLE;
				return btns.concat([
					{
						isDefault: this.viewType === VIEW_TYPES.TABLE,
						name: VIEW_TYPES.TABLE,
						text: $t("列表视图"),
						attrs: 'data-type="table"',
						className: `fx-icon-list-2 list-con j-btn-guide os-list-btn ${this.viewType === VIEW_TYPES.TABLE ? 'cur' : ''}`
					},
					{
						isDefault: this.viewType === VIEW_TYPES.CARD,
						name: VIEW_TYPES.CARD,
						text: $t("卡片视图"),
						attrs: 'data-type="card"',
						className: `map-con j-btn-guide os-dht-card-btn el-icon-menu ${this.viewType === VIEW_TYPES.CARD ? 'cur' : ''}`
					},
					{
						isDefault: this.viewType === VIEW_TYPES.SP,
						name: VIEW_TYPES.SP,
						text: $t("分屏视图"),
						attrs: 'data-type="split"',
						className: `crm-btn fx-icon-view-splitscreen j-btn-guide os-split-btn crm-ui-title ${this.viewType === VIEW_TYPES.SP ? 'cur' : ''}`
					}
				]);
			},

			formatTableOption(options) {
				_.extend(options, {
				}, self.options.tableOptions)
			},

			// 视图模式切换，初始时也会执行
			otherbtnChangeHandle: async (type, obj) => {
				if (!this.$$vue['sortSelectComp']) await this.initSortSelect(); // 初始化排序组件

				// 初始化完成后切换再销毁弹框
				!this.isFirstInit && this.destroyGuideCard();

				this.viewType = type;
				util.setCache('dht-product-view-type', type);

				!this.options.forceHideCategory && !this.isFirstInit && this.toggleCategory();

				if (type === VIEW_TYPES.TABLE) {
					$('.j-dht-card-sort', this.$el).hide(); // 隐藏排序组件

					if (!this.isFirstInit) {
						this.destroyCardView();
						this.destroySPView();
						this.showTable();
					}
				} else if (type === VIEW_TYPES.CARD) {
					$('.j-dht-card-sort', this.$el).show(); // 显示排序组件
					this.changeSortDefaultSelect();

					this.hideTable();
					this.destroySPView();
					const instance = await this.renderCardView();
					const params = obj.getParam();
					this.getCardListData_bofore(params)
				} else if (type === VIEW_TYPES.SP) {
					$('.j-dht-card-sort', this.$el).show(); // 显示排序组件
					this.changeSortDefaultSelect();

					this.hideTable();
					this.destroyCardView();
					const instance = await this.renderSplitScreenView();
					const params = obj.getParam();
					this.getSpListData_before(params);
				}
				this.isFirstInit = false;

				this.setPagePos();
			},

			trclickHandle: (data, $tr, $target) => {
				if (!$target.hasClass('dht-quantity-input') && !$target.parents('td.column-operate').length) {
					const allProducts = this.table.getCurData().data;
					this.openProductDetail(data, allProducts);
				}
			},

			refreshCurView: async (obj) => {
				const params = obj.getParam();
				if (this.viewType === VIEW_TYPES.CARD) {
					this.getCardListData_bofore(params);
				} else if (this.viewType === VIEW_TYPES.SP) {
					this.getSpListData_before(params);
				}
			},
			calcTableWrapperHeight() {
				// 减去tabs和分页底部高度
				const height = $('.dht-shopmall').height() - 40 - 48;
				return height;
			},
		}, opt);

		shopcategory.initAttrs.bind(this)(true);
	};

	_.extend(newPickSelfObject.prototype, PickSelfObject.prototype, {

		/**
		 * 更新分页组件
		 * @param {number} total
		 */
		rerenderPigination(total = 0) {
			const isLimit = total >= this.options._totalNumLimit;

			// TODO PaaS应该为objecttable封装一个pagination组件，并提供更新接口，现在先使用dom控制
			if (!this.table.table._pagination) return;
			this.table.table._pagination.setTotalSize(total);
			this.$(".total-num").toggle(!isLimit);
			this.$(".about-num, .about-num-wrap").toggle(isLimit);
		},

		getCardListData_bofore(params) {
			this.cardView.isLoading = true;
			this.getCardListData(params).then(({ result, total }) => {
				this.cardView.list = result;
				this.cardView.isLoading = false;
        		this.rerenderPigination(total);
			})
		},

		getSpListData_before(params) {
			this.splitView.isLoading = true;
			this.getCardListData(params).then(({ result, total }) => {
				this.splitView.list = result;
				this.splitView.isLoading = false;
				this.rerenderPigination(total);
			});
		},

		async getCardListData_after(rq) {
			const formatData = await this.options.formatDataAsync({
				data: rq.dataList,
				totalCount: rq.total
			});

			rq.dataList = formatData.data;
			return rq
		},

		getCardListData(params) {
			const { tabName, collectionIds = [] } = this.options;

			// 优化没有收藏商品的情况，不进行请求
			if (tabName === 'dht_shopmall_collectionproduct' && !collectionIds.length) {
				this.cardListData = [];
				return Promise.resolve({ result: this.cardListData, total: 0 });
			}

			params.include_describe = !this.commodityOptions.length;
			return new Promise((resolve, reject) => {
				util.FHHApi({
					url: '/EM6HNCRM/API/v1/object/' + this.options.apiname + '/controller/RelatedList',
					data: params,
					success: (res) => {
						if (res.Result.StatusCode === 0) {
							this.getCardListData_after(res.Value).then(r => {
								this.cardListData = this.formatCommodity(r);
								resolve({ result: this.cardListData, total: r.total });
							});
						} else {
							util.alert(res.Result.FailureMessage || $t('暂时无法获取数据!'));
							this.cardListData = [];
							resolve({ result: this.cardListData, total: 0 });
						}
					}
				}, {
					errorAlertModel: 1
				});
			});
		},

		formatCommodity(data) {
			const { dataList = [], objectDescribe } = data;
			// 如果描述存在，标签信息
			if (objectDescribe) {
				const commodityField = objectDescribe.fields.commodity_label;
				if (commodityField) {
					this.commodityOptions = commodityField.options;
				}
			}

			dataList.forEach(item => {
				item.commodityOptions = this.getCommodityOptions(item);
			});
			return dataList;
		},

		getCommodityOptions(product) {
			const options = [];
			 (product.commodity_label || []).forEach(item => {
				const temp = this.commodityOptions.find(option => option.value === item);
				if (temp) {
					options.push(temp);
				}
			});
			return options;
		},

		async renderCardView() {
			const $wrapper = $('<div></div>');
			this.$('.pickself-box .t-wrap').append($wrapper);
			// 是否显示库存
			const { isEnable, visibleType } = window.$dht.config.inventory;
			const isShowStock = isEnable && visibleType !== '1';
			//选对象列表pwc插件
			let pwcCard = null;

			const pwcRst = await this.table?.runPlugin('dht.shopmall.list.render.before').then(res => {
				if(res.StatusCode == 0 && res.Value) {
					return res.Value;
				}
				return null;
			})
			if(pwcRst && pwcRst.shoppingCard) {
				pwcCard = pwcRst.shoppingCard;
				// console.log('dht.shopmall.list.render.before', pwcCard)
			}			
			
			const instance = await widgetService.getWidgetApp('productList', {
				el: $wrapper[0],
				propsData: {
					list: [],
					prefixClass: 'dht-shopmall-card-list',
					isLoading: true,
					options: {
						precision: 2,
						isLazyImg: true,
						// scrollContainer: '.dht-shopmall-card-list',
						isShowStock,
						visibleType: '2',
						pwcCard 
					}
				},
			});
			instance.$on('collectChange', (data) => {
				debugger
				if(this.options.tabName === 'dht_shopmall_collectionproduct') { // 当前是收藏tab时，需做 删除|添加 操作
					if (data.isCollect) {
						let targetObj = _.find(this.cardListData, a => { return a._id === data.id});
						targetObj && instance.list.push(targetObj);
					} else {
						instance.list = _.filter(instance.list, a => { return a._id !== data.id });
					}
				} else { // 其他tab只需修改收藏字段的状态
					let targetObj = _.find(instance.list, a => { return a._id === data.id});
					targetObj && (targetObj.is_in_collection = data.isCollect);
				}

				// 更新缓存的收藏产品/商品id
				this.options.collectionIds = _.keys(data.collectionIds || []);
			})
			this.cardView = instance;
			return instance;
		},

		async renderSplitScreenView() {
			const $wrapper = $('<div></div>');
			this.$('.pickself-box .t-wrap').append($wrapper);

			this.splitView = await widgetService.getWidgetApp('splitScreen', {
				el: $wrapper[0],
				propsData: {
					list: []
				},
			});

			return this.splitView;
		},

		// 初始化排序组件（只有在卡片视图下显示使用）
		async initSortSelect() {
			let $target = 	`<div class="item j-dht-card-sort">
								<span class="line"></span>
								<span class="item-tit">${$t('排序')}:</span>
								<div class="item-con">
									<div class="j-dht-card-sort-content"></div>
								</div>
							</div>`
			this.$('.pickself-box .dt-term-batch .other-item').append($target);

			const orderColumns = (this.table.getAllColumns() || [])
			.filter((item) => item.isOrderBy && item.data)
			.map((item) => {
				return {
					fieldName: item.data,
					label: item.label,
					status: 0,
				};
			});

			const Sort = await widgetService.getWidgetApp('sortSelect', {
				el: $('.j-dht-card-sort-content', this.$el)[0],
				propsData: {
					items: orderColumns
				},
			})
			Sort.$on('change', (data) => {
				this.table.table.setParam({
					SortField: data ? data.fieldName : '',
					SortType: data ? data.status : 0
				}, true);

				let params = this.table.table.getParam();
				params = this.table.parseParam(params);

				if (this.viewType === VIEW_TYPES.CARD) {
					this.getCardListData_bofore(params);
				} else if (this.viewType === VIEW_TYPES.SP) {
					this.getSpListData_before(params);
				}
			})
			this.$$vue['sortSelectComp'] = Sort;
		},

		// 改变排序组件的显示值（在卡片模式下需要同步列表模式上的排序）
		changeSortDefaultSelect() {
			let p = this.table.table.getParam();
			let sortParams = p.SortField ? {fieldName: p.SortField, status: parseInt(p.SortType)} : null
			this.$$vue['sortSelectComp'].setSelectItem(sortParams)
		},

		/**
		 * 显示商城提示
		 */
		async showGuideCard() {
			const shopmallGuideStatus = await Fx.store.getItem('dht.guide.shopmall');
			if (shopmallGuideStatus === 1) {
				return;
			}

			seajs.use('base-modules/guide/guide', (Guide) => {
				this.guideCard = new Guide({
					type: 'onetip',
					notSetKey: true,
					data: [{
						$target: $('.other-item'),
						pos: 'bottom',
						appendBody: true,
						text: $t('dht.guide.shopmall.change_view_type'),
					}]
				})

				Fx.store.setItem('dht.guide.shopmall', 1);
			});
		},

		destroyGuideCard() {
			if (this.guideCard) {
				this.guideCard.destroy();
				this.guideCard = null;
			}
		},

		renderEastCategory() {
			if (!this.isEastCategory || this.eastCategoryView) {
				return;
			}

			$('.dt-caption').after('<div class="east-category-wrapper"></div>');
			this.eastCategoryView = new EastCategory({
				$el: $('.east-category-wrapper'),
				categoryRequestExtra: this.options.categoryRequestExtra,
			});

			this.eastCategoryView.on('on-category-click', (data) => {
				this.table._cateGory?.trigger('update:catcas', data.CategoryId);
				this.table.clickCategoryItemCb(data);
			});
		},

		afterRenderTableComplete() {
			PickSelfObject.prototype.afterRenderTableComplete.apply(this, arguments);
			if (this.viewType !== VIEW_TYPES.TABLE) {
				this.hideTable();

				this.isEastCategory && this.renderEastCategory();
			}

			var hasQuery = this.options.hasQuery;
			if (hasQuery && hasQuery.from === 'homesearch') {
				if (this.table.table._search._select) {
					this.table.table._search._select.setValue('name', false);
					$('.dt-sc-box').find('.dt-ipt').val(this.options.hasQuery.value);
					$('.dt-sc-box').find('.dt-sc-reset').show();
				} else {
					this.table.table._search.once('crm.table.searchrendercomplete', () => {
						this.table.table._search._select.setValue('name', false);
						$('.dt-sc-box').find('.dt-ipt').val(this.options.hasQuery.value);
						$('.dt-sc-box').find('.dt-sc-reset').show();
					});
				}
			}

			this.showGuideCard();
			// 绑定滚动事件
			PickSelfObject.prototype.bindSrollEvent.apply(this, arguments);
			// 绑定侧边栏事件
			FS.MEDIATOR.on('crm.aside.resize', this.setPagePos, this);
		},

		setPagePos: function (left) {
			let value = 0;
			if (left) {
				// 侧边栏展开收起情况
				value = left < 250 ? left : left + 14; // + 余量14

				//  分类宽度216
				if (this.viewType === VIEW_TYPES.TABLE) {
					value += 216;
				}
			} else {
				// table resize
				value = this.$('.t-wrap').offset().left;
			}

			this.$('.dt-page:first').css({
				left: value,
			})
		},

		addToCart(products) {
			return widgetService.getService('cartService').then(({CartService}) => {
				let instance = CartService.getInstance();
				return instance.addProductToCart(products);
			})
		},

		destroyCardView() {
			if (this.cardView) {
				this.cardView.$destroy();
				this.$(this.cardView.$el).remove();
				this.cardView = null;
			}
		},

		destroySPView() {
			if (this.splitView) {
				this.splitView.$destroy();
				this.$(this.splitView.$el).remove();
				this.splitView = null;
			}
		},

		hideTable() {
			this.$('.dt-main').hide();
			this.$('.column-set-wrap').hide();
			this.$('.dt-size').hide();
			this.table.table.closeStart();
			this.resetHeaderEl(true);
		},

		showTable() {
			this.$('.dt-main').show();
			this.$('.column-set-wrap').show();
			this.$('.dt-size').show();
			this.table.table.start();
			this.resetHeaderEl();
		},

		// 解决滚动条滚动事件的后遗症
		resetHeaderEl(isDefault) {
			// 此段逻辑是为了兼容表格/卡片来回切换时，某些固定元素的展示问题
			// 在底层scrollHandle滚动事件方法里有用到__$header
			if(this.__$header) {
				// 每次将其清空，在底层方法里重新去计算定位
				this.table.table.fixedHeader();
				this.__$header.remove();
				this.__$header = null;
			}
			if (isDefault) {
				// 底层会判断有默认值就跳过，所以给个虚拟默认值，在卡片模式下，不用设置元素及定位
				this.__$header = $('<div style="height:42px"></div>');
			}
		},

		toggleCategory() {
			let tableExt = this.table;
			if (this.viewType !== VIEW_TYPES.TABLE) { // 卡片视图显示顶部悬浮分类
				tableExt.floatCategoryCollapseLeft(true);
				if (!tableExt._floatCategory) {
					tableExt._createFloatCategory(this.options.openFloatCategory.floatCategoryEl);
				} else {
					tableExt._floatCategory.$el.show();
				}

				if (this.eastCategoryView) {
					this.eastCategoryView.updateCategory(tableExt._realCategoryId); // 更新分配
					this.eastCategoryView.$el.show();
					$('.category-cascader-filter').hide();
				} else {
					this.isEastCategory && this.renderEastCategory();
				}
			} else { // 表格视图显示左侧树形分类
				tableExt._floatCategory && tableExt._floatCategory.$el.hide();
				if (this.eastCategoryView) {
					this.eastCategoryView.$el.hide();
					$('.category-cascader-filter').show();
				}
				tableExt.floatCategoryCollapseLeft(false);
				if (!tableExt._cateGory) {
					tableExt._createCommonCategory();
				}
			}
		},

		openProductDetail (product, allProducts) {
			window.$dht.openProductDetail({
				title: product.name,
				isSpuMode: this.isSpuObj(product),
				product: this.getDetailProduct(product),
				allProducts: this.getDetailProduct(allProducts),
			});
		},

		getDetailProduct(products) {
			if (Array.isArray(products)) {
				return products.map(product => {
					const isSpuObj = this.isSpuObj(product);
					return {
						spu_id: isSpuObj ? product._id : '',
						_id: isSpuObj ? (product.product_id__ro ? product.product_id__ro._id : '') : product._id ,
						is_package: product.is_package,
					};
				});
			} else {
				const isSpuObj = this.isSpuObj(products);
				return {
					spu_id: isSpuObj ? products._id : '',
					_id: isSpuObj ? (products.product_id__ro ? products.product_id__ro._id : '') : products._id ,
					is_package: products.is_package,
				};
			}
		},

		isSpuObj(product) {
			return product.object_describe_api_name === 'SPUObj';
		},

		destroyEastCategoryView() {
			if (this.eastCategoryView) {
				this.eastCategoryView.destroy();
				this.eastCategoryView = null;
			}
		},

		destroy() {
			const keys = Object.keys(this.$$vue);
			keys.forEach(key => {
				this.$$vue[key] && this.$$vue[key].$destroy();
			});
			this.destroyCardView();
			this.destroySPView();
			this.destroyEastCategoryView();
			this.destroyGuideCard();

			FS.MEDIATOR.off('crm.aside.resize', this.setPagePos);
			PickSelfObject.prototype.destroy.apply(this, arguments);
		}
	});

	module.exports = newPickSelfObject;
});
