define(function (require, exports, module) {
	var util = CRM.util;
	var api = CRM.api;
	var Table = require('crm-modules/components/objecttable/objecttable');
	var sendqx = require('../sendqx/sendqx');
	var slaveTpl = require('./slave-html');

	return Backbone.View.extend({
		template: require('./tpl-html'),

		events: {
			'mouseenter .j-duplicate-sendqxtoadmins': '_sendqxtoadminsHandle',
			'click .j-look': 'lookHandle',
			'click .j-choose': 'receiveCustomerHandle'
		},

		options: {
			objectType: null, //老对象查重需要此参数
			label: '',
			apiname: '',
			data: {},
			type: 'NEW',
			height: 350,

		},

		initialize: function () {
			if (!this.options.noDataTip && this.options.lable) {
				var noDataTipLable = $t('没有找到相关') + this.options.lable + $t('请点击下方按钮新建');
				var btnLable = $t('立即新建');
				var noDataTip = '<div>' + noDataTipLable + '</div><span class="b-g-btn j-add-object" style="font-size: 13px;">' + btnLable + '</span>';
				this.options.noDataTip = noDataTip;
				this._widgets = {};
			}
		},

		render: function (data) {
			var opts = this.options;
			data && (opts.data = data);
			if (this.table) {
				this.table.duplicate(data);
				return;
			}
			this.$el.html(this.template(opts))
			this.$t = this.$('.j-duplicate-table');
			var me = this;
			this.fetchHeader(opts, function (res) {
				me.renderJavaTable(opts, res);
			});
		},

		filterOperate: function (operate) {
			// 云之家无发企信功能
			if (CRM.control.isYunZhiJia || (window.Fx && window.Fx.imAppStatus === 'hidden')) {
				var actions = ['admin', 'owner'];
				var indexs = [];

				_.map(operate.action || [], function (item, index) {
					if (_.contains(actions, item)) {
						indexs.push(index);
					}
				});

				operate.action = _.filter(operate.action, function (item, index) {
					return !_.contains(indexs, index);
				});

				operate.label = _.filter(operate.label, function (item, index) {
					return !_.contains(indexs, index);
				});

				return operate;
			}
		},

		//主对象的渲染表格网
		renderJavaTable: function (opts, res) {
			var me = this;
			me.createPermission = res.create_permission;
			me.adminIds = res.admin_ids;
			var $table = $('.recheck-table', this.$el);

			var MyTable = Table.extend({
				fetchColunms: function (param, cb, context) {
					this.fetchColunmsSuccess(res, cb);
				},
				parseColumns: function (headers, fields) {
					var columns = Table.prototype.parseColumns.apply(this, arguments);
					_.each(columns, function (item) {
						item.fixed = false
					})
					return columns
				},
				getOptions: function () {
					var options = Table.prototype.getOptions.call(this);
					if (me.options.simOrHigh || opts.type === 'NEW') {
						options.url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/DuplicateSearch'
					} else {
						options.url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/SimpleDuplicateSearch'
					}

					options.url = opts.url || options.url;

					options.searchTerm = options.showTerm = options.showManage = options.showMoreBtn = options.showFilerBtn = options.showMultiple = false;
					me.createPermission && (options.noDataTip = opts.noDataTip);
					options.columns.push({
						width: 150,
						data: 'handle',
						title: $t('操作'),
						lastFixed: true,
						render: function (data, type, full) {
							var html = '';
							let cusHtml = me._generateCusOpereateHtml(full);
							if (!full.operate) return html + cusHtml;
							
							me.filterOperate(full.operate);

							_.each(full.operate.action, function (item, index) {
								if (item === 'detail') {
									html += '<a data-action="operate" data-operate="detail" class="j-duplicate-action" style="margin-right: 10px" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else if (item === 'other') {

								} else if (item === 'choose') {
									html += '<a data-action="operate" data-operate="choose" class="j-duplicate-action" style="margin-right: 10px" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else if (item === 'owner') {
									html += '<a data-action="operate" data-operate="owner" class="j-duplicate-action" style="margin-right: 10px" href="javascript:;">' + full.operate.label[index] + '</a>';//联系负责人
								} else if (item === 'change_partner_owner' && me.isConnectApp()) {
									html += '<a data-action="operate" data-operate="change_partner_owner" class="j-duplicate-action" style="margin-right: 10px" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else if (item === 'add_team_member' && me.isConnectApp()) {
									html += '<a data-action="operate" data-operate="add_team_member" class="j-duplicate-action" style="margin-right: 10px" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else {
									html += '<a data-id="' + full._id + '" class="j-duplicate-sendqxtoadmins tr-operate-wrap" href="javascript:;">' + full.operate.label[index] + '</a>';
								}
							})

							return html + cusHtml
						}
					})

					options.columns[0].render = function (data, type, full) {
						// 防止赋值给title的内容带有html标签
						var _data;

						try {
							_data = $('<div></div>').html(data).text();
						} catch (e) {
							_data = '';
						}

						var str = '<span title=' + _data + '>' + data + '</span>';

						if (full.life_status === 'invalid') {
							return '<span class="invalid-tip">(' + $t("已作废") + ')</span>' + str;
						}

						return str;
					}

					options = opts.getOptions ? opts.getOptions.call(me, options) : options;

					options.onTrOperateHover = me._sendqxtoadminsHandle.bind(me);
					var toolCustomOpts = me.options.toolCustomOpts
					if(toolCustomOpts && toolCustomOpts.beforeInitTable) {
						options = toolCustomOpts.beforeInitTable(options)
					}
					return options;
				},
				parseParam: function (obj) {
					var param
					if (me.options.simOrHigh || opts.type === 'NEW') {
						param = Table.prototype.parseParam.apply(this, arguments);
						param.describe_api_name = opts.apiname;
						param.include_object_describes = true;
						param.type = opts.type;
						param.object_data = opts.data;
						param.is_need_duplicate = !!opts.needDuplicate;
						param.page_size = obj.pageSize;
						param.page_number = obj.pageNumber;
						opts.needDuplicate = false;//请求一次之后置为false
					} else {
						param = {}
						param.describe_api_name = opts.apiname;
						// param.type = opts.type;
						param["include_object_describes"] = true;
						param.keyword = $('.simple-ipt').val();
						param.is_need_duplicate = !!opts.needDuplicate;
						param.page_size = obj.pageSize;
						param.page_number = obj.pageNumber;
					}

					param = opts.parseParam ? opts.parseParam.call(me, param) : param;

					opts.data && (param.duplicate_rule_api_name = opts.data.duplicate_rule_api_name);

					if(opts.type === 'NEW') {
						param.rule_api_name_intercepted = me.duplicate_rule_api_name;
					}

					return param;
				},
				parseData: function (obj) {
					obj = opts.parseData ? opts.parseData.call(me, obj) : obj;
					obj.dataList = obj.data_list;
					obj.total = obj.page.total;
					var tm = Table.prototype.parseData.apply(this, arguments);
					console.log(tm)
					if (obj.button_info) {
						var temp = {};
						_.each(obj.button_info.buttons, function (obj) {
							temp[obj.api_name] = obj.label;
						})
						var tb = {}
						_.each(obj.button_info.button_map_list, function (action, id) {
							tb[id] = {
								action: [],
								label: []
							}
							_.each(action, function (item) {
								tb[id].action = action
								tb[id].label.push(temp[item])
							})
						})
						_.each(tm.data, function (obj) {
							obj.operate = tb[obj._id];
						})
					}
					me.related_search_infos = obj.related_search_infos
					me.duplicate_rule_api_name = obj.duplicate_rule_info && obj.duplicate_rule_info.duplicate_rule_api_name;
					me.renderTip(obj)
					return tm;
				},
				trclickHandle: function (data, $str, $target) {
					var action = $target.data('action');
					action === 'operate' && me.excuteAction($target, data);
					action === 'cusoperate' && me.excuteCusOperate($target, data);
				},
				duplicate: function (data) {
					opts.needDuplicate = true;
					opts.data = data;
					this.refresh();
				}
			})
			var table = new MyTable({
				el: opts.$el || me.$('.j-duplicate-table'),
				apiname: opts.apiname,
				showTitle: false,
				noOperate: true,
				isFilter: false
			})
			table.render();

			me.table = table;
		},

		//根据传进来得配置，生成操作按钮模板
		_generateCusOpereateHtml(trData, table) {
			let operateBtns = this.options && this.options.duplicateConfig && this.options.duplicateConfig.operateBtns;
			if(!operateBtns) return '';

			let adds = [];
			_.each(operateBtns, fn => {
				let cus = fn(trData) || {};
				adds = adds.concat(cus.add || []);
			})

			trData._cusOperateBtns = adds;

			let length = adds.length;
			if(length > 1) {
				let table = this.table && this.table.table;
				table && table.updateOpereateWidth && table.updateOpereateWidth(150 + (length - 1) * 80);
			}

			return _.map(adds, (btn, index) => {
				return `<a data-action="cusoperate" data-index="${index}" style="margin-right:10px" href="javascript:;">${_.escape(btn.label)}</a>`
			}).join('');
		},

		excuteCusOperate($target, trData) {
			let index = $target.data('index');
			trData._cusOperateBtns[index].callBack(trData)
		},

		triggerEvent: function (type, data) {
			var me = this;
			setTimeout(function () {
				me.trigger(type, data);
			}, 0)
		},

		fetchSlaveHeader: function (opts, callback) {
			var me = this;
			var url, data
			if (this.options.simOrHigh || opts.type === 'NEW') {
				url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/RelatedDuplicateSearch'
				data = {
					describe_api_name: opts.apiname,
					type: opts.type || 'NEW',
					object_data: opts.data,
					is_need_duplicate: true,
					page_size: 10,
					page_number: 1
				}
			} else {
				url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/RelatedSimpleDuplicateSearch'
				data = {
					describe_api_name: opts.apiname,
					// type: opts.type || 'NEW',
					keyword: $('.simple-ipt').val(),
					is_need_duplicate: true,
					page_size: 10,
					page_number: 1
				}
			}

			data.duplicate_rule_api_name = me.duplicate_rule_api_name;

			util.waiting('', true);
			me._javaAjax = util.FHHApi({
				url: url,
				data: data,
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						var rvr = res.Value.results;
						var a = {}
						for (var i = 0; i < rvr.length; i++) {
							a[rvr[i].object_describe.api_name] = {
								Value: {
									objectDescribe: rvr[i].object_describe,
									objectDescribeExt: rvr[i].describe_ext,
									layout: {
										components: [{
											field_section: [{
												form_fields: _.map(rvr[i].include_fields, function (apiname) {
													return {
														field_name: apiname
													}
												})
											}]
										}]
									}
								},
								create_permission: rvr[i].create_permission,
								admin_ids: rvr[i].admin_ids,
								count: rvr[i].data_list.length,
								typeLength: rvr.length,
								keep_save: rvr[i].keep_save,
								match_type: rvr[i].match_type
							}
							// me.triggerEvent('complete', {
							//     isMaster: true,
							//     isKeepSave: rvr[i].keep_save,
							//     matchType: rvr[i].match_type //PRECISE 精确匹配 FUZZY模糊匹配
							// });
						}
						for (var key in a) {
							callback(a[key])
						}
						console.log(a)
					} else {
						me.triggerEvent('error', res);
						util.alert(res.Result.FailureMessage);
						me.$('.crm-loading').hide();
						// me.$('.loading-text').text(res.Result.FailureMessage || '');
					}


				},
				complete: function () {
					me._javaAjax = null;
					util.waiting(false, true);
				},
				error() {
					util.waiting(false, true);
				}
			}, {
				errorAlertModel: 1
			})
		},

		fetchHeader: function (opts, callback) {
			var me = this;
			var url, data
			if (this.options.simOrHigh || opts.type === 'NEW') {
				url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/DuplicateSearch'
				data = {
					describe_api_name: opts.apiname,
					type: opts.type || 'NEW',
					object_data: opts.data,
					include_object_describes: true,
					is_need_duplicate: true,
					page_size: 10,
					page_number: 1
				}
			} else {
				url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/SimpleDuplicateSearch'
				data = {
					describe_api_name: opts.apiname,
					// type: opts.type || 'NEW',
					keyword: $('.simple-ipt').val(),
					is_need_duplicate: true,
					page_size: 10,
					page_number: 1
				}
			}

			url = opts.url || url
			data = opts.parseParam ? opts.parseParam.call(me, data) : data;

			opts.data && (data.duplicate_rule_api_name = opts.data.duplicate_rule_api_name);

			me._javaAjax = util.FHHApi({
				url: url,
				data: data,
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						var rv = opts.parseData ? opts.parseData.call(me, res.Value) : res.Value;

						if(rv.duplicate_rule_info) {
							me.duplicate_rule_api_name = rv.duplicate_rule_info.duplicate_rule_api_name;
						}

						callback(
							{
								Value: {
									objectDescribe: rv.object_describe,
									objectDescribeExt: rv.describe_ext,
									layout: {
										components: [{
											field_section: [{
												form_fields: _.map(rv.include_fields, function (apiname) {
													return {
														field_name: apiname
													}
												})
											}]
										}]
									}
								},
								create_permission: rv.create_permission,
								admin_ids: rv.admin_ids,

							}
						)
						me.triggerEvent('complete', {
							isMaster: true,
							isKeepSave: rv.keep_save,
							matchType: rv.match_type //PRECISE 精确匹配 FUZZY模糊匹配
						});
					} else {
						me.triggerEvent('error', res);
						util.alert(res.Result.FailureMessage);
						me.$('.crm-loading').hide();
						//me.$('.loading-text').text(res.Result.FailureMessage || '');
					}


				},
				complete: function () {
					me._javaAjax = null;
				}
			}, {
				errorAlertModel: 1
			})
		},

		_renderTitle: function (keepSave, objName, str) {
			if (keepSave) {
				return '<h2>' + $t('系统中存在相似{{obj.typeName}}', {typeName: objName}) + (str || '') + '</h2>';
			} else {
				return '<h2>' + objName + $t('已存在，无法保存！') + (str || '') + '</h2>';
			}
		},

		renderTip: function (slaveTypes, modes) {
			var me = this;
			var content = '';
			var $tip = $('.recheck-tip', this.$el);
			var $tip1 = $('.recheck-title', this.$el);

			$tip.html('').hide();
			$tip1.html('').hide();

			slaveTypes.related_search_infos = slaveTypes.related_search_infos || [];

			if (slaveTypes.related_search_infos.length) {
				_.each(slaveTypes.related_search_infos, function (item, index) {
					if (item.total === 0) {
						return;
					} else {
						if (content != '') {
							content += $t('和');
						}
					}
					content += (item.match_type == 'PRECISE' ? $t('相同的') : $t('相似的')) + item.object_describe.display_name + '(<em>' + item.total + '</em>)';
				});

				$tip.html('<div>' + $t("存在") + content + '<a href="javascript:;" class="j-look" data-type="' + '">' + $t("查看") + '</a></div>').show();
			}

			if (me.options.type === 'NEW') {
				if (slaveTypes.data_list.length) {
					let str = me._renderTitle(slaveTypes.keep_save, me.options.objectName, slaveTypes.duplicate_rule_info ? ' '  + $t('重复规则为') + ': ' + slaveTypes.duplicate_rule_info.duplicate_rule_name : '');
					$tip1.html(str).show();
				}
			}

			// dt.$el.css('height', slaveTypes.length == 0 ? '100%' : 325);
			// dt.resize();
			$(document).on('click.recheck-look', function (e) {
				if ($(e.target).closest('.recheck-slave-table').length > 0) {
					return;
				}

				$('.recheck-slave-table', this.$el).hide();
			});
		},

		//渲染副对象表格
		//点击查看
		lookHandle: function (e) {
			var opts = this.options;
			var me = this,
				$target = $(e.currentTarget),
				$container = $('.recheck-slave-table', this.$el),
				position = $target.position();
			me.fetchSlaveHeader(opts, function (res) {
				me.renderSlaveTable(opts, res);
			})
			// me.slaveTables = me.slaveTables || [];
			// _.map(me.slaveTables, function (table) {
			//     table.destroy && table.destroy();
			// });

			$container.html('').toggle();
			$container.css({
				'top': position.top + 19,
				'left': position.left - 5
			});
			e.stopPropagation();
			e.preventDefault();
		},

		renderSlaveTable: function (opts, res) {
			var me = this;
			var $table = $('.recheck-slave-table', this.$el);
			var MyTable = Table.extend({
				fetchColunms: function (param, cb, context) {
					this.fetchColunmsSuccess(res, cb);
				},
				parseColumns: function (headers, fields) {
					var columns = Table.prototype.parseColumns.apply(this, arguments);
					_.each(columns, function (item) {
						item.fixed = false
					})
					return columns
				},
				getOptions: function () {
					var options = Table.prototype.getOptions.call(this);
					if (me.options.simOrHigh || opts.type === 'NEW') {
						options.url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/RelatedDuplicateSearch'
					} else {
						options.url = '/EM1HNCRM/API/v1/object/' + me.options.apiname + '/controller/RelatedSimpleDuplicateSearch'
					}
					options.searchTerm = options.showTerm = options.showManage = options.showMoreBtn = options.showFilerBtn = options.showMultiple = false;
					me.createPermission && (options.noDataTip = opts.noDataTip);
					options.columns.push({
						width: 150,
						data: 'handle',
						title: $t('操作'),
						lastFixed: true,
						render: function (data, type, full) {
							var html = ''
							if (!full.operate) return '';
							me.filterOperate(full.operate);
							_.each(full.operate.action, function (item, index) {
								if (item === 'detail') {
									html += '<a data-action="operate" data-operate="detail" style="margin-right: 10px" class="j-duplicate-action" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else if (item === 'other') {

								} else if (item === 'choose') {
									html += '<a data-action="operate" data-operate="choose" style="margin-right: 10px" class="j-duplicate-action" href="javascript:;">' + full.operate.label[index] + '</a>';
								} else if (item === 'owner') {
									html += '<a data-action="operate" data-operate="owner" style="margin-right: 10px" class="j-duplicate-action" href="javascript:;">' + full.operate.label[index] + '</a>';//联系负责人
								} else {
									html += '<a data-id="' + full._id + '" class="j-duplicate-sendqxtoadmins" href="javascript:;">' + full.operate.label[index] + '</a>';
								}
							})
							return html
						}
					})

					options.columns[0].render = function (data, type, full) {
						var _data;

						try {
							_data = $('<div></div>').html(data).text();
						} catch (e) {
							_data = '';
						}

						var str = '<span title=' + _data + '>' + data + '</span>';

						if (full.life_status === 'invalid') {
							return str + '<span class="invalid-tip">(' + $t("已作废") + ')</span>';
						}

						return str;
					}
					return options;
				},
				parseParam: function (obj) {
					var param
					if (me.options.simOrHigh || opts.type === 'NEW') {
						param = Table.prototype.parseParam.apply(this, arguments);
						param.describe_api_name = opts.apiname;
						param.type = opts.type;
						param.object_data = opts.data,
							param.is_need_duplicate = !!opts.needDuplicate;
						param.page_size = obj.pageSize || 20;
						param.page_number = obj.pageNumber || 1;
						opts.needDuplicate = false;//请求一次之后置为false
					} else {
						param = {}
						param.describe_api_name = opts.apiname;
						// param.type = opts.type;
						param["include_object_describes"] = true,
							param.keyword = $('.simple-ipt').val(),
							param.is_need_duplicate = !!opts.needDuplicate;
						param.page_size = obj.pageSize || 20;
						param.page_number = obj.pageNumber;
					}

					param.duplicate_rule_api_name  = param.rule_api_name_intercepted = me.duplicate_rule_api_name;

					delete param.object_describe_api_name
					delete param.search_query_info
					return param;
				},
				parseData: function (obj) {
					var tm;
					for (var i = 0; i < obj.results.length; i++) {
						if (obj.results[i].object_describe.api_name == res.Value.objectDescribe.api_name) {
							obj.results[i].dataList = obj.results[i].data_list;
							obj.results[i].total = obj.results[i].page.total;
							tm = Table.prototype.parseData.call(this, obj.results[i]);
							if (obj.results[i].button_info) {
								var temp = {};
								_.each(obj.results[i].button_info.buttons, function (obj) {
									temp[obj.api_name] = obj.label;
								})
								var tb = {}
								_.each(obj.results[i].button_info.button_map_list, function (action, id) {
									tb[id] = {
										action: [],
										label: []
									}
									_.each(action, function (item) {
										tb[id].action = action
										tb[id].label.push(temp[item])
									})
								})
								_.each(tm.data, function (obj) {
									obj.operate = tb[obj._id];
								})
							}
							
						}
					}

					return tm || {total: 0, data: []};
				},
				trclickHandle: function (data, $str, $target) {
					var action = $target.data('action');
					action && action === 'operate' && me.excuteAction($target, data);
				},
			});

			var api_name = res.Value.objectDescribe.api_name;
			var display_name = res.Value.objectDescribe.display_name;

			me.slaveTables = me.slaveTables || [];

			me.$('.recheck-slave-table').append(slaveTpl(
				{
					apiname: api_name,
					displayname: display_name,
					match_type: res.match_type === 'PRECISE'
				})
			);

			var table = new MyTable({
				el: me.$('.tb-' + api_name),
				apiname: opts.apiname,
				showTitle: false,
				noOperate: true,
				isFilter: false,
				showPage: false
			});

			table.render();

			me.slaveTables.push(table);
		},

		excuteAction: function ($target, data) {
			var toolCustomOpts = this.options.toolCustomOpts
			if(toolCustomOpts && toolCustomOpts.operateAction) {
				toolCustomOpts.operateAction($($target).data('operate'), data)
			}
			var fn = this[$($target).data('operate') + 'Handle'];
			fn && fn.apply(this, [$target, data]);
		},

		// 领取
		chooseHandle: function ($target, data) {
			CRM.util.getButtonInfo({
				buttonApiName: "Choose_button_default",
                describeApiName: data.object_describe_api_name,
				objectDataId: data._id,
			}).then((res) => {
				doChoose(res.button);
			}, () => {
				doChoose();
			})

			function doChoose(btn) {
				if (data.object_describe_api_name == "LeadsObj") {
					api.receive_leadsobj({
						dataList: [data],
						btn: btn,
						success: function (type, ids, data) {
							$target.off().remove();
						}
					});
				} else {
					api.receive_accountobj({
						dataList: [data],
						hsID: data.high_seas_id,
						btn: btn,
						success: function (type, ids, data) {
							$target.off().remove();
						}
					});
				}
			}
			
		},

		//查看
		detailHandle: function ($target, data) {
			var opts = this.options;
			FS.MEDIATOR.trigger('crm.detail', {
				type: opts.apiname,
				showMask: true,
				zIndex: 1005,
				data: {
					crmId: data._id + ',' + data.object_describe_api_name
				},
				callback: function () {
					//
				}
			})
		},

		_sendqxtoadminsHandle: function (e) {
			if (this.isConnectApp()) return;
			var $target = $(e.currentTarget);
			this._improveQXDialog();
			sendqx({
				el: $target,
				apiname: this.options.apiname,
				dataId: $target.data('id'),
				list: this.adminIds,
				success(opts) {
					//新版主站 点击联系负责人
					if (window.Fx && window.Fx.theme == 'new') {
						// FS.MEDIATOR.trigger('qx.openChat', data.owner[0], 'employee');
						FS.tpl.event.trigger("qxOpenChat", opts.employeeId, 'employee');
						$('.crm-action-duplicatetool .crm-btn').click();
					}
				}
			})
		},

		//联系负责人
		ownerHandle: function ($target, data) {
			var opts = this.options;
			this._improveQXDialog();
			sendqx({
				el: $target,
				apiname: data.object_describe_api_name,
				dataId: data._id,
				list: data.owner,
				isForce: true
			});

			//新版主站 点击联系负责人
			if (window.Fx && window.Fx.theme == 'new') {
				// FS.MEDIATOR.trigger('qx.openChat', data.owner[0], 'employee');
				FS.tpl.event.trigger("qxOpenChat", data.owner[0], 'employee');
				$('.crm-action-duplicatetool .crm-btn').click();
			}
		},

		/*
         * @desc 抬高企信
         * 三次调用：
         * 1、每次表格初始化时调用一次。
         * 2、详情hide时会还原企信zindex，所以需要调用一次。
         * 3、详情destroy时会还原企信zindex，所以需要调用一次
         *
         */
		_improveQXDialog: function () {
			var zIndex = 2000;
			$('.qx-wrapper').css('zIndex', zIndex);
			$('.qx-chat-window-panel').css('zIndex', zIndex);
			$('.f-qx-container').css('zIndex', zIndex);
		},

		getCurData: function () {
			return this.table.table.getCurData();
		},

		isConnectApp: function () {
			return CRM.util.isConnectApp();
			// return true;
		},

		// 成为外部负责人ajax-下游；
		changePartnerOwnerAjax: function (param) {
			return util.ajax_base('/EM1HNCRM/API/v1/object/partner/service/change_partner_owner', param);
		},

		// 成为外部负责人-下游
		change_partner_ownerHandle: function ($target, data) {
			let _this = this;
			let res = this.changePartnerOwnerAjax({
				"out_tenant_id": $.cookie('EROuterTenantId'),//EROuterTenantId
				"out_owner_id": $.cookie('EROuterUid'),
				"object_api_name": this.options.apiname,
				"data_list": [data._id]
			});

			if (res) {
				util.remind(1, $t("设置成功"));
				setTimeout(function () {
					_this.table.render();
				}, 500);
			}
		},

		// 成为外部团队成员ajax-下游；
		addTeamMemberAjax: function (param) {
			return util.ajax_base('/EM1HNCRM/API/v1/object/partner/service/add_team_member', param);
		},

		// 成为团队成员-下游
		add_team_memberHandle: function ($target, data) {
			let _this = this;
			let res = this.addTeamMemberAjax({
				"dataIDs": [
					data._id
				],
				"teamMemberEmployee": [],
				"teamMemberPermissionType": "1",
				"teamMemberRole": "",
				"teamMemberRoleList": [
					"4"
				],
				"otherObjects": [],
				"outTeamMemberEmployee": [
					{
						"userId": $.cookie('EROuterUid'),
						"outTenantId": $.cookie('EROuterTenantId')
					}
				],
				"object_api_name": this.options.apiname,
			});

			if (res) {
				util.remind(1, $t("设置成功"));
				setTimeout(function () {
					_this.table.render();
				}, 1000);
			}
		},


		getDuplicateRuleName() {
			return this.duplicate_rule_api_name;
		},


		destroy: function () {
			this.off();
			this.undelegateEvents();
			this.table && this.table.destroy();
			this._cusOperateBtns = null;
			this.table = this.options = this.events = null;
		}
	})
})
