/*
 * 公海和线索池的规则条件filter
 * 继承自common下fieldfilter
 * 复写方法changeHandle：解除国家省市区及联关系
 * 复写方法_getCompareOptions：创建人、最后修改人、跟进人特殊处理
 * @author:lingj
 */
define(function(require, exports, module) {
	var util = CRM.util;
	var helper = require('crm-modules/components/helper/helper'),
		FieldFilter = require('crm-modules/common/fieldfilter/fieldfilter');

	function RuleFilter(opts) {
		FieldFilter.call(this, opts);
	}

	(function() {
		var Super = function() {};
		Super.prototype = FieldFilter.prototype;
		RuleFilter.prototype = new Super();
	})();

	//解除国家省市区及联关系
	RuleFilter.prototype.changeHandle = function(data, comp) {
		// var me = this,
		// 	dependData,
		// 	field=_.find(me.fields,function(i){
		// 		return i.api_name==data.value;
		// 	});
		//

		// if (!data.depend || _.contains(['country', 'province', 'city', 'district'], field.type)) {
		// 	return;
		// }
		// var seldata = me.filter.getData();
		// var isExitDep = _.some(seldata, function(item) {
		// 	if (item[0] == data.depend && item[1] == '1' && item[2]) {
		// 		dependData = item;
		// 		return true;
		// 	}
		// 	return false;
		// });
		// if (!isExitDep) {
		// 	var fields = me.fieldsAll || me.fields;
		// 	comp.clear();
		// 	util.alert($t('字段的上级字段【{{label}}】没有填写完整（另比较符需要选择“等于”）', {
		// 		label: fields[data.depend].label
		// 	}));
		// 	return;
		// }
		// me._setSonOptions(data, dependData);
	};

	//创建人、最后修改人、跟进人特殊处理
	RuleFilter.prototype._getCompareOptions = function(field) {
		var me = this;
		var oldType = field.old_type || field.type;
		var compares = this.opts.helper.getCompare(oldType);
		compares = me.opts.parseCompare(compares, field && oldType, me.opts.helper.getCompareConfig());
		var type = this.opts.helper.getType(field.type, me.opts.isRelate);

		if (me.isEmpField(field.api_name)) {
			type = 'employee';
		}
		if (field.api_name == 'out_owner') {
			type = 'exemployee';
		}

		return _.map(compares, function(compare) {
			var child = {
				type: me._getChildType(compare, type, field.type, field),
				ftype: field.type,
				fname: field.api_name,
				field: field,
				options: me._getValueOptions(field),
				isReInit: type == 'employee' || type == 'department' || type == 'exemployee',
				relatedName: field.target_related_list_name || '',
				targetApiName: field.target_api_name,
				dimension_type: field.dimension_type || ''
			};
			if (_.contains(['employee', 'department', 'record_type', 'exemployee', 'area'], type) && _.contains([7, 8, 13, 14], compare.value) && _.contains([7, 8, 13, 14], compare.value)) {
				child.isMultiple = true;
				// 添加勾选子部门的选项
				if(type == 'department' && me.opts.helper.changeDepartmentConfig){
					child = me.opts.helper.changeDepartmentConfig(child,compare);
				}
			}
			return {
				label: compare.name,
				value: compare.value,
				child: child
			}
		})
	};

	RuleFilter.prototype.customParse = function (value, field, item, compare) {
		if (field.api_name == 'out_owner' || field.type == 'exemployee') {
			return item[2];
		}
		return value;
	}

	RuleFilter.prototype.getValue = function() {
		const me = this;
		let val = this.getData();
		val = val.map((filter) => {
			let value = filter[2];
			value = _.isString(value) && value.includes('|') ? value.split('|') : value;
			return {
				field_name: filter[0],
				operator: filter[1],
				operator_name: me.helper.getNameByValue(filter[1], filter[3]),
				field_values: value, //_.isArray(filter[2]) ? filter[2]: [filter[2]],
				connector: 'AND',
				type: filter[3]
			}
		})
		val = _.filter(val, (v) => {
			return v.field_name && v.operator;
		})
		return {
			filters: val,
			connector: 'OR'
		}
	}

	RuleFilter.prototype.valid = function() {
		let val = this.getValue();
		for (let j = 0; j < val.filters.length; j++) {
			let filter = val.filters[j];
			// level 层级不一样, 校验也不一样 todo
			if (this.opts.level === 1) {
				if (!filter.field_name) return false;
			} else {
				if (!filter.field_name || !filter.operator) continue;
				if (['IS','ISN','SAME','NSAME'].indexOf(filter.operator) != -1) continue;
				if (CRM.util.isEmptyValue(filter.field_values)) {
					return false;
				}
			}
		}
		return true;
	}

	/*
	 * 规则数据格式化
	 * arg: 公海-customerrule，线索池-clue
	*/
	RuleFilter.prototype.getParsedData = function(arg) {
		var me = this,
			temp = [],
			data = _.map(me.filter.getData(), function(item) {
				var field = me.fields[item[0]];
				if(_.isArray(me.fields)){
					field=_.find(me.fields,function(f){
						return f.api_name==item[0];
					})
				}
				item.push(field ? field.type : '');
				return item;
			});
		_.each(data, function(item, index) {
			var obj = {}
			obj.FieldName = item[0] || ''
			obj.Compare=item[1]
			obj.FieldType = 0
			obj.FieldOrder = index + 1
			obj.FieldValue = item[2] || ''
			if(arg=="customerrule"){
				obj.RecyclingFilterID = ''
				obj.RecyclingRuleID = ''
			}else if(arg=='clue'){
				obj.RuleFilterID  = '';
				obj.RuleID  = '';
				obj.RuleType  = 1;
			}
			temp.push(obj)
		})
		var rules = [];
		_.each(data, function(value, index) {
			var fieldValue = value[2];
			var obj={
				fieldName: value[0],
				fieldType: value[3],
				fieldValue: _.isArray(fieldValue) ? fieldValue : [fieldValue],
				operate: value[1],
				ruleOrder: index + 1
			}
			if(arg=='clue'){
				obj.RuleType= 1
			}
			rules.push(obj);

		});
		return {
			temp: temp,
			rules: rules
		}
	}

	module.exports = RuleFilter;
});
