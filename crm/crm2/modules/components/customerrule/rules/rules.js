/**
 * @description 公海回收规则
 * <AUTHOR>
 */

define(function (require, exports, module) {
	var util = CRM.util,
		rulesModel = require('./model/model'),
		RulesView = require('crm-components/rulesview/rulesview'),
		EditRule = require('./editrule/editrule'),
		// AccountObjParse = require('crm-modules/common/highseas/highseas').common,
		// LeadsObjParse = require('crm-modules/common/cluepool/cluepool').Parse,
		config = require('./config'),
		titleTpl = require('../template/title-html'),
		rulesLayoutTpl = require('./template/rules-layout-html'),
		tipconTpl = require('../template/tipcon-html');
	

	var Model = Backbone.Model.extend({});
	var Rules = Backbone.View.extend({

		options: {
			EditRule: EditRule,
			apiName: 'AccountObj',
			tipconTpl: tipconTpl,
			maxLen: 30,
			parseFn: 'parseFieldRecycling'
		},

		/*绑定事件*/
		events: {
			'click .j-addrule': 'addRuleHandle',
			'click .j-checkbox-time': 'checkboxHandle',
			'click .j-showCalculateRule': 'showCalculateRule',
			'click .j-checkbox-extend': 'extendHandle'
		},

		/*初始化 */
		initialize: function () {
			this.options = $.extend(true, {}, Rules.prototype.options, this.options);
			let opts = this.options;
			if(this.options.needDepartmentChildren){
				opts.data.needDepartmentChildren = this.options.needDepartmentChildren;
			}
			this.setModel(opts.data);
		},

		/**
		 * @description 此方法中触发model的窗口重置
		 * @return undefined
		 */
		render: function () {
			const me = this;
			let linkMap = {
				LeadsObj: '#crmmanage/=/module-clue/page3',
				AccountObj: '#crmmanage/=/module-customerrule/page6',
			}
			let linkTitleMap = {
				LeadsObj: $t('crm.clue.recyclingruletip'),
				AccountObj: $t('crm.clue.recyclingaccountruletip'),
			}
			this.$el.html(rulesLayoutTpl({
				isHaveRule: !!(this.model.get('RecyclingRuleList') && this.model.get('RecyclingRuleList').length),
				pastTimeVal: this.getConfigState('IsIncludePastTime'),
				isEdit: this.model.get('isEdit'),
				allowExtendVal: this.getConfigState('IsAllowExtend'),
				apiName: this.options.apiName,
				followLink: linkMap[this.options.apiName],
				linkTitle: linkTitleMap[this.options.apiName]
			}));
			this.resetLabel();
			CRM.util.getConfigValue('pool_recycling_rule_max_number').then((value) => {
				value && +value && (me.options.maxLen = +value);
			})
			this.getFieldList((fieldlist) => {
				this.fieldlist = fieldlist;
				this.renderRule();
			})
		},

		// 获取规则列表的数据，同时纠正时间类型的比较符
		getRulelist() {
			var list = this.model.get('RecyclingRuleList') || [];
			if (this.model.get('isHaveValue')) {
				this.model.set('RecyclingRuleList', list || []);
			}
			list.forEach((rule) => {
				rule.RecyclingFilterList.filters.forEach((item) => {
					if (!this.fields[item.FieldName]) return;
					if (['date', 'date_time', 'time'].includes(this.fields[item.FieldName].type)) {
						if (item.Compare == 3) {
							item.Compare = 12
						} else if (item.Compare == 5) {
							item.Compare = 11
						}
					}
				})
			})
			return list;
		},
		formatRuleModel(ruleModel) {
			ruleModel.set({
				fieldlist: this.fieldlist,
				fields: this.fields,
				DataType: this.model.get('DataType'),
				isHaveValue: this.model.get('isHaveValue'),
				CurHighSeasID: this.model.get('HighSeasID') || this.model.get('SalesCluePoolID')
			});

			// if (this.model.get('HighSeasID') == ruleModel.get('HighSeasID')) {
			// 	ruleModel.set('HighSeasName', '');
			// }

			return ruleModel;
		},
		// 处理规则数据
		formatRuleData(ruleModel) {
			let opts = this.options;
			let ruleData = _.extend(ruleModel.toJSON());
			// let obj = null;
			// if (opts.apiName === 'AccountObj') {
			// 	obj = new AccountObjParse()
			// } else if (opts.apiName === 'LeadsObj') {
			// 	obj = new LeadsObjParse()
			// }
			// let parsefield = obj[opts.parseFn](this.fieldlist, ruleModel.toJSON(), opts.apiName);
			// let ruleData = _.extend(ruleModel.toJSON(), parsefield);
			// let datas = ruleData.datas;
			ruleData.scopeText = CRM.util.parseScope(ruleData, opts.apiName);

			let content1 = $t('全部{{objectName}}', {objectName: config[opts.apiName].name});
			if (ruleData.RecyclingFilterList.filters.length > 0) {
				// content1 = obj.getRuleText(datas);//this.formatString(datas);
				content1 = CRM.util.parseFilterRule(ruleData.RecyclingFilterList, this.fields);
			}

			// let content2 = $t(`本${config[opts.apiName].text_zh}`);
			// if (ruleData.HighSeasName && ruleData.HighSeasID !== ruleData.DataID) {
			// 	content2 = $t(`其他${config[opts.apiName].text_zh}`) + '：' + ruleData.HighSeasName;
			// }
			// if (opts.apiName === 'LeadsObj' && ruleData.TargetPoolName && ruleData.TargetPoolId !== ruleData.DataId) {
			// 	content2 = $t(`其他${config[opts.apiName].text}`) + '：' + ruleData.TargetPoolName;
			// }

			return {
				label1: $t('{{objectName}}范围', {objectName: config[opts.apiName].name}),
				label2: $t("收回规则:"),
				content1: content1,
				content2: ruleData.scopeText,
				// content2: ruleData.scopeText.replace($t(config[opts.apiName].text), content2)
			}
		},
		// formatString(datas) {
		// 	var html = [];
		// 	_.each(datas, function (data) {
		// 		html.push('"', data.FieldName, '" ', data.Compare, data.FieldValue && ' "', data.FieldValue && data.FieldValue + '" ', ',' + $t("且"));
		// 	});
		// 	var r = new RegExp(',' + $t("且") + '$', "g");
		// 	return html.join('').replace(r, '');
		//
		onChange() {
			this.trigger('change', this.getValue());
		},
		editHandle(model, ruleView) {
			const me = this;
			var edit = new EditRule({
				from: model.get('DataType'),
				apiName: this.options.apiName
			});
			edit.on('save', function (rule) {
				edit.destroy();
				ruleView.render();
				me.onChange();
			});
			model.needDepartmentChildren = this.model.get('needDepartmentChildren');
			edit.show(model);
		},

		setModel: function (obj) {
			obj = obj || {};
			if (!this.model) {
				this.model = new Model(obj);
				return;
			}
			this.model.set(obj);
		},

		set: function (key, value) {
			this.model.set(key, value);
		},

		get: function (key) {
			return this.model.get('key');
		},

		/**
		 * 收回规则左侧中有收回规则解释，所以修改dom
		 */
		resetLabel: function () {
			var me = this,
				fmWrap = this.$el.closest('.fm-item').length ? this.$el.closest('.fm-item') : this.$el.closest('.item'),
				label = fmWrap.find('label');
			if (!label.length) return;
			var fieldlist = this.model.attributes.fieldList,
				field = _.find(fieldlist, function (item) {
					return item.FieldName &&
						item.FieldName.toLocaleLowerCase() == me.options.suffix;
				});
			field && label.remove();
			field && fmWrap.prepend(titleTpl(field));
			fmWrap.on('mouseenter', '.crm-g-remind-ico', _.bind(me.enterHandle, me));
			fmWrap.on('mouseleave', '.crm-g-remind-ico', _.bind(me.leaveHandle, me));
		},

		enterHandle: function (e) {
			var me = this,
				target = $(e.target);
			util.remindTip(target, me.options.tipconTpl({
				type: target.data('type'),
				from: me.model.get('DataType')
			}));
		},

		leaveHandle: function (e) {
			var target = $(e.target);
			util.remindTip(target);
		},

		/**
		 * @description 遍历集合
		 * @return undefined
		 */
		renderRule: function () {
			this.rulesView = new RulesView({
				$el: this.$('.rules-content'),
				groupName: 'j-customer-rules',	// 拖拽分组名称
				rules: this,
				rulesModel: rulesModel,
				formatRuleModel: this.formatRuleModel,
				rulelist: this.getRulelist(),
				formatRuleData: this.formatRuleData.bind(this),
				editHandle: this.editHandle.bind(this)
			})

			this.BtnVisible(this.rulesView.collection.length);
			this.rulesView.on('change', (collection) => {
				this.BtnVisible(collection.length);
				this.onChange();
			})
		},
		// 新增规则按钮的显示和隐藏
		BtnVisible(n) {
			let opts = this.options;
			if (n >= opts.maxLen) {
				this.$('.j-addrule').hide();
			} else {
				this.$('.j-addrule').show();
			}

			if (n == 0) {
				this.$('.crm-custome-rules-config').hide();
			} else {
				this.$('.crm-custome-rules-config').show();
			}
		},

		/**
		 * 判断是否有收回规则，如果有返回true
		 * */
		isHaveRule: function () {
			var rulelist = this.model.get('RecyclingRuleList') || [];
			return !!(rulelist.length || this.collection.length);
		},

		/**
		 * 返回是配置状态
		 * IsIncludePastTime: 是否包含过去时间的值
		 * is_allow_extend: 是否允许申请延期
		 * */
		getConfigState: function (name) {
			var rulelist = this.model.get('RecyclingRuleList') || [];
			if (rulelist.length == 0) {
				if (name == 'IsIncludePastTime') {
					return true;
				}
				return false;
			} else {
				return rulelist[0][name];
			}
		},

		checkboxHandle: function (e) {
			var me = this,
				target = $(e.target), confirm;
			if (!target.hasClass('state-active')) {
				// confirm = util.confirm($t("请仔细阅读包含已过去天数后的") + '<i class="crm-g-remind-ico"  style="display:inline-block;position:relative;top:0;left:0;margin-left:4px;vertical-align:middle;"></i>' + $t('如选中可能会导致有些' + config[me.options.apiName].name + '立即收回。'), $t("提示"), function () {
				// 	target.addClass('state-active');
				// 	me.rulesView.collection.each(function (rule) {
				// 		rule.set('IsIncludePastTime', target.hasClass('state-active'));
				// 	});
				// 	confirm.hide();
				// });

				target.addClass('state-active');
				me.rulesView.collection.each(function (rule) {
					rule.set('IsIncludePastTime', target.hasClass('state-active'));
				});
			} else {
				target.removeClass('state-active');
				me.rulesView.collection.each(function (rule) {
					rule.set('IsIncludePastTime', target.hasClass('state-active'));
				});
			}
			this.onChange();
		},

		extendHandle: function (e) {
			var target = $(e.target);

			target.toggleClass('state-active');
			this.rulesView.collection.each(function (rule) {
				rule.set('IsAllowExtend', target.hasClass('state-active'));
			});
			this.onChange();
		},

		/*添加一条规则*/
		addRuleHandle: function (e) {
			const me = this;
			let opts = this.options;
			let collection = this.rulesView.collection;

			let m = new rulesModel.Rule({
				Priority: collection.length + 1,
				fieldlist: this.fieldlist,
				fields: this.fields,
				IsIncludePastTime: this.$('.past-time-checkbox').hasClass('state-active'),
				IsAllowExtend: this.$('.allow-extend-checkbox').hasClass('state-active'),
				CurHighSeasID: this.model.get('HighSeasID') || this.model.get('SalesCluePoolID')
			});

			this.editrule = new opts.EditRule({
				from: this.model.get('DataType'),
				apiName: this.options.apiName
			});
			m.needDepartmentChildren = this.model.get('needDepartmentChildren');
			this.editrule.show(m);
			this.editrule.on('save', function (rule, typelist) {
				collection.add(rule);
				me.onChange();
			});

		},

		/*请求字段列表*/
		getFieldList: function (callback) {
			var me = this;
			CRM.util.fetchDescribe(me.options.apiName, {
				"include_detail_describe": false,
				"include_layout": false,
				"apiname": me.options.apiName,
				"layout_type": "add",
				"recordType_apiName": "record_sKbe4__c"
			}, function(res) {
				var list = me.parseData(res.objectDescribe.fields);
				me.fields = res.objectDescribe.fields;
				callback && callback(list);
			}, true);
		},

		parseData: function (data) {
			var key, temp = []
			for (key in data) {
				if (data[key] && data[key] instanceof Object) {
					temp.push(data[key])
				}
			}
			return temp
		},

		/*获取值*/
		getValue: function () {
			return this.rulesView.collection.collect();
		},

		showCalculateRule: function() {
			var html;
			if(this.options.apiName != 'LeadsObj'){
				html = [
					'<div style="text-align: left;">',
						'<div style="margin-bottom: 10px;">',
					'<p>' + $t("包含（勾选）：") + $t("规则配置未跟进收回") + '</p>',
					'<p>' + $t("未成交收回") + '</p>',
						'</div>',
						'<div>',
					'<p>' + $t("不包含（不勾选）：") + $t("规则创建修改时间+设置的回收天数计算收回") + '</p>',
						'</div>',
					'</div>'
				].join('');
			} else {
				html = [
					'<div style="text-align: left;">',
						'<div style="margin-bottom: 10px;">',
					'<p>' + $t("包含（勾选）：") + $t("未跟进收回(包含)") + '</p>',
					'<p>' + $t("未转换收回(包含)") + '</p>',
						'</div>',
						'<div>',
					'<p>' + $t("不包含（不勾选）：") + $t("未跟进收回(不包含)") + '</p>',
					'<p>' + $t("未转换收回(不包含)") + '</p>',
						'</div>',
					'</div>'
				].join('');
			}

			CRM.util.alert(html);
		},

		destroy: function () {
			this.remove();
			this.rulesView && this.rulesView.destroy();
			this.fieldlist = [];
			this.undelegateEvents();
			this.stopListening();
		}
	});

	Rules.EditRule = EditRule;

	module.exports = Rules;
});
