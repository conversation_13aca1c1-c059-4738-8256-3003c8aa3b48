/**
 * @description 公海编辑规则弹窗
 * <AUTHOR>
 */

define(function (require, exports, module) {
	var util = CRM.util,
		Dialog = require('crm-widget/dialog/dialog'),
		Select = require('crm-widget/select/select'),
		Model = require('../model/model').Rule,
		Filter = require('crm-modules/components/fieldfilter/fieldfilter'),
		helperComp = require('crm-modules/components/helper/helper'), // 客户暂与线索不同，todo与线索一致
		helper = require('./helper'),
		config = require('../config'),
		tpl = require('./template/tpl-html');

	var EditRule = Dialog.extend({

		attrs: {
			width: 900,
			className: 'crm-g-editrule',
			title: $t("编辑规则"),
			from: 1, //1-公海管理；2-非公海管理
			showBtns: true,
			showScroll: false,
			apiName: 'AccountObj'
		},

		/*绑定事件*/
		events: {
			'click .radio-btn-rule': 'ruleRadioHandle',
			'click .radio-btn-highsea': 'highseaRadioHandle',
			'click .b-g-btn': 'saveHandle',
			'click .b-g-btn-cancel': 'cancelHandle',
			'click .add-timerule': 'addTimeRule',
			'click .del-timerule': 'delTimeRule',
			'click .rules-fn-add': 'addCustomFn',
			'click .rules-fn-edit': 'updateCustomFn',
			'click .rules-fn-del': 'delCustomFn',
			'click .rules-fn-checkbox .crm-g-checkbox': 'checkboxHandle',
		},

		/*初始化*/
		initialize: function () {
			this.attrs = _.extend({}, EditRule.prototype.attrs, this.attrs);
			return EditRule.superclass.initialize.apply(this, arguments);
		},

		//定义选择器
		initEl: function () {
			this.rulesWrap = this.$('.rules-wrap');
			this.$selectWraps = this.$('.rule-select-wrap');
			this.$ruleradios = this.$('.radio-btn-rule');
			this.$ruleipts = this.$('.time-rule input');
			this.highsearadios = this.$('.radio-btn-highsea');
			this.remindipt = this.$('.j-remind-ipt');
		},

		/*显示对话框*/
		show: function (o) {
			// 每次调用时都重新引入原始模块，确保没有扩展
			// if (!o.needDepartmentChildren) {
			// 	// 如果没有部门需要添加子部门的灰度，使用原始helper
			// 	helper = require('./helper');
			// 	helperComp = require('crm-modules/components/helper/helper');
			// } else 
			if (o.needDepartmentChildren && !helper.isDepartmentExtended) {
				// 如果有部门需要添加子部门的灰度，扩展helper
				let copyGetCompare = helper.getCompare;
				helper = $.extend(true, {}, helper, {
					isDepartmentExtended: true,
					getCompare(type){
						let menus = copyGetCompare.call(this, type);
						// 如果是department类型，添加13(属于)和14(不属于)比较符
						if (type === 'department') {
							let additionalCompares = [13, 14].map(value => 
								_.find(helper.compare, obj => obj.value === value)
							);
							menus = menus.concat(_.compact(additionalCompares));
						}
						return menus;
					},
					changeDepartmentConfig(child,compare) {
						if(compare.value == 13 || compare.value == 14){
						  child.groupIncludeChildrenStatus = 1;
						  child.needDepartmentChildren = true;
						}
						return child;
					  }
				});
				
				let originalGetCompare = helperComp.getCompare;
				helperComp = $.extend(true, {}, helperComp, {
					getCompare(type){
						let menus = originalGetCompare.call(this, type);
						// 如果是department类型，添加13(属于)和14(不属于)比较符
						if (type === 'department') {
							let additionalCompares = [13, 14].map(value => 
								_.find(helper.compare, obj => obj.value === value)
							);
							menus = menus.concat(_.compact(additionalCompares));
						}
						return menus;
					},
					changeDepartmentConfig(child,compare) {
						if(compare.value == 13 || compare.value == 14){
						  child.groupIncludeChildrenStatus = 1;
						  child.needDepartmentChildren = true;
						}
						return child;
					  }
				});
			}
			var result = EditRule.superclass.show.call(this);
			// var obj = this.parseObj(o);
			var obj = o;
			this.parseNewFilter(o)
			this.cache = obj; //原始数据
			this.setModel(obj);
			this.setEvents();
			let label = config[this.get('apiName')];
			this.setContent(tpl(_.extend({
				FunctionApiName: '',
				FunctionName: '',
				ruleTip: $t('支持在“{{followText}}”中单独维护{{objectName}}的跟进行为', {
					objectName: label.name,
					followText: label.ruleFollowText,
				})
			}, this.model.toJSON(), {
				from: this.get('from'),
				label: label,
				isOpenPRM: CRM.get('isOpenPRM'),
			})));
			this.initEl();
			this.initSelect();
			this.initBackSelect();
			this.initFilter();
			this.resizedialog();
			this.cacheData = {
				HighSeasID: '',
				HighSeasName: ''
			};
			// if (!this.filter.collection.validate('null')) {
			// 	util.remind(3, $t("请完成当前规则的设置"));
			// 	return false;
			// }
			//this.validate();
			return result;
		},

		/*
		* 	1、处理多选，把字符串转为数组
		*
		* */
		// parseObj: function (obj) {
		// 	var ruleList = obj.get("RecyclingFilterList");
		// 	if (ruleList.length) {
		// 		ruleList = _.map(ruleList, function (item) {
		// 			let strFieldValue = item.FieldValue + '';
		// 			if (strFieldValue.includes('|')) {
		// 				item.FieldValue = strFieldValue.split('|');
		// 			}
		// 			return item
		// 		})
		// 	}
		// 	obj.set("RecyclingFilterList", ruleList);
		// 	return obj
		// },

		//新的过滤器更换迁移自定义对象获取字段接口之后 根据原来的数据格式需要转换才能回显
		parseNewFilter: function (obj) {
			var rules = obj.get("RecyclingFilterList")
			// var temp = []
			// _.each(rules, function (item) {
			// 	var ind = [];
			// 	var operate = (_.findWhere(helper.getCompareConfig(), {value: item.Compare}) || {}).value1
			// 	ind.push(item.FieldName, operate, item.FieldValue, item.FieldType)
			// 	temp.push(ind)
			// })

			// var result = []
			// _.each(temp, function (value, index) {
			// 	var fieldValue = value[2];
			// 	result.push({
			// 		fieldName: value[0],
			// 		fieldType: value[3],
			// 		fieldValue: _.isArray(fieldValue) ? fieldValue : [fieldValue],
			// 		operate: value[1],
			// 		ruleOrder: index + 1
			// 	});
			// });
			rules.filters = _.map(rules.filters, (rule) => {
				rule.field_values = _.isArray(rule.field_values) ? rule.field_values : [rule.field_values];
				return rule;
			})
			obj.set('RecycleDefault', rules)
			return rules;
		},

		/*初始化model*/
		setModel: function (obj) {
			var data = obj;
			if (obj instanceof Backbone.Model) {
				data = obj.toJSON();
			}
			data = $.extend(true, {}, obj.toJSON());
			this.model = new Model(data);
			this.model.set('needDepartmentChildren', obj.needDepartmentChildren);
		},

		setModelProp: function (key, value) {
			this.model && this.model.set(key, value);
		},

		getModelProp: function (key) {
			return this.model.get(key);
		},

		//绑定model事件
		setEvents: function () {
			var me = this;

			util.fixInputEvent('.time-ipt', function (e) {
				if (me.get('apiName') == 'LeadsObj') {
					me.leadsObjInputHandle(e);
				} else {
					me.inputHandle(e);
				}

			}, this.element);

			this.model.on({
				'change:RecyclingRemindRuleList': function (model, val) {
					if (val.length == 0) {
						me.remindipt.val('');
					} else {
						me.remindipt.val(val[0].RemindDays);
					}
				}
			});
		},

		/**
		 * @description 初始化下拉列表
		 * 当不自动回收时，RecyclingRuleType的值需要传1；
		 * 当自动回收时，RecyclingRuleType随便传
		 */
		initSelect: function () {
			var me = this,
				type = this.model.get('RecyclingRuleType');

			if (type == 1) return;
			me.$selectWraps.each(function (index) {
				if (me['ruleselect' + index]) {
					return;
				}
				var select = new Select({
					$wrap: $(this),
					appendBody: false,
					options: config[me.get('apiName')].options
				});
				// select.setValue(type);
				me['ruleselect' + index] = select;
			});

			var i = 0;
			if (this.model.get('DealDays') > 0) {
				me['ruleselect' + i].setValue(2);
				i++;
			}
			if (this.model.get('FollowUpDays') > 0) {
				me['ruleselect' + i].setValue(3);
				i++;
			}
		},

		initBackSelect: function () {
			this[config[this.get('apiName')].fn] && this[config[this.get('apiName')].fn].apply(this, arguments);
		},

		//初始化回收公海
		initAccountBackSelect: function () {
			var me = this;
			util.getHighSeasList(true).then(function (data) {
				var items = [];
				var curHishseaId = me.model.get('CurHighSeasID');
				var highseaId = me.model.get('HighSeasID');

				_.each(data, function (item) {
					if (item.HighSeasID !== curHishseaId) {
						items.push({
							// name: _.escape(item.Name),
							name: item.NameR || item.Name,
							value: item.HighSeasID
						});
					}
				});
				me.backselect = new Select({
					$wrap: $('.back-select', me.element),
					appendBody: false,
					options: items
				});
				me.backselect.on('change', function (v, item) {
					item = item || {};
					me.model.set('HighSeasID', item.value);
					me.model.set('HighSeasName', item.name);
				});
				if (highseaId && highseaId !== curHishseaId) {
					$('.radio-btn-highsea', me.element).eq(me.get('from') == 1 ? 1 : 0).get(0).click();
					me.backselect.setValue(highseaId, true);
				} else {
					$('.radio-btn-highsea', me.element).eq(0).get(0).click();
				}
			});
		},

		initClueBackSelect: function () {
			var me = this;
			util.getCluePoolList(true, 'ignoreManageGroupRule').then(function (data) {
				var items = [];
				var curHishseaId = me.model.get('CurHighSeasID');
				var highseaId = me.model.get('HighSeasID');

				_.each(data, function (item) {
					if (item.SalesCluePoolID !== curHishseaId) {
						items.push({
							// name: _.escape(item.Name),
							name: item.NameR || item.Name,
							value: item.SalesCluePoolID
						});
					}
				});
				me.backselect = new Select({
					$wrap: $('.back-select', me.element),
					appendBody: false,
					options: items
				});
				me.backselect.on('change', function (v, item) {
					item = item || {};
					me.model.set('HighSeasID', item.value);
					me.model.set('HighSeasName', item.name);
				});
				if (highseaId && highseaId !== curHishseaId) {
					$('.radio-btn-highsea', me.element).eq(me.get('from') == 1 ? 1 : 0).get(0).click();
					me.backselect.setValue(highseaId, true);
				} else {
					$('.radio-btn-highsea', me.element).eq(0).get(0).click();
				}
			});
		},

		initFilter: function () {
			this.get('apiName') === 'AccountObj' ? this.initFilterAccount() : this.initFilterLead();
		},

		/*初始化过滤器*/
		initFilterAccount: function () {
			var me = this,
				data = this.model.get('RecyclingFilterList');//每个实例名字会不同
			var defaultValue = this.model.get('RecycleDefault');
			if(this.model.get('needDepartmentChildren')){
				defaultValue.filters.forEach(filter => {
					if(filter.type == 'department' && (filter.operator == 'IN' || filter.operator == 'NIN') && filter.is_cascade){
						filter.field_values = filter.field_values.map(value => value + '_y');
					}
				})
		}
			if (!this.filter) {
				console.log(this.model.get('needDepartmentChildren'),"是否包含子部门");
				var filter = this.filter = new Filter({
					apiname: 'AccountObj',
					$wrapper: me.rulesWrap.eq(0),
					width: 750,
					title: '<span class="del-span j-delete-filter"></span><span style="color:#919eab">' + $t("且（AND）") + '</span>',
					max: 10,
					filterType: ["group", "image", "file_attachment", 'signature', "formula", "count", "quote", "auto_number", 'employee_many', 'department_many', 'html_rich_text', 'object_reference_many'],
					filterApiname: ['expire_time', 'filling_checker_id', 'is_remind_recycling', 'remaining_time', 'completion_rate', 'life_status', 'high_seas_id', 'recycled_reason', 'out_tenant_id', 'high_seas_name', 'claimed_time', 'lock_status', 'life_status', 'remaining_time', 'returned_time', 'account_status', 'biz_status', 'expire_time', 'account_no', 'location', 'recycled_reason', 'total_refund_amount', 'last_deal_closed_amount', 'owner_modified_time', 'remind_days', 'collected_to', 'completed_field_quantity', 'owner_department', 'account_path', "industry_ext", "is_duplicated", "extend_days", 'out_owner'],
					defaultValue: (defaultValue && defaultValue.filters && _.map(defaultValue.filters, function (item) {
						return [item.field_name, item.operator, item.field_values];
					})) || [],
					// parseCompare: function(compare, type, conf) {
					// 	if (type == 'employee' || type == 'department') {
					// 		return _.map([13, 14, 9, 10], function(item) {
					// 			return conf[item - 1]
					// 		});
					// 	}
					// 	return compare;
					// },
					parseFields: function (fields) {
						// TODO server 暂不支持计算字段返回 日期、时间、日期时间的类型
						// TODO server 要走isIndex 计算公司中包含特定时间的计算
						var tmp = {};
						_.each(fields, function (item, k) {
							var flag = (item.type == 'formula' && _.contains(['date_time', 'date', 'time'], item.return_type)) || (item.type == 'formula' && !item.is_index);
							if (item.type === 'select_one') {
								item.type = 'select_many';
								item.old_type = 'select_one';
							}
							if (item.api_name === 'out_owner') {
								item.type = 'exemployee'
							}
							// if(item.type === 'quote'){
							// 	item.type = item.quote_field_type
							// }

							flag || (tmp[k] = item);
						});

						return tmp;
					},

					disabled: me.get('disabled'),
					data: data.length ? data : {},
					fieldlist: me.model.get('fieldlist'),
					helper: helperComp
				});

				filter.on('render', function () {
					me.resizedialog();
				});
				filter.on('add.item', function () {
					me.resizedialog();
				});
				filter.on('del.item', function () {
					me.resizedialog();
				});
			}

			// this.filter.render();
		},

		initFilterLead: function () {
            var me = this,
                data = this.model.get('RecyclingFilterList');//每个实例名字会不同
            var defaultValue = this.model.get('RecycleDefault');
			if(this.model.get('needDepartmentChildren')){
				defaultValue.filters.forEach(filter => {
						if(filter.type == 'department' && (filter.operator == 'IN' || filter.operator == 'NIN') && filter.is_cascade){
							filter.field_values = filter.field_values.map(value => value + '_y');
						}
					})
				
			}
            if (!this.filter) {
                var filter = this.filter = new Filter({
                    apiname: 'LeadsObj',
                    $wrapper: me.rulesWrap.eq(0),
                    width: 750,
                    title: '<span class="del-span j-delete-filter"></span><span style="color:#919eab">' + $t("且（AND）") + '</span>',
                    max: 10,
					filterType: ["group", "image", "file_attachment", 'signature', "formula", "count", "quote", "auto_number", 'employee_many', 'department_many', 'html_rich_text', 'object_reference_many'],
					filterApiname: ['expire_time', 'is_remind_recycling', 'remaining_time', 'leads_status', 'completed_result', 'conversion_probability', 'remind_days', 'transform_time', 'leads_pool_id', 'assigner_id', 'life_status', 'owner_change_time', 'resale_count', 'transform_time', 'assigned_time', 'collected_to', 'completed_field_quanlity', 'leads_stage_changed_time', "is_duplicated", "extend_days", "out_tenant_id", 'out_owner'],
                    defaultValue: (defaultValue && defaultValue.filters  && _.map(defaultValue.filters, function (item) {
                        return [item.field_name, item.operator, item.field_values];
                    })) || [],
                    parseFields: function (fields) {
                        // TODO server 暂不支持计算字段返回 日期、时间、日期时间的类型
                        // TODO server 要走isIndex 计算公司中包含特定时间的计算
						let _fields = {};
						_.each(fields || [], function (item) {
                            var flag = (item.type == 'formula' && _.contains(['date_time', 'date', 'time'], item.return_type)) || (item.type == 'formula' && !item.is_index);
                            if (item.type === 'select_one') {
                                item.type = 'select_many';
                                item.old_type = 'select_one';
							}
							if (item.api_name === 'out_owner') {
								item.type = 'exemployee'
							}

							// 状态字段选项过滤未分配、已转换
							if (item.api_name === 'biz_status') {
								item.options = _.filter(item.options, function (opt) {
									return !_.contains(['un_assigned', 'transformed'], opt.value);
								});
							}
							!flag && (_fields[item.api_name] = item);

                            // return !flag;
                        });
						return _fields;
                    },
                    disabled: me.get('disabled'),
                    data: data.length ? data : {},
                    fieldlist: me.model.get('fieldlist'),
                    helper: helper
                });

				filter.on('render', function () {
                    me.resizedialog && me.resizedialog();
                });
                filter.on('add.item', function () {
                    me.resizedialog && me.resizedialog();
                });
                filter.on('del.item', function () {
                    me.resizedialog &&  me.resizedialog();
                });
            }

			// this.filter.render();
        },

		/*radio切换*/
		ruleRadioHandle: function (e) {
			var me = this,
				target = $(e.target),
				val = target.data('value');
			this.$ruleradios.removeClass('radio-btn-selected');
			target.addClass('radio-btn-selected');

			if (val == 0) {
				//切换到自动收回
				this.rulesWrap.eq(1).removeClass('hide');
				this.model.set({
					'RecyclingRemindRuleList': [],
					'RecyclingRuleType': 2,
					'DealDays': 0,
					'FollowUpDays': 0
				});
				this.initSelect();
				$('.radio-btn-highsea', this.element).filter('.radio-btn-selected').get(0).click();
			} else {
				//切换到不自动收回
				this.rulesWrap.eq(1).addClass('hide');
				this.model.set({
					'RecyclingRuleType': 1,
					'FollowUpDays': 0,
					'DealDays': 0,
					'HighSeasID': '',
					'HighSeasName': ''
				});
			}
			me.resizedialog();
		},

		/*radio切换*/
		highseaRadioHandle: function (e) {
			var me = this,
				target = $(e.target),
				val = target.data('value');
			this.highsearadios.removeClass('radio-btn-selected');
			target.addClass('radio-btn-selected');
			$('.highsea-wrap', me.element).toggle(val == 1);
			if (val == 0) {
				me.model.set('HighSeasID', '');
				me.model.set('HighSeasName', '');
			} else {
				var id = me.backselect.getValue();
				var name = _.findWhere(me.backselect.get('options'), {value: id}).name;
				me.model.set('HighSeasID', id);
				me.model.set('HighSeasName', name);
			}
			me.resizedialog();
		},

		leadsObjInputHandle(e) {
			var target = $(e.target);
			util.hideErrmsg(target);
		},
		//input事件回调
		inputHandle: function (e) {
			var target = $(e.target),
				val = target.val();

			util.hideErrmsg(target);
			if (target.is('.j-rule-ipt')) {
				var index = $(e.currentTarget).closest('.fm-item').index();
				if (this['ruleselect' + index].getValue() == 2) {
					this.model.set('DealDays', val || 0);
				} else {
					this.model.set('FollowUpDays', val || 0);
				}
			} else {
				if (this.remindipt.val() == '') {
					this.model.set('RecyclingRemindRuleList', []);
				} else {
					this.model.set({
						'RecyclingRemindRuleList': [{
							RemindDays: val * 1 || 0
						}]
					});
				}
			}
		},

		parseSaveData: function () {
			return  this.filter.getParsedData("customerrule");
		},
		getFilterValue() {
			let data = this.filter.getValue();
			// this.hideError();
			if (!this.filter.valid()) {
				// this.showError();
				util.remind(3, $t("请填写筛选值!"));
				return null;
			}
			return data;
		},

		//过滤不符合条件的筛选条件，并设置RecyclingFilterList
		saveHandle: function (e) {
			// this.datas = this.parseSaveData().temp;
			// var filter = _.filter(this.datas, function (d) {
			// 	return d.FieldName !== '' &&
			// 		d.FieldName !== void 0 &&
			// 		d.Compare !== '' &&
			// 		d.Compare !== void 0 &&
			// 		((![10, 9].includes(d.Compare) && d.FieldValue !== '') || [10, 9].includes(d.Compare)) &&
			// 		d.FieldValue !== 'null' &&
			// 		d.FieldValue !== void 0 &&
			// 		!~Object.prototype.toString.call(d.FieldName).indexOf('Null') &&
			// 		!~Object.prototype.toString.call(d.Compare).indexOf('Null') &&
			// 		!~Object.prototype.toString.call(d.FieldValue).indexOf('Null');
			// });


			// var rules = this.parseSaveData().rules;
			// this.model.set('RecyclingFilterList', filter);
			// this.model.set('RecycleDefault', rules);
			let filter = this.getFilterValue();
			if (filter) {
				this.model.set('RecyclingFilterList', filter);
				this.model.set('RecycleDefault', filter);
			}

			if (this.validate() && filter) {
				if (this.model.get('RecyclingRuleType') !== 1) {
					this.collect();
				}
				console.log(this.model.toJSON());
				this.cache.set(this.model.toJSON());
				this.trigger('save', this.cache);
				this.hide();
				return;
			}
		},


		collect: function () {
			var me = this;
			me.model.set({
				'DealDays': 0,
				'FollowUpDays': 0
			});

			if (me.get('apiName') == 'LeadsObj') {
				$('.time-rule', me.$el).each(function (index) {
					if ($(this).hasClass('crm-hide')) {
						return;
					}
					let val = me['ruleselect' + index].getValue();
					let day = $('.j-rule-ipt', me.$el).eq(index).val();
					let hours = $('.j-rule-hours', me.$el).eq(index).val();
					if (val == 2) {
						me.model.set('DealDays', day * 24 + hours * 1);
					} else {
						me.model.set('FollowUpDays', day * 24 + hours * 1);
					}

				});

				let remind_day = $('.j-remind-ipt', this.$el).val()
				let remind_hours = $('.j-remind-hours', this.$el).val()

				if (remind_day === '' && remind_hours === '') {
					me.model.set('RecyclingRemindRuleList', []);
				} else {
					me.model.set('RecyclingRemindRuleList', [{ RemindDays: remind_day * 24 + remind_hours * 1 }]);
				}
			} else {
				$('.time-rule', me.$el).each(function (index) {
					if ($(this).hasClass('crm-hide')) {
						return;
					}
					var val = me['ruleselect' + index].getValue();
					if (val == 2) {
						me.model.set('DealDays', +$('.j-rule-ipt', me.$el).eq(index).val());
					} else {
						me.model.set('FollowUpDays', +$('.j-rule-ipt', me.$el).eq(index).val() || 0);
					}
				});
			}
		},

		//验证数据
		validate: function () {
			var me = this,
				flag = true,
				dayReg = /^(0|\+?[1-9][0-9]*)$/,
				remindVal = this.remindipt.val();
			// util.hideErrmsg(this.rulesWrap);
			// if (!this.datas.length) {
			// 	util.remind(3, '请完成当前规则的设置');
			// 	return
			// }
			if (me.model.get('RecyclingRuleType') !== 1) {
				me.$('.time-rule').each(function (index) {
					var $item = $(this)

					if($item.hasClass('crm-hide')) {
						return;
					}
					dayReg.lastIndex = 0;

					if (me.get('apiName') == 'LeadsObj') {
						let $day = $('.j-rule-ipt', $item)
						let $hours = $('.j-rule-hours', $item)
						let $remind_day = $('.j-remind-ipt', this.$el);
						let $remind_hours = $('.j-remind-hours', this.$el);
						let day = $day.val() || 0;
						let hours = $hours.val() || 0;
						let remind_day = $remind_day.val() || 0;
						let remind_hours = $remind_hours.val() || 0;

						if (!dayReg.test(day) || !dayReg.test(hours)) {
							util.showErrmsg($day, $t('请填写【0～99999】之间的整数值'))
							flag = false;
						}else if (day * 24 + hours * 1 === 0) {
							util.showErrmsg($day, $t('请填写【0～99999】之间的整数值'))
							flag = false;
						}



						if (!dayReg.test(remind_day) || !dayReg.test(remind_hours)) {
							util.showErrmsg($remind_day, $t('请填写【0～99999】之间的整数值'))
							flag = false;
						} else if (remind_day * 24 + remind_hours * 1 === 0 && $remind_day.val() !== '' && $remind_hours.val() !== '') {
							util.showErrmsg($remind_day, $t('请填写【0～99999】之间的整数值'))
							flag = false;
						} else if (remind_day * 24 + remind_hours * 1 > day * 24 + hours * 1 && ($remind_day.val() !== '' || $remind_hours.val() !== '')) {
							util.showErrmsg(me.remindipt, $t("提前提醒天数应小于等于收回天数"));
							flag = false;
						}

					} else {
						let $input = $('.j-rule-ipt', $item)
						let val = $input.val()
						if (val == '') {
							util.showErrmsg($input, $t("请填写自动收回公海天数"));
							flag = false;
						} else if (!dayReg.test(val) || val == 0) {
							util.showErrmsg($input, $t("请填写大于0的整数"));
							flag = false;
						}

						if (remindVal) {
							dayReg.lastIndex = 0;
							if (!dayReg.test(remindVal) || remindVal == 0) {
								util.showErrmsg(me.remindipt, $t("请填写大于0的整数"));
								flag = false;
							} else if (val * 1 < remindVal * 1) {
								util.showErrmsg(me.remindipt, $t("提前提醒天数应小于等于收回天数"));
								flag = false;
							}
						}
					}

					if (me.$('.rules-fn-checkbox .crm-g-checkbox').hasClass('state-active') && !me.model.get('FunctionApiName')) {
						util.showErrmsg(me.$('.rules-fn-wrapper'), $t("请添加自定义函数"));
						flag = false;
					}
				});
				if ($('.time-rule', me.$el).not('.crm-hide').length > 1) {
					var val1 = me['ruleselect0'].getValue(),
						val2 = me['ruleselect1'].getValue();
					if (val1 == val2) {
						var options = config[me.get('apiName')].options;
						util.alert($t("因设置天数较大的条件不会被执行请勿同时选择") + ['', '', '【' + options[0].name + '】', '【' +options[1].name + '】'][val1]);
						flag = false;
					}
				}
			}

			return flag;
		},

		validateRules: function (rules) {
			// 全部
			if (this.model.get('IsAllSalesClue')) {
				return true;
			}

			// 按筛选条件
			var flag = true;
			//9:为空，10:不为空
			_.each(rules, function (rule) {
				if (!~_.indexOf([9, 10], rule.Compare) && !rule.FieldValue) {
					flag = false;
				}
			});

			!flag && util.remind(3, $t("筛选条件规则的值不能为空"));

			return flag;
		},

		addTimeRule: function (e) {
			var $target = $(e.currentTarget);
			$target.closest('.fm-item').siblings('.time-rule').removeClass('crm-hide');
			$('.time-rule .btn', this.$el).removeClass('add-timerule').addClass('del-timerule');
		},

		delTimeRule: function (e) {
			var $target = $(e.currentTarget);
			$target.closest('.fm-item').addClass('crm-hide');
			$('.time-rule .btn', this.$el).removeClass('del-timerule').addClass('add-timerule');
		},

		checkboxHandle: function(e) {
			var $tg = $(e.target);
			var $fn = this.$('.rules-fn-item');
			$tg.toggleClass('state-active');
			util.hideErrmsg(this.$('.rules-fn-wrapper'));
			if ($tg.hasClass("state-active")) {
				$fn.attr('data-status', 'add');
			} else {
				$fn.attr('data-status', '');
				this.model.set('FunctionApiName', '');
				this.model.set('FunctionName', '');
			}
		},

		addCustomFn: function(e) {
			e.preventDefault();
			var me = this;
			util.hideErrmsg(this.$('.rules-fn-wrapper'));
			require.async('paas-function/sdk.js', function(FunctionSdk ) {
				FunctionSdk && FunctionSdk.getRecoveryFunction(
					{
						object_api_name: me.get('apiName'),
						zIndex: 2000 // 默认
					},
					res => {
						if (res.status) {
							me.model.set('FunctionApiName', res.data.function.api_name);
							me.model.set('FunctionName', res.data.function.function_name);
							me.$('.rules-fn-item').attr('data-status', 'edit');
							me.$('.rules-fn-name').html(res.data.function.function_name);
						}
					}
				);
			})
		},

		updateCustomFn: function(e) {
			e.preventDefault();
			var me = this;
			require.async('paas-function/sdk.js', function(FunctionSdk ) {
				FunctionSdk &&  FunctionSdk.update({
					api_name: me.model.get('FunctionApiName'),
					object_api_name: me.get('apiName'),
					zIndex: 2000
				}).on("complete", res => {
					me.model.set('FunctionName', res.data.function.function_name);
					me.$('.rules-fn-name').html(res.data.function.function_name);
				});
			})
		},

		delCustomFn: function() {
			this.model.set('FunctionApiName', '');
			this.model.set('FunctionName', '');
			this.$('.rules-fn-item').attr('data-status', 'add');
		},

		cancelHandle: function (e) {
			this.hide();
		},

		hide: function () {
			this.destroy();
		},

		// showError: function () {
		// 	util.showErrmsg(this.$('.rules-wrap'), $t('请填写筛选值!'));
		// },

		// hideError: function () {
		// 	util.hideErrmsg(this.$('.rules-wrap'));
		// },

		destroy: function () {
			this.backselect && this.backselect.destroy();
			this.ruleselect && this.ruleselect.destroy();
			this.model && this.model.off('change:RecyclingRemindRuleList');
			this.model && this.model.off('change:RecyclingRuleType');
			return EditRule.superclass.destroy.apply(this);
		}
	});

	module.exports = EditRule;
});
