//自定义对象表格
define(function(require, exports, module) {
    var util = CRM.util;
    var CONFIG = require('./config/config');
    var BaseTable = require('crm-assets/widget/table/table');
    var attrValuesTpl = require('./template/attributes-html');
    var CRM_TABLE_CONFIG;
    try {
        CRM_TABLE_CONFIG || FS.store.getItem('crm.table.columnlockconfig').then((config) => {
            if (config) {
                CRM_TABLE_CONFIG = JSON.parse(config);
            } else {
                CRM_TABLE_CONFIG = {};
            }
        });
    } catch (e) {}
    
    var COMPARE = {
        1: 'EQ', //等于
        2: 'N', //不等于
        3: 'GT', //大于
        4: 'GTE', //大于等于
        5: 'LT', //小于
        6: 'LTE', //小于等于
        7: 'LIKE', //包含
        8: 'NLIKE', //不包含
        9: 'IS', //为空
        10: 'ISN', //不为空
        11: 'STARTWITH', //起始于
        12: 'ENDWITH',
        13: 'HASANYOF', // 属于
        14: 'NHASANYOF', // 不属于
        17: 'BETWEEN', // 时间段
        18: 'LTE', // 前N天
        19: 'GTE', // 后N天
        20: 'LTE', // 前N月
        21: 'GTE', // 后N月
        22: 'IN', // 文本类型的属于
        23: 'NIN', // 文本类型的不属于
        24: 'EQL',  // 日期类型 等于
        25: 'LTE', // 前N周
        26: 'GTE', // 后N周（不含当前天/周/月）
        27: 'LTEO', // 前N天
        28: 'GTEO', // 后N天（含当前天/周/月）
        29: 'LTEO', // 前N周
        30: 'GTEO', // 后N周
        31: 'LTEO', // 前N月
        32: 'GTEO', // 后N月
        33: 'LT', // N天前
        34: 'GT', // N天后
        35: 'LT', // N周前
        36: 'GT', // N周后
        37: 'NEQ', //定位字段不等于
        38: 'IN', // 布尔值属于/不属于
        39: 'NIN'
    };
    // 需要添加产品包图标的对象
    var AddPackageIconApiNameList = ['PriceBookProductObj', 'ProductObj', 'SPUObj'];

    var NOOP = function() {};

    var RECOMPARE = _.invert(COMPARE);
    RECOMPARE.LIKE = 7;
    RECOMPARE.LTE = 6;
    RECOMPARE.GTE = 4;
    RECOMPARE.EQL = 1;
    RECOMPARE.LT = 5;
    RECOMPARE.GT = 3;
    RECOMPARE.IN = 22;
    RECOMPARE.NIN = 23;

    var ObjectTable = Backbone.View.extend(_.extend({
        options: {
            apiname: '', //自定义对象编码名称
            mainName: 'name', //服务端约定的主属性名称
            displayName: '', //自定义对象显示名称
            recordType: '',
            isFilter: true, //是否支持筛选
            doStatic: false, //是否不用请求数据，true时不请求
			showTitle: true, //显示左上角的大标题
            isEdit: false,
            onlyGetColumns: false, // 仅获取表格表头数据
            showPage: true,
            parseHeaderData: null, // 特殊处理表头数据
            layout_by_template: true, // true按场景 false 按业务类型
			cellEdit: false,
			isRenderRecordType: true,	//是否渲染业务类型
            // showOperate: false //不显示自定义按钮列
            
			noFetchColumn: false, // 如果走新表头接口，从外部传columns信息，不再请求column；配合columns参数使用
			columnsInfo: {},	//	新表头接口返回的信息，包含所有业务类型的信息；
			tableOptions: {},	// 基础table组件配置
            preSetButton: [],   // 按钮的api_name，如 ['drag_row']
            preSetButtonInfo: {
                drag_row: {      // 拖拽行
                    api_name: 'drag_row',
                    action: 'drag_row',
                    action_type: 'pre_set_by_front',
                    label: '',
                    tip: $t('拖拽可排序'),
                    class_name: 'drag-row',
                    render_type: 'not_fold',
                    width: 40
                },
                set_top: {      // 置顶
                    api_name: 'set_top',
                    action: 'set_top',
                    action_type: 'pre_set_by_front',
                    label: '',
                    tip: $t('置顶'),
                    class_name: 'set-top',
                    render_type: 'not_fold',
                    width: 40
                },
                set_bottom: {      // 置底
                    api_name: 'set_bottom',
                    action: 'set_bottom',
                    action_type: 'pre_set_by_front',
                    label: '',
                    tip: $t('置底'),
                    class_name: 'set-bottom',
                    render_type: 'not_fold',
                    width: 40
                }
            },
            supportFieldMapping: true,
            supportCrossFilters: false, //是否支持跨对象筛选
        },

        initialize: function(opts) {
            this.getPaasConfig();
            this.super = ObjectTable.prototype;
            this._events = null;
            this.options = _.extend({}, ObjectTable.prototype.options, this.options);
            var opts = this.options;
            if (!opts.recordType) {
                opts.recordType = opts.recordType_apiName;
            } else if (!opts.recordType_apiName) {
                opts.recordType_apiName = opts.recordType;
            }
        },

        //是否开启列表缓存 默认都不开启
        isGrayListHeaderCache() {},

        isCrossFiltersMode() {
            return this.get('filterMode') === 'crossFilters';
        },

        get: function(key) {
            return this.options && this.options[key];
        },

        set: function(key, value) {
            if (!this.options) return;
            _.isObject(key) ? _.extend(this.options, key) : (this.options[key] = value);
        },

        render: function() {
            if (!this.$el) return;
            this._fetchFieldMapping();
            this.renderTable();
        },

        refresh: function() {
            if (!this.table) return;
            this.clearRemberData();
            this.table.setParam({}, true);
        },

        // 刷新当前视图
        refreshCurView: NOOP,

        /**
         * completeCb 表格渲染完成的回调函数
         * 用于_termChangeChangeHandle确保表格实力画完成
         * 客户 商机等的试图模式
         */
        renderTable: function(type, completeCb) {
            var me = this;
            me.initPluginService().then(() => {me.getPaasConfig(() => {
                me.fetchColunms(null, function() {
                    if(!me.options) return;

                    if (me.options.onlyGetColumns) {
                        me.trigger('onlyGetColumns', me);
                        return
                    }
                    me._renderTable(function() {
                        if (type) {
                            me.recordTypeChangeChangeHandle(type);
                        }
                        completeCb && completeCb();
                        me._doDelay();
                    });
                });
            });});
        },

        renderBuiltInViewIfNeeded: function(data) {
            if (!_.contains(['card'], this.get('viewType'))) {
                this._fter?.clear();
                return;
            }

            const fter = () => {
                return new Promise((resolve) => {
                    if (this._fter) {
                        resolve(this._fter);
                        return;
                    }
                    require.async('crm-modules/common/flexitable/flexitable_new', (FlexiTable) => {
                        this._fter = new FlexiTable({
                            showLoading: !this.isNewLayout(),
                            coordination: () => {
                                return this.table;
                            },
                            renderItem: (container, options) => {
                                return this.viewService.rci(container, {
                                    ...options,
                                    FlexiTable,
                                });
                            },
                            dispatchBatchChangeEvent: (item) => {
                                this.batchBtnChangeHandle(item);
                            }
                        });

                        resolve(this._fter);
                    });
                });
            };

            fter().then((instance) => {
                instance.requestPaint(data, {
                    viewType: this.get('viewType'), // 列表视图类型
                    viewInfo: _.clone(this.get('viewInfo')), // 列表视图信息
                    describe: _.clone(this.get('objectDescribe') ? this.get('objectDescribe').fields : {}),
                });
            });
        },

		getTable: function (fn) {
			require.async('crm-assets/widget/table/table', (Table) => {
                if (Table) {
                    if (this.getPsrp('treeConfig') && Table.makeTreeTable) {
                        Table = Table.makeTreeTable(Table);
                    }
    				fn && fn(Table);
                }
			});
		},

        _renderTable: function(completeCb) {
            var me = this;
			me.getTable(function (Table) {
				if (!Table) {
					return;
				}

                var options = me.getOptions() || {};

                me.pluginOptionsFormatter(options);

                me.__hackOptions(options);
                
				var initComplete = options.initComplete;
				options.initComplete = function () {
                    me.runRapService();
					initComplete && initComplete();
					me.initCompleteSecond(completeCb);
                };


                me._applyDisabledFeatureControlsToOptions(options);

				me.table && me.table.destroy();

                var _st = new Date().getTime();
				me.table = new Table(_.extend({
					isMyObject: true, //默认都是自定义对象
					noAllowedWrap: true,
                    minContentWidth: 100,
					editable: me.get('isEdit'),
                    thirdAddRecordType: me.get('thirdapp_record_type')
				}, options));
                me.set('mainRenderCostTime', new Date().getTime() - _st);//表格主体渲染时间，此时表格已可见，但是还未请求数据，数据未填充
				me._proxyTableFn(me.table);
				me.bindTableEvents(me.table);
				me.copyDtEvents(me.table);
                me.extendTableEvents(me.table);
			})
        },

		initCompleteSecond:function(completeCb){
        	var me = this;
			me.tableComplete(me.table);
			me.trigger('complete', me);

			setTimeout(function() {
				completeCb && completeCb();
            }, 30);

            if (this.options.pickAttribute){
                $('.crm-table', this.$el).addClass("pick-attribute-table");
            }
		},

        //普通的Header
        parseHeader: function(res) {
            var rv = res.Value;
            var headers = [];
            _.each(res.Value.layout.components[0].field_section, function(a) {
                [].push.apply(headers, a.form_fields);
            })

            this._addWidthInHeaders(headers, rv.fieldList);

            return headers;
        },

        //根据server下发的fieldList对字段进行排序和隐藏
        //目前fieldList只有在全部业务类型的时候才会出现
        //fieldList只针对具体的业务类型
        //fieldList [{name:true}, {sex:false}]
        parseHeaderByFieldList: function(res) {
            var rv = res.Value;
            var fields = rv.objectDescribe.fields;
            var tf = {};
            var rfc = {};
            var headers = [];

            _.each(rv.fieldList, function(a) { //fieldList数据结构定义很糟糕，转换成一个对象列表
                _.extend(tf, a);
            })
            _.each(rv.layout.components[0].field_section, function(a) {
                _.each(a.form_fields, function(b) {
                    rfc[b.field_name] = b;
                })
            })
            _.each(tf, function(v, k) {
                if (!fields[k]) return;

                var temp = rfc[k] || {
                    field_name: k,
                    render_type: fields[k].type
                };
                temp = _.extend({}, temp);
                temp.isHidden = !v;
                headers.push(temp);
            })

            this._addWidthInHeaders(headers, rv.fieldList);

            return headers;
        },

        parseFieldList: function(headers, fieldList, defterm) {
            var _headers = {};
            var termWidth = {};
            var wrapLine = {};
			defterm && _.each(defterm.field_list, a => {
				termWidth[a.field_name] = a.width;
                wrapLine[a.field_name] = a.auto_wrap_line;
			})
            _.each(headers, function(item) {
                _headers[item.field_name] = item;
            })
            _.each(fieldList, function(a) {
                var tmp = _headers[a.field_name];
                if (tmp) {
                    a.is_show = true;

                    let width = a.width || termWidth[a.field_name];
                    width && (tmp.width = width);

                    tmp.auto_wrap_line  = a.auto_wrap_line ?? wrapLine[a.field_name];
                } else {
                    a.is_show = false;
                }
            })

            return fieldList;
        },

        //根据场景的fieldList对字段进行排序和隐藏
        parseHeaderByTerm: function(term, res) {
            var rv = res.Value;
            var fields = rv.objectDescribe.fields;
            var rfc = {};
            var headers = [];
            _.each(rv.layout.components[0].field_section, function(a) {
                _.each(a.form_fields, function(b) {
                    rfc[b.field_name] = b;
                })
            })
            _.each(term.field_list, function(obj) {
                var k = obj.field_name;
                if (!fields[k]) return;

                var temp = rfc[k] || {
                    field_name: k,
                    render_type: fields[k].type
                };
                temp = _.extend({}, temp);
                temp.isHidden = !obj.is_show;
                obj.width && (temp.width = obj.width);
                headers.push(temp);
            })

            return headers;
        },

        // 获取扩展参数
        getExtendAttribute: function() {
            return this.options.extendColumnsParam || {};
        },

        // 对外提供运行插件的方法
        runPlugin: function(name, options) {
            return this.runPshk(name, options);
        },

        //追加宽度属性到表头里面
        _addWidthInHeaders: function(headers, fieldList) {
            _.each(fieldList, function(a) {
                _.each(a, function(v, k) {
                    if (k !== 'width' && a.width) {
                        var temp = _.findWhere(headers, {
                            field_name: k
                        });
                        temp && (temp.width = a.width);
                    }
                })
            })
        },

        fetchColunms: function(param, cb, context) {
            var me = this;
            var isFirst = me.get('isFirstRequestHeader'); // server第一次精确是有逻辑的 处理默认场景的业务类型
            var ra = me.get('recordType');
            // var cache = me.get('_headerCache') || {};
            // var key = me.get('apiname') + '_' + (ra || 'all') + '_' + (!isFirst ? '1' : '0');
            var thirdApp = me.get('thirdapp_record_type');
            // if (cache[key]) {
            // me.fetchColunmsSuccess(cache[key], cb, context);
            // return;
            // }

            var _startTime = new Date().getTime();

			if (me.options.noFetchColumn) {
                me.set('listHeaderCostTime', new Date().getTime() - _startTime);
				me.fetchColunmsSuccess({
					Value: {
						objectDescribe: me.options.columnsInfo.objectDescribe,
						layout: me.options.columnsInfo.detailLayouts[me.options.recordType]
					}
				}, cb, context);
				return
			}

            let cacheRes = this.__getCacheListHeader();
            if(cacheRes) {
                me.set('listHeaderCostTime', new Date().getTime() - _startTime);
                me.options.parseHeaderData && me.options.parseHeaderData(cacheRes.Value);
                me.fetchColunmsSuccess(cacheRes, cb, context);
                return;
            }


            //840排序除了升序 降序两种状态之外还可以恢复到没有排序，考虑不是所有场景都支持，只有明确是调用这个接口出来的表格才支持
            me.set('supportDefaultSort', true);

            var apiname = me.get('apiname');
            me.columnsAjax && me.columnsAjax.abort();

            me.columnsAjax = util.FHHApiCacheFields(_.extend({
                url: '/EM1HNCRM/API/v1/object/' + apiname + '/controller/ListHeader',
                data: _.extend({
                    include_layout: true,
                    apiname: apiname,
                    layout_type: "list",
                    recordType_apiName: thirdApp ? thirdApp : ra === 'all' ? void 0 : ra,
                    layout_by_template: !isFirst && me.get('layout_by_template'),
                    thirdapp_record_type: thirdApp || void 0,
					check_edit_permission: me.get('cellEdit'),
                    list_type: {page: 'list', selected: 'selected', related: 'related'}[me.get('listType')],
                    list_component: me.get('list_component'),
                    cross_object_filter: me.isCrossFiltersMode(),
                }, param, me.getExtendAttribute('from_listheader')),
                success: function(res) {
                    //用于埋点上报
                    me.set('listHeaderCostTime', new Date().getTime() - _startTime);

                    if (res.Result.StatusCode === 0) {
                        if (thirdApp) {
                            me.__hackThirdAppRecordType(res.Value, thirdApp);
                        }
                        if (res.Value.isInApprovalWhiteList) { //hack, 主要用于判断主从审批在锁定解锁时出不同提示。
                            CRM.set('isInApprovalWhiteList', true);
                        }

                        me.__setCacheListHeader(res);
                        // cache[key] = res;
                        // me.set('_headerCache', cache);
                        (me.options && me.options.parseHeaderData) && me.options.parseHeaderData(res.Value);
                        me.afterFetchListHeader().then(() => me.fetchColunmsSuccess(res, cb, context));
                        return;
                    }
                    let msg = res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试");
                    FxUI ? FxUI.Message({
                        isMiddler: true,
                        duration: 3000,
                        message: msg,
                        type: 'error'
                    }) : util.alert(msg);
                },
                complete: function() {
                    me.columnsAjax = null;
                }
            }, {headers: me.options.reqHeaders || {}}), {
                errorAlertModel: 1
            }, me.unreadCachDes)

            isFirst || me.__queryFirstList();

            me.set('isFirstRequestHeader', true);
        },

        afterFetchListHeader() {
            return new Promise((resolve) => {
                this._fetchFieldMapping(() => {
                    resolve();
                })
            })
        },

        //获取对象字段信息
        _fetchFieldMapping(callBack) {
            let apiname = this.get('apiname');
            if(this.get('supportFieldMapping') && apiname) {
                if(!this._fetchFieldMappingCallBack) {
                    CRM.util.findRecordFieldMapping([{
                        describeApiName: apiname
                    }]).then((fiedMapping) => {
                        if(fiedMapping && !_.isEmpty(fiedMapping[apiname])) {
                            this.__fieldMapping = fiedMapping[apiname];
                        }
                    }).finally(() => {
                        _.each(this._fetchFieldMappingCallBack, callBack => callBack());
                        this._fetchFieldMappingCallBack = null;
                    })
                }

                if(callBack) {
                    (this._fetchFieldMappingCallBack || (this._fetchFieldMappingCallBack = [])).push(callBack);
                }
            } else {
                callBack && callBack();
            }
        },

        beforeExcuteAction(action, data) {
            var actionExtendConfig = this.getPsrp('actionExtendConfig');

            if (!actionExtendConfig) {
                return;
            }

            var actionConfig = actionExtendConfig[action];
            if (actionConfig && actionConfig.parseParam) {
                _.extend(data, actionConfig.parseParam(data));
                return;
            }
        },

        __hackThirdAppRecordType(rv, thirdApp) {
            let objectDescribeExt = rv.objectDescribeExt;
            let rr = rv.objectDescribe.fields.record_type;
            if(objectDescribeExt && objectDescribeExt.fields && objectDescribeExt.fields.record_type) {
                _.extend(rr, objectDescribeExt.fields.record_type);
                delete objectDescribeExt.fields.record_type;
            }
           
            rr.options = [_.findWhere(rr.options, {
                api_name: thirdApp
            }) || {
                api_name: thirdApp,
                label: '--'
            }];
        },

        //与表头一起并发请求数据
        __queryFirstList() {
            if(!this.isGrayListHeaderCache()) return;
            if(this.get('customScenes')) return;
            //如果插件指定了默认的业务类型

            if (this.getPsrp('termExtendConfig') || this.getPsrp('customFilterValue')) {
                return;
            }

            if(this.options && !_.isEmpty(this.options.tableOptions)) return;

            let me = this, apiName = me.get('apiname'), recordType = me.get('thirdapp_record_type') || '', qp;
            try {
                qp = this.parseParam({
                    pageSize: CRM.getLocal('table-pagesize') * 1 || 20,
                    pageNumber: 1
                })
            } catch(e) {}

            if(!qp || qp.search_template_id || !qp.search_query_info) return;

            let sq = JSON.parse(qp.search_query_info);
            sq.filters || (sq.filters = []);
            if(recordType && !_.findWhere(sq.filters, {field_name: 'record_type'})) {
                sq.filters.push({
                    field_values: [recordType],
                    field_name: 'record_type',
                    operator: 'EQ'
                })
            }

            let extendAttr = me.getExtendAttribute('from_listheader') || {};
            qp.ignore_scene_record_type = !!recordType;
            qp.extractExtendInfo = true;
            qp.serializeEmpty = true;
            qp.search_template_id = '';
            qp.search_template_type = 'default';
            qp.search_query_info = JSON.stringify(sq);
            qp.params_of_default_template = {
                extend_attribute: extendAttr.extend_attribute || '',
                thirdapp_record_type: recordType
            }

            me.set('queryParam', qp);

            let __queryFirstListData = {
                status: 0
            };
            __queryFirstListData.ajax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/List',
                data: qp,
                success(res) {
                    __queryFirstListData.status = 1;
                    if(res.Result.StatusCode === 0) {
                        __queryFirstListData.res = res;
                    }
                    __queryFirstListData.callback && __queryFirstListData.callback(res);
                },
                error() {
                    __queryFirstListData.status = 1;
                    __queryFirstListData.callback && __queryFirstListData.callback();
                }
            }, {
                errorAlertModel: 1
            })

            me.__queryFirstListData = __queryFirstListData;
        },

        __queryList(param) {
            let qs = this.__queryFirstListData;
            if(qs) {
                let rst = {
                    abort: () => {
                        qs.aborted = true;
                    },
                    _isEm6: ''
                }
                setTimeout(() => {
                    rst._isEm6 = qs.ajax._isEm6;
                    if(qs.aborted) return;

                    if(qs.status) {//已请求完
                        if(qs.res) {//请求是成功的
                            param.success(qs.res);
                            param.complete();
                        } else {//请求失败 默认再发一次
                            let ajax = util.FHHApi(param);
                            _.extend(rst, ajax);
                        }
                    } else {//接口还没请求完 加个回调函数
                        qs.callback = function(res) {
                            if(qs.aborted) return;

                            if(res && res.Result.StatusCode === 0) {//请求成功
                                param.success(qs.res);
                                param.complete();
                            } else { //请求失败 再发一次
                                let ajax = util.FHHApi(param);
                                _.extend(rst, ajax);
                            }
                        }
                    }
                })

                delete this.__queryFirstListData;

                return rst;
            }

            return util.FHHApi(param);
        },

        //缓存表头数据
        __setCacheListHeader(r) {
            if(this.isGrayListHeaderCache()) {
                this.__cachelistHeaderData = r;
            }
        },

        __getCacheListHeader() {
            if(!this.isGrayListHeaderCache() || this.__modifyedTerm) {
                this.__modifyedTerm = false;
                return;
            };
            
            let term = this._getTermById(this.get('curTermId'));
            if(term && term.field_list_type === 0) {
                return this.__cachelistHeaderData;
            }
        },

        fetchColunmsSuccess: function(res, cb, context) {
            this.pluginHeaderResponseFormatter(res);
            this.setAttributes(res);
            cb && cb.apply(context || this, [this.get('columns'), this.get('term')]);
            return this
        },

        //请求头部成功之后需要设置一些必须的属性
        //displayName
        //term 场景
        //layout 布局信息
        //columns table的列信息
        //term 场景
        //field_types 一个存储了字段对应的render_type的map 已废弃，主要兼容其它引用该组件的地方
        setAttributes: function(res) {
            var headers;
            var rv = res.Value;
            var describe = rv.objectDescribe;
            var term = this.parseTerm(rv.templates);
            var defterm;
            var fieldList;

            this.set('supportFilterVariable', rv.supportFilterVariable);

            this.set('upstream_tenant_id', describe.upstream_tenant_id);

            this.set('isBigObject', describe.visible_scope === 'big' || describe.visible_scope === 'public_big');

            if (term) {
                if (this.get('curTermId')) {
                    defterm = _.findWhere(term.options, {
                        id: this.get('curTermId')
                    });
                }
                defterm = defterm || _.findWhere(term.options, {
                    isdef: true
                });
                if (!defterm || defterm.isHide) { // 默认场景兼容
                    defterm = _.findWhere(term.options, {
                        isHide: false
                    });
                    defterm = defterm || term.options[0];
                }
            }

            if (defterm && defterm.field_list_type === 0 && this.get('layout_by_template')) {
                headers = this.parseHeaderByTerm(defterm, res);
            } else if (rv.fieldList && rv.fieldList.length && (!this.get('recordType') || this.get('recordType') == 'all') && !this.get('filterFieldList')) {
                headers = this.parseHeaderByFieldList(res);
            } else {
                headers = this.parseHeader(res);
            }
            fieldList = this.parseFieldList(headers, rv.visibleFieldsWidth, defterm);
            
            var objectDescribeExt = _.extend({
                fields: {}
            }, rv.objectDescribeExt);

            this.set('objectDescribeExt', objectDescribeExt);

            this.set('baseScenes', rv.baseScenes);

            this.__hackArea(describe.fields);

            var columns = this.parseColumns(headers, describe.fields, defterm);
            var filterColumns = rv.visibleFields && this.parseFilterColumns(rv.visibleFields, describe.fields);

            this._handleFieldMappingTitle(columns, this.get('thirdapp_record_type') || (defterm && defterm.record_type), describe.fields.record_type);
            this._handleFieldMappingTitle(filterColumns, this.get('thirdapp_record_type') || (defterm && defterm.record_type), describe.fields.record_type);

            var addOrderColumn = rv.isChildObj ? {
                name: 'name',
                orderby: 'desc'
            } : {
                name: 'last_modified_time',
                orderby: 'desc'
            };
            var orderColumns = defterm?.orderFields?.map((of) => {
                const col = _.findWhere(filterColumns || columns, {data: of.fieldName});
                return (col && col.isDisable) ? null : ({
                    name: of.fieldName,
                    orderby: of.isAsc ? 'asc' : 'desc'
                });
            })?.filter(Boolean);

            if ((!orderColumns || orderColumns.length === 0)) {
                orderColumns = [addOrderColumn];
            }
            // 最近使用场景首次默认不传排序
            if (defterm?.apiname === 'RecentVisit') {
                orderColumns = [];
            }
            
            // var orderColumn = defterm && defterm.orders ? { // 设置排序的列
            //     name: defterm.orders.fieldName,
            //     orderby: defterm.orders.isAsc ? 'asc' : 'desc'
            // } : addOrderColumn;
            // var orderCol = _.findWhere(filterColumns || columns, {
            //     data: orderColumn.name
            // });
            // orderColumn = orderCol && orderCol.isDisable ? addOrderColumn : orderColumn;

            var attrs = {
                displayName: describe.display_name,
                layout: rv.layout,
                objectDescribe: describe,
                columns: columns,
                term: term,
                defterm: defterm,
                orderColumns: orderColumns, // 列表的高级默认排序
                orderColumn: orderColumns[0], // 列表的默认排序
                addOrderColumn: addOrderColumn, // 新建时的默认排序字段
                field_types: this.parseFieldTypes(filterColumns || columns),
                fields: describe.fields,
                fieldList: fieldList,
                searchFiled: this.getSearchField(describe.fields), // 增加搜索字段
                filterColumns: filterColumns,
                batchButtons: this.parseBatchButtons(rv.buttons, rv.layout.buttons),
                layoutButtons: rv.layout.buttons,
                hasEditPermission: rv.hasEditPermission,
                supportTag: rv.supportTag,
                isChildObj: rv.isChildObj,
                listSingleExposed: rv.listSingleExposed, //单条操作数据外露的按钮数量
                quickFilterField:  rv.quickFilterField, //快速筛选的字段
                viewInfo: this._hackViewInfo(rv.viewInfo), //视图信息
                sceneRenderType: rv.sceneRenderType,
                supportOrFilter: rv.supportOrFilter,
                supportSplitView: !!_.findWhere(rv.viewInfo, {name: 'split_view', is_show: true}),
                sumField: rv.summaryInfo && _.map(rv.summaryInfo, function(a) {return a.field_name}),
                allSumField: rv.allPageSummaryInfo,
                selectedSumField: rv.selectedDataSummaryInfo && _.map(rv.selectedDataSummaryInfo, function(a) {return a.field_name}),
                supportFullFieldSearchOnListPage: rv.supportFullFieldSearchOnListPage,
                supportFullFieldSearch: rv.supportFullFieldSearch,
                multiFieldSort: objectDescribeExt.multi_field_sort,
                crossFiltersEnabled: this.get('supportCrossFilters') && objectDescribeExt.cross_object_filter_button,
                disabledFeatureControls: this.parseDisabledFeatureControls(rv.componentHideType),
            };

            this.pluginAttrsFormatter(attrs);
            this.applyDisabledFeatureControlsToAttrs(attrs);
            this.set(this.beforeSetAttributes(attrs));
            this.afterSetAttributes(attrs);
        },

        //处理下viewInfo
        _hackViewInfo(viewInfo) {
            let t = this.getPsrp('viewInfoExtendConfig');
            if (t) {
                _.each(viewInfo, a => {
                    if(a.name === 'map_view') {
                        a.maxMarkers = t.maxMarkers;
                        a.parseMarkerColor = t.parseMarkerColor;
                        a.createPolygon = t.createPolygon;
                        a.legend = t.legend;
                    }

                    if (t.disableDetailOnFullClick) {
                        a.disableDetailOnFullClick = true;
                    }
                })
            }

            return viewInfo;
        },

        _handleFieldMappingTitle(columns, recordType, recordTypeField) {
            if(!this.__fieldMapping) return;
            if(!recordType && recordTypeField && recordTypeField.options.length === 1) {
                recordType = recordTypeField.options[0].value;
                this.__onlyOneRecortType = true;
            } else {
                this.__onlyOneRecortType = false;
            }
            let arias = (this.__fieldMapping[recordType] || {}).fields || {};
            _.each(columns, a => {
                a.really_title = a.title;
                if(arias[a.data]) {
                    a.label = a.title = arias[a.data].label;
                }
            })
        },

        _updateColumnsTitle(recordType) {
            if(!this.__fieldMapping || this.__onlyOneRecortType) return;

            let arias = (this.__fieldMapping[recordType] || {}).fields || {};
            let titles = {};

            if(recordType === this.__mappingRecordType) return;

            this.__mappingRecordType = recordType;

            _.each(this.options.columns, a => {
                if(!a.data) return;
                
                let vv = arias[a.data] && arias[a.data].label;
                if(vv) {
                    titles[a.data] = a.label = a.title = vv;
                } else if(a.really_title && a.really_title !== a.title) {//还原
                    titles[a.data] = a.label = a.title = a.really_title;
                }
            })
            _.each(this.options.filterColumns, a => {
                if(!a.data) return;

                let vv = arias[a.data] && arias[a.data].label;
                if(vv) {
                    a.title = vv;
                } else if(a.really_title && a.really_title !== a.title) {//还原
                    a.title = a.really_title;
                }
            })


            _.isEmpty(titles) || this.table._updateColumnsTitle(titles);
        },

        //
        // 设置搜索字段
        // 默认 name
        //
        getSearchField: function(fields) {
            var obj = CONFIG[this.get('apiname')];
            var field = obj && obj.search_field ? obj.search_field : this.get('mainName');
            var tt = _.findWhere(fields, {
                api_name: field
            });
            if(tt) {
                tt = _.extend({}, tt);
            }
            return tt;
        },

        
        //设置属性之前可做最后拦截
        beforeSetAttributes: function(attrs) {
            return attrs;
        },

        afterSetAttributes: function(attrs) {
            return attrs;
        },

        applyDisabledFeatureControlsToAttrs: function(attrs) {
            const disabledFeatureControls = attrs?.disabledFeatureControls;

            if (!disabledFeatureControls) return;

            // 业务类型
            if (disabledFeatureControls.recordType === true) {
                attrs.isRenderRecordType = false;
            }

            // 不显示汇总
            if (disabledFeatureControls.summary === true) {
                attrs.sumField = attrs.allSumField = attrs.selectedSumField = null;
            }

            // 不显示单行按钮
            if (disabledFeatureControls.operate === true) {
                attrs.operate = null;
                attrs.showOperate = false;
                attrs.columns = _.filter(attrs.columns, col => col.dataType !== 'operate');
            }

            // 不显示多字段排序
            if (disabledFeatureControls.multiSort === true) {
                attrs.multiFieldSort = false;
            }

            return attrs;
        },

        parseDisabledFeatureControls(configureHiddenFeatures) {
            try {
                const disableableFeatures = {
                    view: false,        // 视图切换
                    term: false,        // 场景切换
                    allowTerm: false,   // 支持场景优先级高于term
                    recordType: false,  // 业务类型
                    filter: false,      // 筛选
                    quickFilter: false, // 快速筛选
                    search: false,      // 搜索
                    termBatch: false,   // 场景批量
                    multiple: false,    // 多选
                    multiSort: false,   // 多字段排序
                    
                    button: false,      // 按钮操作(右上角)
                    operate: false,     // 单行操作
                    batchButtons: false,// 批量操作按钮
                    
                    pagination: false,  // 分页
                    refresh: false,     // 刷新
                    setting: false,     // 设置
                    summary: false,     // 汇总
                    guide: false,       // 引导
                    tag: false,         // 标签

                    detail: false,      // 点击是否显示详情页
                };
    
                // 跨对象筛选
                if (this.isCrossFiltersMode()) {
                    return {
                        view: true,
                        multiSort: true,
                        quickFilter: true,
                        recordType: true,
                        filter: true,
                        search: true,
                        allowTerm: true,
                        guide: true,
                        summary: true,
                        tag: true
                    };
                }

                const disabledFeatureControls = this.get('disabledFeatureControls');

                // 没有配置任何功能，保持现状，返回undefined就是线上的逻辑
                if (_.isEmpty(disabledFeatureControls) && _.isEmpty(configureHiddenFeatures)) {
                    return;
                }

                const pendingDisabledFeatures = {};

                // 后台配置的隐藏功能
                if (configureHiddenFeatures) {
                    configureHiddenFeatures?.forEach(name => {
                        if (name in disableableFeatures) {
                            pendingDisabledFeatures[name] = true;
                        }
                    });
                }

                // 用户配置的
                if (disabledFeatureControls) {
                    // 精简模式下禁用其他功能
                    if (disabledFeatureControls.simple === false) {
                        const omitFeatures = ['detail'];
                        Object.keys(disableableFeatures).forEach(key => {
                            if (_.contains(omitFeatures, key)) {
                                return;
                            }
                            pendingDisabledFeatures[key] = true;
                        });
                    }
                    
                    // disabledFeatureControls是用户传的，优先级高
                    Object.assign(pendingDisabledFeatures, disabledFeatureControls);
                }

                return pendingDisabledFeatures;
            } catch (error) {
                console.error('parseDisabledFeatureControls error', error);
            }
        },

        parseFieldTypes: function(columns) {
            var ft = {}
            _.each(columns, function(item) {
                ft[item.data] = item.__originType;
            })
            return ft;
        },

        formatColumnField: function(apiName, field, item, options) {
            const me = this;
            const {
                fields,
                isEdit,
                isFilter,
                roptions,
                lockColumn
            } = options;

            if (!field) return;

            item || (item = {});

            var fieldname = item.field_name || field.api_name;
            var rt = item.render_type || field.type;
            var tr = {};

            tr.__originType = rt;
            if (fieldname === 'owner_department') { //负责人所在部门重置为部门
                tr.__originType = rt = 'department';
                tr.render = function(data, type, full) {
                    return  full.owner_department ? `<span data-departmentid="${full.owner_department_id}">${_.escape(full.owner_department)}</span>` : CRM.config.TEXT_DEFAULT;
                }
            } else if (rt === 'relevant_team' || rt === 'embedded_object_list') { //相关团队重置为人员
                rt = 'employee';
                tr.render = function(data, type, full) {
                    return _.escape(full.relevant_team__r) || CRM.config.TEXT_DEFAULT
                }
                tr.filterCompare = [9, 10];
                tr.noGroup = true; //server暂不支持按部门筛选，相关团队屏蔽筛选屏蔽选部门
            } else if (rt === 'record_type') {
                rt = 'select_one';
                field.is_index = false; //因为有单独的业务类型筛选入口，放开会有点问题
            } else if(fieldname === 'out_owner') {//外部负责人重置为外部人员类型
                tr.__originType =  rt = 'out_employee';
            } else if(field.is_encrypted) {
                field.is_index = true; //795 加密字段强制支持筛选
                field.noSupportTerm = true;  
                field.filterCompare = [3, 4, 5, 6, 7, 8, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23] //仅支持等于 不等于 为空 不为空
            } else if(field.is_support_tree_view) {
                field.filterCompare = [7, 8, 11, 12];
            }

            rt = me.__hackWhat(field, tr, fields, rt);
            me.__hackTr(apiName, fieldname, tr);
            

            var attrs;
            if (isEdit) {
                attrs = {
                    __isEdit: true,
                    isEdit: !item.is_readonly,
                    isRequired: !!item.is_required,
                    placeHolder: field.default_value,
                    showLookupText: true // lookup 展示文本
                }
                if(field.type !== 'date_time') {
                    attrs.date_format =  '';//编辑时不格式化日期
                }
            } else {
                attrs = {
                    isHidden: item.isHidden,
                    isOrderBy: isFilter && field.is_index && !_.contains(['related_object_data', 'owner_department'], fieldname), //是否支持排序
                    orderValues: [1, 2], // 1降序 0升序
                    isFilter: isFilter && field.is_index //是否支持过滤
                }
                item.width && (attrs.width = item.width);
                me.__hackFieldExt(field);
            }

            if (rt == 'quote') {
                var objectDescribeExt = me.get('objectDescribeExt') || {
                    fields: {}
                };
                var fieldExt = objectDescribeExt.fields ? objectDescribeExt.fields[fieldname] || {} : {};
                field.options = fieldExt.options || field.options || null;
                field.length = fieldExt.length || 0;
                field.decimal_places = fieldExt.decimal_places || 0;
                field.date_format = fieldExt.date_format;
                if (field.quote_field_type === 'image') { //引用字段引用图片，重置为图片类型
                    attrs.isEdit = false;
                    rt = 'image';
                } else if(field.quote_field_type === 'record_type') {
                    _.each(field.options, function(a) {
                        a.value = a.api_name;
                    })
                }
            }

            if(rt == 'data_visibility_range') {
                field.objectApiName = me.get('apiname');
                field.upstreamEi = me.get('upstream_tenant_id');
            }

            if(rt === 'formula' || rt === 'count') {
                tr.range = [50, 30];
            }

            me.__hackBigObject(field, rt);

            _.extend(tr, field, item, attrs, {
                id: fieldname,
                data: fieldname,
                dataType: rt,
                title: field.label,
                options: field.options && me._parseFieldOptions(field, roptions),
                returnType: field.quote_field_type || field.return_type,
                range: rt === 'percentile' ? [10, !field.auto_adapt_places ? 6 : field.decimal_places] : [field.length || 0, field.decimal_places || 0],
                referRule: {employee: 'Employee', employee_many: 'Employee'}[field.quote_field_type || rt],
                isId: true,
                supportFilterVariable: me.get('supportFilterVariable'),
                customFixed: lockColumn && lockColumn[fieldname],
                fixed: me.isFixed(isEdit, fieldname, lockColumn),
                type: field.quote_field_type || field.type,
                beforeImagePreview: !isEdit && rt === 'image' ? function(obj, callback) {
                    return me.runIpbpService(field, obj, callback);
                } : void 0,
                isDisable: field.is_active === false // 是否禁用
            });

            tr._options = tr.options;

            return tr;
        },

        parseColumnsByHeadersAndFields: function(headers, fields) {
            var me = this;
            var columns = [];
            var apiName =me.get('apiname');
            var isEdit = me.get('isEdit');
            var isFilter = me.get('isFilter');
            var roptions = fields.record_type ? me._parseRecordTypeOptions(fields.record_type.options) : [];
            var lockColumn = CRM_TABLE_CONFIG && CRM_TABLE_CONFIG.columnlock && CRM_TABLE_CONFIG.columnlock[apiName];
            
            _.each(headers, function(item) {
                var fieldname = item.field_name;
                var field = fields[fieldname];

                if (!field) return;

                columns.push(me.formatColumnField(
                    apiName,
                    field,
                    item,
                    {fields, isEdit, isFilter, roptions, lockColumn}
                ));
            })

            return columns;
        },

        __hackBigObject(field, rt) {
            let vs = field.visible_scope;
            if(vs === 'big' || vs === 'public_big') {
                field.noSupportSearch = true;
                return field.filterCompare = [7,8,11,12,22,23];
            }

            if(!this.get('isBigObject')) return;

            if(_.contains(['text', 'long_text', 'phone_number', 'email', 'auto_number', 'object_reference'], rt)) {
                field.filterCompare = [7,8,11,12,22,23];
            }
        },

        __hackFieldExt(item) {
            var ext = this.get('objectDescribeExt');
            if(!ext || !ext.fields) return;
            var field = ext.fields[item.api_name];
            if(field) {
                _.extend(item, _.pick(field, ['font_color', 'is_show_thousands', 'is_support_tree_view', 'options', 'visible_scope', 'enable_multi_lang', 'support_languages', 'support_file_suffix', 'tree_type', 'show_tag', 'identify_field_mapping', 'is_ocr_recognition', 'display_mode', 'display_mode_page']));
            }
        },

        __hackWhat: function(field, tr, fields, rt) {
            if(field.used_in !== 'component') return rt;
            var fieldName = field.api_name;
            _.find(fields, a => {
                if(a.group_type !== 'what') return;
                if(!a.fields) return;
                if(a.fields.id_field === fieldName) {
                    tr.is_what = true;
                    tr.noSupportTerm = true;
                    tr.what_object_apiname = a.fields.api_name_field;
                    tr.render = function(data, type, full, typeMap, className) {
                        if (!full[fieldName]) return CRM.config.TEXT_DEFAULT;
                        return `<a href="javascript:;" class="j-show-lookup ${className || ''}" data-id="${full[fieldName]}" data-apiname="${full[a.fields.api_name_field]}">${_.escape(full[fieldName + '__r'] || CRM.config.TEXT_DEFAULT)}</a>`;
                    }
                    rt = 'what_data';
                    return true;
                }
                if(a.fields.api_name_field === fieldName) {
                    tr.is_what = true;
                    tr.render = function(data, type, full) {
                        return full[fieldName + '__r'] ? _.escape(full[fieldName + '__r']) : CRM.config.TEXT_DEFAULT
                    }
                    rt = 'what_apiname'
                    return true;
                }
            })

            return rt;
        },

        __hackArea(fields) {
            if(CRM.util.getUserAttribute('crmNewArea') || CRM.util.getUserAttribute('crmAreaV3') || CRM.util.getUserAttribute('crmGrayTownFilter')) {
                let types = ['country', 'province', 'city', 'district', 'town', 'village'];
                _.each(fields, a => {
                    if(a.group_type === 'area') {
                        _.each(a.fields, (b, k) => {
                            let fieldAreaLabels = [];
                            let field = fields[b];
                            if(!field) return;
                            let index = _.indexOf(types, field.type);
                            if(index < 0) return;
                            _.each(types, (t, i) => {
                                if(i > index) return;
                                let tt = fields[a.fields['area_' + t]];
                                fieldAreaLabels.push(tt ? tt.label : t);
                            })

                            field.fieldAreaLabels = fieldAreaLabels;
                        })
                    }
                })
                let a1 = $t('国家'), a2 = $t('省'), a3 = $t('市'), a4 = $t('区'), a5 = $t('乡镇'), a6 = $t('village', null, '村');
                let aa = {
                    country: [a1],
                    province: [a1, a2],
                    city: [a1, a2, a3],
                    district: [a1, a2, a3, a4],
                    town: [a1, a2, a3, a4, a5],
                    village: [a1, a2, a3, a4, a5, a6]
                }
                _.each(fields, a => { //处理引用字段，引用国家省市区
                    if(aa[a.quote_field_type]) {
                        a.fieldAreaLabels = aa[a.quote_field_type];
                    }
                })
            }
        },

		__hackTr: function (apiname, fieldname, tr) {
			if (apiname === 'ElectronicSignObj') { //电子签对象特殊处理 object_api_name object_data_id
				if (fieldname === 'object_api_name') {
					tr.render = function (data, type, full) {
						return _.escape(full.object_api_name__r) || CRM.config.TEXT_DEFAULT
					}
				} else if (fieldname === 'object_data_id') {
					tr.render = function (data, type, full) {
						if (!full.object_data_id) return CRM.config.TEXT_DEFAULT;
						return '<a href="javascript:;" class="j-show-lookup" data-id="' + full.object_data_id + '" data-apiname="' + full.object_api_name + '">' + _.escape(full.object_data_id__r) + '</a>';
					}
				}
			} else if (apiname === 'AccountFrozenRecordObj') { //深研依赖 账户冻结记录 特殊处理两个字段 后期标准化后删除
				if (fieldname === 'check_record_object_api_name') {
					tr.render = function (data, type, full) {
						return _.escape(full.check_record_object_api_name__r) || CRM.config.TEXT_DEFAULT
					}
				} else if (fieldname === 'check_record_object_data_id') {
					tr.render = function (data, type, full) {
						if (!full.check_record_object_data_id) return CRM.config.TEXT_DEFAULT;
						return '<a href="javascript:;" class="j-show-lookup" data-id="' + full.check_record_object_data_id + '" data-apiname="' + full.check_record_object_api_name + '">' + _.escape(full.check_record_object_data_id__r) + '</a>';
					}
				}
			} else if (apiname === 'AccountTransactionFlowObj') { //深研依赖 账户收支流水 特殊处理两个字段 后期标准化后删除
				if (fieldname === 'outcome_object_api_name') {
					tr.render = function (data, type, full) {
						return _.escape(full.outcome_object_api_name__r) || CRM.config.TEXT_DEFAULT
					}
				} else if (fieldname === 'outcome_object_data_id') {
					tr.render = function (data, type, full) {
						if (!full.outcome_object_data_id) return CRM.config.TEXT_DEFAULT;
						return '<a href="javascript:;" class="j-show-lookup" data-id="' + full.outcome_object_data_id + '" data-apiname="' + full.outcome_object_api_name + '">' + _.escape(full.outcome_object_data_id__r) + '</a>';
					}
				}
			}
		},

		isFixed:function(isEdit, fieldname, lockColumn) {
            if(lockColumn && lockColumn[fieldname] === false) return false;
        	return (!isEdit && fieldname == 'name') || (lockColumn && lockColumn[fieldname])
		},

        //格式化表格上的列
        parseColumns: function(headers, fields) {
            var columns = this.parseColumnsByHeadersAndFields(headers, fields);
            var showColumns = [],
                hideColumns = [],
                fixedLen = 0;
            var tmp = _.findWhere(columns, {
                data: 'record_type'
            });
            if (tmp) {
                tmp.isEdit = tmp.isFilter = false;
            }

            _.each(columns, function(column) {
                fixedLen += column.fixed ? 1 : 0;
                if (!column.isHidden) {
                    showColumns.push(column);
                } else {
                    hideColumns.push(column);
                }
            });

            // 全部为固定列，自适应展示
            if (showColumns.length == fixedLen) {
                showColumns[fixedLen - 1] && (showColumns[fixedLen - 1].fixed = false);
            }

            columns = showColumns.concat(hideColumns);

            if (this.get('showOperate')) { //追加自定义按钮操作列
                columns.push({
                    dataType: 'operate',
                    width: this._getOperateColumnWidth()
                });
            }

            this.addPackageIcon(columns);

            return columns;
        },

        _getOperateColumnWidth: function() {
            var width = 0; // 默认宽度

            var preSetButtonInfo = this.get('preSetButtonInfo');

            _.each(this.get('preSetButton'), function(apiname) {
                var info = preSetButtonInfo[apiname];

                if (info && info.render_type === 'not_fold') {
                    width += (info.width || 0);
                }
            });

            return width;
        },

        //所有可添加的列,包含有权限的，无权限的，禁用的
        parseFilterColumns: function(visibleFields, fields) {
            var headers = [];
            _.each(visibleFields, function(k) {
                if (fields[k]) {
                    headers.push({
                        field_name: k,
                        render_type: fields[k].type
                    });
                }
            })
            var columns = this.parseColumnsByHeadersAndFields(headers, fields);
            var headers1 = [];
            _.each(fields, function(a) {
                if (!_.contains(visibleFields, a.api_name)) {
                    headers1.push({
                        field_name: a.api_name,
                        render_type: a.type,
                        noRight: true
                    })
                }
            })

            var filterColumns = headers1.length ? columns.concat(this.parseColumnsByHeadersAndFields(headers1, fields)) : columns;

            this.pluginFilterColumnsFormatter(filterColumns);
            this.addCaseCadeToFilterColumns(filterColumns);

            return filterColumns;
        },

        //追加级联单选筛选
        addCaseCadeToFilterColumns(filterColumns) {
            function fn(column, arr) {
                if(!(column.render_type === 'select_one' || column.render_type === 'select_many')) return;
                if(_.contains(arr, column)) return;
                arr.unshift(column);
                
                var parent = column.cascade_parent_api_name;
                var tt = parent && parent !== 'record_type' && _.findWhere(filterColumns, {data: parent});
                tt && fn(tt, arr);
            }

            function fo(field) {
                if (field.disable_after_filter === false) {
                    return _.filter(field.options, (item) => !item.not_usable);
                }
                return field.options;
            };
            
            var list = []
            _.each(filterColumns, column => {
                var arr = [];
                fn(column, arr);
                if(arr.length > 1) {
                    list.push(arr);
                }
            })
            list = _.filter(list, arr => { //过滤出只有三级的选项，超过的不显示。
                if(arr.length > 3) return;
                if(arr.length > 2) {
                    return !_.find(list, barr => {
                        return barr.length > 3 && _.union(barr, arr).length === barr.length;
                    })
                }
                var tt = _.find(list, barr => {
                    return barr.length > 2 && _.union(barr, arr).length === barr.length
                })
                if(!tt) return true;
            
                return !_.find(list, barr => {
                    return barr.length === 3 &&  _.find(list, carr => {
                        return carr.length > 3 && _.union(carr, barr).length === carr.length;
                    })
                })
            })

            try {
                _.each(list, arr => {
                    var options = _.map(fo(arr[0]), a => { //生成第一级
                        var child1 = [];
                        var opts1 = fo(arr[1]);
                        _.each(a.child_options && a.child_options[0] && a.child_options[0][arr[1].data], b => {
                            var tt = _.findWhere(opts1, {value: b});
                            if(tt) {
                                var tmp = {
                                    value: tt.value,
                                    label: tt.label
                                }
                                child1.push(tmp);
                                if(arr[2]) {
                                    var child2 = [];
                                    var opts2 = fo(arr[2]);
                                    _.each(tt.child_options && tt.child_options[0] && tt.child_options[0][arr[2].data], c => {
                                        var ttt = _.findWhere(opts2, {value: c});
                                        if(ttt) {
                                            child2.push({
                                                value: ttt.value,
                                                label: ttt.label
                                            })
                                        }
                                    })
                                    tmp.children = child2
                                }
                            }
                        })
                        return {
                            value: a.value,
                            label: a.label,
                            children: child1
                        }
                    })
                    filterColumns.unshift({
                        title: _.pluck(arr, ['title']).join('/'),
                        data: _.last(arr).data + '__casecadeselect',
                        dataType: 47,
                        options: options,
                        isFilter: true,
                        noSupportTerm: true,
                        noSupportShow: true
                    })
                })
            } catch(e) {}
        },

        addPackageIcon: function(columns) {
            var me = this;
            if(this.options?.isHideCPQIcon) return;
			if ((CRM._cache.cpqStatus || CRM._cache.fixedCollocationOpenStatus) && (AddPackageIconApiNameList.includes(me.options?.apiname) || (this.options?.btnInfo && this.options.btnInfo.btnId === "addHistory") || (this.options?.btnInfo && this.options.btnInfo.fieldname === "sale_contract_line_id"))) {
                columns.unshift({
					api_name: 'bomIcon',
                    data: null,
                    width: 34,
                    fixed: true,
					fixedIndex:0,
                    title: ' ',
                    render: function(data, type, full) {
                        if (full.is_package === true || full.is_package__v || (full.product_id__ro && full.product_id__ro.is_package__v) || (full.pricebook_product_id__ro && full.pricebook_product_id__ro.is_package === true)) {
                            return '<a data-id="' + full._id + '"   href="javascript:;" class="j-bao" ><span class="package-icon"></span></a>';
                        }
                    }
                })
            }
        },

        _parseRecordTypeOptions: function(options) {
            _.each(options, function(a) {
				if (a) {
					a.value = a.api_name;
				}
            })

            return options;
        },

        _parseFieldOptions: function(field, roptions) {
            var options = [];
            var isEdit = this.get('isEdit');
            _.each(field.options, function(a) {
                (a.not_usable && isEdit) || options.push(_.extend({}, a, {
                    ItemCode: a.value,
                    ItemName: a.label,
                    Children: field.type === 'multi_level_select_one' ? _.map(a.child_options, function(b) {
                        return {
                            ItemCode: b.value,
                            ItemName: b.label
                        }
                    }) : a.child_options,
                    isCustom: a.value === 'other',
                    customRequired: a.is_required
                }))
            });

            if (!isEdit) return options;

            if (field.cascade_parent_api_name === 'record_type') {
                var rp = _.findWhere(roptions, {
                    api_name: this.get('recordType')
                })
                var map = {};
                rp && rp.child_options && _.each(rp.child_options[0], function(arr, k) {
                    var temp = {};
                    _.each(arr, function(v) {
                        temp[v] = 1
                    })
                    map[k] = temp;
                })
                var mr = map[field.api_name];
                options = !mr ? [] : _.filter(options, function(b) {
                    return mr[b.ItemCode];
                })
            }

            return options;
        },


        ////////////////////////////////////////////////////////////////////////
        //
        //*****************************以下为筛选场景处理部分***********************
        //
        ////////////////////////////////////////////////////////////////////////
        parseTerm: function(templates) {
            var me = this;
            var isGrayCache = me.isGrayListHeaderCache();
            var options = _.map(templates, function(a) {
                return {
                    apiname: a.api_name || '',
                    id: a._id,
                    name: a.label,
                    isNotRequest: true, // 场景变化冲新渲染表格
                    isdef: a.is_default,
                    orders: a.orders ? a.orders[0] : null, // 默认排序
                    orderFields: a.orders,
                    isHide: !!a.is_hidden,
                    isNew: a.is_new_scene, // 是否为新增
                    key: '1',
                    _type: a.type, // 原始类型
                    type: {
                        'tenant': 3,
                        'custom': 2
                    }[a.type] || 1,
                    field_list_type: a.field_list_type,
                    record_type: a.record_type,
                    filters: a.filters,
                    field_list: a.field_list,
                    object_describe_api_name: a.object_describe_api_name,
                    fetch: isGrayCache && function(opts) {
                        if(me.__modifyedTerm) return false;

                        setTimeout(() => {
                            opts.success({
                                Result: {StatusCode: 0},
                                Value: {
                                    searchTemplate: a,
                                    success: true
                                }
                            })
                        }, 16)
                    },
                    show_tag: a.show_tag,
                    tag_auto_wrap_line: a.tag_auto_wrap_line,
                    tag_width: a.tag_width
                };
            })

            // 用于首页场景卡片跳转到页面时，根据卡片的场景来设置默认场景
            var secenId = CRM._cache.sceneCardId;
            if (secenId) {
                _.each(options, function(item) {
                    item.isdef = false;
                    if (item.id === secenId) {
                        item.isdef = true;
                    }
                });
                delete CRM._cache.sceneCardId;
            }

            var termExtendConfig = this.get('termExtendConfig');
            if (termExtendConfig) {
                // 处理默认场景
                if (termExtendConfig.default) {
                    // 参数类型统一
                    const defaultConfig = _.isObject(termExtendConfig.default) ? termExtendConfig.default : {
                        id: termExtendConfig.default,
                    };

                    if (defaultConfig.ensure) {
                        const defaultTerm = _.find(options, item => item.id === defaultConfig.id);
                        if (!defaultTerm) {
                            const baseTemplate = _.findWhere(options, {isdef: true}) || options[0];
                            baseTemplate && (options.unshift({
                                ...baseTemplate,
                                isdef: true,
                                isNew: false,
                                filters: null,
                                orders: null,
                                orderFields: null,
                                id: defaultConfig.id,
                                name: defaultConfig.name || defaultConfig.id,
                                apiname: defaultConfig.apiName || defaultConfig.id,
                            }));
                        }
                    }

                    // 设置默认场景
                    _.each(options, function(item) {
                        item.isdef = item.id === defaultConfig.id;
                    });
                }
        
				// 处理场景的保留
				if (termExtendConfig.retain) {
                    options = _.filter(options, a => _.contains(termExtendConfig.retain, a.apiname));
                }
            }

            //插入自定义的场景
			let customScenes = this.get('customScenes');
			if(customScenes && customScenes.length) {
				let allTerm = _.findWhere(options, {apiname: 'All'});//自定义场景需要依托全部场景
				allTerm && _.each(customScenes, (scene, index) => {
					if(scene.isDefault) {
						_.each(options, a => a.isdef = false);
					}
					options.push({
						...allTerm,
						filters: [],
						apiname: '',
						name: scene.label,
						id: '_customScene_' + index,
						isdef: !!scene.isDefault,
                        isNotRequest: true,
                        type: 4,
                        __id: allTerm.id,
                        __filters: scene.filters
					})
				})
			}

            let showManage = true, showCustom = true;

            // 跨对象筛选
            if (this.isCrossFiltersMode()) {
                showManage = false, showCustom = false;
                _.each(options, (o) => {
                    o.isdef = (o.apiname === 'All');
                });
            }

            return {
                type: 'term',
                pos: 'C',
                showManage,
                showCustom,
                defaultValue: me.get('curTermId') || '',
                options: options,
                addUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/createCustomScene', // 新建场景
                editUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/updateCustomScene', // 编辑场景
                saveUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/adjustCustomSceneOrder', // 保存场景顺序和隐藏
                delUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/deleteCustomScene', // 删除场景
                setUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/setDefaultScene', // 设置为默认场景
                getByIdUrl: '/EM1HNCRM/API/v1/object/custom_scene/service/findCustomSceneById', // 根据id 获取场景数据
                parseParam: _.bind(me.parseTermReqParam, me),
                parseResult: _.bind(me.parseTermReqResult, me)
            }
        },

        //
        // 根据id查找term对象
        //
        _getTermById: function(id) {
            var term = this.getTerm();
            term = term && _.findWhere(term.options || [], {
                id: id
            });
            return term;
        },

        parseTermReqParam: function(type, data) {
            var apiname = this.get('apiname');
            var obj = _.extend({
                is_default: data.isDef,
                label: data.tagName,
                object_describe_api_name: apiname,
                field_list: data.fields,
                field_list_type: data.field_list_type || 0,
                orders: data.orders,
                record_type: data.aloneValues && data.aloneValues.record_type != 'all' ? data.aloneValues.record_type : ''
            }, this.getExtendAttribute('from_term', {type: type, data: data}));
            data.id && (obj._id = data.id);

            if(type !== 'getById') {//只有修改过场景，
                this.__modifyedTerm = true;
            }

            if (type == 'add' || type === 'edit') {
                console.log(data.aloneValues);
                obj.filters = this._parseConditions(data.data);
                obj.base_scene_api_name = data.aloneValues ? data.aloneValues.filterTermRange : 'All';
                
                return {
                    scene_data: JSON.stringify(obj)
                }
            }

            if (type === 'getById' || type === 'del') {
                return {
                    describe_api_name: apiname,
                    scene_id: data.id,
                    scene_type: this._getTermById(data.id) ? this._getTermById(data.id)._type : 'custom',
                    scene_api_name: data.apiname || ''
                }
            }

            if (type === 'set') {
                obj.is_default = true;
				obj.type = this._getTermById(data.id) ? this._getTermById(data.id)._type : 'custom';
                return {
                    scene_data: JSON.stringify(obj)
                }
            }

            if (type == 'save') {
                return {
                    scene_data: JSON.stringify(_.map(data.list, function(a) {
                        return {
                            is_hidden: a.isHide,
                            _id: a.id,
                            type: a._type
                        }
                    })),
                    describe_api_name: apiname
                }
            }
        },
        parseTermReqResult: function (type, data, columns) {
            var me = this;
            var ds = data.searchTemplate;
            if (type == 'add' || type == 'edit') {
                this.set('_headerCache', null); // 清空缓存
                return {
                    id: ds._id,
                    name: ds.label,
                    apiname: ds.api_name || '',
                    isNotRequest: true,
                    record_type: ds.record_type,
                    field_list_type: ds
                };
            }
            if (type == 'getById') {
                var filters = [];
                _.each(ds.filters, function(a, index) {
                    var column = _.findWhere(columns || [], {
                        data: a.field_name.split('.')[0]
                    });
                    if (column && !column.isDisable) { // 删除和禁用的过滤掉
                        var dataType = column.returnType || column.dataType;
                        var compare = RECOMPARE[a.operator];
                        var includeVariable = a.include_variable;
                        var fieldValue = a.field_values[0];
                        var disabled = false;

                        if (compare == 22 || compare == 23 || compare == 17) {
                            fieldValue = a.field_values;
                        }

                        if(dataType == 46) {
                            fieldValue = a.field_values;
                        }

						if (dataType == 8 || dataType == 44 || dataType == 45) {
                            // 兼容老数据
                            if (/department|employee/.test(fieldValue)) { //fieldValue.indexOf('employee') != -1 || fieldValue.indexOf('department') != -1
                                fieldValue = JSON.parse(fieldValue) || {};
								if (a.value_type == 5) {
									var circle = fieldValue.department;
									fieldValue = fieldValue.employee || [];
									circle && (fieldValue.__circle = circle);
								} else {
									fieldValue = fieldValue.employee || fieldValue.department || [];
								}
                            } else if(column.data === 'relevant_team' && a.field_values) { //相关团队
                                if(a.value_type == 18) {
                                    let ff = [],__outUserId, __outerTenantId;
                                    _.each(a.field_values, aa => {
                                        if(!aa) return;
                                        aa = JSON.parse(aa);
                                        if(aa.user_id) {
                                            ff.push(aa.user_id)
                                        } else if(aa.out_user_id) {
                                            (__outUserId || (__outUserId = [])).push(aa);
                                        } else {
                                            (__outerTenantId || (__outerTenantId = [])).push(aa);
                                        }
                                    })
                                    ff.__outUserId = __outUserId;
                                    ff.__outerTenantId = __outerTenantId;
                                    fieldValue = ff;
                                } else {
                                    fieldValue = a.field_values;
                                }
                            } else {
                                fieldValue = a.field_values || [];
                            }
                            if (compare == 1) {
                                compare = 13; // 老数据 兼容 部门员工的等于 改为属于
                            }
                        }
                        if (dataType == 4) {
                            if (compare == 3 || compare == 6) {
                                fieldValue = me._getFormatTime(fieldValue, 0) + '';
                            }
                        }
                        if (dataType == 10 && column.date_format == 'yyyy-MM-dd') {
                            if (compare == 3 || compare == 6) {
                                fieldValue = me._getFormatTime(fieldValue, 1) + '';
                            }
                        }
                        // 86399999
                        if (dataType == 4 && compare == 17) { // 日期类型
                            if (a.field_values[1] - a.field_values[0] == 86399999) {
                                compare = 1;
                            }
                        }

                        if (dataType == 10 && compare == 17) { // 日期时间类型
                            if (a.field_values[1] - a.field_values[0] == 59999) {
                                compare = 1;
                            }
                        }

                        if (dataType == 4 || dataType == 10) {

                            //
                            // 前N天月 后N天月转化
                            //
                            if (a.field_values[0] === 'day') {
                                compare = {
                                    LTE: 18,
                                    GTE: 19,
                                    LTEO: 27,
                                    GTEO: 28,
                                    LT: 33,
                                    GT: 34
                                }[a.operator];
                                fieldValue = a.field_values[1];
                            }
                            if (a.field_values[0] === 'month') {
                                compare = {
                                    LTE: 20,
                                    GTE: 21,
                                    LTEO: 31,
                                    GTEO: 32
                                }[a.operator];
                                fieldValue = a.field_values[1];
                            }
                            if (a.field_values[0] === 'week') {
                                compare = {
                                    LTE: 25,
                                    GTE: 26,
                                    LTEO: 29,
                                    GTEO: 30,
                                    LT: 35,
                                    GT: 36
                                }[a.operator];
                                fieldValue = a.field_values[1];
                            }

                            //
                            // 时间段数据处理
                            //
                            if (compare == 17) {
                                if (a.field_values.length == 2) { // 正常的between
                                    fieldValue = a.field_values;
                                } else { // 时间段中的快捷操作
                                    fieldValue = a.field_values[0];
                                }
                            }
                        }

                        // 单选 多选 过滤掉删除的项
                        if (dataType == 6 || dataType == 7) {
                            if(column.fieldAreaLabels) {
                                fieldValue = compare == 13 || compare == 14 ? a.field_values : a.field_values && a.field_values[0] || '';
                            } else {
                                var values = _.pluck(column.options || [], 'ItemCode');
                                fieldValue = _.intersection(a.field_values, values);
                                if (dataType == 6) {
                                    fieldValue = compare == 13 || compare == 14 ? fieldValue : (fieldValue[0] || '')
                                }
                            }
                        }

                        // 布尔值属于不属于筛选符
                        if(dataType == 5) {
                            compare = {
                                IN: 38,
                                NIN: 39,
                                EQ: 1,
                                N: 2,
                                IS: 9,
                                ISN: 10
                            }[a.operator];
                        }
                        
                        //多维度
                        if(dataType == 42) {
                            fieldValue = a.field_values || [];
                            if(a.is_cascade) {
                                fieldValue.__includeChildrenDimension = true;
                            }
                        }

                        if(dataType == 51 && (compare == 13 || compare == 14)) {//公共对象数据范围
                            fieldValue = [JSON.parse(fieldValue)];
                        }

                        // lookup支持变量
                        if (includeVariable && dataType == 38 && compare === RECOMPARE.EQ) {
                            fieldValue = me.varService.getTextByValue(fieldValue ) || fieldValue;
                            disabled = true; // lookup包含变量不支持编辑
                        }

                        if ((fieldValue && (fieldValue.__circle || fieldValue.__outUserId || fieldValue.__outerTenantId)) || !_.isEmpty(fieldValue) || compare == 9 || compare == 10) { // 过滤掉空值
                            if (a.is_cascade) {
                                fieldValue.__includeChildrenCircle = true;
                            }

                            var sysFilter = {
                                Comparison: compare * 1,
                                FieldName: a.field_name.split('.')[0], //引用字段和master为apiname + '.name'所以需要这样处理
                                FilterDetailID: "",
                                FilterValue: fieldValue,
                                IsCascade: a.is_cascade,
                                FilterGroup: a.filterGroup || '1',
                                disabled: disabled,
                                IsSys: true,
                                ...(ds.readonlyFilters ? {
                                    disabled: true,
                                    undeletable: true,
                                    noQuickFilter: true
                                } : {}),
                            };

                            filters.push(sysFilter);
                        }
                    }
                })

                return {
                    filter: _.map(filters, function(a, index) {
                        a._id = [ds._id, index].join('_');
                        return a;
                    }),
                    name: ds.label,
                    id: ds._id,
                    orders: ds.orders || null,
                    fields: ds.field_list,
                    aloneValues: {
                        record_type: ds.record_type || 'all',
                        filterTermRange: ds.base_scene_api_name || 'All'
                    }
                }
            }
        },

        //格式化数据，统一数据格式
        parseData: function(obj) {
            var me = this;
            var list = obj.dataList;
            me.__hackData(list, obj.extendInfo, obj.dataTagInfo);
            var tempOpreate = this._parseButtonInfo(obj.buttonInfo);
            var field_types = this.get('field_types');
            _.each(list, function(item) {

                //影响性能 已在850通知业务团队此属性不再支持
                //item._origin = $.extend(true, {}, item);

                item.operate = tempOpreate[item._id];
                item.operate = me._getCustomOperate(item.operate, item);
                _.each(item, function(v, k) {
                    var rtype = field_types[k];
                    switch (rtype) {
                        // case 'select_many': {
                        // 	v && v.join && (item[k] = v.join('|'));
                        // 	break;
                        // }
                        case 'master_detail':
                        case 'object_reference':
                            {
                                item[k + '__id'] = item[k]; //兼容别的地方取id
                                break;
                            }
                        case 'date':
                        case 'date_time':
                            {
								v && _.isNaN(+v) && v != CRM.config.TEXT_HIDE && (v = (new Date(v)).getTime());
                                item[k] = (v == -1 ? 0 : v);
                                break;
                            }
                    }
                    item[k] = item[v + '__s'] || item[k]; // 没权限字段
                })
            })

            //列表页布局新增功能 计算按钮外露数量 并设置操作行宽度
            this._fixOperateOut(list);

            return {
                totalCount: obj.total,
                data: list
            }
        },

        isNewLayout() {
            return this.get('isListLayout');
        },

        _fixOperateOut: function(list) {
            if (!this.isNewLayout() && !this.get('supportListLayout')) return; //不是用的最新列表页布局不处理

            var btn_max_num = 0;
            var num = this.get('listSingleExposed') || 0; //单条按钮外露数量
            _.each(list, function(item) {
                //计算按钮最大数量
                var op = item.operate;
                if(op && op.length) {
                    num && _.each(op, function(a, index) {
                        if(num > index) {
                            a.not_fold = true;
                        }
                    })
                    btn_max_num < op.length && (btn_max_num = op.length);
                }

                item.__newLayout = true;
            })

            this.countOperateColumnWidth(btn_max_num > num ? num : btn_max_num, btn_max_num > num);
        },

        //格式化自定义对象操作列
        _parseButtonInfo: function(buttonInfo) {

            if (!this.get('showOperate') || !buttonInfo) return {};

            var tempButtons = {};
            _.each(buttonInfo.buttons, function(action) {
                tempButtons[action.api_name] = action;
            })
            var tempOpreate = {};
            _.each(buttonInfo.buttonMap, function(list, id) {
                var arr = [];
                _.each(list, function(apiname) {
					tempButtons[apiname] && arr.push(_.extend({}, tempButtons[apiname]));
                })
                tempOpreate[id] = arr;
            })
            return tempOpreate;
        },

        _getCustomOperate: function(operate, item) {
            operate = operate || [];

            var preSetButtonInfo = this.get('preSetButtonInfo');

            _.each(this.get('preSetButton'), function(apiname) {
                preSetButtonInfo[apiname] && operate.push(preSetButtonInfo[apiname]);
            });

            operate = this.getCustomOperate(operate, item);

            operate = this.pluginOperateButtonsFormatter(operate, item);

            return operate;
        },

        getCustomOperate: function(operate, item) {
            return operate;
        },

        parseBatchButtons: function(btns) {//格式化批量按钮
            var opts = this.options;
            
            if (!opts) return [];

            var fb;
            if (opts.apiname === 'LeadsObj') { //历史遗留逻辑，线索需要过滤分配 收回 领取
                fb = ['AsyncBulkAllocate', 'AsyncBulkTakeBack', 'AsyncBulkChoose'];
            } else if(opts.apiname === 'AccountObj') { //历史遗留逻辑，客户需要过滤分配 转移 收回 领取
                fb = ['AsyncBulkAllocate', 'AsyncBulkTakeBack', 'AsyncBulkChoose'];
                CRM.util.isConnectApp() && fb.push('AsyncBulkMove');
            }
            fb && _.each(fb, function(a) {
                var t = _.findWhere(btns, {action: a})
                t && (t.isHidden = true);
            })

			var emptyAction = [];
			var buttons = _.map(btns, function (a) {
				if (!a.action) {
					emptyAction.push(a.label);
				}
                return _.extend({}, a, {
                    text: a.label,
                    async_button_apiname: a.action,
                    action: 'list_batchbtn_operate',
                    button_api_name: a.api_name,
                    button_label: a.label,
                    button_action: a.action,
                    isCommon: true
                })
            })

			if (emptyAction.length) {
				try {
					CRM.util.uploadLog('table', 'list', {
						eventId: 'batchbuttonnoaction',
						eventData: {label: emptyAction.join(';'), apiname: opts.apiname}
					});
				} catch (e) {
				}
			}

			return buttons;
        },

        //格式化相关团队的数据，将数据拼接成字符串 (6.5废弃)
        _parseRelevant_team: NOOP,

        _parseConditions: function(conditions) {
            var me = this;
            var isCrossFiltersMode = this.isCrossFiltersMode();
            var field_types = this.get('field_types') || {};
            var fields = this.get('fields') || {};
            var otherFilters = [];
            return _.map(conditions, function (a) {
                var metadata = a.metadata;
                var fieldname = a.FieldName;
                var comparison = a.Comparison;
                var tt = field_types[fieldname];
                var field = isCrossFiltersMode ? metadata?.field || {} : fields[fieldname];
                var field_values = _.isArray(a.FilterValue) ? a.FilterValue : [a.FilterValue];
                
                var type = {};

                // 处理跨对象筛选的字段类型
                // metadata 包含跨对象筛选的元数据信息
                if (isCrossFiltersMode && metadata) {
                    // realType 获取真实的字段类型
                    tt = field?.__originType;

                    // objectName
                    if (metadata.objectBiApiName) {
                        type.objName = metadata.objectBiApiName;
                    }

                    if (!_.isUndefined(metadata.objectBiSequence)) {
                        type.objectSequence = metadata.objectBiSequence;
                    }
                }

                if (field) {
                    tt = field.quote_field_type || field.return_type || field.returnType || tt;
                }

				if (_.contains(['employee', 'employee_many', 'department', 'department_many', 'owner_department'], tt)) {
                    if(_.contains(field_values, 'currentLoginUserId__g')) { // 之后走varService.assertContainsVariable判断？
                        type.include_variable = true;
                    }
                    if (field_values && field_values.__includeChildrenCircle) {
                        type.is_cascade = true;
                    }

					if (tt == 'employee' || tt === 'employee_many') {
                        var o = {};
						o.employee = _.map(a.FilterValue || [], function (item) {
                            return item + ''
                        });
                        if(a.FilterValue && a.FilterValue.__circle) {
                            o.department = _.map(a.FilterValue.__circle, function(item) {
                                return item + ''
                            })
                        }
                        type.value_type = 5;
                        field_values = [JSON.stringify(o)];
                    } else {
                        type.value_type = 0;
                        field_values = a.FilterValue || [];
                    }

                    if ((comparison == 9 || comparison == 10) && tt == 'department') {
                        field_values = ['']
                    }
                } else if (tt === 'date' && comparison == 1) {
                    if(field && {'yyyy-QQQ': 1, 'yyyy-MM': 1, 'yyyy': 1}[field.date_format]) {
                        comparison = 24;
                    } else {//日期的等于按between来操作
                        field_values = [field_values[0] * 1, field_values[0] * 1 + 86399999];
                        comparison = 17;
                    }
                } else if (tt === 'date_time' && comparison == 1) {
                    //日期时间的等于按between来操作
                    field_values = [field_values[0] * 1, field_values[0] * 1 + 59999];
                    comparison = 17;
                }else if (tt === 'date' && (comparison == 3 || comparison == 6)) {
                    //日期字段早于等于/晚于传末尾时间戳
                    let value = me._getFormatTime(field_values[0], 1)
                    field_values = [value * 1 + 86399999];
                    // field_values = [field_values[0] * 1 + 86399999];
                } else if (tt === 'date_time' && (comparison == 3 || comparison == 6)) {
                    //日期时间字段早于等于/晚于传末尾时间戳
                    // let field_values_second = field_values[0].split('').slice(8).join('');
                    let value = me._getFormatTime(field_values[0], 0)
                    field_values = [value * 1 + 59999];
                    // field_values = [field_values[0] * 1 + 59999];
                } else if (comparison == 18 || comparison == 19 || comparison == 27 || comparison == 28 || comparison == 33 || comparison == 34) { // 前N天 N天前
                    type.value_type = 3;
                    field_values = ['day', a.FilterValue];
                } else if (comparison == 20 || comparison == 21 || comparison == 31 || comparison == 32) { // 前N月 后N月
                    type.value_type = 3;
                    field_values = ['month', a.FilterValue];
                } else if (comparison == 25 || comparison == 26 || comparison == 29 || comparison == 30 || comparison == 35 || comparison == 36) { // 前N周 N周前
                    type.value_type = 3;
                    field_values = ['week', a.FilterValue];
                } else if (comparison == 17 && !_.isArray(a.FilterValue)) { // 时间段中的快捷筛选
                    me.__hackValidateBetween(a.FilterValue);
                    type.value_type = 3;
                    field_values = [a.FilterValue];
                } else if (tt === 'object_reference' || tt === 'master_detail' || tt === 'object_reference_many') {
                    fieldname += me.__getObjectFilterAttr(a.FieldName, a);
                    if (comparison == RECOMPARE.EQ && tt !== 'object_reference_many') {
                        field_values = field_values.map((value) => {
                            const variable = me.varService.getValueByText(value);
                            if (variable) {
                                type.include_variable = true;
                            }
                            return variable || value;
                        });

                        if (type.include_variable) {
                            fieldname = fieldname.split('.')[0];
                        }
                    }
                } else if (tt === 'relevant_team' || fieldname === 'relevant_team') {
                    fieldname += '.teamMemberEmployee';
                    field_values = a.FilterValue || [];
                    if(field_values.__outUserId || field_values.__outerTenantId) { //增加外部人员 或 外部企业 如果选了的话
                        field_values = _.union(_.map(field_values, aa => (JSON.stringify({user_id: aa}))), _.map(field_values.__outUserId || [], a => JSON.stringify(a)), _.map(field_values.__outerTenantId || [], a => JSON.stringify(a)));
                        type.value_type = 18;
                    }
                } else if (tt === 'dimension' && (field_values && field_values.length && field_values.__includeChildrenDimension)) {
                    type.is_cascade = true;
                } else if(fieldname === 'tag') {
                    type.value_type = 11;
                } else if(/__casecadeselect/.test(fieldname)) { //级联单选 按最后一级筛选
                    field_values = _.union(_.map(a.FilterValue, arr => _.last(arr)));
                    fieldname = fieldname.replace('__casecadeselect', '')
                } else if(tt === 'data_visibility_range' && (comparison == 13 || comparison == 14)) {
                    type.value_type = 23;
                    field_values = [JSON.stringify(field_values[0])];
                }

                if(field_values && field_values.length && field_values.__objectApiName) {
                    var tt = _.findWhere(me.getAllColumns(), {data: fieldname});
                    if(tt) {
                        fieldname += '.name'
                        otherFilters.push({
                            field_name: tt.what_object_apiname,
                            field_values: [field_values.__objectApiName],
                            operator: 'EQ',
                            filterGroup: a.FilterGroup
                        })
                    }
                }

                return _.extend({
                    field_name: fieldname,
                    field_values: field_values,
                    operator: COMPARE[comparison],
                    filterGroup: a.FilterGroup,
                    what_list_query_field: fieldname === 'related_object_data' ? 'name' : void 0//业务筛选特殊逻辑
                }, type)
            }).concat(otherFilters)
        },

        _getFormatTime: function(time, type = 0){
			var date = new Date(time * 1); // 初始化日期
			var year = date.getFullYear(); //获取年份
			var month = date.getMonth() + 1; // 获取月份
			var day = date.getDate(); // 获取具体日
			var hour = date.getHours(); // 获取时
			var minutes = date.getMinutes(); // 获取分
			let newDate = type == 0 ? year + '/' + month + '/' + day + ' ' + hour + ':' + minutes + ':' + '00' :
			year + '/' + month + '/' + day + ' ' + '00' + ':' + '00' + ':' + '00'
			return Date.parse(newDate);
		},

        __getObjectFilterAttr: function(fieldName, condition) {
            if (condition && condition.__fromPlugin) {
                return condition.extName || '';
            }

            let ext = this.get('objectDescribeExt');
            let field = ext && ext.fields && ext.fields[fieldName];
            if(!field) {
                field = this.getColumnByField && this.getColumnByField(fieldName);
            }
            if(field && field.is_support_tree_view) return '';

            return field && field.is_open_display_name ? '.display_name' : '.name';
        },

        //配合serializeEmpty extractExtendInfo 改造 需要在数据层补齐__r __l
        __hackData: function(list, extendInfo, tagInfo) {
            tagInfo && _.each(tagInfo, a => {
                let aa = _.findWhere(list, {_id: a.dataId});
                if(aa) {
                    aa.crm_tag__c = a.tags;
                }
            })

            if(!extendInfo) return;
            var fields = this.get('fields');
            var employeeDatas = extendInfo.employee;
            var departmentDatas = extendInfo.department;
            var dataVisibilityRange = extendInfo.data_visibility_range;
            fields && _.each(list, function(item) {
                _.each(item, function(v, k) {
                    var tt = fields[k];
                    if(!tt) return;
                    var ee = [];
                    if(tt.type === 'employee' || tt.type === 'employee_many' || tt.type === 'out_employee') {
                        employeeDatas && _.each(v, function(id) {
                            var tmp = _.findWhere(employeeDatas, {id: id});
                            tmp && ee.push(_.extend({}, tmp));
                        })
                        item[k + '__r'] = item[k + '__l'] = ee;
                    } else if(tt.type === 'department' || tt.type === 'department_many' || tt.type === 'out_department') {
                        departmentDatas && _.each(v, function(id) {
                            var tmp = _.findWhere(departmentDatas, {deptId: id});
                            tmp && ee.push(_.extend({}, tmp));
                        })
                        item[k + '__r'] = item[k + '__l'] = ee;
                    } else if(tt.type === 'data_visibility_range') {
                        _.each(v, id => {
                            var tmp = _.findWhere(dataVisibilityRange, {id: id});
                            tmp && ee.push(_.extend({}, tmp));
                        })
                        item[k + '__l'] = ee;
                    }
                })
            })
        },

        parseParam: function(obj) {
            var cfv = this.getPsrp('customFilterValue');
            var fts = this.get('field_types') || {};
            var param = {
                object_describe_api_name: this.get('apiname'),
                search_template_id: obj.term,
                include_describe: false,
                include_layout: false,
                need_tag: true
            };

            var queryInfo = {
                limit: obj.pageSize * 1, //转换为数字
                offset: (obj.pageNumber - 1) * obj.pageSize,
                filters: []
            };

            var sortFields = obj.sortFields || (obj.SortField && [{
                name: obj.SortField,
                type: obj.SortType,
            }]);
            if (sortFields) {
                queryInfo.orders = sortFields.map((sf) => {
                    return {
                        //主从 lookup类型的字段需要按名称排序，需要追加.name
                        fieldName: _.contains(['master_detail', 'object_reference', 'object_reference_many'], fts[sf.name])
                            ? sf.name + this.__getObjectFilterAttr(sf.name)
                            : sf.name,
                        isAsc: !(sf.type == 2)
                    }
                });
            }

            // if (obj.SortField) {
            //     var ft = fts[obj.SortField];
            //     queryInfo.orders = [{
            //         //主从 lookup类型的字段需要按名称排序，需要追加.name
            //         fieldName: ft === 'master_detail' || ft === 'object_reference' || ft === 'object_reference_many' ? obj.SortField + this.__getObjectFilterAttr(obj.SortField) : obj.SortField,
            //         isAsc: !(obj.SortType == 2)
            //     }]
            // }


            // conditions
            var conditions;
            if (obj.QueryInfo && obj.QueryInfo.Conditions) {
                conditions = [...obj.QueryInfo.Conditions];
            }
            if (cfv && cfv.conditions) {
                conditions = (conditions || []).concat(
                    _.map(cfv.conditions, (condition) => _.extend({__fromPlugin: true}, condition))
                );
            }
            if (conditions) {
                queryInfo.filters = this._parseConditions(conditions);
            }

			var term = this.getTerm() || {};
            term = _.findWhere(term.options || [], {
                id: obj.term
            });

            if (term) {
                param.search_template_type = term._type;
            }

            if (term && term.type && term.type != 1) { // 自定义对象忽略条件
                param.ignore_scene_filter = obj._isfilter;
            }

            if (obj.Keyword) {
                var searchFiled = this.get('searchFiled') || {};
                var compare = 7;

                if (FS.util.getUserAttribute('wanmaadd')) { // 万马特殊需求 搜索时传递开始于检索
					if (_.indexOf(['object_Nf6iC__c'], this.get('apiname')) != -1 && searchFiled.api_name == 'name') {
                        compare = 11;
                    }
                }

                compare = this.__hackEncrypted(searchFiled.api_name, compare);

                var searchCond = this._parseConditions([{
                    FieldName:   searchFiled.api_name,
                    FilterValue: obj.Keyword,
                    Comparison:  compare
                }]);
                queryInfo.filters.push(searchCond[0]);
            }

            var recordType = this.get('recordType');
            param.ignore_scene_record_type = !!recordType; // 存在就忽略场景中的条件
            if (recordType && recordType !== 'all') {
                queryInfo.filters.push({
                    field_name: 'record_type',
                    field_values: [recordType],
                    operator: 'EQ'
                })
            }

            if(term && term.__id && term.type == 4) {
                param.search_template_id = term.__id,
                [].push.apply(queryInfo.filters, term.__filters || []);
            }

            param.search_query_info = JSON.stringify(this.__hackValidateParam(queryInfo));

            obj = _.omit(obj, '_aloneValuesKey', '_isfilter', 'SortField', 'QueryInfo', 'SortType', 'sortFields', 'term', 'Keyword', 'pageNumber', 'pageSize', 'accurateQuery');

            // 扩展参数
            _.extend(param, obj);
            param.tag_operator && (param.tag_operator = COMPARE[param.tag_operator]);

                
            // 插件hook
            var formatRequestParam = this.getPsrp('formatRequestParam');
            if (formatRequestParam) {
                _.extend(param, formatRequestParam(param) || {});
            }

            // 跨对象筛选所需要的特殊参数
            if (this.isCrossFiltersMode()) {
                param.search_template_id = '';
                param.search_template_type = 'default';
                const crossFiltersExtraData = this.table?.getFiltersExtraData?.();
                if (crossFiltersExtraData?.candidates) {
                    param.businessObjects = crossFiltersExtraData.candidates;
                }
            }

            this.set('queryParam', param);
            this.options.parseParam_after && this.options.parseParam_after(param);

            return param;
        },

        //加密字段特殊处理操作符
        __hackEncrypted(fieldName, compare) {
            var field = (this.get('fields') || {})[fieldName];
            if(!field || !field.is_encrypted) return compare;
            return _.contains([1, 2, 9, 10], compare) ? compare : 1;
        },

        //用于验证查询参数问题，过滤掉有问题的筛选条件
        __hackValidateParam(queryInfo) {
            var isNull = (v) => {
                return v === '' || v === null || v === void 0;
            }

            var fts = this.get('field_types') || {};

            queryInfo.filters = _.filter(queryInfo.filters, b => {
                var flag;
                var fv = b.field_values || [];
                if(b.operator === 'EQ' || b.operate === 'N') {//等于不等于传了空值
                    flag = isNull(fv[0]);
                } else if(_.contains(['date', 'date_time'], fts[b.field_name]) && b.operator === 'BETWEEN') {
                    if(fv.length > 1) {
                        flag = isNull(fv[0]) || isNull(fv[1]);
                    } else {
                        flag = isNull(fv[0]) || _.isNaN(+fv[0]);
                    }
                }

                flag && util.uploadLog('table', 'list', {eventId: 'filtererror', eventData: {filter: JSON.stringify(b), apiname: this.get('apiname')}});
                
                return !flag;
            })

            return queryInfo;
        },

        //用户验证between参数错误问题
        __hackValidateBetween(fv) {
            try {
                fv && fv > 1000 && util.uploadLog('table', 'list', {eventId: 'filterbetweenerror', eventData: {fv: fv, listType: this.get('listType'), apiname: this.get('apiname')}});
            } catch(e) {}
        },

        _applyDisabledFeatureControlsToOptions(options) {
            const disabledFeatureControls = this.get('disabledFeatureControls');

            if (!disabledFeatureControls) {
                return;
            }

            new Map([
                ['term', () => options.hideTermSoftly = true],
                ['allowTerm', () => options.searchTerm = null],
                ['filter', () => options.showFilerBtn = false], 
                ['search', () => options.search = null],
                ['multiSort', () => options.multiFieldSort = false],
                ['quickFilter', () => options.showOutFilter = false],
                ['button', () => options.operate = null],
                ['batchButtons', () => options.batchBtns = []],
                ['multiple', () => options.showMultiple = false],
                ['termBatch', () => options.showTermBatch = false],
                ['pagination', () => this.isNewLayout() || (options.showPage = false)],
                ['refresh', () => options.refreshCallBack = null],
                ['setting', () => options.hideIconSet = true],
                ['summary', () => options.showSummary = false],
                ['tag', () => options.showTagBtn = false],
            ]).forEach((action, feature) => {
                if (disabledFeatureControls[feature] === true) {
                    action();
                }
            });
        },

        clearRemberData: function() {
            if (_.contains(['card'], this.get('viewType')) && this._fter) {
                this._fter.cleanCheckedData();
                return;
            }
            this.table && this.table.clearRemberData();
        },

        getRemberData: function() {
            if (_.contains(['card'], this.get('viewType')) && this._fter) {
                return this._fter.getCheckedData();
            }
            return this.table && this.table.getRemberData();
        },

        getRemberIds: function() {
            return _.pluck(this.getRemberData(), '_id');
        },

        getCheckedData: function() {
            if (_.contains(['card'], this.get('viewType')) && this._fter) {
                return this._fter.getCheckedData();
            }
            return this.table && this.table.getCheckedData();
        },

        getCheckedIds: function() {
            return _.pluck(this.getCheckedData(), '_id');
        },

        getTitle: function() {
            return _.escape(this.get('displayName'));
        },

        getMainTitle: function(apiname) {
            var obj = _.findWhere(this.get('columns'), {
                data: apiname || 'name'
            });
            return obj ? obj.title : '';
        },

        shouldShowCrossFilters() {
            return true;
        },

        getOptions: function() {
            var me = this;
            var apiName = me.get('apiname');
            var columns = me.getColumns();
            var addColumns = me.getAddColumns();
            var filterColumns = me.get('filterColumns');
            var filterAloneColumns = me._getFilterAloneColumns();
            if(!me.options) return {};

			return _.extend({
                $el: me.$el,
                url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/List',
                doStatic: me.get('doStatic'),
                requestType: 'FHHApi',
				reqHeaders: me.options.reqHeaders || null,
                showSize: true,
                showMultiple: true,
                showFilerBtn: true,
                showTerm: true,
                showManage: true,
                showPage: me.options.showPage,
				showMoreBtn: true,
				searchTerm: me.getTerm(),
				search: me.getSearch(),
				operate: me.getOperate(),
				title: me.get('showTitle') && me.getTitle(),
                creationSource: me.get('creationSource'),
				isMyObject: true,
                postData: {},
                orderColumn: me.get('orderColumn'),
                orderColumns: me.get('orderColumns'),
                addOrderColumn: me.get('addOrderColumn'),
                columns: columns,
                drawPageAfter: _.bind(me.renderBuiltInViewIfNeeded, me),
                filterAloneColumns: filterAloneColumns,
                filterColumns: filterColumns,
                filterDatas: me._filterDatas ? _.clone(me._filterDatas) : null,
                showTagBtn: me.get('listType') === 'page' && me.get('supportTag'),
                checked: me.options.checked, // 跨页多选

                addColumns: addColumns,

                disabledcfg: me.options.disabledcfg,

                showDynamicAttribute:false,  //是否可动态显示属性属性值信息

				viewInfo: this.get('viewInfo'), // 列表视图信息

				describe: me.get('objectDescribe') ? me.get('objectDescribe').fields : {},

                beforeTermAdvance: function(fiters, aloneValues) { // 筛选之前的验证
                    var term = me.table.getCurTerm();
                    var recordType = aloneValues ? aloneValues['record_type'] : 'all';
                    var curTy = me.get('recordType') || recordType;
                    if (!me.get('layout_by_template') || (term && term.field_list_type && term.field_list_type == 1 && recordType != curTy)) {
                        me._filterDatas = {
                            aloneValues: aloneValues,
                            filters: fiters
                        }
                        me.set('recordType', recordType);
                        me.set('recordType_apiName', recordType);
                        me.renderTable(recordType);
                        return false;
                    }
                    me.set('recordType', recordType);
                    me._renderTypeSelect && me._renderTypeSelect.setValue(recordType);

                    me._updateColumnsTitle(recordType);
                    return true;
                },

                formatData: _.bind(me.parseData, me),
                paramFormat: function() {
                    let q1 = me.parseParam.apply(me, arguments);

                    //历史遗留特殊逻辑
                    //因为详情页下的关联对象某些时候会把场景强制特殊置为空，但是导出的时候又会用到场景id, 所以此种情况不能再次set
                    //选对象数据列表，订单 产品明细会修改查询条件，并且会把场景id置为空，如果按照场景id为空，就不设置queryParam,会导致精确查询拿不到最新条件
                    //基于上面的原因，特殊判断如果支持精确查询就重新设置
                    if(q1 !== me.get('queryParam') && me.showAboutNum()) {
                        me.set('queryParam', q1);
                    }


                    return _.extend({serializeEmpty: false, extractExtendInfo: true}, q1);
                },
                initComplete: _.bind(me._initComplete, me),
                getDataBack: _.bind(me._getDataBack, me),
                objectApiname: apiName,
                filterOr: me.get('supportOrFilter'),//!!util.getUserAttribute('objectSceneFiltersOr') && /__c$/.test(apiName),

                quickFilterField: me.get('quickFilterField'),
                listSingleExposed: me.get('listSingleExposed'),
                sceneRenderType: me.get('sceneRenderType'),
                searchDefaultKeyword: me.preSearchKeyword,

                queryFirstData: me.isGrayListHeaderCache() ? (param) => me.__queryList(param) : void 0,

                supportDefaultSort: me.get('supportDefaultSort'),
                supportMultiFieldSort: me.get('isFilter') && me.get('multiFieldSort'),

                crossFilterEndpoint: me.getCrossFilterEndpoint(),
                
                aloneValueChangeCallBack: function(value, preValue) {
                    let arias = me.__fieldMapping && me.__fieldMapping[value];
                    let titles = {};
                    let fields = me.get('fields') || {};
                    let preArias = me.__fieldMapping && me.__fieldMapping[preValue];
                    _.each(preArias && preArias.fields, (a, fieldName) => {//还原回去
                        titles[fieldName] = fields[fieldName] && fields[fieldName].label;
                    })
                    _.each(arias && arias.fields, (a, fieldName) => {
                        titles[fieldName] = a.label;
                    })
                    return new Promise(resolve => {
                        resolve({
                            type: 'updateColumnTitle',
                            data: titles
                        })
                    })
                },
            
                errorAlertModel: 1
			}, me.options.tableOptions)
        },

        __hackOptions: function(options) {
            // 大对象屏蔽搜索，因为搜索是按模糊搜索的，大对象暂不支持模糊搜索，故屏蔽
            if (this.get('isBigObject')) {
                options.search = null;
            }

            if (this.isCrossFiltersMode()) {
                Object.assign(options, {
                    title: $t('paas.crm.objecttable.cross_filters_title', {name: options.title}, `高级筛选(${options.title})`),
                    crossFilterService: this.getCrossFilterService(),
                    loadingService: this.getCrossFilterLoadingService(),
                });
            }
        },

		// 获取新建时的列
        //
        getAddColumns: function() {
            return this.get('filterColumns');
        },

        //
        // 获取单独筛选的列
        //
        _getFilterAloneColumns: function() {
            var me = this,
                columns = [];
            var rtColumn = me.getRecordTypeColumn();
            var baseScenes = me.get('baseScenes');
            if (rtColumn) {
                columns.push(rtColumn);
            }
            if (baseScenes) { // 数据范围列
                columns.push({
                    data: 'filterTermRange',
                    dataType: 6,
                    title: $t('基础数据范围'),
                    options: _.map(baseScenes, function(item) {
                        return {
                            name: _.escape(item.label),
                            value: item.api_name
                        }
                    }),
                    defaultVal: 'All',
                    disabled: true
                })
            }
            return columns.length > 0 ? columns : null;
        },

        //
        // 换取业务类型列
        //
        getRecordTypeColumn: function() {
            var me = this;
            var ds = me.get('objectDescribe');
            if (!ds || !ds.fields) return;
            var options = ds.fields.record_type && ds.fields.record_type.options;
            if (!options) return;
            var list = me.get('thirdapp_record_type') ? [] : [{
                name: $t('全部业务类型'),
                value: 'all'
            }]
            _.each(options, function(a) {
                list.push({
                    name: a.label,
                    value: a.api_name
                })
            });
            return {
                data: 'record_type',
                title: ds.fields.record_type && ds.fields.record_type.label || $t('业务类型'),
                dataType: 6,
                options: list
            }
        },

        getTerm: function() {
            return _.clone(this.get('term'));
		},

		getSearch: function () {
			return _.clone(this.get('search'));
		},

		getOperate() {
			return _.clone(this.get('operate'));
		},

		getColumns: function() {
      
            var columns = _.clone(this.get('columns')) || [];
            _.each(this.get('moreCustomColumns') || [], function(item) { // 拼入更多的自定义列
                if (item) {
                    item.isSysDefined = true;
                    columns.push(item);
                }
            });

            // 所属公海不需要链接
            var highSeaPool = _.findWhere(columns, {data: 'high_seas_id'});
            if (highSeaPool) {
                highSeaPool.showLookupText = true;
            }

            return columns;
        },

        getCrossFilterService: function() {
            return this._crossFilterService || (this._crossFilterService = this.createCrossFilterService());
        },

        createCrossFilterService: function() {
            const isEdit = this.get('isEdit');
            const canFilter = this.get('isFilter');
            return this.crossFilterService.create({
                context: {
                    name: this.getTitle(),
                    apiName: this.get('apiname'),
                    getCandidates: () => {
                        return this.table?.getFiltersExtraData?.()?.candidates;
                    }
                },
                formatter: {
                    adjustFields: (fields) => {
                        const fieldTypes = BaseTable.helper.getFieldTypes();
                        return fields.map((field) => {
                            const dataType = field.returnType || field.dataType;

                            // 过滤掉跨对象筛选中不支持的筛选符号
                            // https://wiki.firstshare.cn/pages/viewpage.action?pageId=228611448
                            field.onlyOr = BaseTable.helper.getCrossFilterCompare(dataType);

                            // 部门、部分多选字段屏蔽掉包含子部门
                            if (
                                (dataType === fieldTypes.department || dataType === fieldTypes.department_many) && 
                                field.referRule !== 'Employee'
                            ) {
                                field.groupIncludeChildrenStatus = 0;
                            }
                            
                            return field;
                        });
                    },
                    formatFields: (apiName, fields) => {
                        return _.map(fields, (field) => {
                            const column = this.formatColumnField(
                                apiName,
                                field,
                                null,
                                {
                                    fields,
                                    isEdit,
                                    roptions: [],
                                    lockColumn: null,
                                    isFilter: canFilter,
                                }
                            );

                            this.table?.formatSimpleColumn(column);

                            return column;
                        });
                    }        
                }
            });
        },

        getCrossFilterLoadingService: function() {
            return this._crossFilterLoadingService || (this._crossFilterLoadingService = this.createCrossFilterLoadingService());
        },

        createCrossFilterLoadingService: function() {
            const me = this;
            const service = {
                _loading: null,

                _track: (name) => {
                    window.Fx?.log?.(name, 'cl', {apiName: me.get('apiname')});
                },

                show() {
                    service.close();
                    service._track('s-paasobj-cross_filter_search');
                    service._loading = FxUI.create({
                        wrapper: document.body,
                        template: `
                            <div class="objecttable-cross-filters-loading">
                                <span class="el-icon-loading"></span>
                                <span class="content">
                                    {{$t('paas.crm.objecttable.cross_filters_loading', '执行时间较长，请耐心等待')}}
                                </span>
                                <span
                                    class="fx-btn fx-btn-link fx-btn-sm"
                                    @click="cancelRequest"
                                >{{$t('paas.crm.objecttable.cross_filters_cancel', '取消查询')}}</span>
                            </div>
                        `,
                        methods: {
                            cancelRequest() {
                                service.close();
                                me.table?.abort();
                                service._track('s-paasobj-cross_filter_cancel_search');
                            }
                        }
                    });
                },

                close() {
                    if (!service._loading) return;
                    service._loading.destroy();
                    service._loading = null;
                },
            };

            return service;
        },

        getCrossFilterEndpoint: function() {
            if (
                !this.get('crossFiltersEnabled') ||
                this.isCrossFiltersMode()
            ) {
                return null;
            }

            const app = CRM.util.getConnectAppInfo();
            const urlParams = Object.assign({
                mode: 'crossFilters',
                apiname: this.get('apiname')
            }, app ? {
                fsAppId: app.appId,
                upstreamEa: app.upstreamEa
            } : {});

            return Fx.util.createUrlWithTemplate?.('objlist', urlParams);
        },

        _initComplete: function() {
            var me = this;
            setTimeout(function() {
                me.renderType();
                me._filterDatas = null;
                me.initComplete();
            }, 0)
        },

        //
        // 渲染业务类型
        //
		renderType: function () {
			var me = this;
            var ds = me.get('objectDescribe');
			if (this.options && this.options.isRenderRecordType === false) return;

            if (!ds || !ds.fields) return;

            //已经有record-type时不再渲染了
            if (me.$('.batch-term .record-type').length) return;

            var options = ds.fields.record_type && ds.fields.record_type.options;
            if (!options) return;

            var cls = '';
            if(options.length === 1) { //只有一个业务类型隐藏处理
                cls = ' crm-table-rt-hidden';
            }

            var list = [{
                name: $t("全部业务类型"),
                value: 'all'
            }]
            _.each(options, function(a) {
                list.push({
                    name: a.label,
                    value: a.api_name
                })
            })
            var defaultValue = me.get('curTermId') ? me.get('recordType') : (me.table.getCurTerm() ? me.table.getCurTerm().record_type : '');
            var curOption = _.findWhere(options, {
                value: defaultValue
            });
            defaultValue = curOption ? defaultValue : 'all';
            defaultValue = defaultValue || 'all';

            if (me.get('thirdapp_record_type')) {
                list.shift();
                defaultValue = me.get('thirdapp_record_type')
                me.set('recordType', defaultValue);
            }

            var cc = me.getRecordTypeOptions ? me.getRecordTypeOptions() : {};
            cc.className || (cc.className = 'record-type');
            cc.className += cls;

            var select = me.table.addSelect(_.extend({
                pos: 'before',
                label: ds.fields.record_type && ds.fields.record_type.label || $t("业务类型"),
                showLine: true,
                zIndex: 1010,
                defaultValue: defaultValue,
                options: list
            }, cc));

            select.on('change', function(v, d) {
                var type = d.value;
                var term = me.table.getCurTerm();
                me.set('curTermId', term ? term.id : '');
                me.renderTypeSelectChange && me.renderTypeSelectChange();
                if ((term && term.field_list_type == 1) || !me.get('layout_by_template')) { // 场景根据布局（业务类型变化）
                    me.set('recordType', type);
                    me.set('recordType_apiName', type);
                    me.renderTable(type == 'all' ? '' : type);
                } else {
                    me.set('recordType', type);
                    me.table.setParam({}, true, true);
                    //hack更新业务类型 暂没找到更好方案
                    me.table._termBatch && me.table._termBatch.updateRecordType(type);
                    me.recordTypeChangeChangeHandle(type);
                }

                me._updateColumnsTitle(type);
            });

            me._renderTypeSelect = select;

            me.table._termBatch && this.table._termBatch.updateRecordType && this.table._termBatch.updateRecordType(defaultValue);
        },

        //
        // 筛选显示
        _filterHandle: function() {
            var me = this;
            if (me._renderTypeSelect) {
                me.table.setFilterAloneColumnsValues({
                    'record_type': me._renderTypeSelect.getValue() || 'all'
                });
            }
        },

        doStaticData: function(list, keepScroll) {
            if (this.table) {
                this._clearDelay('doStaticData');
                this.table.doStaticData(list, keepScroll);
            }
            else {
                this._addDelay({
                    name: 'doStaticData',
                    args: arguments
                });
            }
        },

        resize: function() {
            this.table && this.table.resize();
        },

        getCurData: function() {
            if (!this.table) {
                return {
                    data: []
                };
            }
            return this.table.curData;
        },

        getTableAllData: function() {
            if (this.table && this.table.curData) {
				return this.table.curData.data || []
            }
            return []
        },

        /**
         * @desc 把table中的事件统一提取一次出来
         */
        copyDtEvents: function(table) {
            var me = this;
			var events = ['trclick', 'term.change', 'otherbtn.change', 'dt.search', 'checkbox.click', 'term.advance', 'del.filter', 'resize', 'tb.scroll', 'showdetail', 'tb.resetcolumns', 'dt.page', 'dt.pagesize', 'renderListComplete', 'term.bactchHide', 'window.resize', 'height.change'];
            _.each(events, function(item) {
                table.on(item, function() {
                    var args = Array.prototype.slice.call(arguments);
                    args.unshift(item);
                    me.trigger.apply(me, args);
                    if(item === 'trclick') {
                        me._renderTypeSelect && me._renderTypeSelect.hide && me._renderTypeSelect.hide();
                    }
                });
            });
        },

        bindTableEvents: function(table) {
            var me = this;
            table.on('filter.show', _.bind(me._filterHandle, me));
            table.on('trclick', _.bind(me.trclickHandle, me));
            table.on('otherbtn.change', _.bind(me.otherbtnChangeHandle, me));
            table.on('tb.savecolumns', _.bind(me._onSaveColumns, me));
            table.on('renderListComplete', _.bind(function() {me.__initAboutNum(); me._trggerRenderedComplete.apply(me, arguments); me.renderListCompleteHandle.apply(me, arguments)}, me));
            table.on('term.change', _.bind(me._termChangeChangeHandle, me));
            table.on('getDataComplete', _.bind(me.getDataCompleteHandle, me));
            table.on('staticparam.change', _.bind(me.staticParamChangeHandle, me));
            table.on('dt.search', _.bind(me.searchChangeHandle, me));
            table.on('dt.search.before', _.bind(me.searchBeforeHandle, me));
            table.on('term.advance', _.bind(me.termAdvanceChangeHandle, me));
            table.on('del.filter', _.bind(me.delFilterChangeHandle, me));
            table.on('tr.operate', _.bind(me._operateHandle, me)); //自定义按钮
            table.on('checkbox.click', _.bind(me.checkboxclickHandle, me));
            table.on('tb.resize.width', _.bind(me._onSaveColumnWidth, me));
            table.on('tb.columns.resize', _.bind(me._cleanColumsWidth, me));
            table.on('cell.change', _.bind(me._cellChangeHandle, me));
            table.on('tr.error', _.bind(me.trErrorHandle, me));
            table.on('batch.cell.change', _.bind(me.batchCellChangeHandle, me));
			table.on('batchbtn.change', _.bind(me.batchBtnChangeHandle, me));
			table.on('operate.btnclick', _.bind(me._operateBtnClickHandle, me));
			table.on('operate.btnMouseDown', _.bind(me.operateBtnMouseDownHandle, me));
            table.on('td.hover', _.bind(me.tdHoverHandle, me));
            table.on('td.leave', _.bind(me.tdLeaveHandle, me));
            table.on('tag.change', _.bind(me.tagChangeHandle, me));
            table.on('term.bactchHide',  _.bind(me.termBatchHideHandle, me));
            table.on('column.lockChange',  _.bind(me.columnLockChangeHandle, me));
			table.on('show.group.detail', _.bind(me.showGroupDetail, me));

			table.on('beforeAddRow', _.bind(me.beforeAddRow, me));
			table.on('showTipInfo', _.bind(me.showTipInfo, me));
			table.on('afterBatchChangeColumnsData', _.bind(me.afterBatchChangeColumnsData, me));
			table.on('completeRender', _.bind(me.completeRender, me));
			table.on('show.lookup.detail', _.bind(me.showLookUpDetailEve, me));
			table.on('checkbox.disabled', _.bind(me.checkboxDisabled, me));
			table.on('notAllowCheck', _.bind(me.notAllowCheck, me));
			table.on('updateTableData', _.bind(me.updateTableData, me));
			table.on('addRememberData', _.bind(me.addRememberDataHandle, me));
            table.on('reduceRememberData', _.bind(me.reduceRememberDataHandle, me));
            table.on('height.change', _.bind(me.heightChangeHandle, me));
            table.on('dt.page',  _.bind(me.pageNumberChangeHandle, me));
            table.on('dt.pagesize',  _.bind(me.pageSizeChangeHandle, me));
            table.on('dt.search term.advance del.filter', _.bind(me.queryConditionsChangeHandle, me));
            table.on('window.resize', _.bind(me.windowResizeHandle, me));
            table.on('dt.sort',  _.bind(me.tableSortHandle, me));
            table.on('dt.multi.field.sort',  _.bind(me.multiFieldSortHandle, me));
            table.on('size.change',  _.bind(me.pageLineHeightChangeHandle, me));
            table.on('tr.tooltip.mouseover',  _.bind(me.onToolTipMouseOverHandle, me));
            table.on('tr.tooltip.mouseleave',  _.bind(me.onToolTipMouseLeaveHandle, me));
            
		},
        onToolTipMouseOverHandle: $.noop,
        onToolTipMouseLeaveHandle: $.noop,
		showGroupDetail: $.noop,

		beforeAddRow: $.noop,
		showTipInfo: $.noop,
		afterBatchChangeColumnsData: $.noop,
		completeRender: $.noop,
		showLookUpDetailEve: $.noop,
		addRememberDataHandle: $.noop,
		reduceRememberDataHandle: $.noop,

        operateBtnMouseDownHandle: function(e) {
            if ($(e.target).hasClass('tr-operate-drag-row')) {
                this._dragRowHandle(e);
            }
        },

        updateTableScroll: function() {
            this.table && this.table.upDataScroll();
        },

        //代理原table上的一些方法
        _proxyTableFn: function(table) {
            var me = this;

			var fns = ['toggleCheckBoxByPos', 'simplySetCheckStatus', 'cleanHalfChecked', 'setColumnVal', 'setColumn', 'batchEditColumn', 'batchEditColumns', 'toggleColumnEdit', 'upDataScroll', 'toggleFullHeight', 'toggleValidError', 'addRow', 'delRow', 'showColumn', 'hideColumn', 'toggleColumn', 'editValid', 'delCheckedRow', 'addRemberData', 'reduceRemberData', 'getDefaultRowData', '_clearChecked', 'insertRow', 'changeCellStatus', 'getEditValidInfo', 'editRowValid', 'addCellsError', 'removeCellsError', 'setCellsVal', 'setCellsStatus', 'findTrs', 'findTds', 'setCheckedRow', 'exTrs', 'sortByDatas', 'beforeTriggerCellChange', 'addTrError', 'addErrorMsg', 'clearError', 'tableCompleteEvent', 'getColumnByField', 'setTrsVal', 'getDescribeByField', 'getPresetFields', 'setCheckStatusByIds', 'getAllColumns', 'renderByColumns', 'setCheckedData', 'closeColumnEdit', 'openColumnEdit', 'removeSortField', 'fixedHeader', 'fixedTermBatch', 'fixedPageFooter', 'fixedBatchOperate', 'getHeaderWrapper', 'getTermBatchWrapper', 'getMainWrapper', 'getCaptionWrapper', 'getPaginationWrapper', 'getBatchOperateWrapper', 'countOperateColumnWidth', 'clean', 'getSetColumnsPreVisible', 'setCellsValue', 'setUncheckedRow', 'showSetting', 'showColumnSetting', 'toggleSerNialNumberColumn', 'getSernialnumberStatus', 'showColumn_new', 'hideColumn_new', 'isMultiFieldSortMode'];
			me.addOtherFn(fns);
            fns = me.parseProxyFn(fns);
            table && _.each(fns, function(name) {
                me[name] = function() {
                    return table[name] && table[name].apply(table, arguments);
                }
            })
        },

		addOtherFn: $.noop,

        parseProxyFn: function(fns) {
            return fns;
        },

        extendTableEvents: function(table) {
            var me = this;

            var fns = ['needFilterColumnsForBatchEdit'];
            table && _.each(fns, function(name) {
                table[name] = me[name]
            })
        },

        _onSaveColumns: function(data) {
            var fieldList = [];
            _.each(data, function(a) {
                if (a.data) {
                    var temp = {};
                    temp.is_show = !a.isHidden;
                    temp.field_name = a.data;
                    // temp[a.data] = !a.isHidden;
                    a.width && (temp.width = a.width);
                    a.auto_wrap_line && (temp.auto_wrap_line = a.auto_wrap_line);
                    fieldList.push(temp);
                }
            });

            this.set('fieldList', fieldList);
            this.createFieldListConfig(fieldList);
            this.set('_headerCache', null);
            this.__cachelistHeaderData = null;
            this.onSaveColumnsHandle(fieldList);
        },

        onSaveColumnsHandle: NOOP,

        _onSaveColumnWidth: function(data) {
            data.column && this._handleColumnsResize([{
                api_name: data.column.api_name,
                width: data.width
            }])
        },

        _handleColumnsResize: function(columns) {
            var fieldList = this.get('fieldList') || [];
            var arr = _.map(columns, column => {
                var tmp = _.findWhere(fieldList, {
                    field_name: column.api_name
                })
                if(tmp) {
                    tmp.width = column.width
                } else {
                    fieldList.push({
                        field_name: column.api_name,
                        is_show: true,
                        width: column.width
                    })
                }
                return {
                    field_name: column.api_name,
                    width: column.width
                }
            })
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/custom_scene/service/createOrUpdateFieldWidthConfig',
                data: {
                    field_list: arr,
                    object_describe_api_name: this.get('apiname')
                },
                success: () => {
                    this.tdWidthChangeHandle(arr, this.get('apiname'));
                }
            }, {
                errorAlertModel: 1
            })
        },

        //清除个人配置的字段列宽
        _cleanColumsWidth() {
            let r = this.__cachelistHeaderData;
            r && r.Value && _.each(r.Value.visibleFieldsWidth, a => a.width = null);
            
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/custom_scene/service/deleteFieldWidthConfig',
                data: {
                    objApiName: this.get('apiname')
                }
            }, {
                errorAlertModel: 1
            })
        },

        createFieldListConfig: function(fieldList) {
            var term = this.table.getCurTerm();
            if (term && term.isSys) {
                return;
            } // 系统自定义的场景 目前不能保存到场景中
            if(!term) {
                term = _.findWhere(this.options.term && this.options.term.options, {isdef: true});
            }
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/custom_scene/service/createFieldListConfig',
                data: _.extend({}, {
                    field_list: fieldList,
                    object_describe_api_name: this.get('apiname'),
                    scene_id: term ? term.id : null,
                    scene_type: term ? term._type : null,
                    scene_api_name: term ? term.apiname : null
                }, this.getExtendAttribute('from_fieldlistconfig'))
            })
        },

        _getDataBack: function(data) {
            var me = this;
            setTimeout(function() {
                me.getDataBack(data);
            }, 0)
        },

        _operateHandle: function(opts, data) {
            const me = this;
            const url = me.table && me.table.options && me.table.options.url;
            const params = {
				fields: me._filterParamForm(data, opts.param_form),
                data: data,
                title: opts.label,
                button_apiname: opts.api_name,
				button_action: opts.action,
                button_type: opts.button_type,
                apiname: data.object_describe_api_name,
                dataId: data._id,
                redirect_type: opts.redirect_type,
                _from: url && /RelatedList$/.test(url) ? 'relatedList' : 'list',
                success: function() {
                    me.refresh();
                }
            };

            if (opts.isPluginButton) {
                opts.callback?.(params);
                return;
            }

            // call before hook
            const actionExtendConfig = this.getPsrp('actionExtendConfig');
            if (actionExtendConfig) {
                const beforeRowAction = actionExtendConfig[opts.action]?.beforeRowAction
                    || actionExtendConfig.beforeRowAction;

                if (beforeRowAction) {
                    beforeRowAction(params).then(() => {
                        CRM.api.list_btn_operate(params);
                    });
                    return;
                }
            }

            CRM.api.list_btn_operate(params);
        },

		//过滤掉掩码类型的字段参数
		_filterParamForm: function (data, params) {
			if (!params || !params.length) return params;

			var reg = /__s$/;
			var obj = {};
			_.each(data, function (v, k) {
				if (reg.test(k)) {
					obj['form_' + k] = true;
				}
			})

			if (_.isEmpty(obj)) return params;

			return _.filter(params, function (t) {
				return !obj[t.api_name + '__s'];
			})
		},

        //
        // 场景变化
        //
        _termChangeChangeHandle: function(id, term) {
            var me = this;
            me.set('curTermId', id);
            if (term && term.type && term.isNotRequest) {
                me.set('recordType', term.record_type);
                me.set('recordType_apiName', term.record_type);
                if(me.table && me.table._search && me.table._search.getKeyWord) {
                    me.preSearchKeyword = me.table._search.getKeyWord();
                }
                term.forceUpdate && (this.__cachelistHeaderData = null);
                me.renderTable('', function() {
                    me.termChangeChangeHandle(id, term);
                });
            } else {
                me.termChangeChangeHandle(id, term);
            }

            window.logger?.action({
                eventId: 's-paasobj_table_term_change',
                objectApiName: me.get('apiname'),
                apiName: me.get('apiname'),
                msg: term.apiname
            });
        },

        //
        // 列解锁锁定
        //
        columnLockChangeHandle: function(column, status) {
            if (!CRM_TABLE_CONFIG) return;
            var tmp = CRM_TABLE_CONFIG.columnlock || {};
            var fields = tmp[this.get('apiname')] || {};
            fields[column.data] = !!status;
            tmp[this.get('apiname')] = fields;
            CRM_TABLE_CONFIG.columnlock = tmp;
            this.setPaasConfig();
            this.trigger('column.lockChange', ...arguments);
        },

        //table 上的一些钩子事件
        tableComplete: NOOP,
        initComplete: NOOP,
        getDataBack: NOOP,
        trclickHandle: NOOP,
        otherbtnChangeHandle: NOOP,
        termChangeChangeHandle: NOOP,
        recordTypeChangeChangeHandle: NOOP,
        searchChangeHandle: NOOP,
        termAdvanceChangeHandle: NOOP,
        tagChangeHandle: NOOP,
        delFilterChangeHandle: NOOP,
        checkboxclickHandle: NOOP,
        trErrorHandle: NOOP,
        batchCellChangeHandle: NOOP,
        //当用静态数据来处理的时候，如果进行了筛选，依然响应
        //通过重写staticParamChangeHandle发起请求
        staticParamChangeHandle: NOOP,
        cellChangeHandle: NOOP,
        batchBtnChangeHandle: NOOP,
        tdWidthChangeHandle: NOOP,
		needFilterColumnsForBatchEdit: NOOP,
        termBatchHideHandle: NOOP,
		operateBtnClickHandle: NOOP,
		checkboxDisabled: NOOP,
		notAllowCheck: NOOP,
        updateTableData: NOOP,
        heightChangeHandle: NOOP,
        pageSizeChangeHandle: NOOP,
        pageNumberChangeHandle: NOOP,
        queryParamChangeHandle: NOOP,
        windowResizeHandle: NOOP,
        tableSortHandle: NOOP,
        tableMultiFieldSortHandle: NOOP,
        pageLineHeightChangeHandle: NOOP,

        // 搜索事件处理
        searchBeforeHandle: function(keyword, field) {
            if (field) {  // 重新设置搜素字段
                var searchFiled = this.get('searchFiled') || {};
                searchFiled.api_name = field.value;
                searchFiled.label = field.name;
                this.set('searchFiled', searchFiled);
            }
        },

        // 筛选条件变化事件处理
        queryConditionsChangeHandle: function() {
            // 调用钩子事件
            this.queryParamChangeHandle();

            // 调用插件（使用setTimeout, 是为了拿到最新的queryParam）
            _.delay(() => {
                this.runLqpcpService({
                    queryParam: this.get('queryParam')
                });
            }, 50);
        },

        // 多字段排序
        multiFieldSortHandle: function(sortFields) {
            this.tableMultiFieldSortHandle();

            if ((sortFields?.length || 0) > 1) {
                window.logger?.action({
                    eventId: 's-paasobj_table_multi_field_sort',
                    objectApiName: this.get('apiname'),
                    apiName: this.get('apiname'),
                    msg: String(sortFields.length)
                });
            }
        },

        _trggerRenderedComplete(isFirst, opts) {
            const pluginListRenderedCompleteCallback = this.getPsrp('listRenderedCompleteCallback');

            if(opts) {
                opts.listHeaderCostTime = this.get('listHeaderCostTime');
                opts.mainRenderCostTime = this.get('mainRenderCostTime');
            }

            this.trigger('listRenderedComplete', {
                isFirst: isFirst,
                apiname: this.get('apiname'),
                ...opts
            });

            pluginListRenderedCompleteCallback && pluginListRenderedCompleteCallback(isFirst);
        },

        renderListCompleteHandle: function(init) {
            // 首次数据渲染完
            init && this.trigger('initListComplete', this.get('apiname'));
            // 重新请求数据渲染完
            !init && this.trigger('reInitListComplete', this.get('apiname'));
        },

        getDataCompleteHandle: function(init) {
            // 首次请求完数据
            init && this.trigger('getDataComplete');
            // 重新请求完数据
            !init && this.trigger('reGetDataComplete');
        },

        //判断是否有某个权限，已弃用
        hasRight: function(action) {
            return _.findWhere((this.get('layout') || {}).buttons, {
                action: action
            })
        },

        _removeGuideTop: function() {
            this.$('.table-guide .con-wrapper').remove();
            this.$('.table-guide .line').remove();
            CRM.setConfig('newtable-guide-tip', 1);
        },

        //单元格hover事件
		tdHoverHandle: function(e, getData) {
			var me = this;
            var apiname = me.get('apiname');
			if(!me.get('cellEdit') || me.get('hasEditPermission') === false) return;

			CRM.api.fieldHoverEdit({
				$target: $(e.currentTarget),
                from: 'list',
				getData: getData,
                getApiName: me.getCellEditApiName,
				apiname: apiname,
                beforeEdit: me.beforeCellEditHandle,

				success: function(obj) {
                    if(CRM.ea === 'hsmyx888' || CRM.ea === '726333_sandbox') {
                        setTimeout(function() { //延时刷新，防止server数据更新不及时
                            me.refresh();
                        }, 2500);
                    } else {
                        me.updateDataByCellEdit(obj);
                    }
				}
			})
		},

    //单元格leave事件
    tdLeaveHandle: function(e, getData) {
			
		},

		showdetailHandle() {

		},

		//实时编辑回填更新
		updateDataByCellEdit: function(obj) {
			var curData = this.getTableAllData();
			var data = obj.data;
			var dataId = obj.data._id;
			var trIndex;
            var isChildObj = this.get('isChildObj');
			//var updateData = _.pick(data, obj.editFields);
			var td = _.find(curData, function(item, index) {
				if(item._id === dataId) {
					trIndex = index;
					_.extend(item, data);
					return true;
				}
			})
            if(isChildObj) { //如果是子对象统一更新下version
                var version = +data.version + 1;
                _.each(curData, function(a) {
                    a.version = version;
                })
            }
            if(!td) return;

			this.setTrsVal(trIndex, obj.editFields, td);

            this.afterCellEditHandle && this.afterCellEditHandle(obj);
            this.trigger('aftercelledit', obj);
		},

        //pass存储table配置
        setPaasConfig: function() {
            let strs = JSON.stringify(CRM_TABLE_CONFIG);
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/personal_config/service/save',
                data: {
                    key: 'crm.table.config',
                    value: strs
                }
            }, {
                errorAlertModel: 1
            })

            //本地也存一份 一个版本后不再存server直接用本地存的数据
            try {
                FS.store && FS.store.setItem('crm.table.columnlockconfig', strs);
            } catch(e) {}
        },

        getPaasConfig: function(resolve) {
            if (CRM_TABLE_CONFIG) {
                return resolve && resolve();
            }

            try {
                FS.store.getItem('crm.table.columnlockconfig').then((config) => {
                    if (config) {
                        CRM_TABLE_CONFIG = JSON.parse(config);
                    } else {
                        CRM_TABLE_CONFIG = {};
                    }
                    resolve && resolve();
                }, resolve);
            } catch (error) {
                resolve && resolve();
            }
        },

        //拖动排序
        _dragRowHandle: function(e) {
        	var me = this;
        	me._destroyDragMask();
        	me.$el.addClass('crm-md-noselect');
        	var $target = $(e.currentTarget);
        	var table = this.table;
        	var $tr = $target.closest('tr');
        	var $mc = me.$('.crm-table');
        	var $bordermask = $('<div class="crm-md-drag-mask"></div>');
        	var trHeight = $tr.height();
        	var trindex = $tr.index();
        	var startY = e.clientY;
        	var list = this.getTableAllData();
        	var minY = startY - (trindex * trHeight + 60);
        	var maxY = startY + (list.length - $tr.index()) * trHeight + 20;
        	var originTop = $tr.offset().top;
        	$bordermask.css({
        		top: originTop,
        		left: $mc.offset().left,
        		width: $mc.width() - 2,
        		height: trHeight
        	})
        	var $dragmask = $('<div class="crm-md-drag"></div>');
        	$dragmask.css({
				top: -100,
				left: $mc.offset().left,
        		width: $mc.width() - 2,
			});
			me._$dragMask = $dragmask;
			me._$bordermask = $bordermask;
			$('body').append($bordermask).append($dragmask).on('mousemove.newmddrag', function(e) {
				if(e.clientY > maxY || e.clientY < minY) {
					me._destroyDragMask();
				} else {
					$dragmask.css('top', e.clientY);
				}
        	}).on('mouseup.newmddrag', function(e) {
        		if(!$dragmask) {
        			me._destroyDragMask();
        			return;
        		}
				var endY = $dragmask.offset().top;
				me._destroyDragMask();
				var ti = -1;
				if(endY < originTop) { //向上
					ti = trindex - (1 + Math.floor((originTop - endY) / trHeight));
					if(ti < 0) {
						ti = 0
					}
				} else if(endY - originTop > trHeight) {
					ti = trindex + Math.floor((endY - originTop) / trHeight);
					if(ti > (list.length - 1)) {
						ti = list.length - 1;
					}
				}

				if(ti < 0 || ti === trindex) return;

				var cList = _.filter(list, function(a, index) {
					return index !== trindex
				})
				cList.splice(ti, 0, list[trindex]);
                table.sortByDatas(cList);
                me.afterSortByDatas();
			});
        },

        _destroyDragMask: function() {
        	if(this._$dragMask) {
        		this._$dragMask.off();
        		this._$dragMask.remove();
        		this._$dragMask = null;
        		$('body').off('mousemove.newmddrag');
        	}
        	if(this._$bordermask) {
        		this._$bordermask.off();
        		this._$bordermask.remove();
        		this._$bordermask = null;
        		$('body').off('mouseup.newmddrag');
        	}
        	this.$el.removeClass('crm-md-noselect');
        },

        afterSortByDatas: function() {},

        _cellChangeHandle: function(value, field, type, options) {
            const fieldChangeCallback = this.getPsrp('fieldChangeCallback');
            if (fieldChangeCallback) {
                fieldChangeCallback(value, field, options.cellData, options.oldData);
            }

            this.cellChangeHandle.apply(this, arguments);
        },

        _operateBtnClickHandle: function(e) {
            var $target = $(e.target);
            var action = $target.data('action');

            if (action) {
                action = '__' + action.split('_').join('') + 'Handle';
                this[action] && this[action](e);
            }

            this.operateBtnClickHandle.apply(this, arguments);
        },

        __settopHandle: function(e) {
            this._setrowposHandle(e, 0);
        },

        __setbottomHandle: function(e) {
            this._setrowposHandle(e, this.getTableAllData().length);
        },

        _setrowposHandle: function(e, index) {
            var $target = $(e.currentTarget);
        	var $tr = $target.closest('tr');
            var trindex = $tr.index();
            var list = this.getTableAllData();
            var cList = _.filter(list, function(a, index) {
                return index !== trindex
            });
            cList.splice(index, 0, list[trindex]);
            this.table.sortByDatas(cList);
            this.afterSortByDatas();
        },

        _addDelay: function(data) {
            var me = this;

            me._delayList = me._delayList || [];
            me._delayList.push(data);
        },

        _clearDelay: function(name) {
            var me = this;

            name = _.isArray(name) ? name : [name];

            me._delayList = _.filter(me._delayList, function(item) {
                return !_.contains(name, item.name);
            });
        },

        // 延迟到table初始化后执行的方法
        _doDelay: function() {
            var me = this;

            if (!me._delayList || !me._delayList.length) {
                return;
            }

            _.each(me._delayList, function(item) {
                me[item.name] && me[item.name].apply(me, item.args);
            });

            me._clearDelay(_.pluck(me._delayList, 'name'));
        },

        /*
         ********************************************
         * 选择属性值相关，只提供属性行新建，展示，隐藏方法
         ********************************************
         */
        //显示全部选中数据的属性DOM
        //targetAttrInfo:指定要选中的属性值，优先级高于默认，例如来源报价器
        batchShowAttribute(checkedData,targetAttrInfo){
			if (checkedData && checkedData.length >= 1) {
				checkedData.forEach(data => {
                    this.showAttribute(data)
				})
			}
        },
        //显示一行数据的属性DOM
        showAttribute(data,targetAttrInfo) {
			if (this.hasAttribute(data)){
				if(this.options.notShowAttribute || data.notShowAttribute) return;
				let id = data._id,
					$tr = this.getAttrTrDom(data);

				if (!$tr || $tr.length == 0) {
					$tr = this._createAttrTrDom(id, data);
				}
				//初始化需要默认选中的属性值，非筛选等特殊场景下，默认选中"is_default"
				util.setDefaultAttr(data,targetAttrInfo);
				this._displayAttrDom(data, $tr);
			}
        },


        //
        batchHideAttribute(arr){
            if (arr && arr.length >= 1) {
				arr.forEach(data => {
                    this.hideAttribute(data)
				})
			}
        },

		resizeAsync: _.debounce(function () {
			this.resize();
		}, 300),

		hasAttribute(data){
        	return data && (data.attribute && data.attribute.length || data.nonstandardAttribute && data.nonstandardAttribute.length);
		},

        //隐藏属性DOM
		hideAttribute(data, isDel = false) {
			if (this.hasAttribute(data)){
				let id=data._id,
					$atr = this.getAttrTrDom(data);
				if (!$atr || $atr.length == 0) {
					$atr = this._createAttrTrDom(id, data);
				}
				$trs = this.getTrFromTableDom(data, "");
				$trs.removeClass('tr-expend-attr').css('height', "auto");
				$atr.find('.opt-attr-value').removeClass('active');
				if(isDel){
					$atr && $atr.remove();
				}else {
					$atr && $atr.hide();
				}
				// data.selectedAttr = null;
				this.resizeAsync();
			}
		},

        getAttrName(arr,id){
            let item=arr.find(a=>a.id==id);
            return item.name
        },

        //多选属性值
		multiAttrSelect($item,attr,data){
			let attrObj=Object.assign({},attr);
			$item.toggleClass("active");

			// 兼容无默认值
			if(!attrObj.value_ids) attrObj.value_ids = [];
			if ($item.hasClass('active')) {
				attrObj.value_ids.push(data)
			} else {
				attrObj.value_ids = attr.value_ids.filter(c => {
					if (c.id !==data.id) {
						return c;
					}
				})
			}
			return attrObj;
		},
		//单选属性值
		singleAttrSelect($item, attr, data) {
			let attrObj = Object.assign({}, attr);

			if (!$item.hasClass('active')) {
				$item.siblings('.opt-attr-value').removeClass('active');
				$item.addClass("active");

				attrObj.value_ids = [data];
			}
			return attrObj;
		},

		setAttrDomSelect($item){
			if (!$item.hasClass('active')) {
				$item.siblings('.opt-attr-value').removeClass('active');
				$item.addClass("active");
			}
		},

		// 设置属性禁用状态
		setAttrDisabled($item, status){
        	let f = status ? 'addClass' : 'removeClass';
			$item[f]('disabled');
		},

        //创建属性属性值DOM
		_createAttrTrDom(id, data) {
            //属性价目表不显示非标属性输入框
            const isAttrPriceBookLinesProduct=this.options&&this.options.target_related_list_name=="attribute_price_book_lines_product_id";
        
            let $contTr = this.getTrFromTableDom(data, '.tb'),
				$aTr = $('<tr class="show-attr" data-id=' + id + '  data-sourceRowId=' + data.rowId + '></tr>').html(attrValuesTpl({
                    data: data.attribute,
                    nsData:isAttrPriceBookLinesProduct?[]:data.nonstandardAttribute
                }));
            $($aTr).insertAfter($contTr);

			return $aTr;
        },

        //在表格中展示属性属性值，并适配两边固定列高度
		_displayAttrDom(data, $aTr) {
            let id=data._id,
                $startTr = this.getTrFromTableDom(data, '.fix-start-b'),
				$contTr = this.getTrFromTableDom(data, '.tb'),
				$endTr = this.getTrFromTableDom(data, '.fix-end-b');

			$contTr.addClass('tr-expend-attr');

			$aTr.show();
			this._activeAttrValDom(data,$aTr);

			let height = $aTr.height() * 1 + $contTr.height() * 1 - 1 + 'px';
			$startTr.css("height", height);
			$endTr.css("height", height);
			//重置表格整体高度
			this.resizeAsync();
        },

        //选中属性值激活状态
        _activeAttrValDom(data, $aTr){
			let attrData=data.selectedAttr;
			for(let key in attrData){
				let valIds=attrData[key].value_ids;
				if(valIds.length>=1){
					valIds.forEach(v=>{
						$aTr.find('.options .opt-attr-value[data-id="'+v.id+'"]').addClass('active')
					})
				}
			}

			// 非标属性设置值
			if(data.nsAttr){
				let par = $aTr.find('.ns-attribute-values');
				_.each(data.nsAttr, (o, key) => {
					let li = par.find('.item[data-id='+ key +']');
					li.find('.opt-attr-value').val(o.value);
				});
			}
        },

        //通过产品id获取其属性行
        getAttrTrDom(data) {
			return $('tr.show-attr[data-id="' + data._id + '"]', this.$el);
        },

        //通过产品id获取原表格中的行
		getTrFromTableDom(data, wrapper) {
			return $(wrapper + ' tr[data-id="' + data._id + '"]', this.$el)
        },


        //-------------精确查询相关逻辑start---------------//
        showAboutNum: function() {},//返回true支持，默认不支持
        getAboutNumUrl: function() {
            return '/EM1HNCRM/API/v1/object/'+ this.get('apiname') +'/controller/List';
        },
        _getTotal: function(total) {
            total = total || 0;
            if(!this.showAboutNum()) return total;
            var limit = this.__getTotalNumLimit();
            
            total = total > limit ? limit : total;

            // 已经拿到了真实的数据
            if (this._trueTatal) {
                // 条件没有发生变化
                if (this._trueNumQuery === this.__getTrueQueryParam(this.get('queryParam'))) {
                    total  = this._trueTatal;
                } else {
                    this._trueTatal = 0;
                }
            }

            return total;
        },
        __getQueryParam: function() {
          	return this.parseParam(this.get('queryParam'));
        },
        //
        // 拿到去除limit 和 offset 的条件
        //
        __getTrueQueryParam: function (param) {
            var o = {};
            var search_query_info = CRM.util.parseJson(param.search_query_info);
            o.filters = search_query_info.filters;
            return CRM.util.stringify(_.extend({}, param, {  // 真实数据的查询条件
                search_query_info: CRM.util.stringify(o)
            }));
        },

        //
        //查询精确条数
        //
        _onLookNum: function(e) {
            var $target = $(e.target);
            if (!$target.hasClass('coding')) {
                $target.addClass('coding').html($t('计算中'));
                this._getAbountNum(total => {
                    if(!this.table) return;
                    this.table.setPaginationTotal(total);
                    var $page = this.table.getPaginationWrapper();
                    var $outfilter = this.table.$el.find('.dt-out-filter');
                    $page.find('.about-num,.about-num-wrap').hide();
                    if($outfilter.length) {
                        $outfilter.find('.about-num,.about-num-wrap').remove();
                        $outfilter.find('.j-out-clean').after(`<span class="total-num"> ( ${$t('共{{num}}条',{num: '<em>'+  total + '</em>'})} )</span>`)
                    }
                    $page.find('.total-num').show();
                });
            }
        },

        //
        // 获取总数
        //
        _getAbountNum: function(cb) {
            var me = this;
            var param = this.get('queryParam');
            CRM.util.FHHApi({
                url: me.getAboutNumUrl(),
                data: _.extend({
                    find_explicit_total_num: true
                }, param),
                success: function(res) {
                    if(!me.table) return;
                    if (res.Result.StatusCode === 0) {
                        me._trueNumQuery = me.__getTrueQueryParam(param);
                        cb(me._trueTatal = res.Value.total);// 真实条数
                    } else {
                        CRM.util.alert(res.Result.FailureMessage)
                    }
                }
            }, {
                errorAlertModel: 1
            });
        },

        __getTotalNumLimit: function() {
            return this._totalNumLimit || this.options._totalNumLimit || 1000;
        },

        __initAboutNum: function() {
            if(!this.showAboutNum()) return;
            var limit = this.__getTotalNumLimit();
            var flag = this._tatal >= limit && !this._trueTatal;
            var $page = this.table.getPaginationWrapper();
            var $outfilter = this.table.$el.find('.dt-out-filter');
            var $numBtn = $page.find('.about-num-wrap');
            if($outfilter.length) {
                $outfilter.find('.about-num,.about-num-wrap').remove()
                $outfilter.find('.total-num').remove();
                if(!flag) {
                    $outfilter.find('.j-out-clean').after(`<span class="total-num"> ( ${$t('共{{num}}条', {num: '<em>'+  this._tatal + '</em>'})} )<span>`)
                } else {
                    $outfilter.find('.j-out-clean').after(`<span class="about-num"> ( ${$t('约{{num}}条', {num: limit + '+'})}</span><span class="about-num-wrap"><span class="look-num j-look-num">${$t('精确总数')}</span> )</span>`);
                    $outfilter.find('.j-look-num').click((e) => {
                        this._onLookNum(e);
                    })
                }
                $outfilter.find('.total-num').toggle(!flag);
                $outfilter.find('.j-look-num').html($t('精确总数')).removeClass('coding');
                $outfilter.find('.about-num').toggle(flag);
            }
            if (!$numBtn.length) {
                $page.find('.dt-page-right').before(`<span class="about-num">${$t('约{{num}}条', {num: limit + '+'})}</span><div class="about-num-wrap"><span class="look-num j-look-num">${$t('精确总数')}</span></div>`);
                $page.find('.page-num').after(`<span class="about-num">+</span>`);
                $numBtn = $page.find('.about-num-wrap');
                $page.find('.j-look-num').click((e) => {
                    this._onLookNum(e);
                })
            }
            $page.find('.total-num').toggle(!flag);
            $page.find('.j-look-num').html($t('精确总数')).removeClass('coding');
            $page.find('.about-num').toggle(flag);
            $numBtn.toggle(flag);
        },
        //-------------精确查询相关逻辑end---------------//

        destroy: function() {
            // this.set('_termIds', null);
			this.stopListening(); //backbone通用事件移除
			if (this.$el) {
				this.undelegateEvents();
				this.off();
				this.$el.off();
			}

            this.set('_headerCache', null); // 清空缓存
            this.set('isFirstRequestHeader', false); // 清空缓存
            clearTimeout(this._cbTimer);
            this.disposeServices();
            this.columnsAjax && this.columnsAjax.abort();
            this.passConfigAjax && this.passConfigAjax.abort();
            this.columnsAjax = this.passConfigAjax = null;
            this.action && this.action.destroy();
            this.table && this.table.destroy();
            this._events = null;
            this._fetchFieldMappingCallBack = null;
            this.table = this.action = this.$el = this.el = this.events = this.options = null;
        }
    }, require('./services/services')))

    module.exports = ObjectTable;
})
