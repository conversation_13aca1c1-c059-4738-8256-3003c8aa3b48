/**
 *@desc 自定义对象选择
 *<AUTHOR> 2017/02/24
 * @param options:{
 *     width:1020, // 宽度
 *     maxSize:false, // 最大宽度；伪全屏
 * }
 */
// {
// 	options: {
// 		formatOtherBtns: (btns = []) => {return btns},	// 分屏视图按钮
// 		otherBtnChangeHandle: (type, {getParam, setParam}) => {}, // 分屏视图处理函数
// 		formatColumns: (columns, apiname) => {return columns}, // 格式化表格列
// 		refreshCurView: ({getParam, setParam}) => {}, // 刷新当前视图
// 		calcTableWrapperHeight: () => {}, // 计算表格容器高度
// 		formatTableOption: (options) => {return options}, // 处理表格options
// 		trclickHandle: function (...args) {}, // 行点击
// 		setSpecSkuWrapper: () => {return dom}, // 设置选规格页面容器
// 		initSpecSkuComplete: () => {submit, cancel, destroy, table}, // 选规格页面初始化完成
// 		formatSpecSkuColumns: (columns, apiName) => {}, // 格式化选规格页面列
// 		setSpecSkuShowMultiple: () => {return true or false}, // 设置选规格页面是否显示checkBox
// 		initComplete: ({getSKUComponent}) => {}, // 选数据页面初始化完成
//		beforeRequest: (requestParam) => {return requestParam}, // 处理请求参数
//		formatDataAsync: (data) => data, // 异步格式化列表数据
//		getSelectBomComponent: () => {return component}, // 获取选配bom组件
//		renderCategoryExtend: () => {}, // 替换分类组件
//		beforeSelectFunc: () => {}, // 点确定按钮勾子
//		beforeRenderHook: () => {}, // 渲染前勾子
// 	}
// }

define(function (require, exports, module) {

	var Dialog = require('crm-widget/dialog/dialog'),
		Table = require('./table/table'),
		flowTable = require('./table/flow_table'),
		PTable = require('./table/ptable'),
		TreeTable = require('./table/treetable'),
		AreaManageTable = require('./table/arae_manage_table'),
        ProductCollectionTable = require('./table/product_collection_table'),
		AttrTable = require('./table/atable'),
		util = CRM.util;
    const CONFIG = require('crm-modules/buscomponents/md_business_config/md_business_config');

	var PickSelfObject = function (opt) {
		this.options = _.extend({}, opt);
	};

	_.extend(PickSelfObject.prototype, {

		beforeRender() {
			this.options = _.extend(this.options, this._getOptions(this.options));
		},

		render: function (obj) {
			var me = this;
			this.beforeRender();
			this.options = _.extend(this.options, obj);
			this.initOptions();

			this.checkedData = {}; // 记录修改过产品价格的选中行数据
			var options = _.extend({
				accountObjId: '' // 客户id 前端增加已选客户场景
			}, this.options, {
				isEdit: false
			});
			this.options = options;
			this.beforeRenderHook();
			this.setWidth(options);

			this.isShowCategory();
			//强制全屏展示
			this.forceMax = this.isForceMax(options);
			this.createPickDialog(obj);
			this.options.el = this.$('.t-wrap');
			this.doreleatedname(this.options);
			me.afterRender(options);
			me.submit = obj && obj.submit;
			this.afterRenderHook();
		},

		beforeRenderHook: $.noop,
		afterRenderHook: $.noop,

		initOptions:function(){
			if(!this.options.target_related_list_name && this.options.pluginContext){
				this.options.target_related_list_name = this.options.pluginContext.target_related_list_name
			}
			this.options.beforeRenderHook && this.options.beforeRenderHook({opt: this.options});
		},

		/*
		 * 是否强制最大化
		 * 场景1:有分类的全屏显示
		 */
		isForceMax: function (options) {
			let forceMax = false;
			if (this.isShowCat) {
				forceMax = true;
			}
			return forceMax;
		},

		afterRender: function () {
			this.renderTable(this.options);
		},

		$: function (selector) {
			return this.dialog ? $(selector, this.dialog.element) : $('<div>');
		},

		setWidth: function (opts) {
			// let maxWidth=window.innerWidth - 40;
			// this.options.width=this.options.width||maxWidth;
			// if (opts.maxSize) { // 最大化
			// 	this.options.width =maxWidth;
			// }
		},

		createPickDialog: function (obj) {
			this.beforeRenderDialog();
			if (!this.dialog) {
				var _dialog;
				var wrapper = this.options.wrapper;

				// 通过options传入的container可以作为选数据组件的容器，支持jQuery对象及选择器
				if (wrapper instanceof window.jQuery) {
					_dialog = wrapper;
				}
				// jQuery选择器
				else if (typeof wrapper === 'string') {
					_dialog = $(wrapper);
				}

				if (_dialog && _dialog.length) {
					this.dialog = _dialog.element = _dialog;
					this.dialog.html(this.getContent());
				}
				else {
					this.dialog = new Dialog(Object.assign({
						zIndex: obj && obj.zIndex,
						title: obj.title || $t("选择数据"),
						//classPrefix: this.getDialogClass(),
						width: this.options.width || 1020,
						showBtns: obj.showBtns ?? true,
						showScroll: false,
						showFullScreenBtn: this.options.showFullScreenBtn || false,
						// stopPropagation:true,
						content: this.getContent(),
						enableDrag: false,
						classPrefix: obj.classPrefix || 'crm-c-dialog',

					}, obj.dialogOptions || {}));
					// 是否内部调用的dialog组件
					this.dialog.isDialogComp = true;
					this.dialog.on('dialogEnter', this.confirm, this);
					this.dialog.on('dialogCancel', this.dialogCancel, this);
					this.dialog.on('hide', this.destroy, this);
					this.dialog.on('show', this.renderTab, this);
					this.dialog.on('tab', this.ontab, this);


					this._dragHandle();
				}
			}

			this.$twrap = this.$('.t-wrap');

			if (this.dialog.isDialogComp) {
				this._fixBody();
				this.dialog.show();
				if (!this.forceMax) {
					this.appendFullBtn();
				}
				this.$twrap = this.$('.t-wrap');
				this.$enterBtn = this.$('.b-g-btn');
				this.$dialogBtns = this.$('.dialog-btns');
				this.bindSrollEvent();
			}

			this.dialog.element.addClass(this.getDialogClass());

			$(window).on('resize.pickselefobject', () => {
				this.resize();
			});

			return this.dialog;
		},

		_dragHandle() {
			if(CRM.util.getUserAttribute('paasCrmNotSupportDrag') || this.options.useOnList || !this.dialog) return;//代码回滚

			let dialog = this.dialog;
			dialog.element.addClass('crm-comp-pickview-drag');
			dialog.element.on('mousedown', '.' + 'dialog-tit', (e) => {
				if(this.isFull()) return;//全屏不支持

				let left = parseInt(dialog.element[0].style.left);
				let top = parseInt(dialog.element[0].style.top);

				//分页
				if(!this._$dPage) {
					this._$dPage = this.$('.dt-page');
				}

				let left1 = parseInt(this._$dPage.css('left'));
				let top1 = parseInt(this._$dPage.css('top'));

				let left2, top2, right2;
				//表格筛选区
				if(this.__$caption) {
					if(!this._$dCaption) {
						this._$dCaption = this.$('.dt-caption');
					}
					left2 = parseInt(this._$dCaption.css('left'));
					right2 = parseInt(this._$dCaption.css('right'));
					top2 = parseInt(this._$dCaption.css('top'));
				}

				let left3, top3, right3;
				//表头区
				if(this.__$header) {
					if(!this._$dHeader) {
						this._$dHeader = this.$('.dt-main>.header');
					}
					left3 = parseInt(this._$dHeader.css('left'));
					right3 = parseInt(this._$dHeader.css('right'));
					top3 = parseInt(this._$dHeader.css('top'));
				}

				let left4, top4, right4;
				//统计区
				if(this._countComp && this._countComp.$el) {
					left4 = parseInt(this._countComp.$el.css('left'));
					right4 = parseInt(this._countComp.$el.css('right'));
					top4 = parseInt(this._countComp.$el.css('top'));
				}

				let drag = (ee) => {
					ee.preventDefault();
					let x = ee.clientX - e.clientX;
					let y = ee.clientY - e.clientY
					if(top + y < 1) return;

					dialog.element.css({
						left: left + x,
						top: top + y,
					})
					this._$dPage.css({
						left: left1 + x,
						top: top1 + y
					})
					if(left2 !== void 0) {
						this._$dCaption.css({
							left: left2 + x,
							top: top2 + y,
							right: right2 - x
						})
					}
					if(left3 !== void 0) {
						this._$dHeader.css({
							left: left3 + x,
							right: right3 - x,
							top: top3 + y
						})
					}
					if(left4 !== void 0) {
						this._countComp.$el.css({
							left: left4 + x,
							right: right4 - x,
							top: top4 + y
						})
					}
				}

				$(document.body).on('mousemove', drag)

				$(document.body).one('mouseup', () => {
					$(document.body).off('mousemove', drag);
				})
			})
		},

		dialogCancel(){
			this.trigger('dialogCancel');
			this.destroy();
		},

		//某些场景下body高度超标 需要控制下 有瑕疵 如果弹框里再弹框 可能会覆盖。
		_fixBody: function(flag) {
			$('body').css({
				maxHeight: flag ? '' : '100%',
				overflow: flag ? '' : 'hidden'
			})
		},

		isFull: function() {
			return this.forceMax ||util.getCache('crm-comp-pickview-fullstatus') === 'full';
		},

		appendFullBtn: function () {
			var me = this;
			if (me.$full) return;
			var flag = this.isFull();
			me.$full = $('<div class="crm-comp-pickview-fullbtn"><span class="fx-iconfont fx-icon-fullscreen"></span><span class="fx-iconfont fx-icon-fullscreen-exit"></span></div>');
			me.$full.click(function () {
				flag = !flag;
				util.setCache('crm-comp-pickview-fullstatus', flag ? 'full' : 'mini');
				me.dialog.element.toggleClass('crm-comp-pickview-full');
				me.resize();
				me.table && me.table.resize();
			})

			me.$('.dialog-tit').append(me.$full);
		},

		setPagePos: function () {
			if (!this.dialog?.isDialogComp) {
				return;
			}

			const dialogOffset = this.dialog.element.offset();
			const btnOffset = this.$dialogBtns.find(':first-child').offset();
			// 弹框分有按钮和无按钮情况
			const width = btnOffset ? btnOffset.left - dialogOffset.left - 40 : this.dialog.element.width() - 30;
			this.$('.dt-page:first').css({
				left: dialogOffset.left,
				top: dialogOffset.top + this.dialog.element.height() - 53,
				width
			})
		},

		calHeight: function () {
			if (this.options.calcTableWrapperHeight) {
				return this.options.calcTableWrapperHeight();
			}
			return ($('body').height() - 57 - 53 - (this.isFull() ? 40 : 96)); //表格内容高度为屏幕高度 - 表标题栏 - 按钮栏 - 框的top + bottom
		},

		getContent: function () {
			return '<div style="height:' + this.calHeight() + 'px;" class="t-wrap"></div>'
		},

		getDialogClass: function () {
			return 'crm-comp-pickview' + (this.isFull() ? ' crm-comp-pickview-full' : '') + (this.options.useOnList ? ' crm-comp-pickview-list' : '');
		},

		beforeRenderDialog: $.noop,

		//
		// 处理doreleatedname
		// 获取关联数据
		// 设置关联关系的选择数据列表
		//
		doreleatedname: function () {

			var me = this,
				opts = me.options;
			var relatedList = opts.relatedList;
			if (!relatedList || !opts.mainData) {
				return;
			}
			me.relatedIndex = me.relatedIndex || opts.relatedIndex || 0; // 默认第几个tab

			relatedList = opts.relatedList[me.relatedIndex];

			opts.apiname = relatedList.api_name;

			opts.questParam = {
				name: opts.mainData.name,
				associate_object_data_id: opts.mainData.id,
				associate_object_describe_api_name: opts.mainData.apiname,
				associated_object_describe_api_name: relatedList.api_name,
				associated_object_field_related_list_name: relatedList.list_name,
				include_associated: false
			};

			if (!opts.beforeRequest && opts.mainData.account_id) {
				opts.beforeRequest = function (data) {
					if (!_.contains([
							'sales_order_contract_list', 'contract_sales_order_list', 'opportunity_contact_list',
							'contact_opportunity_list', 'product_opportunity_list', 'opportunity_product_list'
						], relatedList.list_name)) {
						return data;
					}
					var info = JSON.parse(data.search_query_info);
					info.filters = info.filters || [];
					info.filters.push({
						field_name: 'account_id',
						field_values: [opts.mainData.account_id],
						operator: 'EQ'
					});
					data.search_query_info = JSON.stringify(info);
					return data;
				}
			}

			//
			// 设置关联关系
			//
			if (!me.submit && me.options.setRelation) {
				me.submit = function (data) {
					if (!data) return;
					var ids = _.pluck(data, '_id');
					me.setRelationToObjects(ids, function () {
						me.trigger('setRelationSuc', ids, relatedList);
						me.destroy();
					});
				}
			}
		},

		//
		// 设置关联
		//
		setRelationToObjects: function (ids, cb) {
			var me = this,
				mainData = me.options.mainData,
				relatedList = me.options.relatedList[me.relatedIndex];
			var postData = {
				associated_obj_ids: ids,
				associate_obj_api_name: mainData.apiname,
				associate_obj_id: mainData.id,
				associated_obj_api_name: relatedList.api_name,
				associated_obj_related_list_name: relatedList.list_name
			};
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + postData.associated_obj_api_name + '/action/BulkAssociate',
				data: postData,
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						util.remind(1, $t('关联成功'));
						cb && cb();
						return;
					}
					util.alert(res.Result.FailureMessage);
				}
			}, {
				errorAlertModel: 1,
				submitSelector: me.$enterBtn
			})
		},

		//
		// 渲染标题
		renderTab: function ($dialog) {
			var relatedIndex = this.relatedIndex || 0;
			var relatedList = this.options.relatedList;
			var str = '';

			if (!relatedList) {
				return;
			}

			_.each(relatedList, function (item, index) {
				str += '<span action-type="tab" class="' + (index == relatedIndex ? 'cur' : '') + '">' + $t('选择') + item.display_name + '</span>'
			});

			$('.dialog-tit', $dialog).html('<div class="tab-wrap">' + str + '</div>');
		},

		ontab: function (evt) {
			$(evt.currentTarget).addClass('cur').siblings().removeClass('cur');
			this.relatedIndex = $(evt.currentTarget).index();
			this.doreleatedname();
			this.renderTable(this.options);
		},

		getTableConfig: function () {
			var otherTable = (CRM._cache.openAttribute||CRM._cache.openNsAttribute) ? "AttrTable" : "PTable",
				otherTableObj = {
					pricebookproduct_salesorderproduct_list: otherTable,
					salesorderproduct_product_list: otherTable,
					price_book_product_quote_lines_list: otherTable,
					product_quote_lines_list: otherTable,
					price_book_product_new_opportunity_lines_list: otherTable,
					product_new_opportunity_lines_list: otherTable,
					target_related_list_pricebookproduct: "PTable", //价目表添加产品
					attribute_price_book_lines_product_id: "AttrTable", //属性价目表添加产品
					target_related_list_CoveredStoresObj_AccountObj__c: 'AREMANAGETABLE',
                    target_related_list_CoveredProductObj_ProductObj__c: 'ProductCollectionTable'
				},
				listName = this.options.target_related_list_name,
				tableConfig = (this.options.isMultiple && (otherTableObj[listName] || CONFIG.PTable.includes(listName) && otherTable)) || "Table";

			if(listName && listName.includes('__c') && (this.options._from === 'bom' || this.options._from === 'attribute')&& !CONFIG.PTable.includes(listName)){
				tableConfig = otherTable;
			}

			if(this.options._renderAttributeTable){
				tableConfig = 'AttrTable';
			}

			if (listName === 'target_related_list_CoveredStoresObj_AccountObj__c') {
				tableConfig = 'AREMANAGETABLE';
			}
            if (listName === 'target_related_list_CoveredProductObj_ProductObj__c') {
                tableConfig = 'ProductCollectionTable';
            }
			let apiName = this.options.apiname
			if(['ApprovalTaskObj'
				,'ApprovalInstanceObj'
				,'StageTaskObj'
				,'BpmInstance'
				,'BpmTask'
			].includes(this.options.apiname)){
				tableConfig = 'flowTable';
			}
			if (this.options.tableConfig) {
				return this.options.tableConfig;
			}

			return tableConfig;
		},

		getTable() {
			const tableConfig = {
					"PTable": PTable,
					"AttrTable": AttrTable,
					"Table": Table,
					"TreeTable": TreeTable,
					'AREMANAGETABLE': AreaManageTable,
                    "ProductCollectionTable":ProductCollectionTable,
				'flowTable':flowTable
				},
				tableKey = this.getTableConfig();
			return tableConfig[tableKey];
		},

		changeParam: $.noop,

		getDialogZindex: function() {
			var zIndex = this.dialog.isDialogComp ? this.dialog.get('zIndex') : this.dialog.css('zIndex');

			return isNaN(+zIndex) ? zIndex : +zIndex;
		},

		renderTable: async function (options) {
			var me = this;
			me.table && me.table.destroy();
			var Comp = this.getTable();
			//商机图谱表格新建权限特殊判断
			var hideAdd = me.options.hideAdd;
			if (options.relatedList && options.mainData.api_name == "NewOpportunityObj") {
				hideAdd = options.relatedList[me.relatedIndex].hideAdd;
			}

			var param = _.extend(options, {
				hideAdd: hideAdd,
				parseHeaderData: me.parseHeaderData.bind(me),
				tableType: me.getTableConfig()
			});

			if (me.dialog.isDialogComp) {
				param.zIndex = me.getDialogZindex() + 10;
			}
			me.changeParam(param);

			if (param.tableExtend) {
				let tableExtend = param.tableExtend;
				delete param.tableExtend;
				Comp = Comp.extend(tableExtend);
			}

			me.table = new Comp(param);

			me.table.on('complete', function (self) {
				if (self && self.table) {
					self.table.on('showdetail', function ($target) {
						me._showDetail($target.attr('data-id'), me.options.apiname);
					});

					self.table.on('showProduct', function ($target) {
						me._showDetail($target.attr('data-id'), $target.attr('data-apiname'));
					});

					self.table.on('checkbox.click', function (status, $target, checkedLen, isSingle, isCheckedAll) {
						me.afterCheckboxClick(status, $target, me.table, isCheckedAll)
					});

					self.table.on('addRememberData', function (data, table) {
						me.addRememberDataHandle(data, table);
						me.updateCount();
					});

					self.table.on('reduceRememberData', function (data, table) {
						me.reduceRememberDataHandle(data, table);
						me.updateCount();
					});

					self.table.on('clearRemberData', function (data, table) {
						me.updateCount();
					});

					me.addTableEventsListen(self.table);
					me.afterRenderTableComplete();

				}

				me.setPagePos()
			});

			me.table.on('includeSelectedChange', function (data, table) {
				me.includeSelectedChangeHandle(data, table)
			});


			me.table.on('add', function (e) {
				me.trigger('add', e);
			});

			me.table.on('addProduct', function (addData) {
				me.checkedData = $.extend(me.checkedData, addData);
			});


			me.listenTo(me.table, 'render', me.resize);
			me.listenTo(me.table, 'initComplete', me.tableInitComplete);
			me.listenTo(me.table, 'datarendercomplete', () => {
				me._destroyScrollDiv();
				me.renderCount();
			});

			me.table.render();

			me.table.completeRender = this.tableCompleteRender.bind(me);

			me.options.tableComplete && me.options.tableComplete(me.table);
			me.options.initComplete && me.options.initComplete({
				getSKUComponent: (opts, callbacks) => {
					me.table.getSKUComponent && me.table.getSKUComponent.bind(me.table, opts, callbacks)();
				}
			});
		},

		afterCheckboxClick: $.noop,
		addTableEventsListen: $.noop,
		addRememberDataHandle: $.noop,
		reduceRememberDataHandle: $.noop,
		includeSelectedChangeHandle: $.noop,
		afterRenderTableComplete: $.noop,
		tableCompleteRender: $.noop,
		tableInitComplete: $.noop,

		/**
		 * @desc 显示详情
		 */
		_showDetail: function (id, apiname) {
			var me = this;
			require.async('crm-components/showdetail/showdetail', function (Detail) {
				var opts = {
					apiName: apiname || '',
					top: 0,
					size: 'hg',
					masterData: me.options.master_data,
					objectData: me.options.object_data,
				};
				if (me.dialog.isDialogComp) {
					opts.zIndex = me.getDialogZindex() + 10;
				}
				me._detail && (me._detail.destroy(), me._detail = null);
				me._detail = new Detail(opts);
				me._detail.on('refresh', function () {
					me.table && me.table.setParam({}, true);
				});
				me._detail.show(id);
			});
		},

		confirm: function (e) {
			var me = this;
			var value = me.table.getValue(true);
			var relatedList = me.options.relatedList || [];

			if (!value) {
				util.alert($t("请选择一条数据") + '!');
				return;
			}
			//属性值选择有错误
			if (CRM._cache.openAttribute && value == "attributeError") {
				return;
			}

			// 此参数的用意：用于【确认】时，校验数据，不合业务逻辑时阻断逻辑（true阻断，false不阻断）
			if (me.options.beforeSelectFunc && me.options.beforeSelectFunc(value)) {
				return;
			}

			me.trigger('select', value, value, relatedList[me.relatedIndex || 0]);
			if (me.submit) {
				me.submit(value, me, e);
				return;
			}

			me.destroy();
		},

		resize: function () {
			this.$twrap && this.$twrap.height(this.calHeight());
			this.dialog && this.dialog.resetPosition && this.dialog.resetPosition();
			this.$twrap && this.$twrap.scrollTop(this.$twrap.scrollTop + 1);
			var me = this;
			setTimeout(function() {
				me.setPagePos();
				me.setCountPos();
			}, 50)
		},

		/**
		 * @desc 选价目表时，过滤掉其他场景，只留全部。因为选价目表是根据适用范围来判断的，跟场景无关。
		 * @param data
		 */
		parseHeaderData: function (data) {
			if (this.options.apiname === "PriceBookObj") {
				data.templates = _.filter(data.templates, function (item) {
					return item.api_name === 'All'
				})
			}
		},

		isPTable: function () {
			return (this.options.isMultiple && {
				pricebookproduct_salesorderproduct_list: "PTable",
				salesorderproduct_product_list: "PTable",
				price_book_product_quote_lines_list: "PTable",
				product_quote_lines_list: "PTable",
				price_book_product_new_opportunity_lines_list: "PTable",
				product_new_opportunity_lines_list: "PTable",
				target_related_list_pricebookproduct: "PTable" //价目表添加产品
			} [this.options.target_related_list_name]) || "Table";
		},

		// 是否展示分类;
		isShowCategory: function () {
			let apiname = this.options.apiname;
			let tb = this.isPTable();
			if (tb === 'PTable' || (apiname === 'ProductObj' || apiname === 'PriceBookProductObj' || apiname === 'StockObj' || apiname === 'ServiceFaultCategoryObj' || apiname === 'ServiceProjectObj')) {
				this.isShowCat = true;
			}
		},


		////////////////////////////////////////////////////////////////745新加滚动逻辑////////////////////////////////////////////////////////////////
		bindSrollEvent: function () {
			var me = this;
			me.$twrap.off().on('scroll', function (e) {
				me.scrollHandle();
			}).on('mouseenter', '.dt-main', function (e) {
				me.mouseenterHandle(e)
			}).on('mouseleave', '.dt-main', function (e) {
				me.mouseleaveHandle(e)
			})
		},

		resizeFixed: function (debounce = true) {
			var me = this;
			if (!me._resizeFixedHandle) {
				const exec = () => {
					var list = me.table && me.table.table;
					var left = list.$el.offset().left;
					me.__$caption && list.getCaptionWrapper().css({
						left: left
					});
					me.__$header && list.getHeaderWrapper().css({
						left: left
					});
				}
				// 兼容，拖拽改变价目表或者分类区域宽度时，不使用防抖
				me._resizeFixedHandle = debounce ? _.debounce(exec, 250 ) : exec;
			}

			me._resizeFixedHandle();
		},

		scrollHandle: function () {
			var me = this;
			if (!me._scrollHandle) {
				me._scrollHandle = _.debounce(function () {
					var list = me.table && me.table.table;
					if (!list || !list.fixedHeader) return;
					var mainWrapper = list.getMainWrapper();
					var captionWrapper = list.getCaptionWrapper();
					var offset = list.$el.offset();
					// var tp = (me.isFull() ? 20 : 48) + 57;
					var tp = this.$('.t-wrap').offset().top;
					if (offset.top < tp) {
						if (!me.__$caption) {
							captionWrapper.before(me.__$caption = $('<div style="height:56px"></div>'));
							captionWrapper.css({
								top: tp,
								right: parseInt(me.dialog.element.css('right')) + 8,
								left: offset.left,
								zIndex: 605,
								position: 'fixed'
							})
						}
					} else if (me.__$caption && me.__$caption.offset().top > tp - 2) {
						captionWrapper.css({
							position: '',
							top: '',
							right: '',
							left: '',
							zIndex: ''
						})
						me.__$caption.remove();
						me.__$caption = null;
					}

					offset = mainWrapper.offset();
					tp = tp + captionWrapper.height();
					if (offset.top < tp) {
						if (!me.__$header) {
							mainWrapper.before(me.__$header = $('<div style="height:42px"></div>'));
							list.fixedHeader({
								top: tp,
								right: parseInt(me.dialog.element.css('right')) + 16,
								left: offset.left,
								zIndex: 600
							})
						}
					} else if (me.__$header && me.__$header.offset().top > tp - 2) {
						list.fixedHeader();
						me.__$header.remove();
						me.__$header = null;
					}

					me.setCountPos();
				}, 60)
			}

			if (!me._scrollDivHandle) {
				me._scrollDivHandle = _.debounce(function () {
					var list = me.table && me.table.table;
					if (!list) return;
					me._createScrollDiv(list.getMainWrapper());
				}, 60)
			}

			me._scrollHandle();
			me._scrollDivHandle();
		},

		//模拟横向滚动条相关逻辑
		//创建模拟滚动条
		_createScrollDiv: function ($main) {
			if (!this._mainenter) {
				this._destroyScrollDiv();
				return;
			};

			if (this.$twrap.height() + this.$twrap.scrollTop() + 4 > $main.parent().height()) {
				this._destroyScrollDiv();
				return;
			}

			var mw = $main.width();
			var fw = $main.find('.tb:first').width();
			if (mw == fw) {
				this._destroyScrollDiv();
				return;
			};

			if (this._scrollDiv) return;

			var offset = $main.offset();
			// var bm = this.isFull() ? 73 : 101;
			var tWrap = this.$('.t-wrap');
			var bm = $(window).height() - tWrap.offset().top - tWrap.height();
			var tranxY = this.isSupportCount() && this.table && this.table.getRemberData && (this.table.getRemberData() || []).length ? 'transform:translateY(-24px)' : '';
			var $scollDiv = $('<div class="crm-scroll fack-scroll" style="' + tranxY + ';position:fixed;left:' + offset.left + 'px;width:' + mw + 'px;bottom:' + bm + 'px;z-index:200;overflow:auto;"><div style="height:16px;width:' + fw + 'px"></div></div>');
			$main.append($scollDiv);
			var $mainScroll = $main.find('.main-scroll:first');
			var noTrigger;
			$scollDiv.scrollLeft($mainScroll.scrollLeft());
			$scollDiv.on('scroll', function () {
				if (noTrigger) {
					noTrigger = null;
					return;
				}
				$mainScroll.scrollLeft($scollDiv.scrollLeft());
			})
			var fn = _.debounce(function () {
				if (Math.abs($scollDiv.scrollLeft() - $mainScroll.scrollLeft()) > 100) {
					noTrigger = true;
					$scollDiv.scrollLeft($mainScroll.scrollLeft());
				}
			}, 100);
			$mainScroll.on('scroll.list20', fn);

			$scollDiv.$mainScroll = $mainScroll;
			this._scrollDiv = $scollDiv;
		},
		_destroyScrollDiv: function () {
			if (this._scrollDiv) {
				this._scrollDiv.remove();
				this._scrollDiv.$mainScroll.off('scroll.list20');
				this._scrollDiv = null;
			}
		},
		mouseenterHandle: function (e) {
			this._mainenter = true;
			this._createScrollDiv($(e.currentTarget));
		},
		mouseleaveHandle: function () {
			this._mainenter = null;
			this._destroyScrollDiv();
		},

		////////////////////////////////////////////////////////////////920新增选数据支持统计已选数据////////////////////////////////////////////////////////////////
		isSupportCount() {
			if(!this.assertCount()) return;

			let si = this.table && this.table.get('selectedSumField');
			if(!si || !si.length) return;

			return true;
		},
		assertCount() {
			let {actionType, isSupportCount} = this.options;
			return isSupportCount || (actionType === 'batchAdd' && isSupportCount !== false);
		},
		renderCount() {
			if(!this.assertCount()) return;

			let selectedSumField = [];
			let filterColumns = this.table.get('filterColumns');
			_.each(this.table.get('selectedSumField'), fieldName => {
				let tt = _.findWhere(filterColumns, {data: fieldName});
				if (!tt || tt.noRight) return;//没有权限的不用统计
				selectedSumField.push(tt);
			})

			if(!selectedSumField.length) return;

			require.async('crm-modules/components/count/count', Count => {
				if(!this.table) return;

				this._countComp && this._countComp.destroy();
				
				let el = $('<div style="height:24px;text-align:center;"><div style="text-align:center;z-index:10001;height:24px;display:flex;align-items:center;justify-content:center;background:#fff"></div></div>');
				this.table.getMainWrapper().after(el);

				this._countComp = new Count({
					el: el.find('>div')
				})

				this._countComp._selectedSumField = selectedSumField;

				this.updateCount();
			})
		},

		updateCount() {
			if(!this._countComp) return;
			let dataList = this.table.getRemberData() || [];
			if(dataList.length) {
				this._countComp.$el.parent().show();
				this._countComp.render([{
					fields: this._countComp._selectedSumField,
					dataList,
					title: $t('selected_data_summary')
				}])
				
			} else {
				this._countComp.$el.parent().hide();
			}

			this.setCountPos();
		},

		setCountPos() {
			if(!this._countComp || !this.$dialogBtns) return;
			let parent = this._countComp.$el.parent();
			let pos1 = parent.offset();
			let pos2 = this.$dialogBtns.offset();

			let $fix = this._countComp.$el;
			if(pos1.top + 24 > pos2.top) {
				$fix.css({
					position: 'fixed',
					top: pos2.top - 24,
					left: pos1.left + 'px',
					width: parent.width()
				})
			} else {
				$fix.css({
					position: 'initial',
					width: 'initial'
				})
			}
		},

		_getOptions(opt) {
			var optFormatColumns = opt.formatColumns;

			return {
				formatColumns: (columns, apiname) => {
					columns = this._formatColumns(columns, apiname);

					return optFormatColumns ? optFormatColumns(columns, apiname) : columns;
				}
			};
		},

		_formatColumns(columns, apiname) {
			columns = this.addCurrencySignToVirtualField(columns);

			return columns;
		},

		/**
		 * 虚拟字段增加货币符号
		 * @param {Array} columns
		 * @returns
		 */
		addCurrencySignToVirtualField(columns) {
			const virtualFields = [
				'virtual_product_price',
				'virtual_price_book_price',
				'virtual_price_book_selling_price',
				'virtual_base_price_book_price'
			];

			columns.forEach(item => {
				// 虚拟字段
				if (virtualFields.includes(item.api_name)) {
					item.render = function(data, type, full, helper, index, item) {
						return full[`${item.api_name}__n`] || full[item.api_name];
					}
				}
			});

			return columns;
		},

		destroy: function () {
			if(this.__destroyed) return;
			$(window).off('resize.pickselefobject');
			this._fixBody(true);

			this.trigger('destroy');
			this.off();
			this.$twrap && this.$twrap.remove();
			this._detail && this._detail.destroy();
			this.table && this.table.destroy();
			this.dialog && this.dialog.destroy && this.dialog.destroy();
			this.editTable && this.editTable.destroy();
			this.options = this.table = this.dialog = this.submit = null;
			this.dialogTabTable && this.dialogTabTable.destroy();
			this._countComp && this._countComp.destroy();
			this.__destroyed = true;
		}

	}, Backbone.Events);

	PickSelfObject.table = Table;
	PickSelfObject.PTable = require('./table/ptable_extend');
	PickSelfObject.AttrTable = require('./table/attributetable');
	PickSelfObject.AreaManageTable = AreaManageTable;
	PickSelfObject.ProductCollectionTable = ProductCollectionTable;

	module.exports = PickSelfObject;
});
