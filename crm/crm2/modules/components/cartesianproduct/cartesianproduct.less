.crm-comp-dketable {
	.f-group-label {
		font-size: 20px;
		color: #191c21;
		padding-bottom: 16px;
		padding-top: 32px;
		width: 100%;
		.full-table{
			display: inline-block;
			float: right;
			color:#3487e2;
			cursor: pointer;
			font-size: 14px;
		}
	}

	.cp-options-container{
		text-align: center;
		.cp-options-content{
			//max-height: 188px; /* 设置一个合适的最大高度 */
			overflow: hidden;
			transition: max-height 0.3s ease;
			text-align: left;
			&.expanded {
				max-height: none;
			  }
		}
		.spec-toggle-btn{
			background: #F2F4FB;
			color: #181C25;
			border:none;
			padding:6px;
			display: none;
		}
	}

	.cp-options,.cp-selects{
		display: flex;
		flex-wrap: wrap;
		column-gap: 120px;
		.cpopt-wrapper {
			padding-bottom: 16px;
			width: calc((100% - 120px) / 2);
			display: inline-block;
			vertical-align: top;
			position: relative;

			.cpopt-select-wrapper {
				width: 100%;
				height: 38px;
				position: relative;
				display: flex;

				.spec-label {
					font-size: 14px;
					color: #191c21;
					padding-bottom: 4px;
					display: inline-block;
					width: 30%;
					text-align: right;
					box-sizing: border-box;
					line-height: 28px;
					height: 28px;
					// span{
					// 	display: inline-block;
					// 	vertical-align: middle;
					// 	line-height: 28px;
					// }
					.require {
						display: inline-block;
						color: red;
						font-size: 20px;
						vertical-align: middle;
					}
					.name{
						max-width: 80%;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

					}
				}

				&.cpopt-choose{
					padding-left: 0 !important;
					width: 100% !important;
					display: block !important;
					.spec-label {
						width: 100%;
					}
				}

				.cpopt-group-wrapper{
					max-width: 480px;
					flex:1;
					padding-bottom: 8px;
					box-sizing: border-box;
					.cpopt-group {
						display: inline-block;
						padding-right: 32px;
						max-width: 400px;
						width: 100%;
						box-sizing: border-box;
						position: relative;
					}
					.cpopt-del {
						font-size: 16px;
						display: inline-block;
						position: absolute;
						right: 8px;
						top:0;
						margin-top: 6px;
						cursor: pointer;
						&::before {
							color: #333;
						}

						&:hover {
							&::before {
								color: var(--color-primary06);
							}
						}
						&.disabled {
							&::after {
								content: '';
								width: 16px;
								height: 18px;
								position: absolute;
								background: rgba(255, 255, 255, 0.3);
							}

							&:hover {
								cursor: not-allowed;
								background-image: url("../../assets/images/menu/menu-delete.png");
								background-image: -webkit-image-set(url(../../assets/images/menu/menu-delete.png) 1x,url(../../assets/images/menu/<EMAIL>) 2x);
								background-repeat: no-repeat;
							}
						}
					}
				}

			}

			.option-all {
				color: #407fff;
				cursor: pointer;
				text-decoration: none;
				display: inline-block;
				margin-left: 8px;
			}

			.cpopt-options{
				padding-left: 160px;
				.cpopt-area {
					overflow: hidden;
					width: 100%;

					.cpopt-item {
						display: inline-block;
						float: left;
						height: 24px;
						line-height: 24px;
						padding: 0 8px;
						background: #e1e9fa;
						margin-right: 4px;
						margin-top: 4px;
						color: var(--color-neutrals19);
						cursor: pointer;
						border-radius: 4px;
						max-width:200px;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;

						&.active {
							background: #407fff;
							color: var(--color-neutrals01);
						}
						&.disabled {
							background: #7a96cc;
							color: var(--color-neutrals01);
							cursor: not-allowed;
						}
					}

					&.cpopt-area-edit {
						.cpopt-item.disabled {
							background: #7a96cc;
							color: var(--color-neutrals01);
							cursor: not-allowed;
						}
						.cpopt-item.not-usable{
							background-color: #f4f4f5;
							border-color: #e9e9eb;
							color:#bcbec2;
							cursor: not-allowed;
						}
					}
				}
				&.cpopt-choose{
					padding-left: 0;
				}
			}

			&.cpopt-edit {

				overflow: hidden;
				.cpopt-select-wrapper,.cpopt-options,.replace-spec{
					display: inline-block;
					float: left;
				}
				.cpopt-select-wrapper {
					height: 28px;
					width: 128px;
					padding-left: 80px;
					padding-right: 8px;
					.spec-label{
						text-align: left;
						padding:0;
					}
					.cpopt-group-wrapper{
						display: none;
					}
				}
				.cpopt-options{
					box-sizing: border-box;
					width: 60%;
					padding-right: 20px;
				}

				.replace-spec {
					display: inline-block;
					position: absolute;
					width: 16px;
					height: 18px;
					right: 8px;
					top:0;
					margin-top: 6px;
					&:hover{
						color:var(--color-primary06);
						cursor: pointer;
					}
				}
			}
		}
		//单选模式样式
		.spec-single-comp{
			width: 100%;
			display: flex;
			box-sizing: border-box;
			display: flex;
			padding:10px 0 6px 0;
			border-bottom:1px dotted #dee1e8;
			.spec-single-label{
				width: 120px; /* 设置固定宽度 */
				line-height: 24px;
				color:#545861;
				display: inline-block;
				padding-right: 8px;
			}
			.spec-single-values{
				flex: 1; /* 剩余宽度自适应 */
				.btn-spec-item{
					background: #F2F4FB;
					border-radius: 2px;
					margin:0 8px 4px 0;
					padding:0 6px;
					line-height: 26px; /* 将 line-height 值稍微增加 */
					height: 24px;
					color: #181C25;
					display: inline-block;
					border: 0.5px solid #F2F4FB;
					cursor: pointer;
					&.active{
						color: #FF8000;
						background: #FFF7E6;
						border: 0.5px solid #FF8000;
					}
					&.disabled{
						color: #C1C5CE;
						background: #F2F4FB;
						cursor: default;
						pointer-events: none;
					}
				}

			}
		}
		//多选模式样式
		&.spec-multiple-container{
			.cpopt-wrapper{
				width: 100%;
				box-sizing: border-box;
				display: flex;
				padding:10px 0 6px 0;
				border-bottom:1px dotted #dee1e8;
				min-height: 0;
				.cpopt-choose{
					display: inline-block;
					vertical-align: middle;
					&.cpopt-options-container{
						flex: 1; 
					}
					.cpopt-area .cpopt-item{
							background: #F2F4FB;
							border-radius: 2px;
							margin-right: 8px;
							padding:0 6px;
							line-height: 26px; /* 将 line-height 值稍微增加 */
							height: 24px;
							color: #181C25;
							display: inline-block;
							margin:0 8px 4px 0;
							border: 0.5px solid #F2F4FB;
							cursor: pointer;
							&.active{
								color: #FF8000;
								background: #FFF7E6;
								border-color: #FF8000;
							}
							&.disabled{
								color: #C1C5CE;
								background: #F2F4FB;
							}
						
					}
					
				}
				.cpopt-select-wrapper{
					width: 160px !important;
					line-height: 28px;
					height: 28px;
					color:#545861;
					display: inline-block;
					padding-right: 8px;
					.option-all{
						float: right;	
					}
				}
			}
			
			
		}
	}

	.all-spec-wrapper {
		display: block;
		width: 100%;
		padding-top: 8px;
		padding-bottom: 16px;

		.all-spec {
			display: inline-block;
			height: 24px;
			line-height: 24px;
			padding: 0 8px;
			text-decoration: none;
			cursor: pointer;
			background: #407fff;
			color: var(--color-neutrals01);
			border-radius: 4px;

			&.all-cancel {
				background: #e1e9fa;
				color: var(--color-neutrals19);
			}
		}
	}

	.add-spec-wrapper {
		display: block;
		width: 100%;
		padding:4px 0 16px 160px;
		.add-spec {
			color: #407fff;
			cursor: pointer;
			text-decoration: none;
			display: inline-block;
		}
	}

	.dke-table-layout {
		margin-top: 16px;
		border: 1px solid #dee1e6;
		border-radius: 4px;
		margin-bottom: 16px;

		.tit {
			font-size: 14px;
			color: #191c21;
			line-height: 46px;
			border-bottom: 1px solid #dee1e6;
			padding-left: 12px;
			background-color: #f5f7fa;

			.op-right {
				display: inline-block;
				float: right;

				.collapse {
					height: 46px;

					.collapse-item {
						display: inline-block;
						float: left;
						vertical-align: middle;
						line-height: 46px;
					}

					.sku-checkbox-item {
						width: 16px;
						height: 16px;
						margin-top: 15px;
						margin-right: 8px;
						background: url("@{imgUrl}/checkbox.png") no-repeat;
						background-position: 100% 0;

					}

					.label {
						font-size: 12px;
						margin-right: 4px;
					}

					.help {
						width: 16px;
						height: 16px;
						position: relative;
						line-height: 16px;
						text-align: center;
						color: var(--color-neutrals01);
						margin-top: 15px;
						border-radius: 50%;
						background: var(--color-special02);

						.help-text {
							position: absolute;
							top: 110%;
							left: -120px;
							display: none;
							background: #212b36;
							color: var(--color-neutrals01);
							font-size: 12px;
							line-height: 20px;
							padding: 5px 8px;
							width: 240px;
							text-align: left;
							z-index: 999;
						}

						&:hover {
							.help-text {
								display: block;
							}
						}
					}

					&.unflod {
						.sku-checkbox-item {
							background: url("@{imgUrl}/checkbox.png") no-repeat;
							background-position: -6% 50%;
						}
					}
				}
			}
		}

		.md-handle {
			font-size: 14px;
			color: #191c21;
			line-height: 46px;
			padding: 0 12px;

			.m-count {
				font-size: 14px;
				margin-right: 8px;
				color: #cfd1d5;
			}
			.del-btn{
				display: none;
				color: #3D83E9;
				cursor: pointer;
				font-size: 14px;
			}
		}

		.crm-table {
			.dt-main{
				height: auto ;
				min-height: 0px !important;
				.main{
					height: auto ;
					min-height: 0px !important;
				}
			}

			.tr-readonly,
			.tr-removed {
				background: #ebedf2;
			}
			.tb-b{
				.tr-readonly,
				.tr-removed {
					cursor: not-allowed;
					.tb-cell {
						pointer-events: none;
						.td-cell-edit:hover{
							border:none;
							box-shadow: none;
							background: none;
						}
					}
				}
			}
			.fix-end-b{
				.tr-readonly{
					cursor: not-allowed;
					.tb-cell {
						pointer-events: none;
					}
				}
			}

			.dt-caption, .dt-term-batch{
				display:none;
			}
		}
	}

	.loading-wrapper {
		background: rgba(255, 255, 255, 0.3);
		display: none;
		.dketable-loading {
			width: 140px;
			height: 70px;
			border: 1px solid #ddd;
			background: var(--color-neutrals01) url("@{imgUrl}/table/loading.gif") no-repeat 50%;
			line-height: 60px;
			font-size: 16px;
			text-align: center;
			#css3 > .radius(5px);
			position: absolute;
			left: 50%;
			top:50%;
			margin: -15px 0 0 -70px;
			z-index: 10;
		}
	}

	.js-btn{
		color:#3487E2;
	}
}
.crm-full-cartesian{
	.cp-options,.cp-selects,.add-spec-wrapper{
		display: none;
	}
}
.spu-replace-spec{
	.spec-info{
		background:#fff8ec;
		padding:8px;
		line-height: 22px;
		font-size: 12px;
		border:1px solid #ffab00;
		color:var(--color-neutrals19);
		i{
			color:var(--color-primary06);
			margin-right: 4px;
		}
	}
	.spec-name{
		margin:20px 0;
		font-size: 14px;
		line-height: 22px;
		color:var(--color-neutrals19);
	}
	.content-wrapper{
		overflow: hidden;
		padding:16px 0 10px;
		border-top:1px solid #dee1e6;
		.select,span{
			display: inline-block;
			vertical-align: middle;
		}
		span{
			line-height: 36px;
			padding-right: 8px;
		}
		.add-spec{
			color: var(--color-primary06);
			cursor: pointer;
			font-size: 12px;
			i{
				font-size: 14px;
				margin-right: 4px;
			}
		}

	}
	.select-item{
		float: left;
		margin-right: 20px;
	}
}
