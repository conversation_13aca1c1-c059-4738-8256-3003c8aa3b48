/**
 * @desc 编辑型表格
 * <AUTHOR>
 * @date 2022/07/07
 */
 define(function(require, exports, module) {
    //编辑队列
    const EditQueue = {};

    const editTask = require('./edittask/index');
    const format = require('./format');
    const batchEdit = require('./edittask/batchedit');

    return Backbone.View.extend({
        options: {
            objectApiname: '',
            autoHeight: true, //高度随数据自从撑开
            maxHeight: $(window).height() - 275, //配合autoHeight 超出后显示滚动条
            showMultiple: true, //显示chekc框 没有理由修改
            zIndex: 1000, //层级
            editUUID: '',
            beforeEdit() {},
            complete() {},
            checkHandle() {}
            // columns: [{
            //     data: 'name',
            //     dataType: 'text',
            //     isEdit: true,
            //     fixed: true,
            //     noSupportBatchEdit: true,
            //     title: $t('所属对象'),
            //     render: function(data) {
			// 		return '<div class="ssss" style="color:green">' + data + '</div>';
            //     }
            // }, {
            //     data: 'title',
            //     dataType: 'text',
            //     help_text: '测试提示',
            //     title: '',
            //     titleHtml: '<span style="color:red">标题自定义</span>',
            //     width: 400,
            //     fixed: true,
            //     isEdit: true,
            //     noLock: true,
            //     render: function(data) {
			// 		return '<div class="ssss" style="color:green">' + data + '</div>';
            //     }
            // }, {
            //     data: 'create_time',
            //     dataType: 'date_time',
            //     isEdit: true,
            //     isOrderBy: true,
            //     title: $t('创建时间'),
            //     render: function(data) {
			// 		return '<div class="ssss" style="color:green">' + data + '</div>';
            //     }
            // }, {
            //     data: null,
            //     width: 80,
            //     title: $t("操作"),
            //     lastFixed: true
            // }]
        },

        initialize: function(opts) {
            this.cacheCellsStatus = {};
            this.render();
        },

        formatTitle: function(formatVal, column, data) {
            let fieldName = column.data, vv = data[fieldName];
            let type = column.quote_field_type || column.type;

            if(this.isNull(vv)) return '';

            if(fieldName === 'relevant_team') return _.escape(data.relevant_team__r || '');//相关团队
            if(type === 'html_rich_text' || type === 'rich_text') return _.escape(data[fieldName + '__o']); //富文本

            let title = '';
            switch(type) {
                case 'long_text':
                    title = _.escape(vv || '');
                    break;
                case 'text':
                case 'currency':
                case 'email':
                case 'url':
                case 'number':
                case 'percentile':
                case 'phone_number':
                case 'object_reference':
                case 'master_detail':
                case 'location':
                case 'employee':
                case 'employee_many':
                case 'department':
                case 'department_many':
                case 'select_one':
                case 'select_many':
                case 'date':
                case 'date_time':
                case 'time':
                    title = formatVal;
            }

            return title;
        },

        render() {
            let me = this;
            let {autoHeight, maxHeight, height, showMultiple, zIndex = 1000, columns, objectApiname, insertMuenFn, disabledcfg, forceTrWrap} = this.options;
            let lockColumn = CRM._cache && CRM._cache.CRM_TABLE_CONFIG && CRM._cache.CRM_TABLE_CONFIG.columnlock && CRM._cache.CRM_TABLE_CONFIG.columnlock[objectApiname];
            _.each(columns, column => {
                let ls = lockColumn && lockColumn[column.data]
                let ty = column.type;
                column.customFixed = ls;
                if(!column.fixed) {
                    column.fixed = ls;
                }
                column.dataType = ty;

                if(ty !== 'date_time') {
                    column.date_format = '';//不支持日期格式化
                }
                
                if(column.render) return;

                if(ty === 'signature') {
                    return column.render = (formatData, type, trData) => {
                        let vv = trData[column.data]
                        return this.isNull(vv) ? column.isEdit ? `<a href="javascript:;">${$t('添加签名')}</a>` : '--' : format(vv, column, trData);
                    }
                }
                
                //图片 附件 大附件先走底层
                if(column.quote_field_type === 'image') {
                    column.dataType = 9;
                    return;
                }
                if(ty === 'image' || ty === 'file_attachment' || ty === 'big_file_attachment') return;

                column.render = function(data, type, trData) {
                    let fv = trData[column.data + '__s'] || format(trData[column.data], column, trData);
                    return `<div class="crm-comp-edittable-cell" title="${me.formatTitle(fv, column, trData)}">${fv}</div>`;
                }
            });

            require.async('crm-widget/table/table', Table => {
                this.helper = Table.helper;
                this.table = new Table({
                    $el: this.$el,
                    searchTerm: null, //默认都是true 没有理由修改
                    showPage: false,//是否需要显示分页 默认都是false 没有理由修改
                    showHelpTip: true, //显示表头字段提示信息
                    showMoreBtn: false, //默认都是false 没有理由修改
                    showFilerBtn: false, //默认都是false 没有理由修改
                    openStart: true, //默认都是true 没有理由修改
                    doStatic: true, //表格为静态数据，不请求server 默认true 没有理由修改
                    noCalculate: true, //默认都是true 没有理由修改
                    showRequiredTip: true,//默认都是true 没有理由修改
                    noAllowedWrap: true,
                    editable: true,
                    isMyObject: true,
                    scrollLoad: true,
                    minContentWidth: 100,
                    sizeType: 'md', //表格行高 没有理由修改
                    noDataStyle: 'small', //表格无数据时的样式 没有理由修改
                    autoHeight,
                    maxHeight,
                    height,
                    showMultiple,
                    zIndex,
                    columns,
                    objectApiname,
                    insertMuenFn,
                    disabledcfg,
                    forceTrWrap,
                    getTrClassName: (data) => {
                        return _.findWhere(this.cacheCellsStatus[data.rowId], {error: true}) ? 'tr-error' : '';
                    },
                    getTdClassName: (data, column) => {
                        let status = this.cacheCellsStatus[data.rowId];
                        let cs = status && status[column.data];
                        if(!cs) return '';

                        //{hide: true | false, require: true| false, readonly: true | false, error: true | false}
                        let cls = [];
                        _.each(cs, (flag, k) => {
                            if(!flag) return;
                            cls.push('td-status-' + k);
                        })

                        return cls.join(' ');
                    },
                    getTdId: (data, column) => {
                        return 'td_' + data.rowId + '_' + column.data;
                    },
                    initComplete: () => {
                        this.options.complete();
                    },
                    beforeEditFn: _.bind(_.debounce(this._beforeEditCellHandle, 50), this), //单元格触发编辑之前的回调
                    beforeDelCellFn: _.bind(_.debounce(this._beforeCleanCellHandle, 50), this),//清除单元格之前的回调
                    beforeBatchEdit: columnName => this._beforeBatchEditHandle(columnName)
                })

                this._bindTableEvents();
            })
        },

        updateOpereateWidth(width) {
            this.table.updateOpereateWidth(width);
        },

        _beforeEditCellHandle(opts, next) {
            if(opts.$target && opts.$target.closest('.td-status-readonly').length > 0) {
                this.options.readOnlyCellEdit && this.options.readOnlyCellEdit({
                    fieldName: opts.column.data,
                    rowId: opts.data.rowId,
                    $target: opts.$target,
                    e: opts.e
                })
                return;
            };

            let me = this;
            next && next(false);
            let {$target, data} = opts;
            let field = opts.column;
            let type = field.type;

            let {editUUID, beforeEdit, noSupportPaste, aiUserLicense} = this.options;
            let eq = EditQueue[editUUID] || (EditQueue[editUUID] = {});
            if(eq.cur) {
                eq.waiter = opts;
                eq.cur.getValue && eq.cur.getValue();
                return;
            }
            eq.waiter = null;

            if(!$target.length) return

            eq.cur = true;
            
            let rowId = data.rowId;
            beforeEdit({
                e: opts.e,
                $target,
                data,
                field,
                rowId,
                type,
                next(editField, extParam) {
                    if(editField === false) {
                        eq.cur = false;
                        if(eq.waiter) {
                            me._beforeEditCellHandle(eq.waiter);
                            eq.waiter = null;
                        }
                    }
                    return {
                        then(resolve) {
                            eq.cur = editTask({
                                $target,
                                field: me._sortEditField(editField || field, rowId),
                                data,
                                type,
                                noSupportPaste,
                                extParam,
                                isSelect: me._isSeleted(field, opts.e, $target),
                                aiUserLicense: aiUserLicense,
                                callback(changeData, exOpts = {}) {
                                    eq.cur && (eq.cur.destroy && eq.cur.destroy(), eq.cur = null);
                                    if(changeData && !me.diffCellData(changeData, data)) changeData = void 0;
                                    resolve({
                                        changeData,
                                        exOpts,
                                        field,
                                        next() {
                                            eq.cur = null;
                                            if(exOpts.keyCode) {
                                                me.keyDownHandle({
                                                    keyCode: exOpts.keyCode,
                                                    fieldName: field.data,
                                                    rowId
                                                })
                                            } else if(eq.waiter) {
                                                me._beforeEditCellHandle(eq.waiter);
                                            }
                                        }
                                    })
                                }
                            })
                        }
                    }
                }
            })
        },

        diffCellData(nData, oData) {
            let flag;
            _.find(nData, (v, k) => {
                if(/__r|__l$/.test(k)) return;
                let vv = oData[k];
                let vnull = this.isNull(v);
                let vvnull = this.isNull(vv);
                if(!(vnull && vvnull)) {
                    if(vnull === vvnull) {//两个都有值
                        if(_.isArray(v)) { //数组对比
                            let tmp = v[0];
                            if(tmp && tmp.path) { //图片附件签名
                                v = _.pluck(v, 'path');
                                vv = _.pluck(vv, 'path');
                            }
                            flag = v.length !== vv.length || (_.union(v, vv).length !== v.length);
                        } else {
                            flag = v != vv;
                        }
                    } else { //或者其中一个有值
                        flag = true
                    }
                }
                return flag;
            })

            return flag;
        },

        //判断当前单元格是否只读
        assertColumnIsReadOnly(column = {}, rowId) {
            if(!column.isEdit) return true;
            let status = (this.cacheCellsStatus[rowId] || {})[column.data];
            return status && (status.readonly || status.hide);
        },

        _sortEditField(editField, rowId) {
            if(editField && _.isArray(editField)) {
                let nEditField = [];
                _.each(this.getAllColumns(), a => {
                    let tt = _.findWhere(editField, {api_name: a.data});
                    if(tt) {
                        let tmp = {...tt};
                        if(rowId) {
                            tmp.is_readonly = this.assertColumnIsReadOnly(a, rowId);
                        }
                        nEditField.push(tmp);
                    }
                })
                return nEditField;
            } else {
                return editField;
            }
        },

        _isSeleted(field, $event, $target) {
            if(!_.contains(['object_reference', 'object_reference_many'], field.type)) return;
            return $event && $event.clientX > $target.width() + $target.offset().left;
        },

        _bindTableEvents() {
            this.table.on('trclick', (...args) => this.trclickHandle(...args));
            this.table.on('staticparam.change', (param) => this.staticParamChangeHandle(param));
            this.table.on('tb.scroll', () => this.scrollHandle());
            this.table.on('checkbox.click', (...args) => this.checkClickHandle(...args));
            this.table.on('tb.resize.width', (data) => this._saveColumnWidth(data));
            this.table.on('column.lockChange',  (column, status) => this._saveLockStatus(column, status));
            this.table.on('cell.change',  (...args) => this._cellChangeHandle(...args));
		},

        _cellChangeHandle(data, column, type, opts) {
            try {
                if(!_.contains(['image', 'file_attachment'], column.type)) return;
                let editField = column.data;
                data = {
                    [editField]: data
                }
                this.options.afterBatchEditHandle({
                    changeData: {
                        [opts.cellData.rowId]: data
                    },
                    data,
                    editField,
                });
            } catch(e) {}
        },

        //更新列锁定
        _saveLockStatus(column, status) {
            let cc = CRM._cache && CRM._cache.CRM_TABLE_CONFIG;
            if(!cc || !cc.columnlock) return;
            let tmp = cc.columnlock[this.options.objectApiname];
            if(!tmp) {
                tmp = cc.columnlock[this.options.objectApiname] = {};
            }
            tmp[column.data] = !!status;

            let value = JSON.stringify(cc);
            if(!value) return;

            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/personal_config/service/save',
                data: {
                    key: 'crm.table.config',
                    value
                }
            }, {
                errorAlertModel: 1
            })

            //本地也存一份 一个版本后不再存server直接用本地存的数据
            try {
                FS.store && FS.store.setItem('crm.table.columnlockconfig', value);
            } catch(e) {}
        },

        //更新列宽
        _saveColumnWidth(data) {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/custom_scene/service/createOrUpdateFieldWidthConfig',
                data: {
                    field_list: [{
                        field_name: data.column.api_name,
                        width: data.width
                    }],
                    object_describe_api_name: this.options.objectApiname
                },
                success() {}
            }, {
                errorAlertModel: 1
            })
        },

        getPosByRowId(rowId) {
            let pos;
            _.find(this.getTableData(), (a, index) => {
                if(a.rowId === rowId) {
                    pos = index;
                    return true;
                }
            })
            return pos;
        },

        getDataByOffsetPos(rowId, offsetPos, isTile) {
            let pos = this.getPosByRowId(rowId);
            let list = this.getTableData();
            let item = list[pos + offsetPos];

            if(isTile && !item) return list[pos];

            this.findTr(rowId).removeClass('tr-hover tr-edit');

            if(item) {
                this.findTr(item.rowId).addClass('tr-hover tr-edit');
                let $scroll = this.$('.main .main-scroll');
			    $scroll.scrollTop($scroll.scrollTop() + offsetPos * 34);
            }
            
            return item;
        },

        getDataByRowId(rowId) {
            return _.find(this.getTableData(), a => a.rowId == rowId);
        },

        getCheckedData() {
            return this.table.getCheckedData();
        },

        getTableData() {
            return this.table && this.table.getCurData() || [];
        },

        getAllColumns() {
            return this.table.getAllColumns()
        },

        getColumn(data) {
            return _.findWhere(this.table.getAllColumns(), {data: data});
        },

        simplySetCheckStatus(pos, status) {
            this.table.simplySetCheckStatus(pos, status);
        },

        cleanHalfChecked(pos, $checkItem) {
            this.table.cleanHalfChecked(pos, $checkItem);
        },

        _beforeCleanCellHandle(obj) {
            this.options.beforeCleanCellHandle({
                rowId: obj.cellData.rowId,
                changeData: obj.changeData,
                fieldName: obj.delColumn.data
            });
        },

        _beforeBatchEditHandle(columnName) {
            let me = this;
            let column = this.getColumn(columnName);
            if(!column) return;

            let data = this.getTableData();
            if (!data.length) {
                FxUI.Message({
                    isMiddler: true,
                    duration: 1500,
                    message: $t('无可编辑数据'),
                    type: 'warning'
                })
				return false;
			}

            let {beforeBatchEditHandle, objectApiname} = this.options;
            if(beforeBatchEditHandle) {
                beforeBatchEditHandle({
                    field: column,
                    next(opts) {
                        nfield = me._sortEditField(opts.fields);
                        batchEdit({
                            zIndex: opts.zIndex,
                            field: nfield, 
                            fieldLabel: 
                            column.title,
                            requireFields: me.__getRequiredFields(),
                            masterData: opts.masterData,
                            objectApiname
                        }).then(res => {
                            me.options.afterBatchEditHandle(me.__handleBatchRes(res));
                        })
                    }
                })
            } else {

            }

            return false;
        },
        __handleBatchRes(res) {
            let cd = this.getCheckedData();
            let datas = cd && cd.length ? cd : this.getTableData();
            
            let changeData = {};
            _.each(datas, item => {
                let tt = {};
                _.each(res.data, (v, k) => {
                    if(!this.assertColumnIsReadOnly(this.getColumn(k), item.rowId)) {
                        if(res.isUp) {
                            let v1 = (item[k] || 0) + '';
                            let v2 = v + '';
                            let dl = _.max([(v1.split('.')[1] || '').length, (v2.split('.')[1] || '').length]);
                            tt[k] = (v1 * 1 + v2 * 1).toFixed(dl);
                        } else {
                            tt[k] = v;
                            let vo = res.data[k + '__o'];
                            let vr = res.data[k + '__r'];
                            let vl = res.data[k + '__l'];
                            if(vo !== void 0) {
                                tt[k + '__o'] = vo;
                            }
                            if(vr !== void 0) {
                                tt[k + '__r'] = vr;
                            }
                            if(vl !== void 0) {
                                tt[k + '__l'] = vl;
                            }
                        }
                    }
                })

                changeData[item.rowId] = tt;
            })

            res.changeData = changeData;

            return res;
        },
        __getRequiredFields() {
            let tmp = {}
            _.each(this.getAllColumns(), a => a.isRequired && (tmp[a.data] = true));
            return tmp;
        },

        batchEdit(opts) {
            if(!(opts.fields || []).length) {
                FxUI.Message({
                    isMiddler: true,
                    duration: 1500,
                    message: $t('无可编辑列！'),
                    type: 'warning'
                })
				return;
            }
            batchEdit({
                requireFields: this.__getRequiredFields(),
                showContinue: opts.showContinue,
                allFields: opts.fields,
                zIndex: opts.zIndex,
                objectApiname: this.options.objectApiname
            }).then(res => {
                this.options.afterBatchEditHandle(this.__handleBatchRes(res));
            })
        },

        trclickHandle() {
            this.options.trclickHandle && this.options.trclickHandle(...arguments);
        },

        scrollHandle() {
            // let {editUUID} = this.options;
            // let editIntance = EDITINSTANCE[editUUID] && EDITINSTANCE[editUUID].cur;
            // editIntance && editIntance.updatePos();
        },

        checkClickHandle(isChecked, $checkItem, a, b, isAll, pos) {
            this.options.checkHandle({isChecked, $checkItem, isAll, pos});
        },

        sortByDatas(datas) {
            this.table.sortByDatas(datas);
            this.options.afterSortList && this.options.afterSortList(datas);
        },

        setCheckStatusByRowIds(rowIds, isUncheck) {
            this.table.setCheckStatusByPos(_.map(rowIds, rowId => this.getPosByRowId(rowId)), isUncheck);
        },

        clearClecked() {
            this.table && this.table._clearChecked();
        },

        isNull(a) {
            return a === void 0 || a === '' || a === null || (_.isObject(a) && _.isEmpty(a));
        },

        resize() {
            this.table.resize();
        },

        //查找行
        findTr(rowId, isTb, isFirst) {
            let pos;
            if(_.isArray(rowId)) {
                pos = _.map(rowId, a => this.getPosByRowId(a))
            } else {
                pos = this.getPosByRowId(rowId);
            }
            return this.table.findTrs(pos, isTb, isFirst);
        },

        //查找单元格
        findTd(rowId, columnName) {
            return this.$(`#td_${rowId}_${columnName}`);
        },

        //隐藏显示指定行的checkBox
        toggleCheckBoxByDatas(rowIds, status) {
            rowIds.length && this.table.toggleCheckBoxByPos(_.map(rowIds, rowId => this.getPosByRowId(rowId)), !!status);
        },

        //设置单元格状态
        /**
		 * @desc 修改单元格状态
		 * @param rowId {{String}} 行数据id
		 * @param cellStatus {{Object}}
		 * @example setCellsStatus('xxxxx', {name: {hide: true | false, require: true| false, readonly: true | false, error: true | false}});
		 */
		setCellsStatus(rowId, cellStatus) {
            let t = this.cacheCellsStatus[rowId];
            if(!t) {
                t = this.cacheCellsStatus[rowId] = cellStatus;
            } else {
                _.each(cellStatus, (status, cellName) => {
                    let tt = t[cellName]
                    if(tt) {
                        _.extend(tt, status)
                    } else {
                        t[cellName] = status;
                    }
                })
            }

            _.each(cellStatus, (status, cellName) => {
                if(!this.getColumn(cellName)) return;

                let $td = this.findTd(rowId, cellName);
                let rc = [], ad = [];
                _.each(status, (v, k) => {
                   (v ? ad : rc).push('td-status-' + k);
                    
                   if((k === 'hide' && v) || (k === 'require' && !v)) {
                        this.removeTdError(rowId, cellName);
                   }
                })

                rc.length && $td.removeClass(rc.join(' '));
                ad.length && $td.addClass(ad.join(' '));
            })

            let _cc = {};
            _.each(t, (c, f) => {
                let _c = {};
                if(c.require) _c.require = true;
                if(c.require === false) _c.notrequire = true;

                if(c.readonly) _c.readonly = true;
                if(c.require === false) _c.notreadonly = true;

                if(c.hide) _c.hide = true;
                if(c.hide === false) _c.show = true;

                _cc[f] = _c
            })

            let trData = this.getDataByRowId(rowId) || {};
            trData._cellStatus = _cc;


            //设置行的错误状态
            this.toggleTrError(rowId);
		},

        //---------------------数据 增 删 改 处理 start---------------------//
        //全量刷新当前数据
        doStaticData(data = []) {
            this.table.doStaticData(data);
        },

        //添加数据
        addRow(data) {
            this.table.addRow(data);
        },

        //更新行数据
        updateRow(item, extKeys) {
            let datas = this.getTableData();
            let opts = {};
            _.each(datas, (a) => {
                let obj = item[a.rowId];
                if(obj) {
                    let tobj = this._diff(obj, a);
                    let keys = _.union(_.keys(tobj), extKeys || []);
                
                    keys.length && (opts[a.rowId] = {cells: keys, data: _.extend(a, obj)});
                }
            })
            this.setCellsVal(opts);
        },

         //仅更新前后有数据变化的
         _diff(d, l) {
            let tobj = {};
            let columns = this.getAllColumns();
            let types = ['number', 'percentile', 'currency', 'text', 'formula'];
            _.each(d, (v, k) => {
                if(k === 'record_type' || k === 'object_describe_api_name' || k === 'rowId') return;
                if(this.isNull(v) && this.isNull(l[k])) return;
                let tt = _.findWhere(columns, {data: k});
                if(!tt) {
                    l[k] = v;
                    return;
                }
                if(_.contains(types, tt.return_type || tt.render_type || tt.type) && (v === l[k])) return;

                tobj[k] = v;
            })

            return tobj;
        },

        //在指定的行下面插入数据
        //data: [{insertRow: {....}, datas: [{},....]}]
        insertRow(data) {
            let list = this.getTableData();
            _.each(data, (a) => {
                let pos;
                _.find(list, (b, i) => {
                    if(b.rowId === a.insertRow.rowId) {
                        pos = i;
                        return true;
                    }
                })
                this.table.insertRow(a.datas, {
                    pos: pos,
                    isBefore: a.isBefore
                })
            })
        },

        //删除行
        delRow(rowIds) {
            if(!rowIds) return;
            let index = [];
            _.each(this.getTableData(),(a, pos) => {
                _.contains(rowIds, a.rowId) && index.push(pos);
            })
            _.each(rowIds, rowId => delete this.cacheCellsStatus[rowId]);

            index.length && this.table.delRow(index);

            this._triggerErrorNum();
        },

        // 设置单元格的值
		// opts: {rowId: {cells: ['name', 'sex'], data: {name: 112, sex: 2}}}
		setCellsVal(opts) {
            _.each(opts, (item, rowId) => {
                _.each(item.cells, columnName => {
                    let column = this.getColumn(columnName);
                    if(!column) return;
                    let $td = this.findTd(rowId, column.data).find(column.isEdit ? '.td-cell-edit' : '.tb-cell');
                    this.removeTdError(rowId, columnName);
                    if(column.cellsRender) return column.cellsRender($td, item.data, this.helper.getFormatVal(item.data[column.data], column, 'default', item.data));

                    let vv = column.render(item.data[column.data], column, item.data);
                    $td.html(vv);
                })

                this.toggleTrError(rowId, true);
            })
		},
        //---------------------数据 增 删 改 处理 end---------------------//


        //---------------------错误处理start---------------------//
        //移除指定单元格的错误
        removeTdError(rowId, columnName, $td) {
            let status = (this.cacheCellsStatus[rowId] || {})[columnName];
            if(!status || !status.error) return;

            status.error = false;
            $td = $td || this.findTd(rowId, columnName);
            $td.removeClass('td-status-error');
        },

        //根据当前行上是否存在错误决定显示隐藏行的错误样式
        toggleTrError(rowId) {
            let tt = _.find(this.cacheCellsStatus[rowId], t => t.error || t.error === false);
            if(!tt) return;

            let hasError = _.findWhere(this.cacheCellsStatus[rowId], {error: true});

            this.findTr(rowId)[hasError ? 'addClass' : 'removeClass']('tr-error');
            this._triggerErrorNum();
        },

        //执行错误数据回调，如果有的话
        _triggerErrorNum() {
            if(!this.__triggerError) {
                this.__triggerError = _.debounce(() => {
                    let num = 0;
                    _.each(this.cacheCellsStatus, status => {
                        _.findWhere(status, {error: true}) && num++;
                    });
                    this.options.errorHandle && this.options.errorHandle(num);
                }, 50)
            }
            this.__triggerError();
        },

        //{rowId: ['name', 'sex']}
        toggleValidError(errorTrs, flag) {
            _.each(errorTrs, (columnNames, rowId) => {
                let status = {}
                _.each(columnNames, columnName => {
                    status[columnName] = {error: !flag};
                })
                
                this.setCellsStatus(rowId, status);
            })
        },
        //---------------------错误处理end---------------------//

        //---------------------排序 start---------------------//
        staticParamChangeHandle(param) {
            this.sortByFieldName(param.SortField, !(param.SortType == 2));
        },

        sortByFieldName(fieldName, isAsc) {
            let datas = this.getTableData();
            if(!datas || !datas.length || !this.table.sortByDatas ) return;
    
            let list = this.sortList(datas, fieldName, isAsc);
            if(list && list.length) {
                this.sortByDatas(list);
            }
        },
    
        sortList(datas, fieldName, isAsc) {
            let field = this.getColumn(fieldName);
            if(!field) return;
            let rt = field.return_type || field.quote_field_type || field.type;
            let arr = [];
            let nullList = [];
            _.each(datas, item => {
                (this.isNull(item[fieldName]) ? nullList : arr).push(item);
            })
    
            let list;
    
            if(_.contains(['number', 'currency', 'date', 'date_time', 'time', 'percentile'], rt)) { //数值类型按大小
                list = _.sortBy(arr, a => {
                    if(field.type === 'quote' && _.contains(['date', 'date_time', 'time'], rt)) {
                        return new Date(a[fieldName]).getTime();
                    } else {
                        return a[fieldName] * 1;
                    }
                })
            } else if(_.contains(['select_many', 'image', 'file_attachment'], rt)) { //多选类型的按选中的个数进行排序
                list = _.sortBy(arr, a => a[fieldName].length)
            } else if(rt === 'object_reference' && field.type !== 'quote') { //引用类型按__r
                list = _.sortBy(arr, a => a[fieldName + '__r'])
            } else { //其余的按默认的规则排序
                list = _.sortBy(arr, a => a[fieldName])
            }
    
            [].push.apply(list, nullList);
            isAsc || (list = list.reverse());

            if(this.options.beforeSortList) {
                list = this.options.beforeSortList(list);
            }
    
            return list;
        },
        //---------------------排序 end---------------------//

        //历史遗留快捷键功能，方向键控制编辑
        keyDownHandle(opts) {
            let {keyCode, rowId, fieldName} = opts;
            if(!keyCode) return;
            let me = this;
            let columns = me.getAllColumns(), column = me.getColumn(fieldName);
            let columnIndex =  _.indexOf(columns, column);
            let pos = me.getPosByRowId(rowId);
            if(pos === void 0) return;

            let list = me.getTableData();
            let totalNum = list.length;
            let columnNum = columns.length;

            let innerFn1 = function() {
                let isBreak;
                let tc = columns[columnIndex];
                if(!me.assertColumnIsReadOnly(tc, rowId) && _.contains(['text', 'long_text', 'email', 'url', 'number', 'currency', 'percentile', 'phone_number'], tc.type)) {
                    me.table.scrollToTd20(tc.data);
                    setTimeout(() => me.findTd(rowId, tc.data).find('.td-cell-edit').trigger('click'), 150);
                    isBreak = true;
                }
                return isBreak;
            }
            let innerFn2 = function() {
                let isBreak;
                let td = list[pos] || {};
                if(!me.assertColumnIsReadOnly(column, td.rowId)) {
                    setTimeout(function() {
                        me.findTd(td.rowId, fieldName).find('.td-cell-edit').trigger('click');
                        me.table.scrollToTr20(pos);
                    }, 50);
                    isBreak = true;
                }
                return isBreak;
            }

            switch(keyCode) {
                case 37: //←
                    while(columnIndex > 0) {
                        if(innerFn1(--columnIndex)) break;
                    }
                    break;
                case 38: //↑
                    while(pos > 0) {
                        if(innerFn2(--pos)) break;
                    }
                    break;
                case 39: //→
                    while(columnIndex < columnNum) {
                        if(innerFn1(++columnIndex)) break;
                    }
                    break;
                case 40: //↓
                    if(pos === totalNum - 1) {
                        me.options.addOneRowHandle && me.options.addOneRowHandle().then(() => {
                            innerFn2(++pos);
                        })
                    } else {
                        while(pos < totalNum) {
                            if(innerFn2(++pos)) break;
                        }
                    }
            }
        },

        hideColumn(fieldName) {
            this.table.hideColumn_new(fieldName);
        },

        showColumn(fieldName) {
            this.table.showColumn_new(fieldName);
        },

        destroyEQ() {
            try {
                let eq = EditQueue[this.options && this.options.editUUID];
                if(eq) {
                    eq.cur && (eq.cur.destroy && eq.cur.destroy(), eq.cur = null);
                    delete EditQueue[this.options.editUUID];
                }
            } catch(e) {
                console.error(e);
            }
        },
        destroy() {
            this.destroyEQ();
            this.table && (this.table.destroy(), this.table = null);
            this.off();
            this.$el = this.options = this.cacheCellsStatus = null;
        }
    })
});