/**
 *  @desc  创建分组dialog
 *  <AUTHOR>
 *  @date 2019/3/14
 */
define(function (require, exports, module) {
    var util = CRM.util;

    module.exports = Backbone.View.extend({

        options: {
            fields: null,
			defNumber: 1,              // 默认序号
			data: null,                // 填充分组信息
			type: 'add',               // add: 新建分组 ; edit:编辑分组
			showAddSubBtn: true,      // 是否显示确定并添加子产品按钮
			currentRow: [],     	  // 点击的当前行数据
			table: {},
			pData: {},				 // 父级数据,过滤掉分组
            parentData: {}          // 父级节点
        },

        initialize: function () {
            this.oldName = null;
            if (this.options.type === 'edit') {
                this.oldName = this.options.data.name;
            }
            this.dialogView();
        },

		useOldDialog:true,

		/**
		 * @desc 加载分组dialog
		 */
        dialogView: function () {
            var _this = this;

            require.async('crm-modules/action/field/field', function (field) {
                
                function getFields(fields) {
                    return fields.reduce((obj, item) => {
                        _this.options.fields[item] && (obj[item] = _this.options.fields[item]);
                        return obj;
                    }, {});
                }
                
                var fields = getFields(['name', 'order_field', 'group_options_control', 'enabled_status']);
                var fields2 = getFields(['min_prod_count', 'max_prod_count']);
                var fields3 = {
                    is_required: {
                        api_name: 'is_required',
                        label: $t('是否必选'),
                        type: 'true_or_false',
                        is_required: true,

                    },
                };
                var fields4 = getFields(['min_amount_count', 'max_amount_count']);
                var btns = [
                    {
                        label: $t("确定"),
                        action: 'submit_addContact',
                        isMain: true
                    },
                    {
                        label: $t("取 消"),
                        action: 'cancel',
                    },
                ];

                if (_this.options.showAddSubBtn) {
                    btns.unshift({
                        label: $t("确定并添加部件"),
                        action: 'submit',
                        isMain: true
                    })
                }
                if (_this.options.type === 'add') {
                    _this.options.data = Object.assign(_this.options.data || {}, {
                        group_options_control: false,
                        enabled_status: _this.options.parentData ? _this.options.parentData.enabled_status : true
                    })
                }

                // 编辑态，单选且最少可选个数为1时，is_required为必选；否则，非必选
                if (_this.options.data) {
                    _this.options.data.is_required = (_this.options.data.group_options_control && _this.options.data.min_prod_count == 1) ||
                        !_this.options.data.group_options_control
                }

                field.add({
                    title: _this.options.type === 'add' ? $t('创建分组') : $t('编辑分组'),
					className: 'dialog_addGroup',
					classPrefix: 'dialog_addGroup crm-c-dialog ',
					size: 'sm',
                    record_type: 'default__c',
                    apiname: 'SubProductCatalogObj',
                    show_type: 'dialog',
					useOldDialog:_this.useOldDialog,
                    btns: btns,
                    data: _this.options.data || {
                        group_options_control: false,
                        // min_prod_count: 1,
                        // max_prod_count: 1,
                        order_field: _this.options.defNumber,
                        is_required: true,
                        enabled_status: true
                    },
                    submit_addContact: function (e, t) {
                        _this.groupListData = t.view.collect();
                        let api_name = 'name'
                        let v = t.model.getData(api_name + '__lang')
                        if (v){
                            _this.groupListData[api_name + '__lang'] = v;
                        }
                        if (!t.view.validate()) {
                            _this.groupListData = null;
                            return
                        }
                        _this.parseData();
                        _this.verifyData() && (_this.trigger('dialogEnter', _this.groupListData), t.dialog.destroy());
                    },

					cancel(e, t){
						_this.trigger('dialogCancel');
						t.dialog.destroy();
					},

                    Model: field.Model.extend({
                        fetch: function () {
                            this.parse()
                        },
                        parse: function () {
                            this.set({
                                layout: [{
                                    label: '',
                                    api_name: 'basic',
                                    columns: 1,
                                    components: fields
                                }, {
                                    label: '',
                                    api_name: 'basic2',
                                    columns: 2,
                                    components: fields2
                                },
                                {
                                    label: '',
                                    api_name: 'basic3',
                                    columns: 2,
                                    components: fields3
                                },
                                {
                                    label: '',
                                    api_name: 'basic4',
                                    columns: 2,
                                    components: fields4
                                }
                                ],
                                fields: _.extend({}, fields, fields2, fields3, fields4)
                            })
                        },
                        change: function (field, val) {
                            _this.fieldChange(field, val);
                        },
                    }),

                    View: field.View.extend({
                        submit: function (e, t) {
                            _this.groupListData = this.collect();
                            if (!this.validate()) {
                                _this.groupListData = null;
                                return
                            }
                            _this.parseData();
                            _this.verifyData() && (_this.trigger('addSubObj', _this.groupListData, t.dialog.destroy()));
                        }
                    })
                });

                setTimeout(function () {
					var group = $('.dialog_addGroup').eq(0);
					if(_this.useOldDialog){
						// 多选
						if (!_this.options.data || (_this.options.data && !_this.options.data.group_options_control)) {
							group.find('.f-group').eq(1).show();
							group.find('.f-group').eq(2).hide();
						} else {                                      // 单选
							group.find('.f-group').eq(1).hide();
							group.find('.f-group').eq(2).show();
						}

					}else{
						// 多选
						var item = group.find('.f-g-item');
						if (!_this.options.data || (_this.options.data && !_this.options.data.group_options_control)) {
							item.eq(3).show();
							item.eq(4).show();
							item.eq(5).hide();
						} else {                                      // 单选
							item.eq(3).hide();
							item.eq(4).hide();
							item.eq(5).show();
						}
					}
                    if (_this.options.parentData && _this.options.parentData.enabled_status === false) {
                        _this.disabledCompStyle('enabled_status');
                    }
				})
            })
        },
        disabledCompStyle(field) {
            var group = $('.dialog_addGroup').eq(0);
            let comp = group.find('[data-apiname="' + field + '"]:first')
            if (!comp.length) return; 
            comp.addClass('disabled')
            comp.css('pointer-events', 'none');
            comp.css('color', '#c0c4cc');
        },
		/**
		 * @desc 处理最后的数据； 如果分组时单选，则将是否必填字段关联到最少可选个数； 将分组的是否比选字段置为null，server不需要这个字段
		 */
        parseData: function () {
            if (this.options.data) this.groupListData = _.extend(this.options.data, this.groupListData);
            if (!this.groupListData.hasOwnProperty('children')) this.groupListData.children = [];
            if (this.groupListData.group_options_control) {    // 单选
                this.groupListData.min_prod_count = this.groupListData.is_required ? '1' : '0';
                this.groupListData.max_prod_count = '1';
			}
			this.groupListData.is_required = null;
        },

        /**
         * @desc 表单change事件
         * @param field
         * @param val
         * @todo: 为啥没有设置表单的api！蓝瘦!!!
         */
        fieldChange: function (field, val) {
            if (field === 'group_options_control') {            // 组内选择规则
                var group = $('.dialog_addGroup').eq(0);
                if(this.useOldDialog){
					if (val) {                    // 单选；
						group.find('.f-group').eq(1).hide();
						group.find('.f-group').eq(2).show();
					} else {
						group.find('.f-group').eq(1).show();
						group.find('.f-group').eq(2).hide();
					}

				}else{
					var item = group.find('.f-g-item');
					if (val) {                    // 单选；
						item.eq(3).hide();
						item.eq(4).hide();
						item.eq(5).show();
					} else {
						item.eq(3).show();
						item.eq(4).show();
						item.eq(5).hide();
					}
				}
            }
        },

        /**
         * @desc 校验数据
         * @returns {boolean}
         */
        verifyData: function () {
            var data = this.groupListData;
            var res = new RegExp("^[\|0-9]+(\\.[0-9]{0,0})?$");

            if ((!res.test(data.min_prod_count) && data.min_prod_count !== null) || (!res.test(data.max_prod_count) && data.max_prod_count !== null)) {
                util.alert($t('数量应大于等于0'));
                return false
            }

            if (Number(data.min_prod_count) > Number(data.max_prod_count) && util.hasValue(data.min_prod_count ) && util.hasValue(data.max_prod_count )) {
                util.alert($t('最少子产品个数应小于等于最多子产品个数'));
                return false
            }

			if (util.hasValue(data.max_prod_count ) && data.max_prod_count <= 0) {
				util.alert($t('最多子产品个数应大于零'));
				return false
			}

            if (data.group_options_control && data.max_prod_count != '1') {             // 单选
                util.alert($t('单选时，最多子产品个数为1'));
                return false
            }
            if (!this.validAmountCount(data)) {
                return false;
            }
            //todo:需要改校验wangshaoh
            if (data.name !== this.oldName) {
				return this.checkGroupName(data.name);
            }
            return true
        },
        validAmountCount({min_amount_count, max_amount_count}) {
            if (!(min_amount_count && max_amount_count)) return true;
            var reg = /^\d+$/;
            let message = $t('请填写正整数')
            let min_label = this.options.fields['min_amount_count']?.label
            let max_label = this.options.fields['max_amount_count']?.label
            var validator = () => {
                if ((min_amount_count && max_amount_count) 
                    && Number(min_amount_count) > Number(max_amount_count)) {
                    util.alert($t('sfa.crm.bomcore.group.amountCount.valid1', {
                        label1: min_label,
                        label2: max_label
                    }, '[{{label1}}]应小于等于[{{label2}}]'));
                    return
                }
                return true;
            }
            return (
                this.validNumber({label: min_label, value: min_amount_count, reg, message, validator})
                && this.validNumber({label: max_label, value: max_amount_count, reg, message, validator})
            )

        },
        validNumber({label, value, isRequired, reg, message, validator} = field) {
            if (!isRequired && !value) return true;
            if (isRequired && !value) {
                util.alert($t('{{text}}不能为空', {
                    text: label
                }));
                return 
            }
            if (_.isNumber(value)) {
                util.alert($t('请输入数字'))
                return;
            }
            if (reg && !reg.test(Number(value))) {
                util.alert(message);
                return;
            }
            if (validator) {
                return validator(value);
            }
            return true;
        },
		/**
		 * @desc 校验分组名称；同一层级，分组名称不能重复;
		 * @param name
		 * @returns {boolean}
		 */
		checkGroupName: function (name) {

			function _check(data) {
				let len = data.length;
				while (len--) {
					if (data[len].product_id === name) {
						util.alert($t('分组名称"{{name}}"已存在', {"name": name}));
						return false
					}
				}
				return true
			}

			if (this.options.type === 'add') {	// 新建
				if (this.options.currentRow.children) {
					return _check(this.options.currentRow.children)
				}
				return true;
			} else {								// 编辑
				let pData = this.options.pData || {};
				if(!pData.children) return true;
				let l = pData.children.filter(c => c.isGroup);
				return _check(l)
			}
		},

        destroy: function () {
        	if(this.useOldDialog){
				this.dialog && this.dialog.destroy();
			}
        }
    })
});
