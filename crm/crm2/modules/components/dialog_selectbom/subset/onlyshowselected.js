/**
 * @Desc: 仅查看已选
 * <AUTHOR>
 * @date 2023/2/10
 */
define(function (require, exports, module) {
	var util = CRM.util;

	return {

		// 根据查看已选 和 包含已选母件子产品 两个按钮状态，处理数据展示；
		onlyShowSelectedRow(data, isRefresh = true, from =''){
			let tableData = data || this.treeTable.getCurData();
			let onlyShowSelected = this._isOpenOnlyShowSelected(); // 仅查看已选
			if(onlyShowSelected){
				this.filterChildrenByAttr(tableData);
				this._todoOnlyShowSelected(tableData, from);
			}else{
				this._todoOnlyShowSelected(tableData, from);
				this.filterChildrenByAttr(tableData);
			}
			this.filterDataBySearchKey(tableData);
			if(!isRefresh) return;
			this.refreshRowIsShow(tableData);
			this.displayAttribute2(true);
			this.options.onlyShowSelectAfterHook && this.options.onlyShowSelectAfterHook({
				data: this.treeTable.getAllData(),
				api: this._getApi(),
				status: this._isOpenOnlyShowSelected()
			});
			this.treeTable.resize();
		},

		_getLocal_showRoot(){
			return  CRM.getLocal('crmShowRootSwitch');
		},

		_todoOnlyShowSelected(tableData, from){
			let onlyShowSelected = this._isOpenOnlyShowSelected(); // 仅查看已选
			let showRootSelected = this._getLocal_showRoot();	// 包含已选母件下所有子产品
			let isGray = this.getIsGrayShowRootSelected();
			// 开了包含母件已选，处理第一层子产品和其所有子产品
			if(isGray && showRootSelected && onlyShowSelected){
				tableData.forEach(row => {
					let s = !!row.isChecked;
					row.isShow = s;
					if(row.children){
						util.forEachTreeData(row.children, c => c.isShow = s);
					}
				})
			}else{
				util.forEachTreeData(tableData, row => {
					if(onlyShowSelected){
						if(row.isGroup && row.children){
							let i = row.children.find(r => r.isChecked);
							row.isShow = !!i;
						}else{
							row.isShow = !!row.isChecked;
						}
					}else{
						if(from === 'btn') row.isShow =  true;
					}
				});
			}
			this._todoOnlyShowSelectedHook_after(tableData, onlyShowSelected);
		},

		_todoOnlyShowSelectedHook_after: $.noop,

		// 刷新表格行展示
		refreshRowIsShow(tableData){
			util.forEachTreeData(tableData, item => {
				this.treeTable.setShowType(item, !!item.isShow, false, true);
			})
		},

		// 添加 查看已选产品按钮
		addFilterSelected() {
			let isGray = this.getIsGrayShowRootSelected();
			let showSelected = isGray ? `<div class="showRootSelectedSwitch displayNone"></div>` : '';

			this.treeTable.$el.find('.dt-caption').append(`
			<div class="showSelectedBox">
				<div class="onlyShowSelectedBox">
					<span class="onlyShowSelectedName">${$t('仅查看已选')}</span>
					<div class="showSelectedRowBtn""></div>
				</div>
				${showSelected}
			</div>
			`);
			this.renderShowSelectedBtn();
			if (isGray) this.renderShowRootSwitch();
		},

		_isOpenOnlyShowSelected(){
			return this._onlyShowSelected;
		},

		// 仅查看已选按钮
		renderShowSelectedBtn() {
			let _this = this;
			this.selectedBtn && this.selectedBtn.destroy && this.selectedBtn.destroy();
			let status = _this._onlyShowSelected = false;

			this.selectedBtn = FxUI.create({
				wrapper: '.showSelectedRowBtn',
				template: `<fx-switch
								  v-model="value"
								  size="mini"
								  @change="change"
							  >
						</fx-switch>`,
				data() {
					return {
						value: status,
					}
				},
				methods: {
					change(data, node, tree) {
						_this._onlyShowSelected = this.onlyShowSelectedBom = data;
						_this.showSelectedChange();
						if(_this.showRootSwitch) _this.showRootSwitch.disabled = !this.onlyShowSelectedBom;
						_this.setIncludeSelectedIsShow(data);
					},
				}
			})
		},

		async showSelectedChange(){
			await this.onlyShowSelectedRow(null, true, 'btn');
		},

		// 设置包含子产品按钮 显示隐藏
		setIncludeSelectedIsShow(data){
			let m = data ? 'removeClass' : 'addClass';
			this.treeTable.$el.find('.showRootSelectedSwitch')[m]('displayNone');
		},

		// 如果开了仅查看已选，反勾选的数据需要隐藏
		hideRowAfterCheck(rowData){
			// 反选数据父级是分组的，需要把分组
			let pData;
			if(rowData.pid){
				pData = this.treeTable.getRowDataById(rowData.pid);
				if(pData && pData.isGroup){
					let f = pData.children.find(item => item.isChecked);
					if(!f) {
						pData.isChecked = false;
					}
				}
			}
			//
			let onlyShowSelected = this._isOpenOnlyShowSelected(); // 仅查看已选
			if(onlyShowSelected){
				let isGray = this.getIsGrayShowRootSelected();
				let showRootSelected = this._getLocal_showRoot();	// 包含已选母件下所有子产品
				if(isGray && showRootSelected && rowData.pid) return;
				util.forEachTreeData([rowData], item => {
					this.treeTable.setShowType(item, false)
				});
				if(pData && !pData.isChecked){
					this.treeTable.setShowType(pData, false);
				}
			}
		},

		// 有搜索字段，按当前搜索匹配显示
		filterDataBySearchKey(tableData){
			let dataObj;
			// 设置父级显示
			function _setPDShow(pid) {
				let pd = dataObj[pid];
				if(pd){
					pd.isShow = true;
					if(pd.pid) _setPDShow(pd.pid);
				}
			}
			let {key, data} = this._searchInfo || {};
			if (key) {
				let searchData = util.getDataByKeyWord(key, data.value, tableData);
				if (searchData.length){
					// 根据筛选结果，过滤要展示的行；
					dataObj = this.treeTable.getDataToObj();
					let searchDataObj = this._getObj(searchData);
					util.forEachTreeData(tableData, row => {
						if(row.isShow){
							if(!searchDataObj[row.rowId]) row.isShow = false;
							if(row.isShow && row.pid) _setPDShow(row.pid);
						}
					})
				}else{
					util.forEachTreeData(tableData, row => {
						row.isShow = false;
					})
				}
			}
		},

		_getObj(list){
			let res = {};
			list.forEach(item => {
				res[item.rowId] = 1;
			});
			return res;
		},

		// 渲染开关组件；
		renderShowRootSwitch () {
			let _this = this;
			this.showRootSwitch && this.showRootSwitch.destroy && this.showRootSwitch.destroy();
			this.showRootSwitch = FxUI.create({
				wrapper: '.showRootSelectedSwitch',
				template: `<fx-checkbox v-model="checked" @change="change">${$t('包含已选母件下所有子产品')}</fx-checkbox>`,
				data() {
					return {
						checked: _this._getLocal_showRoot(),
					}
				},
				methods: {
					change(data, node, tree) {
						CRM.setLocal('crmShowRootSwitch', data);
						this.value = data;
						_this.onlyShowSelectedRow();
					},
				}
			})
		},

		// 属性约束执行完，如果有新增勾选，需要执行一次数据展示
		afterAttrConstraint(){
			if(this._fromRender){
				return;
			}
			let onlyShowSelected = this._isOpenOnlyShowSelected(); // 仅查看已选
			if(!onlyShowSelected) return;
			let allData = this.treeTable.getAllData();
			this.cloneData = this.cloneData || [];
			let on = this._getSelectedNum(this.cloneData);
			let nn = this._getSelectedNum(allData);
			if(nn > on){
				this.onlyShowSelectedRow();
			}
		},

		// 获取勾选行数量
		_getSelectedNum(data){
			let n = 0;
			util.forEachTreeData(data, item => {
				if(item.isChecked) n++;
			});
			return n;
		},

		// 包含母件下已选所有子产品
		getIsGrayShowRootSelected(){
			return CRM.util.isGrayScale('CRM_SHOW_ROOT_SELECTED');
		},

		hideOnlyShowBtn(){
			this.$el.find('.showSelectedBox').hide();
		},

		showOnlyShowBtn(){
			this.$el.find('.showSelectedBox').show();
		},


	}
})
