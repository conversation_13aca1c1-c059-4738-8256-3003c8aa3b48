/**
 * @Desc: BOM的属性约束规则
 * <AUTHOR>
 * @date 2023/2/10
 */
define(function (require, exports, module) {
	var util = CRM.util;

	return {

		// 执行属性约束规则；可能同时执行新规则和取消当前规则
		async executeBomAttrConstraint({beforeHook = null} = {}) {
			if (!this._attrConstraintRule.length) {
				beforeHook && beforeHook();
				return true;
			}
            try {
				let startTime = new Date().getTime();
                util.showLoading_tip();
                util.closeError_fx();
				this.cloneAttrConstraintRule();
                this._attrChangeRow = [];
				// 先根据当前选中项，取消失效的规则；比如属性的禁用，复选框的置灰
				let r3 = this._doRevokeAttrRule();
				if (!r3) {
					this.resetTableData();
					return false;
				}
				beforeHook && beforeHook();
				// 再执行一次取消规则，因为可能有子件属性被同步修改了
				let r2 = this._doRevokeAttrRule();
				if (!r2) {
					this.resetTableData();
					return false;
				}

                let r1 = true;
				r1 = await this._doNewAttrRule();
				util.hideLoading_tip();
                this.displayAttribute();
                if (!r1) {
					this.resetTableData();
					return false;
				}
               	await this.calculateFormulaRow(this._attrChangeRow, 'attribute');
				this._attrChangeRow = [];
                this.calculateTotalMoney();
                this.afterAttrConstraint();
                this.updateCloneData();
			
				let endTime = new Date().getTime();
				console.log('crmBomAttrConstraintTime', endTime - startTime);
				this.sendLogForTime('crmBomAttrConstraintTime', endTime - startTime, {
					fromRender: this._fromRender,
				});
				this._fromRender = false;
                return true;
            } catch (e) {
            	console.error(e);
                util.hideLoading_tip();
            }
		},

		// 拷贝一份当前约束规则，约束冲突时回滚用
		cloneAttrConstraintRule(){
			this._cloneAttrConstraintRule = util.cloneObjArr(this.currentRule);
		},

		alertMsg(msg = '', type = 'error') {
			if (type === 'error') {
				util.showError_fx(msg);
			} else if (type === 'info') {
				Vue.prototype.$notify.info({
					title: $t('提示'),
					message: msg,
					duration: 0,
					dangerouslyUseHTMLString: true
				});
			}
		},

		// 新增执行规则
		_getNewRule(todoRule) {
			let currentRule = this.currentRule;
			let newRule = todoRule;
			if (currentRule.length) {
				newRule = todoRule.filter(item => {
					let isUse = currentRule.find(r => r._newRuleId === item._newRuleId);
					return !isUse
				});
			}
			return newRule
		},

		// 要取消的约束规则；当前要执行的规则，和当前已经执行的规则进行比对，缺少的规则就是要取消的约束规则
		_getRevokeRule(todoRule) {
			if (!todoRule.length && this.currentRule) return $.extend(true, [], this.currentRule);
			let cancelRule = [];
			if (!this.currentRule.length) return cancelRule;
			cancelRule = this.currentRule.filter(item => {
				let isUse = todoRule.find(r => r._newRuleId === item._newRuleId);
				return !isUse
			});
			return cancelRule
		},

		// 处理新增的约束规则结果
		doNewRuleRes(todoRuleRes) {
			let r1 = this.disposeCheckboxStatus(todoRuleRes);
			if (!r1) return false;
			let allData = this._getAllDataIncludeRoot();
			let r2 = this.disposeAttrStatus(todoRuleRes, allData, true);
			if (!r2.status) return false;
			return r2;
		},

		// 处理取消的约束规则结果
		doRevokeRuleRes(revokeRuleRes) {
			let r1 = this.disposeCheckboxStatus(revokeRuleRes);
			if (!r1) return false;
			let allData = this._getAllDataIncludeRoot();
			let r2 = this.disposeAttrStatus(revokeRuleRes, allData, false);
			if (!r2.status) return false;
			if (revokeRuleRes.revokeRule) this.reduceCurrentRule(revokeRuleRes.revokeRule);
			this.alertCancelRuleMsg(revokeRuleRes.revokeRule);
			return true;
		},

		// 提示用户取消的必选约束规则
		alertCancelRuleMsg(rule){
			if(this._fromRender) return;
			let msg = '';
			// 产品A存在【必须选doRevokeRuleRes择】的约束关系，被约束产品B、C已选中，如不需要，请手工取消！
			rule.forEach(item => {
				let conditionProduct = [];
				let resultProduct = [];
				if(item.constraint_type === '1'){
					conditionProduct = item.condition_range.map(c => c.product_name).join();
					resultProduct = item.result_range.map(c => c.product_name).join();

					msg += $t('sfa.crm.bom.cancelrule.msg', {
						p1: conditionProduct,
						p2: resultProduct,
					}, '产品【{{p1}}】存在【必须选择】的约束关系，被约束产品【{{p2}}】已选中，如不需要，请手工取消！');
					msg += '<br>';
				}
			});
			if(msg) this.alertMsg(msg, 'info');
		},

		// 取消规则之前，提前获取下剩余的规则；
		getRestRule(revokeRule) {
			return this.currentRule.filter((rule) => {
				let f = revokeRule.find(r => r._newRuleId === rule._newRuleId);
				return !f
			});
		},

		// 获取所有勾选数据(只保留有属性约束标记的)，包含根节点，但根节点必须有选择属性
		_getCheckedDataIncludeRootData() {
			let checkedData = this.treeTable.getCheckedData() || [];
			checkedData = checkedData.filter(c => c._attrConMark);
			if (this.rootData.selectedAttr) checkedData.push(this.rootData);
			return checkedData;
		},

		// 获取所有数据，包含根节点
		_getAllDataIncludeRoot() {
			this.rootData.children = this.treeTable.getCurData();
			return [this.rootData];
		},

		// 执行新约束规则
		async _doNewAttrRule() {
			let checkedData = this._getCheckedDataIncludeRootData();
			if (!checkedData.length) return true;
			let todoRuleRes = {status: true};
			let todoRule = this.getAttrRule(checkedData, this._attrConstraintRule);
			todoRule = _.uniq(todoRule, item => item._newRuleId);
			let newRule = this._getNewRule(todoRule, this.currentRule);
			if (!newRule.length) return true;
			let allData = this._getAllDataIncludeRoot();
			todoRuleRes = this.todoBomAttrConstraint({checkedData, allData, rule: this._attrConstraintRule});
			if (!todoRuleRes.status) return false;
			await this._doCalculateAmount();
			this.currentRule = this.currentRule.concat(todoRuleRes.newRule || []);
			await this.getRealPriceForBom(todoRuleRes.attrChangeRow);
			this.afterDoNewRule(todoRuleRes.mustSelectData);
			return true;
		},

		// 重新计算因约束关系而必选数据的父级价格
		afterDoNewRule(mustSelectData = []) {
			if (mustSelectData && mustSelectData.length) {
				mustSelectData = _.uniq(mustSelectData, item => item.rowId);
				mustSelectData.forEach(d => {
					let lowerData = this._getLowerData(d);
					this.upDateEveryParentPrice(lowerData.pid, lowerData, 'parent');
				})
			}
		},

		// 执行取消某些约束规则
		_doRevokeAttrRule() {
			let checkedData = this._getCheckedDataIncludeRootData();
			let revokeRuleRes = {status: true};
			let todoRule = this.getAttrRule(checkedData, this._attrConstraintRule);
			todoRule = _.uniq(todoRule, item => item._newRuleId);
			let revokeRule = this._getRevokeRule(todoRule, this.currentRule);
			if (!revokeRule.length) return true;
			let allData = this._getAllDataIncludeRoot();
			revokeRuleRes = this.revokeBomAttrConstraint({
				checkedData,
				allData,
				rule: this._attrConstraintRule
			});
			if (!revokeRuleRes.status) return false;
			return this.doRevokeRuleRes(revokeRuleRes);
		},

		// 执行前先校验一遍勾选数据的属性，是否有错误状态，如果有，提示；
		checkAttrError() {
			let checkedData = this.treeTable.getCheckedData() || [];
			let msg = '';
			checkedData.forEach(item => {
				if (item.attribute && item.attribute.length) {
					item.attribute.forEach(zAttr => {
						zAttr.attribute_values.forEach(attr => {
							// 已选产品【XXX】属性【XX】属性值【X】在产品属性约束规则中存在冲突，请更改属性值或修改产品属性约束关系
							if (attr.showError) msg += $t('已选产品') + '[ ' + item.product_id__r + ' ] ' + $t('属性') + '[ ' + zAttr.name + ' ]' + $t('属性值') + '[ ' + attr.name + ' ], ' + $t('在产品属性约束规则中存在冲突，请更改属性值或修改产品属性约束关系') + '<br/>';
						})
					})
				}
			});
			if (msg) this.alertMsg(msg);
		},

		// 校验约束条件是否跟被约束产品是同级或者父级；如果都不是，则可以反选掉父级；
		_checkParentNeedDisabled(pid, conditionRange, allData) {
			let res = true;
			let pData = CRM.util.getDataByKey(pid, allData, 'rowId');
			let pAndc = [].concat([pData]).concat(pData.children);
			let allConditionBomId = conditionRange.map(c => c.bom_id);
			allConditionBomId.forEach(bId => {
				let f = pAndc.find(d => d.bom_id === bId);
				if (f) res = false;
			});
			return res;
		},

		/**
		 * @desc 生效属性约束关系
		 * @param checkedData 当前勾选数据
		 * @param allData 表格所有数据
		 * @param rule 所有的约束规则
		 * @returns {{status: boolean, msg: string, disabled: Array, checked: Array, attrSelect: Array, attrDisabled: Array}}
		 */
		todoBomAttrConstraint({checkedData = [], allData = [], rule = []} = {}) {
			if (!rule.length) return;
			let _this = this;
			let res = {
				status: true,
				disabled: [], 			// 要设置disabled的数据
				checked: [],  			// 需要设置勾选的数据
				attrSelect: [],   		// 属性必选
				nsAttrSelect: [],		// 非标属性必选
				attrDisabled: [],		// 属性不允许选择
				nsAttrDisabled: [],		// 非标属性不允许选择
			};
			let newRule = [];			// 缓存这次执行的规则
			let attrChangeRow = [];		// 缓存这次执行的规则
			let mustSelectData = []; 	// 必须选择行
			let tip = '';           	// 提示属性有变化
			let msg = '';           	// 错误提示
			let continueLoop = []; 		// 缓存还需要执行约束规则的数据；
			let groupList = [];			// 缓存分组，之后要校验分组规则是否冲突

			// 获取最新的勾选数据，且有配规则的数据
			function _getCheckedData() {
				let r = [];
				CRM.util.forEachTreeData(allData, item => {
					if (!item.isGroup && item.isChecked && item._attrConMark) r.push(item)
				});
				return r;
			}

			// 校验父级；
			// 1、如果父级是单选分组，校验是否已有勾选项；
			// 2、父级是否已经被禁止选中；
			function _checkParent(pid, curRow) {
				let pData = CRM.util.getDataByKey(pid, allData, 'rowId');
				// if (!pData) {
				// 	res.status = false;
				// 	msg = $t('未找到父节点');
				// 	return false;
				// }
				if (pData.isGroup) groupList.push(pData);

				// 父级已经选中了，则校验分组是否是单选，且已经有选中项，如果已有，则提示；
				if (pData.isChecked) {
					if (pData.isGroup && pData.group_options_control) {
						let hasChecked = _.filter(pData.children, item => item.isChecked);
						if (hasChecked.length) {
							// 如果已勾选的数据中包含自己，则不提示
							let findMe = _.find(hasChecked, item => item.rowId == curRow.rowId);
							if (!findMe || hasChecked.length > 1) {
								res.status = false;
								// 所选产品【XXX】与 产品所在产品分组【XX】选项控制存在冲突，请联系相关人员修改
								msg = $t('所选产品') + '[' + curRow.product_id__r + ']' + $t('与产品所在分组[{{group}}]选项控制存在冲突，请联系相关人员修改', {
									group: pData.name
								});
								return false;
							}
						}
					}
				} else {
					// 如果已经被禁止选中，则提示；
					if (pData.disabled) {
						res.status = false;
						if (pData.__isNotFlowRootPricebook){
							msg = '[' + CRM.util.getBomPath(pData) + ']' + $t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表');
						}else if(pData.__isConstraintSetting ){
							msg = $t('父级产品[{{P}}]，受其它约束关系影响，不允许选择', {
								P: CRM.util.getBomPath(pData)
							});
						}else{
							msg = $t('父级产品[{{P}}]，受其父级产品价格不可编辑影响，不允许选择', {
								P: CRM.util.getBomPath(pData)
							});
						}
						return false;
					}

					pData.isChecked = true;
					res.checked.push(pData);
					if (pData._attrConMark) continueLoop.push(pData);

					CRM.util.setBomChecked(pData.children, function (item) {
						if (item.isChecked) {
							res.checked.push(item);
							if (item._attrConMark) continueLoop.push(item);
						}
						if (item.disabled) res.disabled.push(item);
					}, false, true);

					if (pData.pid) return _checkParent(pData.pid, pData);
				}
				return pData
			}

			// 执行必须选择节点；选中，并置灰，不允许反选；
			function _doMustSelect(mustSelect) {
				let len = mustSelect.length;
				while (len--) {
					let {conditionRange, bomId, ruleId, current_root_new_path, product_name} =  mustSelect[len];
					let d = CRM.util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
					if (d) {
						if (d.pid) {
							let r1 = _this._checkParentNeedDisabled(d.pid, conditionRange, allData);
							if (r1) {
								d.__isMustSelectedByConstraint = d.__isMustSelectedByConstraint || [];
								d.__isMustSelectedByConstraint.push(ruleId); // 加标识；有规则约束它为必选
							}
						}
						mustSelectData.push(d);
						// 如果已经被禁止选中，则提示；
						if (!d.isChecked && d.disabled) {
							res.status = false;
							if (d.__isNotFlowRootPricebook){
								msg = '[' + CRM.util.getBomPath(d) + ']' + $t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表');
							}else if (d.__isConstraintSetting) {
								msg = $t('[{{B}}]因为其它约束关系已被禁选，不允许选择', {
									B: CRM.util.getBomPath(d)
								});
							} else {
								msg = $t('[{{B}}]受父级产品价格不可编辑影响，不允许选择', {
									B: CRM.util.getBomPath(d)
								});
							}
							return res;
						}

						if (d.__isConstraintSetting) continue;

						// 如果还没选，则选中；
						if (!d.isChecked) {
							d.isChecked = true;
							d.__checkedByConstraint = true; // 加标识；因为约束关系被选中；
							res.checked.push(d);
							if (d._attrConMark) continueLoop.push(d);
							// 如果还没选中，且有子级，初始化子级，并需要执行约束规则；
							if (d.children && d.children.length) {
								d.__checkedByConstraint = false;
								CRM.util.setBomChecked(d.children, function (item) {
									if (item.isChecked) {
										res.checked.push(item);
										if (item._attrConMark) continueLoop.push(item);
									}
									if (item.disabled) res.disabled.push(item);
								}, false, true);
							}

							// 如果有父级；需要将父级勾选，不置灰，但取消勾选父级的时候需要提示用户，子级有被约束，不能反选父级；
							if (d.pid) {
								let pd = _checkParent(d.pid, d);
								if (!pd) return res;
							}
						}

						// 如果还没置灰，则置灰；
						if (!d.disabled) {
							d.disabled = true;
							d.__isConstraintSetting = true; // 加标识，是通过约束条件置灰的；用于取消约束条件时，去掉置灰；避免和通过bom规则置灰冲突；
							res.disabled.push(d);
						}

					} else {
						res.status = false;
						msg = $t('产品[{{P}}]，已下架或删除，请调整相关约束规则后再进行配置操作',{
							P: product_name
						});
						return res;
					}
				}
			}

			// 执行不允许选择节点；置灰；如果已经被选中，提示；
			function _doCannotSelect(cannotSelect) {
				let len = cannotSelect.length;
				while (len--) {
					let {bomId, current_root_new_path, conditionRange, product_name, group_id, group_name } = cannotSelect[len];
					// 若被约束的分组下子件被选中，则提示
					if (group_id) {
						let groupData = util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
						let d = groupData.children.find(child => child.isChecked);
						if (d) {
                            res.status = false;
                            msg = $t('选择【{{A}}】下的产品不允许选择【{{B}}】下的产品', {
                                A: '<span class="crm-bom-attributeTip">' + conditionRange[0].group_name  + '</span>',
                                B: '<span class="crm-bom-attributeTip">' + group_name + '</span>'
                            })
                            return res;
						}
                        util.forEachTreeData(groupData.children, c => {
                            // 禁用不允许选择的产品
                            if (!c.disabled) {
                                console.log('禁用产品--' + c.product_id__r)
                                c.disabled = true;
                                res.disabled.push(c);
                                c.__isConstraintSetting = true;
                            }
                        })
						return res;
					}
					let d = CRM.util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
					if (d) {
						// 如果已经被勾选，提示；
						if (d.isChecked) {
							res.status = false;
							msg = $t('已设置约束关系：选[{{A}}]不允许选择[{{B}}]，但[{{B}}]已经被勾选', {
								A: '<span class="crm-bom-attributeTip">' + CRM.util.parseBomConditionRange(conditionRange) + '</span>',
								B: '<span class="crm-bom-attributeTip">' + CRM.util.getBomPath(d) + '</span>'
							});
							return res;
						}
						if (!d.disabled) {
							d.disabled = true;
							res.disabled.push(d);
							d.__isConstraintSetting = true;
						}
					} else {
						res.status = false;
						msg = $t('产品[{{P}}]，已下架或删除，请调整相关约束规则后再进行配置操作',{
							P: product_name
						});
						return res;
					}
				}
			}

			// 过滤掉已经执行的规则，不再重复执行
			function _filterRule(todoRule) {
				if (newRule.length) {
					return todoRule.filter(item => {
						let f = newRule.find(r => r._newRuleId === item._newRuleId);
						return !f
					})
				}
				return todoRule
			}

			function _loop(data) {
				let todoRule = _this.getAttrRule(data, rule);
				let m = [], c = [];
				continueLoop = []; // 重置缓存
				todoRule = _.uniq(todoRule, item => item._newRuleId);
				todoRule = _this._getNewRule(todoRule);
				todoRule = _filterRule(todoRule);
				if (!todoRule.length) return;
				todoRule.forEach(item => {
					if (item.constraint_type == '1') { 				// 必须选择
						item.result_range.forEach(rd => {
                            // 分组不处理，server校验
                            if (rd.group_id) {
                                return;
                            }
							m.push({
								bomId: rd.bom_id,
								product_name: rd.product_name,
								conditionRange: item.condition_range,
								ruleId: item._newRuleId,
								current_root_new_path: item.current_root_new_path,
								formula: rd.formula,
							});
							rd._ruleId = item._newRuleId;
							rd.current_root_new_path = item.current_root_new_path;
							let d;
							// 有属性必选的需要再次执行约束
							if (rd.attribute && rd.attribute.length) {
								res.attrSelect.push(rd);
								d = CRM.util.findDataByBomIdAndPath(allData, rd.bom_id, rd.current_root_new_path);
								if (d) {
									if (d._attrConMark) continueLoop.push(d);
									tip += d.product_id__r + ', ';
								}
								_this._attrChangeRow.push(d);
							}
							if (rd.nonstandardAttribute && rd.nonstandardAttribute.length) res.nsAttrSelect.push(rd);
							// 约束数量
							// if(rd.formula?.length){
							// 	if(!d) d = CRM.util.findDataByBomIdAndPath(allData, rd.bom_id, rd.current_root_new_path);
							// 	if (d) {
							// 		_this._cacheCalculateAmount.push({
							// 			formula: rd.formula,
							// 			rowData: d,
							// 		})
							// 	}
							// }
						})
					} else if(item.constraint_type == '2') {		// 不允许选择
						item.result_range.forEach(rd => {
							rd._ruleId = item._newRuleId;
							rd.current_root_new_path = item.current_root_new_path;
							if (rd.attribute && rd.attribute.length) res.attrDisabled.push(rd);
							if (rd.nonstandardAttribute && rd.nonstandardAttribute.length) res.nsAttrDisabled.push(rd);
							if (rd.attribute && rd.attribute.length || rd.nonstandardAttribute && rd.nonstandardAttribute.length) return;
							c.push({
								conditionRange: item.condition_range,
								bomId: rd.bom_id,
								product_name: rd.product_name,
								current_root_new_path: item.current_root_new_path,
								group_name: rd.group_name,
								group_id: rd.group_id
							})
						})
					} else if(item.constraint_type == '3'){			// 可选范围
						let cloneRes = util.cloneObjArr(item.result_range);
						cloneRes.forEach(rd => {
							rd._ruleId = item._newRuleId;
							rd.current_root_new_path = item.current_root_new_path;
							if (rd.attribute && rd.attribute.length) {
								let row = util.findDataByBomIdAndPath(allData, rd.bom_id, item.current_root_new_path);
                                if (!row) return;
								rd.attribute.forEach(a => {
									let ra = row.attribute.find(i => i.id === a.id);
									a.attribute_values = _this._filterAttribute(ra.attribute_values, a.attribute_values);
								});
								res.attrDisabled.push(rd);
							}
						})
					}
				});
				newRule = newRule.concat(todoRule);
				_doMustSelect(m);
				_doCannotSelect(c);
				// 执行约束结果
				if (res.status) {
					_this._uniqRes(res);
					let r = _this.doNewRuleRes(res);
					if (!r) res.status = false;
					if (r && r.attrChangeRow) attrChangeRow = r.attrChangeRow; // 属性有变化的行数据
				}
				// 如果还有要执行约束的数据，那需要把当前所有的勾选数据再执行一遍约束规则，因为不知道新选中的数据和哪些勾选数据有配约束关系
				if (res.status && continueLoop.length) {
					_this.initSelectedAttr(continueLoop);
					let cdata = _getCheckedData();
					res = {
						status: true,
						disabled: [], 		// 要设置disabled的数据
						checked: [],  		// 需要设置勾选的数据
						attrSelect: [],   	// 属性必选
						nsAttrSelect: [],	// 非标属性必选
						attrDisabled: [],	// 属性不允许选择
						nsAttrDisabled: [],	// 非标属性不允许选择
					};
					_loop(cdata);
				}
			}

			_loop(checkedData);

			this._uniqRes(res);

			// 最后再校验一下分组规则是否有冲突
			// if (res.status && groupList.length) {
			// 	let r = this._checkGroupRule(groupList);
			// 	if (!r) res.status = false;
			// }
			if (!res.status && msg) this.alertMsg(msg);
			// 提示属性有变化；
			if (res.status && tip) {
				let reg = /, $/gi;
				tip = tip.replace(reg, '');
				this.alertMsg($t('因约束关系，这些产品属性有变化') + '[ ' + tip + ' ]', 'info');
			}
			res.mustSelectData = mustSelectData;
			res.newRule = newRule;
			res.attrChangeRow = attrChangeRow;
			return res;
		},

		cloneObj(obj){
			return $.extend({}, obj)
		},

		// 过滤掉重复的属性
		_filterAttribute(allAttr, filterAttr){
			return allAttr.filter(item => {
				return !filterAttr.find(a => a.id === item.id)
			})
		},

		_uniqRes(res = {}) {
			res.checked = _.uniq(res.checked, item => item.rowId); 			// 因为约束关系，要被勾选项
			res.disabled = _.uniq(res.disabled, item => item.rowId);		// 因为约束关系，要被禁止勾选项
			res.newRule = _.uniq(res.newRule, item => item._newRuleId);			// 新生效的约束关系
		},

		// 校验分组规则是否通过，不通过给提示
		_checkGroupRule(groupList = []) {
			let msg = '';
			groupList = _.uniq(groupList, item => item.rowId);
			groupList.forEach(item => {
				let r = util.validGroupData(this.treeTable, [item], true, true);
				if (!r) msg += '[' + item.product_id__r + '] ' + $t('约束规则和分组规则冲突') + '<br/>'
			});
			if (msg) this.alertMsg(msg);
			return !msg
		},

		// 初始化默认属性值
		initSelectedAttr(data = []) {
			data.forEach(item => {
				if (!item.selectedAttr && item._defaultAttr) item.selectedAttr = item._defaultAttr;
			})
		},

		// 是否有属性
		_hasAttr(data = {}) {
			return data.attribute && data.attribute.length || data.nonstandardAttribute && data.nonstandardAttribute.length
		},

		// 从标记中删除对应的规则id
		_reduceRuleIdForMark(bomId, ruleId, allData, current_root_new_path) {
			let d = CRM.util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
			if (d && d.__isMustSelectedByConstraint && d.__isMustSelectedByConstraint.length) {
				util.deleteArrChildren(d.__isMustSelectedByConstraint, ruleId)
			}
		},

		/**
		 * @desc 撤销约束关系；
		 * @desc 去掉约束条件中，必须选择和不允许选择的节点的置灰；有勾选的还保留勾选；
		 * @param checkedData
		 * @param allData 所有数据
		 * @param rule 所有规则
		 * @returns {{msg: string, status: boolean}}
		 */
		revokeBomAttrConstraint({checkedData = [], allData = [], rule = [],} = {}) {
			let res = {
				status: true,
				msg: '',
				disabled_c: [], 			// 要取消disabled的数据
				attrDisabled: [],  			// 要取消掉置灰的属性
				cancelAttrMustSeleted: [],	// 要取消必选的属性
				cancelNsAttr: [],    		// 要取消的非标属性规则，允许选择范围
				cancelNotAllowedNsAttr: [], // 要取消的非标属性规则，不允许选择范围
			};

			// 去掉父级的置灰；
			// function _checkParentRevokeDisabled(pid) {
			// 	let pData = CRM.util.getDataByKey(pid, allData, 'rowId');
			// 	if (!pData) {
			// 		res.status = false;
			// 		res.msg = $t('未找到父节点');
			// 		return false;
			// 	}
			//
			// 	// 如果父级是因为约束关系被禁止选中的；
			// 	if (pData.disabled && pData.__isConstraintSetting) {
			// 		pData.disabled = false;
			// 		pData.__isConstraintSetting = false;
			// 		res.disabled_c.push(pData);
			// 	}
			//
			// 	if (pData.pid) _checkParentRevokeDisabled(pData.pid);
			//
			// 	return pData
			// }

			let needRevoke = [];
			let revokeRule = this.getAttrRule(checkedData, rule);
			revokeRule = _.uniq(revokeRule, item => item._newRuleId);
			revokeRule = this._getRevokeRule(revokeRule);
			if (!revokeRule.length) return res;
			let restRule = this.getRestRule(revokeRule);
			revokeRule.forEach(item => {
				if (item.constraint_type == '1') {		// 必须选择项
					let cloneRes = util.cloneObjArr(item.result_range);
					cloneRes.forEach(rd => {
						this._reduceRuleIdForMark(rd.bom_id, item._newRuleId, allData,  rd.current_root_new_path);
						let r = this._checkBomIsAllowRevoke(restRule, rd.bom_id, false, item.constraint_type, item.current_root_new_path);
						if (r) needRevoke.push({
							bomId: rd.bom_id,
							current_root_new_path: rd.current_root_new_path,
						});
						rd._ruleId = item._newRuleId;
						rd.current_root_new_path = item.current_root_new_path;
						// 如果有必选属性，则需要把其他属性的置灰去掉
						if (rd.attribute && rd.attribute.length) {
							res.cancelAttrMustSeleted.push(
								{
									attribute: util.cloneObjArr(rd.attribute),
									bom_id: rd.bom_id,
									current_root_new_path: rd.current_root_new_path,
								}
							);
							let row = util.findDataByBomIdAndPath(allData, rd.bom_id, item.current_root_new_path);
							rd.attribute.forEach(a => {
								let ra = row.attribute.find(i => i.id === a.id);
								a.attribute_values = this._filterAttribute(ra.attribute_values, a.attribute_values);
							});
							// 剩余约束规则中是否还有相同的被约束节点及属性
							let disabledAttr = this._getAllowCancelAttr2(restRule, rd.bom_id, rd.attribute, 'disabled', row, item.current_root_new_path);
							if (!disabledAttr.length) return;
							res.attrDisabled.push({
								bom_id: rd.bom_id,
								attribute: disabledAttr,
								current_root_new_path: rd.current_root_new_path,
							});
						}
						if (rd.nonstandardAttribute && rd.nonstandardAttribute.length) res.cancelNsAttr.push(rd);
					})
				} else if (item.constraint_type == '2') {								// 不允许选择项
					item.result_range.forEach(rd => {
						rd._ruleId = item._newRuleId;
						rd.current_root_new_path = item.current_root_new_path;
						if (!this._hasAttr(rd)) {
							let r = this._checkBomIsAllowRevoke(restRule, rd.bom_id, true, item.constraint_type, item.current_root_new_path);
							if (r) needRevoke.push({
                                group_id: rd.group_id,
                                group_name: rd.group_name,
								bomId: rd.bom_id,
								current_root_new_path: rd.current_root_new_path,
							});
						}
						if (rd.attribute && rd.attribute.length) {
							// 剩余约束规则中是否还有相同的被约束节点及属性
							let row = util.findDataByBomIdAndPath(allData, rd.bom_id, item.current_root_new_path);
							let disabledAttr = this._getAllowCancelAttr2(restRule, rd.bom_id, rd.attribute, 'disabled', row, item.current_root_new_path);
							if (!disabledAttr.length) return;
							res.attrDisabled.push({
								bom_id: rd.bom_id,
								attribute: disabledAttr,
								current_root_new_path: rd.current_root_new_path,
							});
						}
						if (rd.nonstandardAttribute && rd.nonstandardAttribute.length) res.cancelNotAllowedNsAttr.push(rd);
					})
				} else if(item.constraint_type == '3'){			// 可选范围
					let cloneRes = util.cloneObjArr(item.result_range);
					cloneRes.forEach(rd => {
						rd._ruleId = item._newRuleId;
						rd.current_root_new_path = item.current_root_new_path;
						if (rd.attribute && rd.attribute.length) {
							let row = util.findDataByBomIdAndPath(allData, rd.bom_id, item.current_root_new_path);
							if (!row) return;
                            rd.attribute.forEach(a => {
								let ra = row.attribute.find(i => i.id === a.id);
								a.attribute_values = this._filterAttribute(ra.attribute_values, a.attribute_values);
							});
							// 剩余约束规则中是否还有相同的被约束节点及属性
							let disabledAttr = this._getAllowCancelAttr2(restRule, rd.bom_id, rd.attribute, 'disabled', row, item.current_root_new_path);
							if (!disabledAttr.length) return;
							res.attrDisabled.push({
								bom_id: rd.bom_id,
								attribute: disabledAttr,
								current_root_new_path: rd.current_root_new_path,
							});
						}
					})
				}
			});

			needRevoke.forEach(item => {
				let {bomId, current_root_new_path, group_id} = item;
				let d = CRM.util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
				if (d) {
					if (d.disabled && d.__isConstraintSetting) {
						d.disabled = false;
						d.__isConstraintSetting = false;
						res.disabled_c.push(d);
						// if(d.pid) _checkParentRevokeDisabled(d.pid);
					}
				}
                if (group_id) {
                    let groupData = util.findDataByBomIdAndPath(allData, bomId, current_root_new_path);
                    if (groupData?.children) {
                        util.forEachTreeData(groupData.children, child => {
                            if (child.disabled && child.__isConstraintSetting) {
                                child.disabled = false;
                                child.__isConstraintSetting = false;
                                res.disabled_c.push(child);
                            }
                        })
                    }
                }
                
				// else {
				// 	res.status = false;
				// 	res.msg = $t('未找到被约束的子产品');
				// 	return res;
				// }
			});

			res.disabled_c = _.uniq(res.disabled_c, item => item.rowId);
			res.revokeRule = revokeRule;
			if (res.msg) this.alertMsg(res.msg);
			return res;
		},

		/**
		 * @desc 在剩余规则中，查找是否有相同的被约束节点或属性；
		 * @param restRule 剩余规则
		 * @param bomId
		 * @param includeAttr 是否校验有无属性
		 * @param constraintType 约束类型
		 * @param current_root_new_path
		 * @returns {boolean}
		 * @private
		 */
		_checkBomIsAllowRevoke(restRule = [], bomId = '', includeAttr = false, constraintType = '', current_root_new_path) {
			let r = true;
			restRule = restRule.filter(c => c.current_root_new_path === current_root_new_path); // 过滤出 只属于当前bom的规则
			restRule.forEach(item => {
				if (item.constraint_type == constraintType) {
					let f = item.result_range.find(rule => {
						return rule.bom_id === bomId;
					});
					if (f) {
						// 不管有没有属性，有相同的bom 就是匹配成功
						if (!includeAttr) {
							r = false;
						} else { // 要管属性；必须没有属性才算匹配成功
							if (!item.attribute && !item.nonstandardAttribute || !item.attribute.length && !item.nonstandardAttribute.length) {
								r = false;
							}
						}
					}
				}
			});
			return r;
		},

		/**
		 * @desc  过滤出允许操作 取消置灰的属性
		 * @param restRule 剩余规则
		 * @param bomId
		 * @param attrList 需要取消置灰的数据
		 * @param type disabled：查找过滤允许置灰的属性
		 * @param row  行数据
		 * @returns {*}
		 * @private
		 */
		_getAllowCancelAttr2(restRule, bomId, attrList, type, row, current_root_new_path){
			attrList = util.cloneObjArr(attrList);
			restRule = restRule.filter(c => c.current_root_new_path === current_root_new_path);
			restRule.forEach(item => {
				// 1 先找相同的bom
				let f = item.result_range.find(rule => {
					return rule.bom_id === bomId;
				});
				if(!f || !f.attribute || !f.attribute.length) return;

				// 2 再找相同的属性；
				f.attribute.forEach(attr => {
					let fa = attrList.find(a => a.id === attr.id);  // 当前想要取消的规则属性
					if (fa) {
						let sameAttr = [];
						if(type === 'disabled'){         		// 查找相同的不允许选择的属性
							if (item.constraint_type == '2') {  // 剩余规则的类型 也是不允许选择
								// 3 再找相同的属性值
								attr.attribute_values.forEach(at => {
									let ft = fa.attribute_values.find(a2 => a2.id === at.id);
									if (ft) {
										sameAttr.push(ft)
									}
								})
							}else if(item.constraint_type == '1' || item.constraint_type == '3'){    // 剩余规则的类型 是必须选择或者范围。需要反向查找其他属性
								let ra = row.attribute.find(i => i.id === attr.id);
								if(!ra) return;
								let disAttr = this._filterAttribute(ra.attribute_values, attr.attribute_values);   // 因为必选或者范围 而置灰的属性
								let sa = this._findSameAttr(fa.attribute_values, disAttr);
								sameAttr = sameAttr.concat(sa);
							}
							fa.attribute_values = this._filterAttribute(fa.attribute_values, sameAttr);
						}
					}
				});
			});

			return attrList;
		},

		// 查找相同的属性
		_findSameAttr(allAttr, sameAttr){
			let r = [];
			allAttr.forEach(at => {
				let ft = sameAttr.find(a2 => a2.id === at.id);
				if (ft) {
					r.push(at)
				}
			});
			return r;
		},

        // 获取要执行的约束规则
		getAttrRule(data, rules) {
			let res = [];
			rules.forEach(item => {
				let r = true;
				// 先找数据，后匹配属性，全都匹配上才算匹配到规则
				item.condition_range.forEach(c => {
					let fd = false;
					let findGroup = false
					if (c.group_id && c.bom_id) {
                        let allData = this._getAllDataIncludeRoot();
						// 根据bom_id获取分组数据
						let groupData = util.findDataByBomIdAndPath(allData, c.bom_id, item.current_root_new_path);
						if (groupData.children) {
                            // 遍历分组子件是否有选中 ，若有则分组为被选中
                            let checked_data = groupData.children.find(child => child.isChecked);
                            if (checked_data) {
                                findGroup = true;
                            }
						}
					} else {
                        fd = data.find(d => d.bom_id === c.bom_id && item.current_root_new_path === d.current_root_new_path);
                    }
					if (!fd && !findGroup) return r = false;
					if (c.attribute && c.attribute.length) {
						c.attribute.forEach(pAttr => {
							if (!fd.selectedAttr || !fd.selectedAttr[pAttr.id]) return r = false;
							let selectedAttr = fd.selectedAttr[pAttr.id].value_ids;
							pAttr.attribute_values.forEach(attr => {
								let findAttr = selectedAttr.find(attrInfo => attrInfo.id === attr.id);
								if (!findAttr) return r = false;
							})
						})
					}
					// 非标属性值 必须要匹配约束规则的最大、最小值范围
					if (c.nonstandardAttribute && c.nonstandardAttribute.length) {
						if (!fd.nsAttr) return r = false;
						c.nonstandardAttribute.forEach(pAttr => {
							let matchAttr = fd.nsAttr[pAttr.id];
							if (!matchAttr) return r = false;
							let nsAttrValue = Number(matchAttr.value);
							pAttr.attribute_values.forEach(attr => {
								if (nsAttrValue < Number(attr.minValue)) return r = false;
								if (util.hasValue(attr.maxValue) && nsAttrValue > Number(attr.maxValue)) return r = false;
							})
						})
					}
				});
				if (r) res.push(item);
			});
			return res;
		},

		/**
		 * @desc 处理约束关系结果信息；勾选数据和禁选数据
		 * @param res
		 * @returns {boolean}
		 */
		disposeCheckboxStatus: function (res) {
			if (!res.status) {
				this.alertMsg(res.msg);
				return false;
			}
			if (res.checked && res.checked.length) {
				res.checked = _.uniq(res.checked, c => c.rowId);
				this.treeTable.setChildrenRowStatus(res.checked, true, true);
				this.displayAttribute(res.checked)
			}
			if (res.disabled && res.disabled.length) this.treeTable.setCheckboxDisabled(res.disabled, true);
			if (res.disabled_c && res.disabled_c.length) this.treeTable.setCheckboxDisabled(res.disabled_c, false);
			return true
		},

		noFindRow(product_name){
			let msg = $t('产品[{{P}}]，已下架或删除，请调整相关约束规则后再进行配置操作',{
				P: product_name
			});
			this.alertMsg(msg);
		},

		// 处理属性约束结果；属性选中和禁选
		disposeAttrStatus(res = {}, allData = [], disableStatus) {
			let msg = [];
			let r = {status: true, attrChangeRow: []};
			// 属性必选选择
			if (res.attrSelect && res.attrSelect.length) {
				this.displayAttribute();
				res.attrSelect.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					if (rowData._isRoot) {
						let r1 = this.setRootAttrMustSelected(rowData, item.attribute);
						if (!r1) return r.status = false;
					} else {
						let r1 = this.treeTable.setRowAttrMustSelected(rowData, item.attribute);
						if (!r1) return r.status = false;
						msg.push(rowData.product_id__r);
						r.attrChangeRow.push(rowData);
					}
				});
			}
			// 属性不允许选择
			if (res.attrDisabled && res.attrDisabled.length) {
				res.attrDisabled.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					if (rowData._isRoot) {
						let r1 = this.setRootAttrDisabled(rowData, item.attribute, !disableStatus);
						if (!r1) return r.status = false;
					} else {
						let r1 = this.treeTable.setRowAttrDisabled(rowData, item.attribute, disableStatus);
						if (!r1.status) return r.status = false;
					}
				})
			}
			// 非标属性必选范围
			if (res.nsAttrSelect && res.nsAttrSelect.length) {
				res.nsAttrSelect.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					item.nonstandardAttribute.forEach(attr => {
						let f = rowData.nonstandardAttribute.find(a => a.id === attr.id);
						if (!f) return;
						f.valueRange = f.valueRange || {};
						f.valueRange[item._ruleId] = attr.attribute_values[0];
						let checkRes = this.checkNsAttrValue(rowData, attr.id);
						if(!checkRes) return r.status = false;
					})
				})
			}
			// 非标属性不允许选择范围
			if (res.nsAttrDisabled && res.nsAttrDisabled.length) {
				res.nsAttrDisabled.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					item.nonstandardAttribute.forEach(attr => {
						let f = rowData.nonstandardAttribute.find(a => a.id === attr.id);
						if (!f) return;
						f.notAllowedValueRange = f.notAllowedValueRange || {};
						f.notAllowedValueRange[item._ruleId] = attr.attribute_values[0];
						let checkRes = this.checkNsAttrValue(rowData, attr.id);
						if(!checkRes) return r.status = false;
					})
				})
			}
			// 要取消的非标属性规则-必选选择范围
			if (res.cancelNsAttr && res.cancelNsAttr.length) {
				res.cancelNsAttr.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					item.nonstandardAttribute.forEach(attr => {
						let f = rowData.nonstandardAttribute.find(a => a.id === attr.id);
						if (!f || !f.valueRange) return;
						delete f.valueRange[item._ruleId];
					})
				})
			}
			// 要取消的非标属性规则-不允许选择范围
			if (res.cancelNotAllowedNsAttr && res.cancelNotAllowedNsAttr.length) {
				res.cancelNotAllowedNsAttr.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					item.nonstandardAttribute.forEach(attr => {
						let f = rowData.nonstandardAttribute.find(a => a.id === attr.id);
						if (!f || !f.notAllowedValueRange) return;
						delete f.notAllowedValueRange[item._ruleId];
					})
				})
			}

			// 取消属性必选选择
			if (res.cancelAttrMustSeleted && res.cancelAttrMustSeleted.length) {
				res.cancelAttrMustSeleted.forEach(item => {
					let rowData = util.findDataByBomIdAndPath(allData, item.bom_id, item.current_root_new_path);
					if(!rowData) {
						this.noFindRow(item.product_name);
						return r.status = false;
					}
					item.attribute.forEach(c => {
						let attr = rowData.selectedAttr[c.id];
						if(attr){
							c.attribute_values.forEach(v => {
								let f = attr.value_ids.find(a => a.id === v.id);
								if(f){
									f.isRequired = false;
								}
							});
						}
					});
				});
			}
			return r
		},

		// 重置表格数据
		resetTableData() {
			if(!this.cloneData) return;
			if(this._cloneAttrConstraintRule) this.currentRule = this._cloneAttrConstraintRule;
			this.treeData = this.cloneData;
			this.treeTable.refresh(this.cloneData);
		},

		// 从缓存中删除要取消的约束规则
		reduceCurrentRule(revokeRule) {
			revokeRule.forEach(rule => {
				let fIndex = this.currentRule.findIndex(r => r._newRuleId === rule._newRuleId);
				if (fIndex > -1) {
					this.currentRule.splice(fIndex, 1);
				}
			})
		},

		// 是否有属性约束标记
		checkHasConstraintRule(data = {}) {
			if (data._attrConMark) return true;
            let allData = this._getAllDataIncludeRoot();
            let groupData = util.findDataByBomIdAndPath(allData, data.product_group_id, data.current_root_new_path);
            // 如果data的父级是分组，且有标记
            if (groupData && groupData.isGroup && groupData._attrConMark) return true;
			let r = false;
			if (data.children && data.children.length) {
				util.forEachTreeData(data.children, item => {
					if (item._attrConMark) r = true;
				})
			}
			return r;
		},

		// 校验非标属性值，是否符合当前约束规则
		checkNsAttrValue(data = {}, nsAttrId = '', noAlert = false, nsAttrVal) {
			let status = true;
			let msg = $t('产品') + '【 ' + data.product_id__r + ' 】';
			if (data.nonstandardAttribute && data.nonstandardAttribute.length) {
				data.nonstandardAttribute.forEach(nst => {
					let v = data.nsAttr && data.nsAttr[nst.id] || {};
					if (nsAttrId && nst.id !== nsAttrId) return;
					let val = nsAttrVal === undefined ? v.value : nsAttrVal;
					if(!util.hasValue(val)) return;
					let nv = Number(val);
					if (nst.valueRange || nst.notAllowedValueRange) msg += $t('属性') + '【  ' + nst.name + ' 】 ';
					if (nst.valueRange) {
						_.each(nst.valueRange, rv => {
							let min = Number(rv.minValue);
							let max = util.hasValue(rv.maxValue) ? Number(rv.maxValue) : '';
							msg += $t('该属性值范围是') + '：' + min + ' - ' + (max || $t('不限')) + '; ';
							if (!util.hasValue(val) || nv < min || max && nv > max) {
								status = false;
								this.treeTable.showNsAttrError(data, nst.id);
							}
						});
					}
					if (nst.notAllowedValueRange) {
						_.each(nst.notAllowedValueRange, nrv => {
							let min = Number(nrv.minValue);
							let max = util.hasValue(nrv.maxValue) ? Number(nrv.maxValue) : '';
							msg += $t('该属性值不允许在') + '：' + min + ' - ' + ((max || $t('不限')) + $t('范围内')) + '; ';
							if (nv >= min && ((max && nv <= max) || !max)) {
								status = false;
								this.treeTable.showNsAttrError(data, nst.id);
							}
						});
					}
				});
			}
			if (!status && !noAlert) this.alertMsg(msg);
			return status;
		},

		// 清掉错误提示
		_clearNsAttrError(attrId, $item){
			this.treeTable.clearNsAttrError(attrId, $item);

		},

		// 非标属性blur事件
		async nsAttributeBlur(data, oldVal, $item, attrId) {
			if (data.nsAttr) {
				this._clearNsAttrError(attrId, $item);
				let checkRes = this.checkNsAttrValue(data, attrId);
				if (!checkRes) {
					this.resetOldNsAttr(data, oldVal, $item);
					checkRes = this.checkNsAttrValue(data, attrId, true);
					if (checkRes) this._clearNsAttrError(attrId, $item);
					return;
				}
				await this.executeBomAttrConstraint();
				await this.nsAttributeBlurHook(data);
				await this.calculateFormulaRow(data, 'nonAttribute');
				if(this.options.changeNsAttrHook){
					let obj = {
						attrInfo: {
							[attrId]:{
								value: data.nsAttr[attrId].value
							}
						},
						rootData: this.rootData,
						changeRow: data,
						allData: this.treeTable.getAllData(),
						api: this._getApi(),
					};
					this.options.changeNsAttrHook(obj)
				}
			}
		},

		nsAttributeBlurHook:$.noop,

		// 非标属性失焦之前
		nsAttributeBlurBefore(data) {
			this.updateCloneData();
		},

		// 重置非标属性值
		resetOldNsAttr(data, oldVal, $item) {
			this.treeTable.setNsAttrValue(data, oldVal, $item);
			_.each(oldVal, (val, key) => {
				this.ChildrenAttrRange?.setValue(key, val.value);
			});
		},

		// 根节点属性dom
		_getRootAttrElement(){
			return this.options.rootAttrElement && this.options.rootAttrElement.find('.dialog_attribute') || this.$el.find('.dialog_attribute');
		},

		//展示根节点属性
		renderParentAttribute() {
			const me = this;
			if (!me.rootData.attribute && !me.rootData.nonstandardAttribute) return;
			require.async('crm-modules/components/attribute_comp/attribute_comp', function (AttributeComp) {
				me.attributeComp = new AttributeComp({
					$el: me._getRootAttrElement(),
					data: me.rootData,
				});
				me.attributeComp.on('clickAttrVal', function (data) {
					me.rootAttrChange(data);
				});
				me.attributeComp.on('inputNsAttrVal', function (data) {
					me.rootNonAttrChange(data);
				});
				me.attributeComp.on('render.after', function (el) {
					me.attributeCompRenderAfter(el);
				});
			});
		},

		// 根节点的属性渲染完，需要重新计算表格高度
		attributeCompRenderAfter(el) {
			if(this._isStyle3()) return;
			let h = el.parent().height();
			var height = $(window).height() - 57 - 55 - h;
			if(this.options.changeHeightHook){
				height = this.options.changeHeightHook(height);
			}
			this.$el.find('.dialog_selectbom').height(height);
			this.attributeCompRenderAfter_hook(height);
			this.treeTable.resize();
		},

		attributeCompRenderAfter_hook(h){},

		/**
		 * 根节点标准属性change
		 * @param attrInfo
		 * @param resetAttr
		 * @param noChangeChildrenAttr: 不同步子件属性
		 * @returns {Promise<boolean>}
		 */

		async rootAttrChange(attrInfo, resetAttr = false, noChangeChildrenAttr = false) {
			this.updateCloneData();
			if (!this.rootData.selectedAttr) this.rootData.selectedAttr = {};
			let oldData = $.extend(true, {}, this.rootData.selectedAttr);
			let attrList = Array.isArray(attrInfo) ? attrInfo : [attrInfo];
			let resetAttrChildren = resetAttr ? this.resetAllAttr() : [];
			attrList.forEach(attr => {
				let f = this.rootData.attribute?.find(a => a.id === attr.attrId);
				if(!f) return;
				this.rootData.selectedAttr[attr.attrId] = {
					name: attr.name,
					value_ids: [attr.attrValue]
				};
			});
			let changeChildren = [];
			let r = await this.executeBomAttrConstraint({beforeHook: () => {
				if(!noChangeChildrenAttr){
					changeChildren = this.synchronizedChildrenAttr(attrList, 'attribute', noChangeChildrenAttr);
				}
				}});
			if (!r) {
				 this._resetRootDataAttrValue(this.rootData, oldData);
				 return false;
			};
			if(resetAttrChildren) changeChildren = [...changeChildren, ...resetAttrChildren || []];
			let realRes;
			if(this.options.synchronizedChildrenAttr) realRes = await this.getRealPriceForBom([...changeChildren, this.rootData]);
			let isFilterChildren = this._getIsFilterChildrenByAttr();
			if(isFilterChildren) this.onlyShowSelectedRow();
			await this.afterRootDataAttrChange(realRes);
			await this.calculateFormulaRow([this.rootData].concat(changeChildren), 'attribute');
			this.trigger('rootAttrChange', this.rootData.selectedAttr);
			return true;
		},

		/**
		 * @desc  重置根节点的属性值
		 * @param rowData
		 * @param attrValue: {
		 * 					6107b8d128b99a00019c9436: {
		 * 						name: "颜色" ,
		 * 						value_ids: [{id: '6107b8d128b99a00019c94dc', name: '白'}]
		 * 						}
		 * 					}
		 * @private
		 */
		_resetRootDataAttrValue(rowData, attrValue) {
			if (!attrValue) return;
			_.each(attrValue, (item, key) => {
				let attrVal = item.value_ids[0].id;
				this._setRootAttrValue(rowData, key, attrVal);
			})
		},

		_setRootAttrValue(data = {}, attrId = '', attrVal = ''){
			this.attributeComp?.setAttrVal(data, {
				attrId: attrId,  		//非标属性Id,
				valueId: attrVal
			}, true);
			this.AttrRange?.setValue(attrId, [attrVal]);
		},


		// 同步根节点属性信息
		updateNewPriceBookDataAttr() {
			if(!this.newPriceBookProductData) return;
			let keys = ['selectedAttr', 'nsAttr', 'attribute', 'nonstandardAttribute'];
			keys.forEach(key => {
				if (this.rootData[key]) this.newPriceBookProductData[key] = this.rootData[key];
			})
		},

		// 根节点属性change之后；需要走取价，重新计算调整金额；
		async afterRootDataAttrChange(realRes) {
			if(!this._hasAccountId()) return;
			this.updateNewPriceBookDataAttr();
			let data = this.parseNormalDataForPriceService(this.newPriceBookProductData);
			let newRst = realRes?.length ? realRes : await this._getRealPrice(data);
			let result = _.find(newRst, item => item.product_id === this.options.rootId);
			this.newPriceBookProductData = result;
			this.updateNewPriceBookDataAttr();
			this.options.originalTotalMoney = result.hasOwnProperty('selling_price') ? result.selling_price : result.pricebook_sellingprice;
			this.updateDefTotalMoney();
			this.calculateTotalMoney();
		},

		// 根节点非标属性change
		async rootNonAttrChange(attrList, resetAttr = false, noChangeChildrenAttr) {
			this.updateCloneData();
			if (!this.rootData.nsAttr) this.rootData.nsAttr = {};
			attrList = Array.isArray(attrList) ? attrList : [attrList];
			if(this.rootData.nsAttr[attrList[0].attrId]?.value == attrList[0].attrValue.value) return true;
			let oldVal = {};
			if (Object.keys(this.rootData.nsAttr).length) {
				oldVal = $.extend(true, {}, this.rootData.nsAttr);
			} else {
				attrList.map(attr => {
					oldVal[attr.attrId] = {}
				})
			}

			let resetAttrChildren = resetAttr ? this.resetAllAttr() : [];

			attrList.forEach(attrInfo => {
				let f = this.rootData.nonstandardAttribute?.find(a => a.id === attrInfo.attrId);
				if(!f) return;
				this.rootData.nsAttr[attrInfo.attrId] = attrInfo.attrValue;
			});

			let checkRes = this.checkNsAttrValue(this.rootData);
			if (!checkRes) {
				this.resetRootOldNsAttr(this.rootData, oldVal);
				return false;
			};

			let changeChildren = [];
			let res = await this.executeBomAttrConstraint({beforeHook: () => {
					if(!noChangeChildrenAttr){
						changeChildren = this.synchronizedChildrenAttr(attrList, 'nonAttribute');
					}
				}});

			if (!res) {
				this.resetRootOldNsAttr(this.rootData, oldVal);
				return false;
			};

			if(resetAttrChildren) changeChildren = [...changeChildren, ...resetAttrChildren];
			await this.calculateFormulaRow([this.rootData].concat(changeChildren), 'nonAttribute');
			if(this.options.changeNsAttrHook){
				let obj = {
					attrInfo:{
						[attrList[0].attrId]:{
							value: attrList[0].attrValue.value
						}
					},
					attrList,
					rootData: this.rootData,
					changeRow: this.rootData,
					allData: this.treeTable.getAllData(),
					api: this._getApi(),
				};
				this.options.changeNsAttrHook(obj)
			}
			this.trigger('rootNsAttrChange', this.rootData.nsAttr);
			return true;
		},

		// 设置根节点非标属性值
		resetRootOldNsAttr(data = {}, oldVal = {}) {
			_.each(oldVal, (val, key) => {
				this._setRootNsAttrValue(data, key, val.value);
			});
		},

		_setRootNsAttrValue(data = {}, attrId = '', attrValue = ''){
			let v = util.hasValue(attrValue) ? Number(attrValue) : null;
			this.attributeComp?.setNsAttrVal(data, {
				attrId: attrId,  	//非标属性Id,
				value: v 		//属性值-数字
			}, true);
			this.AttrRange?.setValue(attrId,  v);
		},

		// 设置根节点属性必选
		setRootAttrMustSelected(rowData = {}, attrList = []) {
			if (rowData.attribute && rowData.attribute.length) {
				let disabledAttr = this.treeTable.filterAttrBySelectedAttr(rowData.attribute, attrList);
				let r = this.setRootAttrSelected(rowData, attrList);
				if (!r) return r;
				r = this.setRootAttrDisabled(rowData, disabledAttr, false);
				if (!r) return r;
				this.afterRootDataAttrChange();
			}
			return true;
		},

		/**
		 * @desc 设置根节点属性选中
		 * @param rowData
		 * @param attrList:[
		 *    {
		 *        id:'',
		 *        name:'',
		 *        attribute_values:[
		 *            {
		 *                id:'',
		 *                name:''
		 *            }
		 *        ]
		 *    }
		 * ]
		 */
		setRootAttrSelected(rowData = {}, attrList = []) {
			let msg = '';
			attrList.forEach(attrInfo => {
				attrInfo.attribute_values.forEach(attr => {
					let r = this.attributeComp?.setAttrVal(rowData, {
						attrId: attrInfo.id,  		// 非标属性Id,
						valueId: attr.id
					}, true);

					if (r === false){
						msg += '[' + rowData.product_id__r + '] ' + attrInfo.name + ':' + attr.name + '，' + $t('属性已禁选，不可选中') + '<br/>';
					}else{
						this.AttrRange?.setValue(attrInfo.id, [attr.id]);
					}
				});
			});
			if (msg) this.alertMsg(msg);
			return !msg;
		},

		// 校验属性是否已被选中
		checkAttrSelected(data, attrId, attrVal) {
			if (data.selectedAttr && data.selectedAttr[attrId]) {
				let val = data.selectedAttr[attrId].value_ids;
				let f = val.find(a => a.id === attrVal);
				if (f) return true;
			}
			return false;
		},

		// 设置根节点属性禁用
		setRootAttrDisabled(rowData = {}, attrList = [], status) {
			let msg = '';
			attrList.forEach(attrInfo => {
				attrInfo.attribute_values.forEach(attr => {
					if (!status) {
						let checkRes = this.checkAttrSelected(rowData, attrInfo.id, attr.id);
						if (checkRes) return msg += '[' + rowData.product_id__r + '] ' + attrInfo.name + ':' + attr.name + '，' + $t('约束规则冲突，该属性已选中，不可置灰') + '<br/>';
					}
					this.attributeComp?.setAttrStatus(rowData, {
						attrId: attrInfo.id,  		//非标属性Id,
						valueId: attr.id
					}, status);
					this.AttrRange?.setValueDisabled(attrInfo.id, {
						[attr.id]: !status,
					});
				});
			});
			if (msg) this.alertMsg(msg);
			return !msg;
		},

		// ============约束数量==================

		// 计算数量
		async _doCalculateAmount() {
			if (!this._cacheCalculateAmount.length) return true;
			let status = true;
			let param = this.getParamForCalculateAmount(this._cacheCalculateAmount);
			let startTime = new Date().getTime();
			let res = await CRM.util.ajaxCalculateBomAmount(param).catch(e => {
				status = false;
			});
			let endTime = new Date().getTime();
			this.sendLogForTime('crmBomCalculateAmountServiceTime', endTime - startTime);
			if (!res?.dataList) status = false;
			if(status) await this.setAplResult(res.dataList);
			this._cacheCalculateAmount = [];
			return status;
		},

		// 处理计算结果
		async setAplResult(arr){
 			// let res = {
			// 	"dataList": [{ //每个子件对应的计算公式计算结果
			// 		"bom_id": "66b0afe4d50a240007af0e47",
			// 		"new_bom_path": "66b0afe4d50a240007af0e47",
			// 		"product_id": "66b0afe4d50a240007af0exx",
			// 		"valueResultList": [{ //支持同时计算数量、属性、非标属性，针对高级公式，只有数量，APL属性赋值，采用相同返回格式
			// 			"type": 0, //0表示计算产品普通属性
			// 			"fieldName": "amount", //要赋值的字段
			// 			"value": {
			// 				"amount": 10
			// 			}
			// 		}, {
			// 			"type": 1, // 1表示属性赋值
			// 			"fieldName": "attribute", //预留字段，可以没值
			// 			"value": { //key为属性ID，value为属性值（属性值的ID？？）
			// 				"66b0afe4d50a240007af0e22": "66b0afe4d50a240007af0exs",
			// 				"66b0afe4d50a240007af0e33": "66b0afe4d50a240007af0ed2"
			// 			}
			// 		}, {
			// 			"type": 2, // 2表示非标属性赋值
			// 			"fieldName": "nonAttribute",
			// 			"value": { //key为非标属性ID，value为非标属性值
			// 				"66b0afe4d50a240007af0e44": 20,
			// 				"66b0afe4d50a240007af0e55": 30
			// 			}
			// 		}]
			// 	}]
			// }

			let allData = this._getAllDataIncludeRoot();
			let needRunAttr = false;
			let msg = '';
			let errorData = [];
			let status = true;
			let _cacheChangeQuantity = [];

			arr.forEach(item => {{
				let rowData = util.findDataByNewBomPath(allData, item.new_bom_path);
				item.valueResultList.forEach( r => {
					switch (r.type) {
						case 0:			// 改数量
							let f = r.fieldName;
							let val = r.value[f];
							if(!util.hasValue(val)) return;
							// 校验数量计算结果，不能为 0
							if(f === 'amount'){
								if(val <= 0) {
									errorData.push(rowData.product_id__r);
									status = false;
									return;
								}
							}
							_cacheChangeQuantity.push(rowData);
							this.setTableAmount(rowData, f, r.value[f]);
							break;
						case 1:			// 改属性
							needRunAttr = true;
							let arr = [];
							for(let key in r.value){
								let o = this.getAttrInfoByAttrId(rowData, key, r.value[key]);
								arr.push(o)
							}
							if(rowData._isRoot){
								let r1 = this.setRootAttrSelected(rowData, attrList);
								if (!r1) status = false;
							}else{
								let r2 = this.treeTable.setRowAttrSelected(rowData, arr);
								if(!r2) status = false;
							}
							break;
						case 2:			// 改非标属性
							if(rowData._isRoot){
								this.setRootNsAttributeValue(rowData, r.value)
							}else{
								let obj = {};
								for(let key in r.value){
									obj[key] = {
										value: r.value[key]
									}
								}
								this.setNsAttributeValue(rowData.rowId, obj);
							}
							break;
					}
				})
			}});
			if(errorData.length) {
				// 产品a、b、c的数量为非正数，请检查约束规则中的【高级公式】或者【赋值函数】是否正确！
				let m = '【' + errorData.join(',') + '】' + $t('bom.apl.alert3'); // 数量为非正数，请检查约束规则中的【高级公式】或者【赋值函数】是否正确！
				this.alertMsg(m);
			}
			if(needRunAttr) status = await this.executeBomAttrConstraint();
			this.updateChangeRowParentPrice(_cacheChangeQuantity);
			this.resetAllAmount();
			this.calculateTotalMoney();
			return status;
		},

		// 根据属性 id 和属性值 id，获取属性完整信息
		getAttrInfoByAttrId(rowData, attrId, attrValueId){
			if(!rowData.attribute) return;
			let attr = rowData.attribute.find(item => item.id === attrId);
			if(!attr) return;
			let attrValInfo = attr.attribute_values.find(item => item.id === attrValueId);
			if(!attrValInfo) return;
			return {
				id: attrId,
				name: attr.name || '',
				attribute_values:[
					{
						id: attrValueId,
						name: attrValInfo.name || ''
					}
				]
			};
		},

		// 改数量的行，需要重新计算父级加个
		updateChangeRowParentPrice(list){
			list.forEach(item => {
				this.upDateEveryParentPrice(item.pid);
			})
		},

		// 全跑一次数量成倍
		resetAllAmount(){
			let data = this._getTableData();
			util.forEachTreeData(data, item => {
				this.changeChildrenAmount(item);
			})
		},

		// 改表格数量;
		setTableAmount(rowData, field, val){
			if(field === 'amount'){
				let dp = this._getDescribe('amount')?.decimal_places;
				val = util.formatDecimalPlace(val, dp);
				rowData.__amount = val;
				this.treeTable.setCellsVal({
					[rowData.rowId]:{
						data:{
							amount: val
						}
					}
				});
			}
		},

		// 获取当前选中行数据
		_getSelectedBomList(onlyChecked){
			let allData = this._getAllDataIncludeRoot();
			let allCheckedData = CRM.util.parseTreeToNormal(allData, true, true);
			if(onlyChecked) allCheckedData = allCheckedData.filter(item => item.isChecked);
			return allCheckedData;
		},

		// 获取当前选择数据，组装数据给 server 用
		_getSelectBomListForServer(){
			let allCheckedData = this._getSelectedBomList();
			return util.parseBomForCheckRule(allCheckedData, null, true);
		},

		// 获取计算入参
		getParamForCalculateAmount(formulaList){
			let formulas = [];
			formulaList.forEach(rd => {
				let row = rd.rowData;
				rd.formula.forEach(item => {
					formulas.push({
						"expression": item.expression,
						"bom_id": row.bom_id,
						"product_id": row.product_id,
						"field_name": item.field_name,
						"new_bom_path": row.new_bom_path,
						"current_root_new_path": row.current_root_new_path
					})
				});
			});
			return {
				formulas,
				selectBomList: this._getSelectBomListForServer(),
			};
		},

		// 校验选中行计算公式
		async checkFormula(){
			let _this = this;
			let checkedData = this._getSelectedBomList(true);
			let arr = this.getFormulaByData(checkedData);
			if(!arr.length) return true;
			let allData = this._getAllDataIncludeRoot();
			let param = this.getParamForCalculateAmount(arr);
			let res = await CRM.util.ajaxCheckFormula(param);
			if(res){
				if(res.pass) return true;
				return new Promise(resolve => {
					let tableData = res.details.map(c => {
						let rowData = util.findDataByNewBomPath(allData, c.new_bom_path);
						return {
							productName: rowData.product_id__r,
							currentValue: c.currentValue,
							formulaValue: c.formulaValue,
						}
					});
					util.confirm20(null, $t('bom.apl.alert4'), { // 公式校验结果
						size: 'normal',
						type: 'table',
						tableColumns:[
							{
								label: $t('产品名称'),
								prop: 'productName',
								width: 260,
							},	{
								label: $t('当前') + $t('值'),
								prop: 'currentValue',
								width: 150,
							},	{
								label: $t('计算') + $t('值'),
								prop: 'formulaValue',
							}
						],
						tableData: tableData,
						showWarning: true,
						warningMsg: $t('bom.checkFormula.warning'),    // 以下产品高级公式校验不通过
						buttons:[
							{
								label: $t('crm.Recalculation'), // 重算
								type: 'primary',
								action: 'recalculationAction',
							},
							{
								label: $t('忽略'),
								type: 'primary',
								action: 'ignoreAction',
							},
							{
								label: $t('取消'),
								action: 'cancelAction',
							},
						],

						recalculationActionHandle(){
							_this.doRecalculation(res.details);
							this.hide();
							resolve(true)
						},
						ignoreActionHandle(){
							this.hide();
							resolve(true)
						},
						cancelActionHandle(){
							this.hide();
							resolve(false)
						},
					});
				})
			}
			return false;
		},

		// 重算
		doRecalculation(details){
			let allData = this._getTableData();
			details.forEach(item => {
				let rowData = util.findDataByNewBomPath(allData, item.new_bom_path);
				this.setTableAmount(rowData, item.fieldName || '', item.formulaValue);
				this.upDateEveryParentPrice(rowData.pid);
				this.calculateTotalMoney();
			});
		},

		// 找匹配的 apl 函数，优先匹配当前复用 bom 的，没有，再匹配根节点的
		_getAplName(rootPath){
			if(!this._attrAssignAplFunctionList?.length) return;
			let r = this._attrAssignAplFunctionList.find(item => item.current_root_new_path === rootPath)?.api_name;
			if(!r){
				let rp = rootPath.split('.')[0];
				r = this._attrAssignAplFunctionList.find(item => item.current_root_new_path === rp)?.api_name;
			}
			return r;
		},

		// apl 按钮计算
		async aplCalculateHandle(row){
			let aplApiName = this._getAplName(row.current_root_new_path);
			if(!aplApiName){
				this.alertMsg($t('bom.apl.alert1'),'info'); // 没有可执行的 APL 函数
				return
			}
			let selectBomList = this._getSelectBomListForServer();
			if(!selectBomList.length){
				this.alertMsg($t('bom.apl.alert2'),'info'); // 当前没有选中项
				return
			}
			let param = {
				aplApiName,
				triggerRowId: row.rowId,
				selectBomList,
			};
			let res = await CRM.util.ajaxCalculateByAPL(param);
			if (!res?.dataList) return;
			await this.setAplResult(res.dataList);
		},

		/**
		 * @desc 实时计算高级公式相关行
		 * @param changeRow
		 * @param changeField amount || attribute || nonAttribute
		 * @returns {Promise<void>}
		 */
		async calculateFormulaRow(data, changeField, ){
			if(!this._attrFormulaMap?.length) return;
			let allData = this._getAllDataIncludeRoot();
			data = Array.isArray(data) ? data : [data];
			let msg = [];
			data.forEach(changeRow => {
				let calRow = this._attrFormulaMap.find(item => item.bom_id === changeRow.bom_id && item.field_name === changeField);
				if(!calRow?.calculateRow) return;
				let current_root_new_path = calRow.current_root_new_path;
				calRow.calculateRow.forEach(item => {
					let rowData = CRM.util.findDataByBomIdAndPath(allData, item.bom_id, current_root_new_path);
					if(!rowData){
						msg.push(item.product_name);
						return;
					}
					let o = {
						formula: [
							{
								"expression": item.expression,
								"bom_id": item.bom_id,
								"product_id": rowData.product_id,
								"field_name": item.field_name || 'amount',
								"new_bom_path": rowData.new_bom_path,
								"current_root_new_path": rowData.current_root_new_path
							}
						],
						rowData,
					};
					this._cacheCalculateAmount.push(o);
				});
			});
			if(msg.length){
				this.alertMsg('【' + msg.join() + '】' + $t('sfa.bom.formula.msg1'), 'info'); // 这些要计算的数据不存在，请检查相关的计算公式；
			}
			await this._doCalculateAmount();
		},

		// 计算所有行公式
		async calculateAllFormula(){
			if(!this._attrConstraintRule?.length) return;
			let startTime = new Date().getTime();
			let msg = [];
			this._cacheCalculateAmount = [];
			let allData = this._getAllDataIncludeRoot();
			this._attrConstraintRule.forEach(item => {
				item.result_range.forEach(result => {
					if(result.formula){
						let rowData = CRM.util.findDataByBomIdAndPath(allData, result.bom_id, item.current_root_new_path);
						if(rowData){
							this._cacheCalculateAmount.push({
								formula: result.formula,
								rowData,
							})
						}else{
							msg.push(result.product_name)
						}
					}
				})
			});
			if(msg.length){
				this.alertMsg('【' + msg.join() + '】' + $t('sfa.bom.formula.msg1'), 'info'); // 这些要计算的数据不存在，请检查相关的计算公式；
			}
			await this._doCalculateAmount();
			let endTime = new Date().getTime();
			this.sendLogForTime('crmBomCalculateFormulaAllTime', endTime - startTime);
		},

		// 获取某些行计算公式
		getFormulaByData(data){
			let res = [];
			this._attrConstraintRule.forEach(item => {
				item.result_range.forEach(result => {
					if(result.formula){
						let rowData = CRM.util.findDataByBomIdAndPath(data, result.bom_id, item.current_root_new_path);
						if(rowData){
							res.push({
								formula: result.formula,
								rowData,
							})
						}
					}
				});
			});
			return res;
		},



	}
})
