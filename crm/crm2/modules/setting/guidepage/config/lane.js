define(function(require, exports, module) {"use strict";
/**
 * moduleId 配置模块id
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.lane_COMMON = exports.lane_MES = exports.lane_FMCG = exports.lane_HES = void 0;
const TEXT = {
    'shangpin': { text: `${$t('商品')}【${$t('多规格')}】`, moduleId: 'spuConfig' },
    'shuxing': { text: $t('属性'), moduleId: 'attributeConfig' },
    'product': { text: $t('产品'), moduleId: 'product' },
    'productClassification': { text: $t('产品分类'), moduleId: 'productClassification' },
    'multiunit': { text: $t('crm.multiunit', null, '多单位'), moduleId: 'multipleUnit' },
    'productPortfolio': { text: $t('产品组合'), moduleId: 'productPortfolio' },
    'productSelection': { text: $t('产品选配明细'), moduleId: 'productSelection' },
    'pricebook': { text: $t('价目表'), moduleId: 'pricebook' },
    'availableRange': { text: $t('可售范围'), moduleId: 'availableRange' },
    'promotion': { text: $t('价格政策'), moduleId: 'advancedPricing' },
    'rebate': { text: $t('返利政策'), moduleId: 'rebate' },
    'coupon': { text: $t('优惠券'), moduleId: 'coupon' },
    'quote': { text: $t('报价单'), moduleId: 'Quote' },
    'saleContract': { text: $t('销售合同'), moduleId: 'SaleContract' },
    'salesOrder': { text: $t('crm.SalesOrderObj', null, '销售订单'), moduleId: 'SalesOrder' },
    'deliveryNote': { text: $t('发货单'), moduleId: 'deliveryNote' },
    'outboundOrder': { text: $t('出库单'), moduleId: 'outboundOrder' },
    'exchangeGoods': { text: $t('退换货'), moduleId: 'exchangeGoods' },
    'receivableNote': { text: $t('crm.应收单'), moduleId: 'receivableNote' },
    'paymentPlan': { text: $t('回款计划'), moduleId: 'PaymentPlan' },
    'projectmanage': { text: $t('项目管理') },
    'invoiceApplication': { text: $t('开票申请'), moduleId: 'InvoiceApplication' },
    'payment': { text: $t('回款'), moduleId: 'Payment' },
    'customerAccount': { id: 26, text: $t('客户账户'), moduleId: 'customerAccount' },
};
// 四个行业配置流程图
let laneScale = 0.8;
let config1 = {
    containerHeight: 900,
    // 缩放比例
    laneScale
};
let config2 = {
    containerHeight: 800,
    // 缩放比例
    laneScale
};
let config3 = {
    containerHeight: 800,
    // 缩放比例
    laneScale
};
exports.lane_HES = formatData([]);
// 快消
exports.lane_FMCG = formatData([
    {
        id: 'container1',
        title: $t('crm.FMCGIndustry', null, '快消行业') + $t('crm.materialManage', null, '物料管理'),
        config: config3,
        laneList: [
            {
                id: 'lane1',
                lane_title: $t('crm.basicDataConfig', null, '基础数据配置'),
                nodes: [
                    Object.assign(Object.assign({ id: '1' }, TEXT['shangpin']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '3' }, TEXT['multiunit']), { row: 1, col: 3 }),
                    Object.assign(Object.assign({ id: '4' }, TEXT['product']), { row: 2, col: 2 }),
                    Object.assign(Object.assign({ id: '5' }, TEXT['productClassification']), { row: 3, col: 2 })
                ],
                links: [
                    { source: '1', target: '4', shape: 'dashed' },
                    { source: '3', target: '4', shape: 'dashed' },
                    { source: '5', target: '4' },
                    { source: '4', target: '8' }
                ]
            },
            {
                id: 'lane2',
                lane_title: $t('价格管理'),
                nodes: [
                    Object.assign(Object.assign({ id: '8' }, TEXT['pricebook']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '9' }, TEXT['availableRange']), { row: 3, col: 1 })
                ],
                links: [
                    { source: '8', target: '9', shape: 'dashed' },
                    { source: '8', target: '10', shape: 'dashed' },
                    { source: '8', target: '11', shape: 'dashed' },
                    { source: '8', target: '12', shape: 'dashed',
                        vertices: [
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p1.x + nodeWidth + node_margin_left, y: _p1.y + nodeWidth / 2
                                };
                            },
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p1.x + nodeWidth + node_margin_left, y: _p2.y
                                };
                            },
                        ],
                    },
                    { source: '8', target: '26', shape: 'dashed',
                    }
                ]
            },
            {
                id: 'lane3',
                lane_title: $t('营销活动管理'),
                nodes: [
                    Object.assign(Object.assign({ id: '10' }, TEXT['promotion']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '11' }, TEXT['rebate']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '26' }, TEXT['customerAccount']), { row: 3, col: 1 }),
                    Object.assign(Object.assign({ id: '12' }, TEXT['coupon']), { row: 4, col: 1 })
                ],
                links: []
            }
        ]
    },
    {
        id: 'container2',
        title: $t('crm.FMCGIndustry', null, '快消行业') + $t('crm.transactionManage', null, '交易管理'),
        config: config3,
        laneList: [
            {
                id: 'lane4',
                lane_title: $t('crm.quoteManage', null, '报价管理'),
                nodes: [Object.assign({ id: '25', row: 1, col: 1 }, TEXT['quote'])],
                links: [{ source: '25', target: '13', }]
            },
            {
                id: 'lane5',
                lane_title: $t('销售合同'),
                nodes: [Object.assign({ id: '13', row: 1, col: 1 }, TEXT['saleContract'])],
                links: [{ source: '13', target: '14', }]
            },
            {
                id: 'lane6',
                lane_title: $t('crm.orderFulfillment', null, '订单执行'),
                nodes: [
                    Object.assign({ id: '14', row: 1, col: 1 }, TEXT['salesOrder']),
                    Object.assign({ id: '15', row: 2, col: 1 }, TEXT['deliveryNote']),
                    Object.assign({ id: '16', row: 3, col: 1 }, TEXT['outboundOrder']),
                    Object.assign({ id: '17', row: 4, col: 1 }, TEXT['exchangeGoods'])
                ],
                links: [
                    { source: '14', target: '20', },
                    { source: '14', target: '15', attrs: { line: { refX: -10 } } },
                    { source: '14', target: '19', shape: 'dashed' },
                    { source: '15', target: '16' },
                    { source: '15', target: '19' },
                    {
                        source: '15',
                        target: '20',
                        vertices: [
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return { x: 1432, y: 169 + 84 + 30 };
                            }
                        ],
                    },
                    { source: '16', target: '17' },
                    { source: '16', target: '19', shape: 'dashed' }
                ]
            },
            {
                id: 'lane7',
                lane_title: $t('应收管理'),
                nodes: [Object.assign({ id: '19', row: 2, col: 1 }, TEXT['receivableNote'])],
                links: [
                    {
                        source: '19',
                        target: '20',
                        vertices: [
                            // { x: 1432 + 42, y: 307 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return { x: _p1.x + nodeWidth, y: _p1.y - 30 };
                            }
                        ]
                    }
                ]
            },
            {
                id: 'lane8',
                lane_title: $t('crm.invoicesManage', null, '发票管理'),
                nodes: [Object.assign({ id: '20', row: 1, col: 1 }, TEXT['invoiceApplication'])],
                links: []
            },
            {
                id: 'lane9',
                lane_title: $t('回款'),
                nodes: [
                    Object.assign({ id: '21', row: 2, col: 1 }, TEXT['payment']),
                    { id: '23', row: 3, col: 1, text: 'ERP', disabled: true }
                ],
                links: [
                    { source: '21', target: '19', label: $t('crm.cancelAfterVerification', null, '核销'), attrs: { line: { refY: 10 } } },
                    { source: '23', target: '21', shape: 'dashed' }
                ]
            }
        ]
    }
]);
// 制造
exports.lane_MES = formatData([
    {
        id: 'container1',
        title: $t('crm.manufacturing', null, '制造行业') + $t('crm.materialManage', null, '物料管理'),
        config: config2,
        laneList: [
            {
                id: 'lane1',
                lane_title: $t('crm.basicDataConfig', null, '基础数据配置'),
                nodes: [
                    Object.assign(Object.assign({ id: '3' }, TEXT['shuxing']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '4' }, TEXT['product']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '5' }, TEXT['productClassification']), { row: 3, col: 1 })
                ],
                links: [
                    // { source: '1', target: '4', shape: 'dashed' },
                    { source: '3', target: '4', shape: 'dashed' },
                    { source: '5', target: '4', }
                ]
            },
            {
                id: 'lane2',
                lane_title: $t('crm.advancedProduct', null, '高级产品配置'),
                nodes: [
                    Object.assign(Object.assign({ id: '6' }, TEXT['productPortfolio']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '7' }, TEXT['productSelection']), { row: 3, col: 1 })
                ],
                links: [
                    { source: '4', target: '6', shape: 'dashed', },
                    { source: '6', target: '7', }
                ]
            },
            {
                id: 'lane3',
                lane_title: $t('价格管理'),
                nodes: [
                    Object.assign(Object.assign({ id: '9' }, TEXT['availableRange']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '8' }, TEXT['pricebook']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '10' }, TEXT['promotion']), { row: 1, col: 2.5 }),
                    Object.assign(Object.assign({ id: '11' }, TEXT['rebate']), { row: 2, col: 2.5 }),
                    Object.assign(Object.assign({ id: '26' }, TEXT['customerAccount']), { row: 3, col: 2.5 }),
                ],
                links: [
                    {
                        source: '4',
                        target: '8',
                        attrs: { line: { refX: 0 } },
                        vertices: [
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p1.x + nodeWidth, y: _p1.y - 25
                                };
                            }
                        ]
                    },
                    { source: '8', target: '9', shape: 'dashed', },
                    { source: '8', target: '10', shape: 'dashed', },
                    {
                        source: '8',
                        target: '11',
                        shape: 'dashed',
                        // vertices: [
                        //     (source, target, config) => {
                        //         let _p1 = source.store.data.position;
                        //         let _p2 = target.store.data.position;
                        //         let { nodeWidth, node_margin_left } = config;
                        //         return {
                        //             x: _p1.x + nodeWidth + 30, y: _p1.y + nodeWidth / 2
                        //         }
                        //     }
                        //     // { x: 686 + 84 + 30, y: 333 + 42 }
                        // ],
                        attrs: { line: { refX: 0 } }
                    },
                    { source: '8', target: '26', shape: 'dashed' }
                ]
            }
        ]
    },
    {
        id: 'container2',
        title: $t('crm.manufacturing', null, '制造行业') + $t('crm.transactionManage', null, '交易管理'),
        config: config2,
        laneList: [
            {
                id: 'lane4',
                lane_title: $t('crm.quoteManage', null, '报价管理'),
                nodes: [Object.assign({ id: '25', row: 1, col: 1 }, TEXT['quote'])],
                links: [{ source: '25', target: '13', }]
            },
            {
                id: 'lane5',
                lane_title: $t('销售合同'),
                nodes: [Object.assign({ id: '13', row: 1, col: 1 }, TEXT['saleContract'])],
                links: [{ source: '13', target: '14', }]
            },
            {
                id: 'lane6',
                lane_title: $t('crm.orderFulfillment', null, '订单执行'),
                nodes: [
                    Object.assign({ id: '14', row: 1, col: 1 }, TEXT['salesOrder']),
                    Object.assign({ id: '15', row: 2, col: 1 }, TEXT['deliveryNote']),
                    Object.assign({ id: '17', row: 3, col: 1 }, TEXT['exchangeGoods'])
                ],
                links: [
                    { source: '14', target: '15', attrs: { line: { refX: 0 } } },
                    {
                        source: '14', target: '18', vertices: [
                            // { x: 1800, y: 169 - 30 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p2.x, y: _p2.y - 30
                                };
                            }
                        ]
                    },
                    { source: '14', target: '19', shape: 'dashed' },
                    { source: '14', target: '20' },
                    { source: '15', target: '17' },
                    { source: '15', target: '19', shape: 'dashed' },
                    // { source: '16', target: '17' },
                    // { source: '16', target: '19', shape: 'dashed' }
                ]
            },
            {
                id: 'lane7',
                lane_title: $t('应收管理'),
                nodes: [Object.assign({ id: '19', row: 2, col: 1 }, TEXT['receivableNote'])],
                links: []
            },
            {
                id: 'lane8',
                lane_title: $t('crm.invoicesManage', null, '发票管理'),
                nodes: [Object.assign({ id: '20', row: 1, col: 1 }, TEXT['invoiceApplication'])],
                links: []
            },
            {
                id: 'lane9',
                lane_title: $t('回款'),
                nodes: [
                    Object.assign({ id: '18', row: 1, col: 1 }, TEXT['paymentPlan']),
                    Object.assign({ id: '21', row: 2, col: 1 }, TEXT['payment']),
                    { id: '23', row: 3, col: 1, text: 'ERP', disabled: true }
                ],
                links: [
                    {
                        source: '18', target: '19', vertices: [
                            // { x: 1432 + 42, y: 333 - 30 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_bottom } = config;
                                return {
                                    x: _p1.x - nodeWidth / 2, y: _p2.y - node_margin_bottom / 2
                                };
                            }
                        ]
                    },
                    { source: '21', target: '19', label: $t('crm.cancelAfterVerification', null, '核销'), attrs: { line: { refY: 0 } } },
                    { source: '23', target: '21', shape: 'dashed' }
                ]
            }
        ]
    }
]);
// 通用
exports.lane_COMMON = formatData([
    {
        id: 'container1',
        title: $t('crm.generalIndustry', null, '通用行业') + $t('crm.materialManage', null, '物料管理'),
        config: config1,
        laneList: [
            {
                id: 'lane1',
                lane_title: $t('crm.basicDataConfig', null, '基础数据配置'),
                nodes: [
                    Object.assign(Object.assign({ id: '1' }, TEXT['shangpin']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '2' }, TEXT['multiunit']), { row: 1, col: 2 }),
                    Object.assign(Object.assign({ id: '3' }, TEXT['shuxing']), { row: 1, col: 3 }),
                    Object.assign(Object.assign({ id: '4' }, TEXT['product']), { row: 2, col: 2 }),
                    Object.assign(Object.assign({ id: '5' }, TEXT['productClassification']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '6' }, TEXT['productPortfolio']), { row: 3, col: 2 }),
                    Object.assign(Object.assign({ id: '7' }, TEXT['productSelection']), { row: 4, col: 2 })
                ],
                links: [
                    { source: '1', target: '4', shape: 'dashed', attrs: {} },
                    { source: '2', target: '4', shape: 'dashed', },
                    { source: '3', target: '4', shape: 'dashed', },
                    { source: '5', target: '4', },
                    { source: '4', target: '6', shape: 'dashed' },
                    { source: '6', target: '7', },
                    { source: '4', target: '8', }
                ]
            },
            {
                id: 'lane2',
                lane_title: $t('价格管理'),
                nodes: [
                    Object.assign(Object.assign({ id: '8' }, TEXT['pricebook']), { row: 2, col: 1, imageUrl: 'https://yyb.gtimg.com/aiplat/page/product/visionimgidy/img/demo6-16a47e5d31.jpg?max_age=31536000' }),
                    Object.assign(Object.assign({ id: '9' }, TEXT['availableRange']), { row: 3, col: 1 })
                ],
                links: [
                    { source: '8', target: '9', shape: 'dashed' },
                    { source: '8', target: '10', shape: 'dashed' },
                    { source: '8', target: '11', shape: 'dashed', },
                    { source: '8', target: '12', shape: 'dashed',
                        vertices: [
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p1.x + nodeWidth + node_margin_left, y: _p1.y + nodeWidth / 2
                                };
                            },
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                return {
                                    x: _p1.x + nodeWidth + node_margin_left, y: _p2.y
                                };
                            },
                        ],
                    },
                    { source: '8', target: '26', shape: 'dashed', }
                ]
            },
            {
                id: 'lane3',
                lane_title: $t('营销活动管理'),
                width: 184,
                height: 620,
                nodes: [
                    Object.assign(Object.assign({ id: '10' }, TEXT['promotion']), { row: 1, col: 1 }),
                    Object.assign(Object.assign({ id: '11' }, TEXT['rebate']), { row: 2, col: 1 }),
                    Object.assign(Object.assign({ id: '26' }, TEXT['customerAccount']), { row: 3, col: 1 }),
                    Object.assign(Object.assign({ id: '12' }, TEXT['coupon']), { row: 4, col: 1 }),
                ],
                links: []
            }
        ]
    },
    {
        id: 'container2',
        title: $t('crm.generalIndustry', null, '通用行业') + $t('crm.transactionManage', null, '交易管理'),
        config: config1,
        laneList: [
            {
                id: 'lane4',
                lane_title: $t('crm.quoteManage', null, '报价管理'),
                nodes: [Object.assign({ id: '25', row: 1, col: 1 }, TEXT['quote'])],
                links: [{ source: '25', target: '13', }]
            },
            {
                id: 'lane5',
                lane_title: $t('销售合同'),
                nodes: [Object.assign({ id: '13', row: 1, col: 1 }, TEXT['saleContract'])],
                links: [
                    { source: '13', target: '14', },
                    {
                        source: '13',
                        target: '18',
                        vertices: [
                            // { x: 1432, y: 169 - 40 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_bottom } = config;
                                let x = _p1.x + nodeWidth / 2;
                                let y = _p1.y - nodeWidth / 2;
                                return { x: x, y: y };
                            }
                        ],
                    },
                    {
                        source: '13', target: '22',
                        vertices: [
                            // { x: 880 + 42, y: 721 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                let x = _p1.x + nodeWidth;
                                let y = _p2.y - 26;
                                return { x: x || 880 + 42, y: y || 721 };
                            }
                        ]
                    }
                ]
            },
            {
                id: 'lane6',
                lane_title: $t('crm.SalesOrderObj', null, '销售订单'),
                nodes: [Object.assign({ id: '14', row: 1, col: 1 }, TEXT['salesOrder'])],
                links: [
                    { source: '14', target: '18', },
                    { source: '14', target: '15', },
                    {
                        source: '14', target: '20',
                        vertices: [
                            // { x: 1800, y: 169 - 30 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                let x = _p2.x;
                                let y = _p2.y - 25;
                                return { x: x || 1800, y: y || 169 - 30 };
                            }
                        ]
                    }
                ]
            },
            {
                id: 'lane7',
                lane_title: `${$t('实物类产品')} ${$t('crm.orderFulfillment', null, '订单执行')}`,
                nodes: [
                    Object.assign({ id: '15', row: 2, col: 1 }, TEXT['deliveryNote']),
                    Object.assign({ id: '16', row: 3, col: 1 }, TEXT['outboundOrder']),
                    Object.assign({ id: '17', row: 4, col: 1 }, TEXT['exchangeGoods'])
                ],
                links: [
                    { source: '15', target: '16', },
                    { source: '15', target: '19', shape: 'dashed', attrs: { line: { refY: 0 } } },
                    { source: '16', target: '17', },
                    {
                        source: '16', target: '18', shape: 'dashed',
                        vertices: [
                            // { x: 1432 + 42, y: 479 }
                            (source, target, config) => {
                                let _p1 = source.store.data.position;
                                let _p2 = target.store.data.position;
                                let { nodeWidth, node_margin_left } = config;
                                let x = _p2.x + nodeWidth / 2;
                                let y = _p1.y;
                                return { x: x || 1432 + 42, y: y || 479 };
                            }
                        ]
                    }
                ]
            },
            {
                id: 'lane8',
                lane_title: `${$t('服务类产品')} ${$t('crm.orderFulfillment', null, '订单执行')}`,
                nodes: [
                    Object.assign({ id: '18', row: 1, col: 1 }, TEXT['paymentPlan']),
                    Object.assign(Object.assign({ id: '22', row: 5, col: 1 }, TEXT['projectmanage']), { disabled: true })
                ],
                links: [
                    { source: '18', target: '19' },
                    { source: '22', target: '19' }
                ]
            },
            {
                id: 'lane9',
                lane_title: $t('应收管理'),
                nodes: [Object.assign({ id: '19', row: 2, col: 1 }, TEXT['receivableNote'])],
                links: [
                    {
                        source: '19',
                        target: '20',
                        label: $t('服务类产品'),
                        attrs: {
                            line: { refY: -10 }
                        }
                    }
                ]
            },
            {
                id: 'lane10',
                lane_title: $t('crm.invoicesManage', null, '发票管理'),
                nodes: [Object.assign({ id: '20', row: 1, col: 1 }, TEXT['invoiceApplication'])],
                links: []
            },
            {
                id: 'lane11',
                lane_title: $t('回款'),
                nodes: [
                    Object.assign({ id: '21', row: 3, col: 1 }, TEXT['payment']),
                    { id: '23', row: 4, col: 1, text: 'ERP', disabled: true }
                ],
                links: [
                    { source: '21', target: '19', label: $t('crm.cancelAfterVerification', null, '核销') },
                    { source: '23', target: '21', shape: 'dashed' }
                ]
            }
        ]
    }
]);
function formatData(lane) {
    return lane;
    // return lane.map(item => {
    //     item.laneList.forEach(({ nodes }) => {
    //         nodes.forEach(node => {
    //             node.text = node.moduleId && moduleMap[node.moduleId] ? moduleMap[node.moduleId].title : node.text;
    //         });
    //     });
    //     return item;
    // });
}
})
//# sourceMappingURL=../../../../ts-map/modules/setting/guidepage/config/lane.js.map
