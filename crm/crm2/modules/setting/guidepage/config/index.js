define(function(require, exports, module) {"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const lane_1 = require("./lane");
//COMMON-通用行业   MES-制造行业  FMCG-快消行业   HES-高服行业 
const _config = {
    // 通用
    COMMON: {
        // 流程图
        lane: lane_1.lane_COMMON,
        sectionList: [
            {
                title: $t('crm.basicDataConfig', null, '基础数据配置'),
                configItems: ['spuConfig', 'multipleUnit', 'attributeConfig', 'cpqConfig']
            },
            {
                title: $t('价格管理'),
                configItems: ['pricebook', 'availableRange']
            },
            {
                title: $t('crm.marketingActivity', null, '营销活动管理'),
                configItems: ['advancedPricing', 'simpleCpq', 'rebate', 'coupon']
            },
            {
                title: $t('crm.quoteManage', null, '报价管理'),
                configItems: ['Quote']
            },
            {
                title: $t('销售合同'),
                configItems: ['SaleContract']
            },
            {
                title: $t('销售订单'),
                configItems: ['SalesOrder']
            },
            {
                title: $t('实物类产品') + $t('crm.orderFulfillment', null, '订单执行'),
                configItems: [
                // 发货单
                // 出库单
                // 退换货
                ]
            }, {
                title: $t('服务类产品') + $t('crm.orderFulfillment', null, '订单执行'),
                configItems: [
                // 回款计划
                // 项目管理
                ]
            }, {
                title: $t('应收管理'),
                configItems: [
                // 应收管理
                ]
            }, {
                title: $t('crm.invoicesManage', null, '发票管理'),
                configItems: [
                    'InvoiceApplication'
                ]
            }, {
                title: $t('回款'),
                configItems: [
                    'Payment'
                ]
            }
        ],
    },
    // 制造
    MES: {
        // 流程图
        lane: lane_1.lane_MES,
        // caseConfigIndustry: [{
        //     title: $t('crm.industryScenarioConfig', null, '行业场景配置'),
        //     configItems: [
        //         {
        //             title: '渠道下单配置',
        //             desc: '渠道订单场景包括： B2B门店订货（终端门店向厂商订货）； B2B经销商订货（经销商或者分销商向厂商订货）；B2B2B多级订货（终端门店向经销商订货，经销商向厂商订货）',
        //             moduleList: [
        //                 {
        //                     title: $t('前往配置'),
        //                     key: 'config',
        //                     router: ''
        //                 }
        //             ]
        //         },
        //         {
        //             title: '序列号插件管理',
        //             desc: '对于大型机械设备，或者家电、3C等类型的产品来说，将产品卖出去并不是终点，后续还涉及到设备的维护、保养等售后服务，而售后服务又是基于设备的序列号为基础进行的，因此需要用到序列号来管理产品的全生命周期。',
        //             moduleList: [
        //                 {
        //                     title: $t('前往配置'),
        //                     key: 'config',
        //                     router: ''
        //                 }
        //             ]
        //         }
        //     ]
        // }],
        sectionList: [
            {
                title: $t('crm.basicDataConfig', null, '基础数据配置'),
                configItems: [
                    // 产品 
                    'spuConfig', 'attributeConfig'
                ]
            },
            {
                title: $t('crm.advancedProduct', null, '高级产品配置'),
                configItems: ['cpqConfig']
            },
            {
                title: $t('价格管理'),
                configItems: ['pricebook', 'availableRange', 'advancedPricing', 'rebate']
            },
            {
                title: $t('crm.quoteManage', null, '报价管理'),
                configItems: ['Quote']
            },
            {
                title: $t('销售合同'),
                configItems: ['SaleContract']
            },
            {
                title: $t('crm.orderFulfillment', null, '执行订单'),
                configItems: [
                    'SalesOrder',
                    // 发货单
                    // 出库单
                    // 退换货
                ]
            },
            {
                title: $t('应收管理'),
                configItems: [
                // 应收单
                ]
            }, {
                title: $t('crm.invoicesManage', null, '发票管理'),
                configItems: [
                    'InvoiceApplication'
                ]
            }, {
                title: $t('回款'),
                configItems: [
                    'Payment',
                    // 回款计划
                ]
            }
        ]
    },
    // 快消
    FMCG: {
        // 流程图
        lane: lane_1.lane_FMCG,
        // caseConfigIndustry: {
        //     title: $t('crm.industryScenarioConfig', null, '行业场景配置'),
        //     configItems: [
        //         {
        //             title: 'B2B订单配置',
        //             desc: 'B2B订单场景包括： B2B门店订货（终端门店向厂商订货）；B2B经销商订货（经销商或者分销商向厂商订货）；B2B2B多级订货（终端门店向经销商订货，经销商向厂商订货）',
        //             moduleList: [
        //                 {
        //                     title: $t('前往配置'),
        //                     key: 'config',
        //                     router: ''
        //                 }
        //             ]
        //         }, {
        //             title: '引单配置引导',
        //             desc: '引单模式，是由业务员到店对现有的产品进行理货，并下订单，库管根据订单将货品集中备货，然后由配送司机集中装车到店配送模式。',
        //             moduleList: [
        //                 {
        //                     title: $t('前往配置'),
        //                     key: 'config',
        //                     router: ''
        //                 }
        //             ]
        //         }, {
        //             title: '车销配置引导',
        //             desc: '车销模式等于把库房搬到了车上；门店订货、退换、换货、终端陈列等工作一次性完成。',
        //             moduleList: [
        //                 {
        //                     title: $t('前往配置'),
        //                     key: 'config',
        //                     router: ''
        //                 }
        //             ]
        //         }]
        // },
        sectionList: [
            {
                title: $t('crm.basicDataConfig', null, '基础数据配置'),
                configItems: [
                    // 产品
                    'spuConfig', 'multipleUnit'
                ]
            },
            {
                title: $t('价格管理'),
                configItems: ['pricebook', 'availableRange']
            },
            {
                title: $t('crm.marketingActivity', null, '营销活动管理'),
                configItems: ['advancedPricing', 'rebate', 'coupon']
            },
            {
                title: $t('crm.quoteManage', null, '报价管理'),
                configItems: ['Quote']
            },
            {
                title: $t('销售合同'),
                configItems: ['SaleContract']
            },
            {
                title: $t('crm.orderFulfillment', null, '执行订单'),
                configItems: [
                    'SalesOrder',
                    // 发货单
                    // 出库单
                    // 退换货
                ]
            },
            {
                title: $t('服务类产品') + $t('crm.orderFulfillment', null, '订单执行'),
                configItems: [
                // 回款计划
                // 项目管理
                ]
            }, {
                title: $t('应收管理'),
                configItems: [
                // 应收单
                ]
            }, {
                title: $t('crm.invoicesManage', null, '发票管理'),
                configItems: [
                    'InvoiceApplication'
                ]
            }, {
                title: $t('回款'),
                configItems: [
                    'Payment'
                ]
            }
        ]
    },
    // 高服
    HES: {
        lane: lane_1.lane_HES
    }
};
// 配置
exports.default = _config;
})
//# sourceMappingURL=../../../../ts-map/modules/setting/guidepage/config/index.js.map
