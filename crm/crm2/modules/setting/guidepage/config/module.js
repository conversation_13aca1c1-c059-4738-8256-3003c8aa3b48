define(function(require, exports, module) {"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var cmmodityproduct = require('crm-modules/setting/cmmodityproduct/data.js'); // 商品/产品
var pricemanage = require('crm-modules/setting/pricemanage/config.js');
var cpqconfigure = require('crm-modules/setting/cpqconfigure/data.js');
var promotionrebate = require('crm-modules/setting/promotionrebate/promotionrebate.js').config; // 促销与返利
var tradeconfigure = require('crm-modules/setting/tradeconfigure/tradeconfigure.js').config; // 交易相关单据配置
/**
 * InvoiceApplication: "开票申请"
    Payment: "回款"
    Quote: "报价单"
    SaleContract: "销售合同"
    SalesOrder: "销售订单"
    advancedPricing: "高级定价"
    attributeConfig: "属性配置"
    availableRange: "可售范围设置"
    coupon: "优惠券"
    cpqConfig: "CPQ配置"
    mobileMultiUnitConfig: "多单位模式配置"
    moduleReprice: "重新取价配置"
    moduleSelectData: "选数据页配置"
    multipleUnit: "多单位配置"
    pricebook: "crm.价目表"
    rebate: "返利单"
    simpleCpq: "固定搭配"
    spuConfig: "商品配置"
 */
/**
 * 0: "高级定价"
1: "返利单"
2: "优惠券"
3: "多单位配置"
4: "移动端多单位模式配置"
5: "商品配置"
6: "属性配置"
7: "固定搭配"
8: "报价单"
9: "销售合同"
10: "销售订单"
11: "回款"
12: "开票申请"
13: "重新取价配置"
14: "选数据页配置"
15: "价目表"
16: "可售范围设置"
17: "CPQ配置"
 */
/**
 * 格式化模块配置
 * @param configData
 * @returns
 */
function formatData(configData, routerModule) {
    let moduleMap = {};
    let pushModule = true;
    configData.forEach((item) => {
        pushModule = true;
        let { moduleId, moduleList, domId } = item;
        item.moduleList.forEach((module) => {
            // 存在二级
            if (module.moduleList) {
                pushModule = false;
                moduleMap[module.moduleId] = Object.assign(Object.assign({}, module), { routerModule, router: module.domId ? `#crmmanage/=/module-${routerModule}/${module.domId.replace('level', 'level-')}` : '' });
                module.moduleList.forEach(btn => {
                    // 开关路由跳转
                    btn.router = btn.key ? `#crmmanage/=/module-${routerModule}/key-${btn.key.toString().replace(',', '__')}` : '';
                });
            }
            else {
                // 开关路由跳转
                module.router = module.key ? `#crmmanage/=/module-${routerModule}/key-${module.key.toString().replace(',', '__')}` : '';
            }
        });
        if (pushModule) {
            moduleMap[moduleId] = Object.assign(Object.assign({}, item), { routerModule, router: domId ? `#crmmanage/=/module-${routerModule}/${domId.replace('level', 'level-')}`
                    : `` });
        }
    });
    return moduleMap;
}
let { _module, moduleMap } = Object.entries({
    'promotionrebate': promotionrebate.CONFIG_DATA,
    'cmmodityproduct': cmmodityproduct,
    'tradeconfigure': tradeconfigure.CONFIG_DATA,
    'pricemanage': pricemanage.CONFIG_DATA,
    'cpqconfigure': cpqconfigure
}).reduce((res, [key, configData], index) => {
    let _moduleMap = formatData(configData, key);
    Object.assign(res.moduleMap, _moduleMap);
    let names = Object.values(_moduleMap).map(item => {
        return item.title;
    });
    res._module = res._module.concat(names);
    return res;
}, {
    _module: [],
    moduleMap: {}
});
exports.default = moduleMap;
})
//# sourceMappingURL=../../../../ts-map/modules/setting/guidepage/config/module.js.map
