define(function (require, exports, module){
    var Table = require('crm-widget/table/table');
    var Dialog = require('crm-widget/dialog/dialog');
    var Hseas = require('crm-modules/common/highseas/highseas');
    var Parse = Hseas.common;
    var rulesFieldStatic = Hseas.rulesFieldStatic;
    var util = CRM.util;
    var circleRecoverTpl = require('./saveprompt-html');
    module.exports = Backbone.View.extend({
        events: {
            'click .j-add-recover': 'addRecoverHandle',
        },
        async initialize() {
          this.departmentRule = 'main'; // 'mainAndSup'
          this.haveRule = false;
          this.deptRuleOptionsDetail = {
            'main': $t('crm.recycling.rule.depement.mainDetail'),
            'mainAndSup': $t('crm.recycling.rule.depement.mainAndSupDetail')
          }
          this.deptRuleOptionsDescribe = {
            'main': $t('crm.recycling.rule.depement.mainDescribe'),
            'mainAndSup': $t('crm.recycling.rule.depement.mainAndSupDescribe')
          }
          	// 部门选项属于和不属于是否开启包含子部门开关获取处 （非公海回收）
          this.needDepartmentChildren = await CRM.util.getDepartmentChildrenSwitch('sfa_vcrm_leadpool_department_includechildren');
          this.initTable();
        },
        initTable() {
            var me = this,
                parse = this.getCommonParse();
            if (this._ruleDt) {
                this._ruleDt.resize();
                return;
            }
            this._ruleDt = new Table({
                // $el: me.$('.rule-recover'),
				// url: '/EM1HCRM/BasicSetting/GetAllUnHighSeasRecyclingRuleList',
                $el: me.$el,
				url: '/EM1HNCRM/API/v1/object/account_recycling_rule/service/get_account_recycling_rule',
                requestType: 'FHHApi',
                autoHeight: true,
                showPage: false,
                noAllowedWrap: false,
                operate: {
                    pos: 'T',
                    btns: [{
                        className: 'j-add-recover',
                        text: $t("新建")
                    }]
                },
                columns: [{
                    data: 'ruleName',
                    title: $t("规则名称"),
                    width: 200,
                    render: function(data, $tr, fulldata) {
						return data ? data : "--"
                    }
                },{
					data: 'departmentIds',
                    title: $t("crm.适用部门"),
                    width: 200,
                    render: function(data, $tr, fulldata) {
						// var names = [];
						var names = fulldata.departmentIds.map((item) => {
							return FS.contacts.getCircleById(item) && FS.contacts.getCircleById(item).name || '--';
						})
						return names.join(',')
						// _.each(fulldata.departmentIds, function(item) {
						//     names.push(item.CircleName);
						// });
						// return names.join($t("、"));
                    }
                }, {
					data: 'recyclingRuleList',
                    title: $t("收回规则"),
                    width: 'auto',
                    render: function(data, $tr, fulldata) {
                        var parseDate = [],
                            html = '';

                        if(data.length > 1) {
                            data = data.sort(function(a, b) {
								return a.priority - b.priority;
							});
                        }
                        _.each(data, function(item) {
							parseDate.push(parse.parseFieldRecycling(me.addStaticField([]), item));
							// parseDate.push(parse.parseFieldRecycling(me.addStaticField(me.fieldlist), item));
                        });

                        if(parseDate.length < 1) {
                            return $t("无收回规则");
						} else {
							parseDate = [parseDate.shift()];
                            _.each(parseDate, function(item, num) {
                                var subHtml = '';
                                html += '<div class="item-con-sec">' +
                                    '<label class="sec-label">'+ $t("优先级") +(num + 1)+'：</label>';
                                _.each(item.datas, function(data, index) {
                                    if(index == item.datas.length - 1) {
                                        subHtml += '"'+ data.FieldName + '"' +' '+ data.Compare +' '+ '"' +data.FieldValue + '"';
                                    } else {
                                        subHtml += '"'+data.FieldName + '"' +' '+ data.Compare +' ' + '"' + data.FieldValue + '"' + ' '+ $t("且") + ' ';
                                    }
                                });

                                if(item.datas.length) {
                                    html += '<div class="sec-con">'+subHtml+$t("；")+item.scopeText+'</div></div>';
                                } else {
                                    html += '<div class="sec-con">'+item.scopeText+'</div></div>';
                                };
                            });
                        }

                        return html;
                    }
                }, {
                    title: $t("操作"),
                    data: '',
                    width: 200,
                    render: function() {
                        return '<a href="javascript:;" class="edite-recover">'+ $t("编辑") +'</a>' +
                                '<a href="javascript:;" class="del-recover">'+ $t("删除") +'</a>' +
                                '<a href="javascript:;" class="copy-recover">'+ $t("复制并新建") +'</a>';
                    }
                }],
                formatData: function(data) {
                    data.areaList && data.areaList.length && CRM.util.setAreaCache(data.areaList);
                    if(data.recyclingRuleList && data.recyclingRuleList[0]) {
                      let newRule = data.recyclingRuleList[0].department_rule || 'main';
                      me.haveRule = true;
                      me.updateDepartmentRule(newRule);
                    }else{
                      me.haveRule = false;
                    }
                    if(data?.recyclingRuleList?.length == 0) {
                      me.updateDepartmentRule('mainAndSup');
                    }
                    return {
                        totalCount: (data.Page && data.Page.TotalCount) || 0,
						data: data.recyclingRuleList
                    }
                }
            });

            this._ruleDt.on('trclick', function(data, td, current, tr) {
                //加取数据函数
				var highseasId = data.recyclingRuleList.length > 0 ? data.recyclingRuleList[0].HighSeasID : '';
				var dataIDList = [];
				data.departmentIds.forEach((item) => {
					item = FS.contacts.getCircleById(item);
					if (item) {
						dataIDList.push({
							id: item.id,
							name: item.name,
							type: 'g'
						});
					}
				})
                var obj = _.extend(data, {
                    DataType: 2,
                    highSeasList: highseasId,
					recyclingRuleList: data.recyclingRuleList,
                    filterList: me.getFilterList(this.curData.data),
					// OldDataIDList: _.map(data.CircleList, function(item) {
					//     return item.CircleID;
					// }),
                    RuleName : data.ruleName || '',
					OldDataIDList: data.departmentIds,
					DataIDList: dataIDList,
          department_rule: me.departmentRule
					// DataIDList: _.map(data.CircleList, function(item) {
					//     return {
					//         id: item.CircleID,
					//         name: item.CircleName,
					//         type: 'g'
					//     };
					// })
                });

                if(current.hasClass('del-recover')) {
                    me.delRecoverHandle(data);
                } else if(current.hasClass('edite-recover')) {
                    me.editeRecoverHandle(obj);
                }  else if(current.hasClass('copy-recover')){
                    me.copyRecoverHandle(obj);
                }

            });
        },

        updateDepartmentRule(newRule) {
          this.departmentRule = newRule;
          this.initDepartmentRule();
        },
        initDepartmentRule() {
          const me = this;
          this.deptRuleWarp && this.deptRuleWarp.destroy && this.deptRuleWarp.destroy();
          this.deptRuleWarp = null;
          this.deptRuleWarp = FxUI.create({
            wrapper: $('.rule-recover-deptment')[0],
              template: `
              <div class="nonhigh-secrch-rule-deptment" v-if="haveRule">
                  <fx-alert
                      :closable="false"
                      type="warning"
                      close-text="${$t('crm.recycling.rule.depement.toswitch')}>"
                      >
                      <template slot="title">
                        <div class="rule-deptment-content">
                          <span>{{deptRuleText}}</span>
                          <fx-tooltip :visible-arrow="false" :open-delay="500" effect="light" :content="hoverTipText"
                            placement="bottom">
                            <span class="fx-icon-info crm-ui-title"></span>
                          </fx-tooltip>
                          <span style="color: #0c6cff;cursor: pointer;margin-left: 4px" @click="showChangeDeptmentRule">${$t('crm.recycling.rule.depement.toswitch')}></span>
                        </div>
                      </template>
                  </fx-alert>  
              </div>
              `,
              data() {
                return {
                    deptRuleText: me.deptRuleOptionsDetail[me.departmentRule],
                    haveRule: me.haveRule,
                    hoverTipText: me.deptRuleOptionsDescribe[me.departmentRule],
                };
              },
              methods: {
                showChangeDeptmentRule: function (e) {
                  let dialog = FxUI.create({
                    template: `<fx-dialog
                          :visible.sync="dialogVisible"
                          :title="$t('crm.recycling.rule.depement.dialog')"
                          max-height="500px"
                          :append-to-body="true"
                          @closed="handler"
	                        custom-class="change-deptment-rule-dialog"
                        >
                        <div ref="body">
                          <div class='change-deptment-rule-top'>${$t('crm.recycling.rule.depement.title')}</div>
                          <div class='change-deptment-rule-top-desc'>${$t('crm.recycling.rule.depement.title.desc')}</div>
                          <fx-radio-group is-vertical v-model="radio1" @change="onChange1">
                            <fx-radio :label="3" size="mini">${$t('crm.recycling.rule.depement.main')}</fx-radio>
                          </fx-radio-group>
                          <div class='change-deptment-rule-main-dept'>${$t('crm.recycling.rule.depement.mainDescribe')}</div>
                          <fx-radio-group is-vertical v-model="radio2" @change="onChange2">
                            <fx-radio :label="6" size="mini">${$t('crm.recycling.rule.depement.mainAndSup')}</fx-radio>
                          </fx-radio-group>
                          <div class='change-deptment-rule-main-higher-dept'>${$t('crm.recycling.rule.depement.mainAndSupDescribe')}</div>
                        </div>
                        <div slot="footer" >
                            <fx-button type="primary" @click="saveRecyclingRule" size="small">{{$t('确定')}}</fx-button>
                            <fx-button @click="dialogVisible = false" size="small">{{$t('取消')}}</fx-button>
                        </div>
                      </fx-dialog>`,
                    components:{
                        
                    },
                    data() {
                        return {
                            dialogVisible: true,
                            radio1: me.departmentRule == 'main' ? 3 : null,
                            radio2: me.departmentRule == 'mainAndSup' ? 6 : null,
                        }
                    },
                    methods: {
                      onChange1() {
                        if(this.radio1 == 3) {
                          this.radio2 = null;
                        }
                      },
                      onChange2() {
                        if(this.radio2 == 6) {
                          this.radio1 = null;
                        }
                      },
                      handler() {
                        dialog.$destroy();
                        dialog = undefined;
                      },
                      saveRecyclingRule() {
                        let _this = this;
                        let param;
                        if(this.radio1 == 3) { param = 'main'};
                        if(this.radio2 == 6) { param = 'mainAndSup'};
                        // 获取当前系统设置的规则
                        util.FHHApi({
                          url: '/EM1HNCRM/API/v1/object/account_recycling_rule/service/get_account_recycling_rule',
                          data: {
                            _isfilter: false
                          },
                          success: function(data) {
                            if (data.Result.StatusCode == 0) {
                              let oldRule = 'main'
                              if(data?.Value?.recyclingRuleList && data.Value.recyclingRuleList[0]) {
                                oldRule = data.Value.recyclingRuleList[0].department_rule || 'main';
                              }
                              if(oldRule != param) {
                                util.FHHApi({
                                  url: '/EM1HNCRM/API/v1/object/account_recycling_rule/service/save_department_rule',
                                  data: {
                                    department_rule: param
                                  },
                                  success: function(data) {
                                    if (data.Result.StatusCode == 0) {
                                      me.updateDepartmentRule(param)
                                      util.remind('1', $t("保存成功"));
                                      _this.dialogVisible = false;
                                      me._ruleDt.setParam({}, true);
                                      return;
                                    }else{
                                      util.alert(data.Result.FailureMessage);
                                    }
                                  }
                                });
                              }else{
                                me.updateDepartmentRule(param)
                                util.remind('1', $t("保存成功"));
                                _this.dialogVisible = false;
                                me._ruleDt.setParam({}, true);
                                return;
                              }
                            }else{
                              util.alert(data.Result.FailureMessage);
                            }
                          }
                        });

                        
                        
                      }
                    }
                })
                }
              },
          })
        },

        // 添加非公海回收规则
        addRecoverHandle: function(e) {
            var me = this,
                dt = me._ruleDt,
                data = dt.curData.data;

            this._doAction('addRule', {
                DataType: 2,
                needDepartmentChildren: this.needDepartmentChildren,
                filterList: me.getFilterList(data),
                department_rule: me.departmentRule
            });
        },
        // 复制并新建非公海回收规则
        copyRecoverHandle : function(data){
            data.RuleName = data.RuleName  + $t('副本');
            data.DataIDList = [];
            data.department_rule = this.departmentRule;
            data.needDepartmentChildren = this.needDepartmentChildren;
            var me = this;
            this._doAction('addRule', data);
        },
    
        // 编辑非公海回收规则
        editeRecoverHandle: function(data) {
            var me = this;
            data.needDepartmentChildren = this.needDepartmentChildren;
            this._doAction('editRule', data);
        },

        _doAction: function(fn, param) {
            var me = this;
            require.async('crm-modules/components/customerrule/customerrule', function(Action) {
                if(!me.highseas) {
                    me.highseas = new Action();
                    me.highseas.on('refresh', function(result, value) {
						if (result.conflictRuleList.length == 0) {

                            if(value.isEdit) {
                                util.remind(1, $t("编辑成功"));
                                me._ruleDt.setParam({}, true);
                            } else {
                                util.remind(1, $t("新建成功"));
                                me._ruleDt.setParam({}, true);
                            }
                            return;
                        }
                        me.showSaveRecoverDialog(result, value);
                    });
                }

                me.highseas[fn] && me.highseas[fn](param);
            });
        },
        // 删除回收规则
        delRecoverHandle: function(data) {
            var me = this,
            confirm = util.confirm($t("确认删除这条数据"), $t("提示"), function() {
                confirm.hide();
                me.delPost(data);
            });            
        },

        delPost: function(data) {
            var me = this;
            util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/account_recycling_rule/service/delete_account_recycling_rule',
                data: {
					groupId: data.groupId
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        util.remind(1, $t("删除成功"));
                        me._ruleDt.setParam({}, true);
                        return;
                    };
                    util.alert(data.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            });
        },

        getCommonParse: function() {
            var me = this;
            if(!this._parse) {
                this._parse = new Parse();
            }
            return this._parse;
        },
        addStaticField: function(list) {
            list = list.concat(rulesFieldStatic);
            return list;
        },
        getFilterList: function(data) {
            var filterList = [];
            _.each(data, function(item) {
				filterList = filterList.concat(item.departmentIds.map(id => +id))
            });
            return filterList;
        },
        showSaveRecoverDialog: function (data, value){
            var me = this;
            me.curSaveRecoverDialog &&　me.curSaveRecoverDialog.destroy();
            var SaveRecoverDialog = Dialog.extend({
                attrs: {
                    title: $t("提示"),
                    width: 500,
                    content: circleRecoverTpl( data ),
                    showScroll:   false,
                    showBtns:  true,
                    className: 'crm-s-customerrule'
                },
                events: {
                    'click .b-g-btn-cancel': 'hide',
                    'click .climits-employee': 'checkNavHandle',
                    'click .b-g-btn': 'submit'
                },
                checkNavHandle: function(e){
                    var _me = this;
                    var $target = $(e.currentTarget);
                    if( $target.hasClass('active') ){
                        return;
                    }
                    _me.$('.climits-employee').removeClass('active');
                    $target.addClass('active');
                    var targetToShow = $target.data('employee')+'circle';
                    _me.$('.climits-circle').hide();
                    _me.$( '.' + targetToShow ).show();
                },
                submit: function(){
                    var _me = this;
                    var selectedDom = _me.$('.climits-circle .mn-selected');
                    var employeeCircleDatas = [];
                    _.each( selectedDom, function (item,index){
                        employeeCircleDatas.push({
							employeeId: $(item).data('employeeid'),
							dataId: $(item).data('circleid')
                        });
                    });
                    var httpDatas = _.extend({}, value.fieldData, {
						employeeRuleList: employeeCircleDatas
                    });
                    me.saveRecover( httpDatas, function() {
                        me.curSaveRecoverDialog.trigger('refresh');
                    }, false );
                },
                hide: function(){
                    var result = SaveRecoverDialog.superclass.hide.call(this);
                    return result;
                }
            });
            me.curSaveRecoverDialog = new SaveRecoverDialog();
            me.curSaveRecoverDialog.render();
            me.curSaveRecoverDialog.on('refresh', function() {
                me._ruleDt.setParam({}, true);
            });
            me.curSaveRecoverDialog.show();
        },
        // 保存非公海回收
        saveRecover: function( requseData ,callBack , firstHttpFlag) {
            var me = this;
			console.log('requseData', requseData)
            delete requseData.RecyclingRuleList;
            requseData.department_rule = me.departmentRule;
            util.FHHApi({
				// url: '/EM1HCRM/BasicSetting/SetUnHighSeasRecyclingRules',
				url: '/EM1HNCRM/API/v1/object/account_recycling_rule/service/save_account_recycling_rule',
                data: requseData,
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
						if (firstHttpFlag && data.Value.conflictRuleList && data.Value.conflictRuleList.length > 0) {
                            me.showSaveRecoverDialog( data.Value );
                        }else{
                            util.remind('1', $t("保存成功"));
                        }
                        callBack && callBack(data.Value);
                        me.curSaveRecoverDialog && me.curSaveRecoverDialog.destroy();
                        return;
                    }else{
                        util.alert(data.Result.FailureMessage);
                    }
                }
            });
        },
        destroy() {
            this._ruleDt && this._ruleDt.destroy && this._ruleDt.destroy();
            this.curSaveRecoverDialog && this.curSaveRecoverDialog.destroy && this.curSaveRecoverDialog.destroy();
        },
    })
})