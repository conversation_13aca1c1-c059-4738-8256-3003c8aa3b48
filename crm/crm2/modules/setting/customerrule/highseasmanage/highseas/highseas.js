/**
 * @desc 
 * <AUTHOR>
 * Modify  qigb
 */
define(function (require, exports, module) {
	var util = require('crm-modules/common/util'),
		ObjectTable = require('crm-modules/components/objecttable/objecttable'),
		format = require('../format');

    var Highseas = Backbone.View.extend({
        initialize: function(opts) {
            var me = this;
			me.apiname = "HighSeasObj";
			Promise.all([me.getEnterpriseQuotaStatistics(),me.getGrayByServerForLeads('graySfaOutTenantPoolBatchAdd'),CRM.util.getDepartmentChildrenSwitch('sfa_vcrm_leadpool_department_includechildren')]).then(([quotaLicense,graySfaOutTenantPoolBatchAdd,needDepartmentChildren]) => {
					// 是否开启互联和互联新建
					me.quotaStatic = (quotaLicense && graySfaOutTenantPoolBatchAdd) || false;
					// 部门选项属于和不属于是否开启包含子部门开关获取处 (公海批量新建)
					me.needDepartmentChildren = needDepartmentChildren;
					me.getPRMRight(function(){
						me.initTable();  
					});
			}).catch((err) => {
			    me.quotaStatic =  false;
				me.getPRMRight(function(){
					me.initTable();  
				});
			})
            this.on('refresh', function() {
				me.refresh();
            });
            me.totalCount = 0;
        },

        MAX_NUM : 2000,//公海上限
        
        events: {
            'click .j-add':  'addHandle',
        },

        // 获取PRM状态以及外部联系人数据
		getPRMRight: function(cb){
			var me = this; 
			if (typeof me.isOpenPRM === 'undefined'){
				$.when(CRM.util.getPRMRight()).then(function(data1){
					me.isOpenPRM = data1;
					cb && cb();
				});
				return;
			}
			cb && cb();
		},
		// 判断是否开启了互联企业
		getEnterpriseQuotaStatistics(){
			return new Promise((resolve, reject) => {
				CRM.util.FHHApi(
					{
						url: `/EM1HER2/admin/enterpriseMeta/getEnterpriseQuotaStatistics`,
						data: {
							
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value.data.hasLicense);
								return;
							}
							CRM.util.alert(res.Result.FailureMessage);
						},
					},
					{
						errorAlertModel: 1,
					}
				);
			});
		},
		// 是否开启了互联新建
		getGrayByServerForLeads(type='',apiname='LeadsPoolObj'){
			return new Promise((resolve,reject) => {
				if(!type){
					reject()
				}
				try {
					CRM.util.FHHApi({
						url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
						data: {
							api_name: apiname,
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value?.[type]);
								return;
							}
							resolve(false);
						},
						
					}, {
						errorAlertModel: 1
					})
				} catch (error) {
					reject()
				}
			})
		},
        
        // 新建
        addHandle: function() {
            var me = this;
            if (me.totalCount < me.MAX_NUM) {
                require.async('crm-modules/components/customerrule/customerrule', function(Action) {
                    if(!me.highseas) {
                        me.highseas = new Action();
                        me.highseas.on('refresh', function(result, value) {
                            me.totalCount++;
							me.refresh();
                        });
                    }

                    // me.highseas.add({
                    //     type: 3,
                    //     DataType: 1, //公海提醒规则,
                    // });
					me.highseas.add();
                });


            } else {
                util.remind(3, $t("贵公司可创建的公海数量已达上限"));
            }
        },

		addfromdpmHandle() {
			const me = this;
			// require.async('vcrm/sdk', function(components){
			// 	components.getComponent('poolBatchnew').then(function(comp) {
			// 		let Comp = comp.default;
			// 		me.batchnewDialog = new Comp({
			// 			propsData: {
			// 				apiname: me.apiname,
			// 				dData: [{
			// 					name: 'test',
			// 					_id: '1032'
			// 				},{
			// 					name: 'test2',
			// 					_id: '1001'
			// 				}],
			// 			}
			// 		});
			// 		me.batchnewDialog.$on('success', function() {
			// 			me.refresh();
			// 		})
			// 	})
			// })
			// return;
			if (me.totalCount >= me.MAX_NUM) {
				util.remind(3, $t("贵公司可创建的公海数量已达上限"));
				return;
			}
			util.sendLog(`${this.apiname}Manage`, 'batchnew', {
				operationId: 'create',
			})
			require.async('crm-modules/components/dpmdialog/dpmdialog', function(dpmDialog){
				let dpmdialog = dpmDialog({
					selectHandle(list) {
						CRM.util.waiting();
						require.async('vcrm/sdk', function(components){
							components.getComponent('poolBatchnew').then(function(comp) {
								let Comp = comp.default;
								me.batchnewDialog = new Comp({
									apiname: me.apiname,
									dData: list,
									type:'DepartmentObj', //部门
								});
								me.batchnewDialog.$on('success', function() {
									me.refresh();
								})
							})
						})
					}
				})
			});
		},
		addfromOtherHandle() {
			this.selectDpmDialog()
		},
		selectDpmDialog() {
            const me = this
            this.getDataDialog && this.getDataDialog.$destroy()
            this.getDataDialog = FxUI.create({
                template: `    <fx-dialog
      :title="$t('请选择新建方式')"
      size="small"
      :visible.sync="dialogSelectType"
      custom-class="select-dpm-dialog"
    >
      <ul>
        <li
          v-for="item in retentionCordType"
          :key="item.apiName"
          @click="nowType = item.apiName"
          :class="{
            'red-border': item.apiName === nowType,
          }"
        >
          {{ item.label }}
          <span class="fx-icon-question" v-show="item.description" :title="item.description"></span>
          <span class="fx-icon-ok-2"></span>
        </li>
      </ul>
      <div slot="footer" class="dialog-footer">
        <fx-button type="primary" @click="addTable" size="small"
          >{{$t('确定')}}</fx-button
        >
        <fx-button @click="dialogSelectType = false" size="small"
          >{{$t('取消')}}</fx-button
        >
      </div>
    </fx-dialog>`,
                data() {
                    return {
						dialogSelectType:true,
						nowType:'DepartmentObj',
						retentionCordType:[{
							apiName : 'DepartmentObj',
							label:$t('从部门新建'),
							description:$t('借助部门快速生成对应{{name}}', {name: $t('crm.线索池')}),
						},{
							apiName:'EnterpriseRelationObj',
							label:$t('从互联企业新建'),
							description:me.apiname === 'LeadsPoolObj' ? $t('sfa.crm.leadsPoolAddFromRelation'):$t('sfa.crm.highseasAddFromRelation')
						}]
					 }
                },
                mounted() {

                },
                methods: {
					addTable() {
					    let type = this.nowType;
						// 从部门新建
						if(type === 'DepartmentObj'){
							util.sendLog(`${me.apiname}Manage`, 'batchnew', {
								operationId: 'create',
							})
							me.renderDpmDialog(type)	
						}
						if(type === 'EnterpriseRelationObj'){
							// 从互联企业新建
							if(!me.isOpenPRM){
								CRM.util.alert($t("sfa.noHavePrm"))
								return
							}
							me.renderDpmDialog(type)

						}
					}
                },
            })
        },
		renderDpmDialog(type) {
			const me = this;
			require.async('crm-modules/components/dpmdialog/dpmdialog', function(dpmDialog){
				let dpmdialog = dpmDialog({
					objname: me.apiname,
					type:type,
					selectHandle(list) {
						me.getDataDialog.dialogSelectType = false
						CRM.util.waiting();
						require.async('vcrm/sdk', function(components){
							components.getComponent('poolBatchnew').then(function(comp) {
								let Comp = comp.default;
								me.batchnewDialog = new Comp({
									apiname: me.apiname,
									dData: list,
									authority : me.authority,
									needDepartmentChildren:me.needDepartmentChildren,
									type:type,
								});
								me.batchnewDialog.$on('success', function() {
									me.refresh();
								})
							})
						})
					}
				})
			});
        },

		cloneHandle(id, data) {
            var me = this;
			if (me.totalCount < me.MAX_NUM) {
            	me.editHandle(id, data, 'clone');
			} else {
                util.remind(3, $t("贵公司可创建的公海数量已达上限"));
            }
        },

		editHandle: function(id, data, type) {
            var me = this;
			type = type || 'edit';
            require.async('crm-modules/components/customerrule/customerrule', function(Action) {
                if(!me.highseas) {
                    me.highseas = new Action();
                    me.highseas.on('refresh', function(result, value) {
                        me.trigger('refresh');
                    });
                }
				me.highseas[type](id);
				// Promise.all([CRM.util.getHighSeasByID(id, true), CRM.util.getRecycleRule({
				// 	api_name: "HighSeasObj",
                //     data_id: id,
				// })]).then((values) => {
				// 	let detailData = values[0];
				// 	detailData.pool_permission_templates = format.filterHideField(detailData.pool_permission_templates);
				// 	detailData = format.formatObj(detailData, data);
				// 	detailData.recycling_rule_list = values[1];
				// 	me.highseas[type](_.extend(detailData, {
				// 		type: 3, //根据此值请求不同接口
				// 		DataType: 1 //公海提醒规则中有使用,公海回收规则1，非公海为2
				// 	}));
				// })
            });
        },

		batcheditCheck() {
			const me = this;
			return new Promise((resolve) => {
				let checkedData = me.table.getRemberData();
				let len = checkedData ? checkedData.length : 0;
				if (!len) {
					CRM.util.remind(3, $t('请至少选择一条数据'));
					return;
				}
				if (len > 20) {
					CRM.util.remind(3, $t('单次批量编辑不能超过20条'));
					return;
				}

				let exDataList = [];
				let dataList = [];
				_.each(checkedData, (item) => {
					if (item.pool_type == 'private') {
						exDataList.push(item);
					} else {
						dataList.push(item);
					}
				})
				
				if (exDataList.length) {
					CRM.util.confirm($t('独家伙伴类型的数据不允许批量编辑'), null, function(){
						me.table.setUncheckedRow('_id', exDataList);
						me.table.reduceRemberData(exDataList, true)
						this.hide();
						if (!dataList.length) {
							CRM.util.remind(3, $t('请至少选择一条数据'));
							return;
						}
						resolve(dataList);
					}, {
						btnLabel: {
							confirm: $t('继续保存'),
						}
					})
				} else {
					resolve(dataList);
				}
			})
		},

		batcheditHandle() {
			const me = this;
			me.batcheditCheck().then((list) => {
				require.async('vcrm/sdk', function(components){
					components.getComponent('poolBatchedit').then(function(comp) {
						let Comp = comp.default;
						me.batcheditDialog = new Comp({
							apiname: me.apiname,
							model: {
								isOpenPRM: me.isOpenPRM,
								outer: CRM.get('outer') || {},
								dataList: list,
							},
							needDepartmentChildren:me.needDepartmentChildren,
							isValid: true,
							isSubmit: true,
						});
						me.batcheditDialog.$on('success', function() {
							me.refresh();
						})
					})
				})
			})
		},


		transferHandle: function(id, data) {
            var me = this;
            CRM.api.transfer4mgr({
                hsID: id,
                success: function() {
					data.accounts_count = 0;
					data.last_modified_time = new Date().getTime();
                    me.trigger('refresh');
                }
            })
        },

		delHandle: function(id, data) {
            var me = this;

            require.async('crm-modules/components/pooldelete/delete.js', function(del) {
                CRM.util.getHighSeasList(true).then((res) => {
                    var _opt = _.filter(res, function(item) {
                        return item._id != data._id;
                    })
                    del({
                        data: data,
                        options: _opt,
                        count: data.accounts_count,
						api_name: me.apiname,
                        success: function() {
                            me.trigger('refresh', 'delhighsea');
                        },
                    });
                })
            })
        },

        //初始化表格
        initTable: function() {
			var me = this;
			if (me.table) return this;
			let buttons = [{
				action: "add",
				attrs: "data-action=add",
				className: "j-action",
				text: $t("新建")
			}, {
				action: "addfromdpm",
				attrs: "data-action=addfromdpm",
				className: "j-action",
				isFold: false,
				text: `${$t("从部门新建")}<i class="crm-ui-title btn-addfromdpm-tip" data-pos="bottom" data-title="${$t('借助部门快速生成对应{{name}}', {name: $t('crm.线索池')})}">?</i>`
			},
			// {
			// 	action: "addfromOther",
			// 	attrs: "data-action=addfromOther",
			// 	className: "j-action",
			// 	isFold: false,
			// 	text: `${$t("批量新建")}`
			// }, 
			{
				action: "batchedit",
				attrs: "data-action=batchedit ",
				className: "j-action",
				isFold: false,
				text: $t("批量编辑"),
			}]
			if(me.quotaStatic){
				buttons = buttons.filter(button => button.action !== "addfromdpm");
							
				// 插入新的项到倒数第二个位置
				const newItem = {
					action: "addfromOther",
					attrs: "data-action=addfromOther",
					className: "j-action",
					isFold: false,
					text: `${$t("批量新建")}`,
				};
				buttons.splice(buttons.length - 1, 0, newItem);
			}
			let myObjectTable = ObjectTable.extend({
				trclickHandle(data, $tr, $target) {
					if(!$target.hasClass('action-btn')) {
						me.showDetail(data);
						return;	
					}
					let action = $target.data('action');
					me[`${action}Handle`] && me[`${action}Handle`]($target.data('id'), data);
					
				},
				operateBtnClickHandle(e) {
					var action = $(e.target).attr('data-action');
					me[`${action}Handle`] && me[`${action}Handle`]();
					// if (action === 'add') {
					// 	me.addHandle()
					// }
				},
				getOptions: function () {
					var options = ObjectTable.prototype.getOptions.apply(this, arguments);
					options = _.extend(options, {
						custom_className: 'crm-highseas-table',
						searchTerm: false,
						isOrderBy_allColumn: false,
						showFilerBtn: false,
						showMultiple: true,
						checked: {
							idKey: '_id',
							data: [],
						}
					})
					_.each(options.columns, function (a) {
						if (a.data === 'name') {
							a.render = function (name) {
								return `<a href="javascript:;">${name}</a>`
							}
						}
					});
					return options;
				},
				getColumns() {
					let columns = this.options.columns;
					columns.push({
						data: null,
						width: 200,
						title: $t("操作"),
						lastFixed: true,
						render(data, type, full) {
							let _html = '';
							_html += `<a data-id="${full._id}" class="action-btn" data-action="edit" href="javascript:;">${$t("编辑")}</a>`;
							if (full.accounts_count > 0) {
								_html += `<a data-id="${full._id}" class="action-btn" data-action="transfer" href="javascript:;">${$t("转移")}</a>`;
							}
							_html += `<a data-id="${full._id}" class="action-btn" data-action="del" href="javascript:;">${$t("删除")}</a>`;
							_html += `<a data-id="${full._id}" class="action-btn" data-action="clone" href="javascript:;">${$t("复制")}</a>`;
							return _html;
						}
					})
					return columns;
				},
				getExtendAttribute: function () {
					let baseScenes = this.get('baseScenes')
					let currentScenes = baseScenes && baseScenes[0] ? baseScenes.find(item => item.api_name === 'All') : {};
					return {
						scene_id: currentScenes._id,
						scene_type: currentScenes.type,
						scene_api_name: currentScenes.api_name
					}
				}
			})
			me.table = new myObjectTable({
				el: me.$el,
				apiname: me.apiname,
				showTitle: false,
				showTermBatch: true,
				search: {
					placeHolder: $t("搜索"),
					type: 'Keyword',
					highFieldName: 'name',
					pos: 'T'
				},
				operate: {
					pos: 'T',
					btns: buttons
				}
			});
			this.table.render();
        },
        
        // 显示详情
        showDetail: function(data) {
			var me = this;
            require.async('../customerrule/customerrule', function(Detail) {
                if (!me.detail) {
                    me.detail = new Detail({width: 600});
                    me.detail.on('refresh', function(type) {
                       type === 'delhighsea' && me.totalCount--;
						me.refresh();
                    });
                }
				me.detail.show(data, me.dt); // me.dt 为埋点使用 
            });
		},

		// 列表刷新
		refresh: function () {
			this.table && this.table.table._clearChecked();
			this.table && this.table.refresh();
		},
		
        destroy: function() {
            var me = this;
			me.batchnewDialog && me.batchnewDialog.cusDestroy();
            _.each(['dt', 'add', 'detail', 'highseas'], function(item) {
                if (me[item]) {
                   me[item].destroy();
                   me[item] = null;
                }
            });
        }
    }); 
    
    module.exports = Highseas;    
});
