define(function (require, exports, module) {
    var util = require('crm-modules/common/util'),
        manageGuide = require('./manageguide/manageguide'),
        tpl  = require('./template/index-html');

    var Clue = Backbone.View.extend({
        config: {
            page1: {
                id: 'clue',
				wrapper: '.clue-pool-box',
				path: './clue/clue'
            },
            page2: {
                id: 'transform',
				wrapper: '.clue-transform-box',
				path: './transform/transform'
            },
            page3: {
                id: 'behavior',
				wrapper: '.clue-behavior-box',
				path: './behavior/behavior'
			},
			page4: {
				id: 'ownership',
				wrapper: '.clue-ownership-box',
				path: 'crm-modules/setting/ownership/ownership'
			},
            page5: {
                id:'systemsetting',
                wrapper:'.clue-scene-box',
                path:'./systemsetting/systemsetting'
            },
            page6: {
                id:'leadsbase',
                wrapper:'.clue-leadsbase-box',
                path:'./leadsbase/leadsbase'
            }
        },

        events: {
            'click .crm-tab a': 'onHandle',
            'click .clue-manageguide': 'showGuide',
        },

        initialize: function (opts) {
			this.setElement(opts.wrapper);
        },

        onHandle: function(e) {
            var $target = $(e.target);
            e.preventDefault();
            if ($target.hasClass('page1')) {
                this.switchPage(['page1'])
            } else if($target.hasClass('page2')) {
                this.switchPage(['page2']);
			} else if ($target.hasClass('page3')) {
                this.switchPage(['page3']);
			}else if($target.hasClass('page5')) {
                this.switchPage(['page5']);
			} else if($target.hasClass('page6')) {
                this.switchPage(['page6']);
			}else{
				this.switchPage(['page4']);
			}
            // return false;
        },

        showGuide: function() {
            if (!this.manageGuide) {
                this.manageGuide = manageGuide();
            }
            this.manageGuide.show();
        },

        switchPage: function(param) {
            this.renderPage(param);
        },

		render: function (param) {
			let me = this;
            let params = util.getTplQueryParams();
            params = _.values(params);
            param = param || [params[1]];
			this.pages = {};
			this.$el.html(tpl({
                param: param
            }));
            this.renderPage(param);
            
            CRM.api.get_licenses({
                key: 'accounts_leads_limit_app',
                objectApiName: 'LeadsObj',
                cb: licenses => {
                    if (licenses.accounts_leads_limit_app) {
                        // require.async('base-modules/guide/guide', function (Guide) {
                            // new Guide({
                                // type: 'onetip',
                                // key: '680_ownership_clue',           // 确保唯一性 页面级别用hash
                                // data: [{
                                    // $target: me.$el.find('.item.page4'),
                                    // pos: 'bottom',
                                    // left: 27,
                                    // top: -18,
                                    // appendBody: true,
                                    // width: 300,
                                    // text: '你可以灵活定义线索的保有量啦！'
                                // }]
                            // })
                        // });
                    } else {
                        this.$el.find('.page4').hide();
                    }
                }
            });
        },

        renderPage: function(param) {
            var me = this;
            var $navItems = me.$('.crm-tab .item');

            $navItems.removeClass('cur');
            (param && param[0]) ? $navItems.filter('.' + param[0]).addClass('cur') : $navItems.eq(0).addClass('cur');

            var id = me.curId = (param && param[0]) ? param[0] : 'page1' ;

            _.map(me.pages, function (page) {
                page.hide();
            });

            if (me.pages[id]) {
                me.pages[id].show();
            }
            else {

                (function (id) {
					require.async(me.config[id].path, function(Page) {
                        var myPage;

						myPage = new Page(_.extend({ wrapper: me.config[id].wrapper, apiname: 'LeadsObj'}));

                        me.curId === id && myPage.show();
                        me.pages[id] = myPage;
                    });
                }(id));
            }
        },

        destroy: function () {
            _.map(this.pages, function (item) {
                item.destroy && item.destroy();
            });
            this.manageGuide && this.manageGuide.destroy && this.manageGuide.destroy();

            this.pages = this.curId = null;
        }
    });

    module.exports = Clue;
});
