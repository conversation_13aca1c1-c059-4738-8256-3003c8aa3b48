<div class="crm-tit crm-manageclue-title">
	<h2><span class="tit-txt">{{$t("线索和线索池管理")}}<a class="crm-doclink" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-3-1线索和线索池管理.html" target="_blank"></a></span></h2>
	<div class="clue-manageguide">{{$t("线索配置向导")}}</div>
</div>
<div class="crm-module-con">
	<div class="crm-tab">
		<a class="item page1" href="#crm/setting/clue/=/page1">{{$t("线索池管理")}}</a>
		## if(param.indexOf('system_setting') > -1) { ##
			<a class="item page5" href="#crm/setting/clue/=/page5">{{$t("线索池场景管理")}}</a>
		## } ##
		<a class="item page2" href="#crm/setting/clue/=/page2">{{$t("线索转换设置")}}</a>
		<a class="item page4" href="#crm/setting/clue/=/page4">{{$t("线索保有量")}}</a>
		<a class="item page3" href="#crm/setting/clue/=/page3">{{$t("线索跟进规则")}}</a>
		<a class="item page6" href="#crm/setting/clue/=/page6">{{$t("线索基础设计")}}</a>
	</div>
	<div class="tab-con">
		<div class="crm-p20 crm-scroll">
			<div class="item clue-pool-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
			<div class="item clue-scene-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
			<div class="item clue-transform-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
			<div class="item clue-behavior-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
			<div class="item clue-ownership-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
			<div class="item clue-leadsbase-box" style="display:none;">
				<div class="crm-loading"></div>
			</div>
		</div>
	</div>
</div>
