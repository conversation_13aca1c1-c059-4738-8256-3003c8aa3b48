/**
 * 线索池
 * 遵循seajs module规范
 */
define(function (require, exports, module) {
	var util = require('crm-modules/common/util'),
		Slide = require('crm-modules/common/slide/slide'),
		ObjectTable = require('crm-modules/components/objecttable/objecttable'),
		rulesFieldStatic = require('crm-modules/common/cluepool/cluepool').rulesFieldStatic,
		// Parse = require('crm-modules/common/cluepool/cluepool').Parse,
		FGutil = require('crm-modules/common/filtergroup/util'),
		detailTpl = require('./template/detail-html');

	/**
	 * @desc 页面视图
	 */
	var Cluepool = Backbone.View.extend({

		initialize: function (opts) {
			var me = this;
			me.setElement(opts.wrapper);
			me.opts = opts;
			me.apiname = "LeadsPoolObj";
			me.slide = new Slide({
				width: 600,
				className: 'crm-s-clue'
			});
			me.slide.on('action', function (type) {
				me.excuteAction(type, me.tableData);
				// me.doAction(type, me.tableData);
			});
			me.cluepoolNum = 0;
			me.setUsersInfo();
			Promise.all([CRM.util.getAuthority('LeadsPoolObj'),me.getEnterpriseQuotaStatistics(),me.getGrayByServerForLeads('graySfaOutTenantPoolBatchAdd'),CRM.util.getDepartmentChildrenSwitch('sfa_vcrm_leadpool_department_includechildren')]).then(([authority,quotaLicense,graySfaOutTenantPoolBatchAdd,needDepartmentChildren]) => {
				// 时段领取及时段分配权限
				me.authority = authority || false;
				// 部门选项属于和不属于是否开启包含子部门开关获取处 （线索池批量新建）
				me.needDepartmentChildren = needDepartmentChildren;
				// 是否开启互联 和 互联新建
				me.quotaStatic = (quotaLicense && graySfaOutTenantPoolBatchAdd) || false;
				me.getPRMRight(function () {
					me.initTable();
				});
			}).catch(() => {
			    me.quotaStatic =  false;
				me.getPRMRight(function(){
					me.initTable();  
				});
			})
		},

		MAX_NUM: 500, //线索池最大数量

		/**
		 *@desc 刷新视图
		 *@param {{Object}} 表格数据对象
		 */
		refresh: function () {
			this.table.table._clearChecked();
			this.table.refresh();
		},

		events: {
			'click .j-add': 'addHandle'
		},
		// 判断是否开启了互联企业
		getEnterpriseQuotaStatistics(){
			return new Promise((resolve, reject) => {
				CRM.util.FHHApi(
					{
						url: `/EM1HER2/admin/enterpriseMeta/getEnterpriseQuotaStatistics`,
						data: {
							
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value.data.hasLicense);
								return;
							}
							CRM.util.alert(res.Result.FailureMessage);
						},
					},
					{
						errorAlertModel: 1,
					}
				);
			});
		},
		getGrayByServerForLeads(type='',apiname='LeadsPoolObj'){
			return new Promise((resolve,reject) => {
				if(!type){
					reject()
				}
				try {
					CRM.util.FHHApi({
						url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
						data: {
							api_name: apiname,
						},
						success: function (res) {
							if (res.Result.StatusCode === 0) {
								resolve(res.Value?.[type]);
								return;
							}
							resolve(false);
						},
						
					}, {
						errorAlertModel: 1
					})
				} catch (error) {
					reject()
				}
			})
		},
		// 添加
		addHandle: function () {
			if (this.cluepoolNum >= this.MAX_NUM) {
				util.remind(3, $t("贵公司可创建的线索池数量已达上限"));
				return false;
			}
			this.doAction('add');
		},

		addfromdpmHandle() {
			const me = this;
			// require.async('vcrm/sdk', function(components){
			// 	components.getComponent('poolBatchnew').then(function(comp) {
			// 		let Comp = comp.default;
			// 		me.batchnewDialog = new Comp({
			// 			propsData: {
			// 				apiname: me.apiname,
			// 				dData: [{
			// 					name: 'test',
			// 					_id: '1032'
			// 				},{
			// 					name: 'test2',
			// 					_id: '1001'
			// 				}],
			// 			}
			// 		});
			// 		me.batchnewDialog.$on('success', function() {
			// 			me.refresh();
			// 		})
			// 	})
			// })
			// return;
			if (this.cluepoolNum >= this.MAX_NUM) {
				util.remind(3, $t("贵公司可创建的线索池数量已达上限"));
				return false;
			}
			util.sendLog(`${this.apiname}Manage`, 'batchnew', {
				operationId: 'create',
			})
			require.async('crm-modules/components/dpmdialog/dpmdialog', function(dpmDialog){
				let dpmdialog = dpmDialog({
                    objname: me.apiname,
					selectHandle(list) {
						CRM.util.waiting();
						require.async('vcrm/sdk', function(components){
							components.getComponent('poolBatchnew').then(function(comp) {
								let Comp = comp.default;
								me.batchnewDialog = new Comp({
									apiname: me.apiname,
									dData: list,
									authority : me.authority,
									needDepartmentChildren: me.needDepartmentChildren,
									type:'DepartmentObj',
								});
								me.batchnewDialog.$on('success', function() {
									me.refresh();
								})
							})
						})
					}
				})
			});
			return;
		},

		batcheditCheck() {
			const me = this;
			return new Promise((resolve) => {
				let checkedData = me.table.getRemberData();
				let len = checkedData ? checkedData.length : 0;
				if (!len) {
					CRM.util.remind(3, $t('请至少选择一条数据'));
					return;
				}
				if (len > 20) {
					CRM.util.remind(3, $t('单次批量编辑不能超过20条'));
					return;
				}

				let exDataList = [];
				let dataList = [];
				_.each(checkedData, (item) => {
					if (item.pool_type == 'private') {
						exDataList.push(item);
					} else {
						dataList.push(item);
					}
				})

				if (exDataList.length) {
					CRM.util.confirm($t('独家伙伴类型的数据不允许批量编辑'), null, function(){
						me.table.setUncheckedRow('_id', exDataList);
						me.table.reduceRemberData(exDataList, true)
						this.hide();
						if (!dataList.length) {
							CRM.util.remind(3, $t('请至少选择一条数据'));
							return;
						}
						resolve(dataList);
					}, {
						btnLabel: {
							confirm: $t('继续保存'),
						}
					})
				} else {
					resolve(dataList);
				}
			})
		},

		batcheditHandle() {
			const me = this;
			me.batcheditCheck().then((list) => {
				require.async('vcrm/sdk', function(components){
					components.getComponent('poolBatchedit').then(function(comp) {
						let Comp = comp.default;
						me.batcheditDialog = new Comp({
							apiname: me.apiname,
							model: {
								isOpenPRM: me.isOpenPRM,
								outer: CRM.get('outer') || {},
								dataList: list,
							},
							isValid: true,
							isSubmit: true,
							authority : me.authority,
							needDepartmentChildren:me.needDepartmentChildren
						});
						me.batcheditDialog.$on('success', function() {
							me.refresh();
						})
					})
				})
			})
		},

		/**
		 *@desc 初始化表格
		 */
		initTable: function () {
			var me = this;
			if (me.table) return this;
			let buttons = [{
				action: "add",
				attrs: "data-action=add",
				className: "j-action",
				text: $t("新建")
			}, {
				action: "addfromdpm",
				attrs: "data-action=addfromdpm",
				className: "j-action",
				isFold: false,
				text: `${$t("从部门新建")}<i class="crm-ui-title btn-addfromdpm-tip" data-pos="bottom" data-title="${$t('借助部门快速生成对应{{name}}', {name: $t('crm.线索池')})}">?</i>`
			},
			// {
			// 	action: "addfromOther",
			// 	attrs: "data-action=addfromOther",
			// 	className: "j-action",
			// 	isFold: false,
			// 	text: `${$t("批量新建")}`
			// }, 
			{
				action: "batchedit",
				attrs: "data-action=batchedit ",
				className: "j-action",
				isFold: false,
				text: $t("批量编辑"),
			}]
			if(me.quotaStatic){
				buttons = buttons.filter(button => button.action !== "addfromdpm");
							
				// 插入新的项到倒数第二个位置
				const newItem = {
					action: "addfromOther",
					attrs: "data-action=addfromOther",
					className: "j-action",
					isFold: false,
					text: `${$t("批量新建")}`,
				};
				buttons.splice(buttons.length - 1, 0, newItem);
			}
			let myObjectTable = ObjectTable.extend({
				trclickHandle(data, $tr, $target) {
					if(!$target.hasClass('action-btn')) {
						me.showDetail(data._id, data);
						return;
					}
					let action = $target.data('action');
					me.excuteAction(action, data, true);
				},
				operateBtnClickHandle(e) {
					var action = $(e.target).attr('data-action');
					me[`${action}Handle`] && me[`${action}Handle`]();
					// if (action === 'add') {
					// 	me.addHandle()
					// }
				},
				getOptions: function () {
					var options = ObjectTable.prototype.getOptions.apply(this, arguments);
					options = _.extend(options, {
						custom_className: 'crm-cluepool-table',
						searchTerm: false,
						isOrderBy_allColumn: false,
						showFilerBtn: false,
						showMultiple: true,
						checked: {
							idKey: '_id',
							data: [],
						}
					})

					return options
				},

				getColumns() {
					let columns = this.options.columns;
					columns.push({
						data: null,
						width: 200,
						title: $t("操作"),
						lastFixed: true,
						render(data, type, full) {
							let _html = '';
							_html += `<a data-id="${full._id}" class="action-btn" data-action="edit" href="javascript:;">${$t("编辑")}</a>`;
							if (full.leads_count > 0) {
								_html += `<a data-id="${full._id}" class="action-btn" data-action="shift" href="javascript:;">${$t("转移")}</a>`;
							}
							_html += `<a data-id="${full._id}" class="action-btn" data-action="del" href="javascript:;">${$t("删除")}</a>`;
							_html += `<a data-id="${full._id}" class="action-btn" data-action="clone" href="javascript:;">${$t("复制")}</a>`;
							return _html;
						}
					})
					_.each(columns, function (a) {
						if (a.data === 'name') {
							a.render = function (name) {
								return `<a href="javascript:;">${name}</a>`
							}
						}
						if (a.data === 'allocate_overtime') {
							a.render = function(data, type, full) {
								let txt = [];
								if (full.allocate_overtime_hours) {
									txt.push(full.allocate_overtime_hours);
									txt.push($t("小时"));
								}
								if (full.allocate_overtime_minutes) {
									txt.push(full.allocate_overtime_minutes);
									txt.push($t("分钟"));
								}
								if (!txt.length) {
									txt.push("--");
								}
								return txt.join('');
							}
						}
						if (a.data === 'follow_overtime') {
							a.render = function(data, type, full) {
								let txt = [];
								if (full.overtime_hours) {
									txt.push(full.overtime_hours);
									txt.push($t("小时"));
								}
								if (full.overtime_minutes) {
									txt.push(full.overtime_minutes);
									txt.push($t("分钟"));
								}
								if (!txt.length) {
									txt.push("--");
								}
								return txt.join('');
							}
						}
					});
					return columns;
				},

				getExtendAttribute: function () {
					let baseScenes = this.get('baseScenes')
					let currentScenes = baseScenes && baseScenes[0] ? baseScenes.find(item => item.api_name === 'All') : {};
					return {
						scene_id: currentScenes._id,
						scene_type: currentScenes.type,
						scene_api_name: currentScenes.api_name
					}
				}
			})
			me.table = new myObjectTable({
				el: me.$el,
				apiname: me.apiname,
				showTitle: false,
				showTerm: false,
				showTermBatch: false,
				search: {
					placeHolder: $t("搜索"),
					type: 'Keyword',
					highFieldName: 'name',
					pos: 'T'
				},
				operate: {
					pos: 'T',
					btns: buttons
				},
			});
			me.table.render();
		},
		// getCommonParse: function () {
		// 	var me = this;
		// 	if (!this._parse) {
		// 		this._parse = new Parse();
		// 	}
		// 	return this._parse;
		// },
		_getAdminList: function (data) {
			var arr = [];
			_.each(data, function (a) {
				a.Name && arr.push(a.Name);
			});
			return arr.join($t("，"));
		},

		/*
		 * 	处理多选，把字符串转为数组
		 * */
		parseObj: function (obj) {
			var ruleList = obj.SalesCluePoolAllocateRuleList || [];
			// if (ruleList.length) {
			// 	ruleList = _.map(ruleList, function (items) {
			// 		if (items.RuleFilterList.length) {
			// 			items.RuleFilterList = _.map(items.RuleFilterList, function (item) {
			// 				if ((item.FieldType == 8 || item.FieldType == 9) && item.FieldValue.includes('|')) {
			// 					item.FieldValue = item.FieldValue.split('|');
			// 				}
			// 				return item
			// 			});
			// 		}
			// 		return items
			// 	})
			// }
			obj.SalesCluePoolAllocateRuleList = ruleList;
			return obj;
		},

		parseRecObj: function (obj) {
			var ruleList = obj.RecyclingRuleList || [];
			if (ruleList.length) {
				ruleList = _.map(ruleList, function (item) {
					if (item.RecyclingFilterList.length) {
						item.RecyclingFilterList = _.map(item.RecyclingFilterList, function (item) {
							if ((item.FieldType == 8 || item.FieldType == 9) && item.FieldValue.includes('|')) {
								item.FieldValue = item.FieldValue.split('|');
							}
							return item
						})
					}
					return item
				})
			}
			obj.RecyclingRuleList = ruleList;
			return obj;
		},
		getScopeHtml(fulldata) {
			let data = [];
			if (fulldata.length) {
				let filter_group = fulldata;
				for (let i = 0; i < filter_group.length; i++) {
					let condition = filter_group[i];
					let row = []
					for (let j = 0; j < condition.filters.length; j++) {
						const filter = condition.filters[j];
						let field = CRM.get(`fields.PersonnelObj`)[filter.field_name];
						if (field) {
							let text = FGutil.formatFieldValue(field, filter.field_values, filter.operator);
							row.push(`${field.label} ${filter.operator_name} ${text ? '' + text + '' : ''}`)
						}
					}
					if (i == 0) {
						data.push(`<p style="color:#333;">${row.join(` ${$t('且')} `)}</p>`)
					} else {
						data.push(`<p style="color:#333;">${$t('或')} ${row.join(` ${$t('且')} `)}</p>`)
					}
				}
			}
			let text = data.join(' ') || $t('成员已被全部禁用');
			return `<div class="item-rule">${text}</div>`;
		},
		/**
		 *@desc 显示详情
		 *@param {{Object}} 表格数据对象
		 */
		showDetail: function (id, data) {
			var me = this;
			var _data = data;
			me.getDetailById(id, function (value) {
				me.slide.show();
				// 灰度了新的国家省市区用新的接口获取数据（非全量）
				// let fn = !CRM.util.isCrmRulesNewArea() ? CRM.util.getCountryAreaOptions : CRM.util.areaCode2Label;
				// let param = !CRM.util.isCrmRulesNewArea() ? '' : me.getRulesAreaCode(value.SalesCluePool);
				// fn(param).then(() => {
					me.parseData(value.SalesCluePool, _data).then((data) => {
						me.slide.setDetailCon(detailTpl(me.formatPostData(data)));
					})
				// })
			});
		},
		// getRulesAreaCode(data) {
        //     let codes = [];
        //     let code1 = CRM.util.getRuleListAreaCode(data.RecyclingRuleList, 'Wheres');
        //     let code2 = CRM.util.getRuleListAreaCode(data.SalesCluePoolAllocateRuleList, 'Wheres');
        //     let code3 = CRM.util.getRuleListAreaCode(data.SalesCluePoolAllocateRuleList, 'MemberWheres');
        //     let code4 = CRM.util.getRuleAreaCode(data.PoolOwnerRule);

        //     codes = _.union(code1, code2, code3, code4);
        //     return codes;
        // },

		// 此处listData只用于更新leads_count or SalesClueCount 线索条数的更新
		parseData(data, listData) {
			const me = this;
			return new Promise((resolve) => {
				// var parse = this.getCommonParse();
				data.isOpenPRM = CRM.get('isOpenPRM');
				data = me.formatObj(data, listData);
				me.parseObj(data);
				me.parseRecObj(data);
				data = _.extend({
					display_name: $t('crm.线索池'),
					object_display_name: $t('crm.销售线索'),
				}, data);
				me.tableData = data;
				data.UpdateTime = FS.moment(data.UpdateTime || data.LastModifiedTime,null,null,null,true).format('YYYY-MM-DD HH:mm');
				data.adminList = me._getAdminList(data.Employees);
				data.SalesCluePoolAllocateRuleList = _.sortBy(data.SalesCluePoolAllocateRuleList || [], function (item) {
					return item.Priority;
				});

				me.getFieldList(function (fieldlist) {
					me.fieldlist = fieldlist;
					data.ownerRuleContent = CRM.util.parseFiltersRule(data.PoolOwnerRule, fieldlist);
					data.rules = _.map(data.SalesCluePoolAllocateRuleList, function (rule) {
						let pData = CRM.util.parseAllocateRule(rule, me.fieldlist, CRM.get(`fields.PersonnelObj`), data.isOpenPRM);
						let _rule = _.extend({}, rule, {
							ruleText: pData.ruleText,
							content: rule.AllocatePattern == 3 ? '': pData.memberText,
						})
						return _rule;
						// var _rule = parse.parseField(me.fieldlist, rule);
						// var RuleFilterGroupList = _rule.RuleFilterGroupList || [];
						// _rule.ruleText = parse.parseFilterRule(fieldlist, _rule.RuleFilterList, FGutil)//parse.getRuleText(_rule.datas);
						// if (data.isOpenPRM) {
						// 	var _c1 = _rule.member.length ? $t('内部') + $t('：') + _rule.member.join('、') : '';
						// 	var _c2 = _rule.exContact.length ? $t('伙伴') + $t('：') + _rule.exContact.join('、') : '';
						// 	_rule.content = _c1.length && _c2.length ? _c1 + '<br/>' + _c2 : _c1 + _c2;
						// 	if (RuleFilterGroupList.length > 0) {
						// 		_rule.content += me.getScopeHtml(RuleFilterGroupList) + '<br/>';
						// 	}
						// } else {
						// 	_rule.content = _rule.member.join('、');
						// 	if (RuleFilterGroupList.length > 0) {
						// 		_rule.content +=  me.getScopeHtml(RuleFilterGroupList) + '<br/>';
						// 	}
						// }
						// return _rule;
					});
					data.RecyclingCondition = _.map(data.RecyclingRuleList, function (item) {
						// var parsed_data = _.extend(parse.parseFieldRecycling(me.fieldlist, item, 'LeadsObj'));
						// parsed_data.ruleText = parse.getRuleText(parsed_data.datas);
						// parsed_data.scopeText = parsed_data.scopeText.replace($t("crm.线索池"), item.TargetPoolId == data.SalesCluePoolID ? $t("本线索池") : ($t("其他线索池") + '：' + item.TargetPoolName));
						let parsed_data = CRM.util.parseRecycleRule(item, me.fieldlist, 'LeadsObj')
						return parsed_data;
					})
					resolve(data);
				});
			})
		},
		addfromOtherHandle() {
			this.selectDpmDialog()
		},
		selectDpmDialog() {
            const me = this
            this.getDataDialog && this.getDataDialog.$destroy()
            this.getDataDialog = FxUI.create({
                template: `    <fx-dialog
      :title="$t('sfa.cluepool.addSelectType')"
      size="small"
      :visible.sync="dialogSelectType"
      custom-class="select-dpm-dialog"
    >
      <ul>
        <li
          v-for="item in retentionCordType"
          :key="item.apiName"
          @click="nowType = item.apiName"
          :class="{
            'red-border': item.apiName === nowType,
          }"
        >
          {{ item.label }}
          <span class="fx-icon-question" v-show="item.description" :title="item.description"></span>
          <span class="fx-icon-ok-2"></span>
        </li>
      </ul>
      <div slot="footer" class="dialog-footer">
        <fx-button type="primary" @click="addTable" size="small"
          >{{$t('确定')}}</fx-button
        >
        <fx-button @click="dialogSelectType = false" size="small"
          >{{$t('取消')}}</fx-button
        >
      </div>
    </fx-dialog>`,
                data() {
                    return {
						dialogSelectType:true,
						nowType:'DepartmentObj',
						retentionCordType:[{
							apiName : 'DepartmentObj',
							label:$t('从部门新建'),
							description:$t('借助部门快速生成对应{{name}}', {name: $t('crm.线索池')}),
						},{
							apiName:'EnterpriseRelationObj',
							label:$t('sfa.cluepool.addfrom.enterpriseRelationObj'),
							description:me.apiname === 'LeadsPoolObj' ? $t('sfa.crm.leadsPoolAddFromRelation'):$t('sfa.crm.highseasAddFromRelation')
						}]
					 }
                },
                mounted() {

                },
                methods: {
					addTable() {
					    let type = this.nowType;
						// 从部门新建
						if(type === 'DepartmentObj'){
							util.sendLog(`${me.apiname}Manage`, 'batchnew', {
								operationId: 'create',
							})
							me.renderDpmDialog(type)	
						}
						if(type === 'EnterpriseRelationObj'){
							// 从互联企业新建
							if(!me.isOpenPRM){
								CRM.util.alert($t("sfa.noHavePrm"))
								return
							}
							me.renderDpmDialog(type)

						}
					}
                },
            })
        },
		renderDpmDialog(type) {
			const me = this;
			require.async('crm-modules/components/dpmdialog/dpmdialog', function(dpmDialog){
				let dpmdialog = dpmDialog({
					objname: me.apiname,
					type:type,
					selectHandle(list) {
						me.getDataDialog.dialogSelectType = false
						CRM.util.waiting();
						require.async('vcrm/sdk', function(components){
							components.getComponent('poolBatchnew').then(function(comp) {
								let Comp = comp.default;
								me.batchnewDialog = new Comp({
									apiname: me.apiname,
									dData: list,
									authority : me.authority,
									needDepartmentChildren:me.needDepartmentChildren,
									type:type,
								});
								me.batchnewDialog.$on('success', function() {
									me.refresh();
								})
							})
						})
					}
				})
			});
        },

		

		formatObj: function (obj, _data) {
			_.isString(obj.RolePool) && obj.RolePool == "" && (obj.RolePool = 0);
			_.isString(obj.LimitType) && obj.LimitType == "" && (obj.LimitType = 1);

			_.isString(obj.RolePool) && (obj.RolePool = {
				private: 1,
				normal: 0,
				0: 0,
				1: 1,
			} [obj.RolePool]);

			_.isString(obj.LimitType) && (obj.LimitType = {
				personal: 0,
				enterprise: 1,
				0: 0,
				1: 1,
			} [obj.LimitType]);

			let _counts = _data.leads_count || _data.SalesClueCount || 0;
			(!_.isUndefined(_data.leads_count) || !_.isUndefined(_data.SalesClueCount)) && (obj.LeadsCount = obj.SalesClueCount = _counts);

			return obj;
		},

		formatPostData: function (data) {
			var type = data.RecyclingRuleType,
				day = type == 2 ? data.DealDays : (type == 3 ? data.FollowUpDays : 0),
				adminList = [];
			data.RemindRuleWords = [];
			_.each(data.RemindRuleList, function (item, index) {
				var word1 = item.RuleType == 2 ? item.DealDays : item.FollowUpDays;
				var word2 = [$t("不收回"), $t("未成交"), $t("未跟进")][item.RuleType - 1];
				data.RemindRuleWords.push(word1 + $t("天") + word2 + $t("提醒负责人"));
			});
			data.RemindRuleWords = data.RemindRuleWords.join($t("；")) || $t("未设置");

			var type1 = [],
				type2 = [];
			_.each(data.AdminList, function (item) {
				if (item.Type == 1) {
					type1.push(item.DataID);
				} else if (item.Type == 2) {
					type2.push(item.DataID);
				}
			});

			if (util.getNameByIds(type2, 'g', true) && util.getNameByIds(type1, 'p', true)) {
				data.Administrator = util.getNameByIds(type2, 'g', true) + ' , ' + util.getNameByIds(type1, 'p', true);
			} else if (util.getNameByIds(type2, 'g', true)) {
				data.Administrator = util.getNameByIds(type2, 'g', true);
			} else if (util.getNameByIds(type1, 'p', true)) {
				data.Administrator = util.getNameByIds(type1, 'p', true);
			}

			/*=========================================================================================*/

			var circleEm = [],
				em = [];
			_.each(data.EmployeeList, function (item) {
				if (item.Type == 1) {
					em.push(item.DataID);
				} else if (item.Type == 2) {
					circleEm.push(item.DataID);
				}
			});

			if (util.getNameByIds(circleEm, 'g', true) && util.getNameByIds(em, 'p', true)) {
				data.EmployeeName = util.getNameByIds(circleEm, 'g', true) + ' , ' + util.getNameByIds(em, 'p', true);
			} else if (util.getNameByIds(circleEm, 'g', true)) {
				data.EmployeeName = util.getNameByIds(circleEm, 'g', true);
			} else if (util.getNameByIds(em, 'p', true)) {
				data.EmployeeName = util.getNameByIds(em, 'p', true);
			}

			data.UpdateTime = data.UpdateTime || new Date().getTime();
			// this.data = _.extend(this.data, data);
			data.ClaimIntervalDays = data.ClaimIntervalDays || 0;
			if(this.authority.grayAllocateLimitFlag){
				let config = {
					'day':$t('每天'),
					'month':$t('每月'),
				}
				data.GrayAllocateLimitFlag = this.authority.grayAllocateLimitFlag
				data.TimeAllocateLimitText = data.TimeAllocateLimitNum ? `${config[data.TimeAllocateLimitType]}${data.TimeAllocateLimitNum}${$t('条')}` : $t('crm.leads.rule_timeAllocation_nohave')
				data.TimeClaimLimitText = data.TimeClaimLimitNum ? `${config[data.TimeClaimLimitType]}${data.TimeClaimLimitNum}${$t('条')}` :  $t('crm.leads.rule_timeClaim_nohave') 
			}
			if(this.authority.graySfaAllocateContinue){
				let config = {
					0:$t('crm.leads.rule_allocation_over'),
					1:$t('crm.leads.rule_allocation_next')
				}
				data.GraySfaAllocateContinue = this.authority.graySfaAllocateContinue
				// 905全网时放开
				data.AllocateChoice = `${$t('crm.leads.rule_allocation_fail')}${config[+data.AllocateChoice || 0]}`
			}
			return data;
		},

		formatDetail: function (options) {
			return _.map(options, function (item) {
				return {
					ItemCode: item.value,
					ItemName: item.label
				}
			});
		},

		getCountryComponents: function (fieldlist) {
			var ret = [];
			var me = this;
			var hasCountryComp = _.findWhere(fieldlist, {
				FieldType: 25
			}) || [].Fields;
			return util.getCountryAreaOptions().then(function (res) {
				if (!hasCountryComp) {
					return fieldlist;
				}

				if (res && hasCountryComp) {
					_.each(fieldlist, function (field) {
						// EnumDetailID: "d17043182a4047fd96df30c7a45b1789"
						// IsDeleted: false
						// IsSysItem: false
						// ItemCode: "default__c"
						// ItemName: "预设业务类型"
						if (_.indexOf([26, 27, 28, 29], field.FieldType) != -1) {
							if (field.FieldType == 26) {
								field.EnumDetails = me.formatDetail(res.country.options);
							} else if (field.FieldType == 27) {
								field.EnumDetails = me.formatDetail(res.province.options);
							} else if (field.FieldType == 28) {
								field.EnumDetails = me.formatDetail(res.city.options);
							} else if (field.FieldType == 29) {
								field.EnumDetails = me.formatDetail(res.district.options);
							}
							ret.push(field);
							return;
						}
						ret.push(field);
					})
				}
				return ret;
			})
		},

		filterFieldList: function (list) {
			list = list.concat(rulesFieldStatic);
			return _.filter(list, function (item) {
				return _.indexOf(['SalesCluePoolID', 'PicturePath', 'MarketingEventID'], item.FieldName) == -1;
			});
		},

		/*=============================================分配规则替换接口=============================================*/

		/*请求字段列表*/
		getFieldList: function (callback) {
			var me = this;
			var data = FS.getAppStore('crm-fieldlist-cluepool');
			if (data) {
				callback && callback(data);
				return;
			}
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/LeadsObj/controller/DescribeLayout',
				data: {
					"include_detail_describe": false,
					"include_layout": false,
					"apiname": "LeadsObj",
					"layout_type": "add",
					"recordType_apiName": "record_sKbe4__c"
				},
				success: function (res) {
					if (res.Result.StatusCode == 0) {
						var list = me.parseFieldData(res.Value.objectDescribe.fields);
						FS.setAppStore('crm-fieldlist-cluepool', list);
						// var preList = me.addStaticField(list);
						// callback && callback(me.parseField(preList));
						callback && callback(list);
						return;
					}
					util.alert(res.Result.FailureMessage);
				}
			}, {
				errorAlertModel: 1
			})
		},


		parseFieldData: function (data) {
			var key, temp = [];
			for (key in data) {
				if (data[key] && data[key] instanceof Object) {
					temp.push(data[key])
				}
			}
			return temp
		},

		/*过滤字段*/
		parseField: function (fields) {
			return _.filter(fields || [], function (item) {
				var flag = (item.type == 'formula' && _.contains(['date_time', 'date', 'time'], item.return_type)) || (item.type == 'formula' && !item.is_index);
				if (item.type == 'formula') {
					item.type = item.return_type || item.type;
				}
				return !flag;
			});
		},

		addStaticField: function (list) {
			list = list.concat(rulesFieldStatic);
			return _.filter(list, function (item) {
				// 1:系统预置字段 2:用户自定义字段
				if (item.define_type === "package") {
					return _.indexOf(['back_reason', 'owner_department', 'high_seas_name', 'last_modified_by', 'claimed_time', 'out_owner', 'out_tenant_id', 'leads_id',
						'created_by', 'lock_status', 'remaining_time', 'partner_id', 'out_resources', 'life_status', 'returned_time', 'account_status', 'address', 'biz_status',
						'high_seas_id', 'owner', 'expire_time', 'last_deal_closed_time', 'completion_rate', 'data_own_department', 'account_no', 'location',
						'fax', 'url', 'recycled_reason', 'total_refund_amount', 'last_deal_closed_amount', 'filling_checker_id', 'is_remind_recycling', 'owner_modified_time'
					], item.api_name) == -1 && item.is_active === true;
				} else {
					return _.indexOf(["group", 'true_or_false', 'url',
						"time", "image", "file_attachment", "percentile", 'signature',
						"formula", "department", "count", 'employee', 'location', 'master_detail'
					], item.type) == -1 && item.is_active === true;
				}
			});
		},

		/*==========================================================================================*/

		// /*请求字段列表*/
		// getFieldList: function(callback) {
		//     var me = this;
		//     var list = CRM.get('__cluepool-depend-fieldlist');
		//     if (list) {
		//         callback && callback(list);
		//         return;
		//     }
		//     util.FHHApi({
		//         url: '/EM1HCRM/UserDefinedField/GetUserDefinedFieldListByOwnerType',
		//         data: {
		//             OwnerType: 1
		//         },
		//         success: function(res) {
		//             if(res.Result.StatusCode == 0) {
		//                 res.Value.Items.length && res.Value.Items.shift();
		//                 res = me.filterFieldList(res.Value.Items);
		//                 CRM.set('__cluepool-depend-fieldlist', res);
		//                 callback && callback(res);
		//                 return;
		//             }
		//             util.alert(res.Result.FailureMessage);
		//         }
		//     }, {
		//         errorAlertModel: 1
		//     })
		// },

		getDetailById: function (id, fn) {
			var me = this;

			util.getCluePoolByID(id, true).then(function (res) {
				// me.data = res.Item;
				console.log('res', res)
				fn && fn(res);
			})
		},

		setUsersInfo() {
			CRM.util.getUserGroups2();
			CRM.util.getUserRoles2();
		},

		// 获取PRM状态以及外部联系人数据
		getPRMRight: function (cb) {
			var me = this;
			if (typeof me.isOpenPRM === 'undefined' ) {
				$.when(CRM.util.getPRMRight(), CRM.util.getFieldsByApiName('PersonnelObj')).then(function (data1, data2) {
					me.isOpenPRM = data1;
					me.PersonnelObj = data2;
					cb && cb();
				});
				return;
			}
			cb && cb();
		},

		excuteAction(action, data, fromList) {
			const me = this;
			let getType;
			if (_.contains(['edit', 'clone'], action)) {
				getType = 'new';
			}
			let id = data._id || data.SalesCluePoolID;
			// 兼容详情数据没有取到线索数量的问题
			let leads_count = data.leads_count || data.SalesClueCount;
			CRM.util.getCountryAreaOptions().then(() => {
				CRM.util.getCluePoolByID(id, true, getType).then((res) => {
					let _data = res.SalesCluePool || res;
					let _key = _.isUndefined(_data.SalesClueCount) ? 'leads_count' : 'SalesClueCount';
					_data[_key] = leads_count;
					me.doAction(action, _data, fromList);
				})
			})
		},

		/**
		 *@desc 根据元素的action 执行线索池的相关操作
		 */
		// doAction: function (action, data) {
		// 	var me = this;
		// 	if (action === 'del') {
		// 		me.doingAction(action, data);
		// 		return;
		// 	}
		// 	me.getPRMRight(function(bData){
		// 		me.doingAction(action, _.extend({}, data, bData));
		// 	})
		// },
		doAction: function (action, data, fromList) {
			var me = this;
			require.async('crm-modules/action/cluepool/cluepool', function (Action) {
				if (!me.action) {
					me.action = new Action();
					me.listenTo(me.action, {
						success: me.doSuccess,
						error: me.doError,
					})
				}
				me.action[action] && me.action[action](data, fromList , me.authority);
			});
		},

		/**
		 *@desc 关于线索池的各种操作成功之后执行
		 *@param {{Number}} 1添加 2编辑 3转移 4删除
		 *@param {{Object}} 更新过的数据
		 */
		doSuccess: function (type, data, fromList) {
			if (fromList) {
				this.refresh();
				return;
			}
			data = data.data || data;
			let _id = data.SalesCluePoolID || data._id || data.id;
			switch (type) {
				case 2:
					// this.showDetail(data.SalesCluePoolID, data);
					this.showDetail(_id, data);
					break;
				case 3:
					data.UpdateTime = $.now();
					data.SalesClueCount = 0;
					// _.extend(this.tableData, data);
					// this.showDetail(this.tableData.SalesCluePoolID, this.tableData);
					this.showDetail(_id, data);
					break;
				case 4:
					this.slide && this.slide.hide();
					this.cluepoolNum--;
					break;
				case 1:
					this.cluepoolNum++;
					break;
			}
			this.refresh();
		},

		/**
		 *@desc 关于线索池的各种操作失败之后执行
		 *@param {{Number}} 1添加 2编辑 3转移 4删除
		 *@param {{Object}}
		 */
		doError: function (type, data) {
			//code..........
		},

		show: function () {
			this.$el.show();
		},

		hide: function () {
			this.$el.hide();
		},

		/**
		 *@desc 销毁视图页面
		 */
		destroy: function () {
			var me = this;
			me.stopListening();
			me.remove();
			me.batchnewDialog && me.batchnewDialog.cusDestroy();
			me.action && me.action.destroy();
			me.slide && me.slide.destroy();
			me.table && me.table.destroy();
			me = me.table = me.action = me.$el = me.el = me.events = me.options = null;
		}
	});

	module.exports = Cluepool;

});
