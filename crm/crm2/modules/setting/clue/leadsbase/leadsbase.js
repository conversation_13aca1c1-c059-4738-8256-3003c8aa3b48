/**
 * 线索池场景
 */
define(function (require, exports, module) {
    var systemSetting = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
            this.render()
        },
        render() {
            var me = this;
            var system_setting = document.createElement('div');
            let manage = document.getElementsByClassName('clue-leadsbase-box');
            manage[0].appendChild(system_setting);
            CRM.util.getConfigValue('is_show_creator_auto_add_team').then(status => {
                // 兼容字符串和布尔值
                const isTrue = status === true || status === 'true';
                if (!isTrue) {
                    me.renderWrapper(system_setting,false);
                } else {
                    me.renderWrapper(system_setting,true);
                }
            })
        },
        renderWrapper(el,value) {
            this.$el.find('.crm-loading').remove();
            this.baseLeadsSetting && this.baseLeadsSetting.destroy();
            this.baseLeadsSetting = FxUI.create({
                wrapper: el,
                template: `<div class="leadsbase-wrapper">
                    <div class="leadsbase-action">
                        <h3>${$t("crm.setting.clue.leadsbaseaction.title")}</h3>
                        <div class="leadsbase-action-smalltext">${$t('crm.setting.clue.leadsbaseaction.text')}<div>
                        <div class="leadsbase-action-config">
                        <fx-switch
                            v-model="configValue"
                            size="small"
                            :before-change="beforeChangeHandle"
                        >
                        </fx-switch>
                        <div>
                    </div>
                    </div>
                </div>`,
                data(){
                    return {
                        configValue: value
                    }
                },
                methods: {
                    beforeChangeHandle(value) {
                    const me = this
                    return  CRM.util.setConfigValue({
                            key: 'is_show_creator_auto_add_team',
                            value: !me.configValue
                        }).then(() => {
                            return true;
                        })
                    }
                }
            })
        },
        show: function () {
            this.$el.show();
        },

        hide: function () {
            this.$el.hide();
        },
        /**
         *@desc 销毁视图页面
         */
        destroy: function () {
            var me = this;
            me.baseLeadsSetting && me.baseLeadsSetting.$destroy();
            me.remove();
        }
    });

    module.exports = systemSetting;

});
