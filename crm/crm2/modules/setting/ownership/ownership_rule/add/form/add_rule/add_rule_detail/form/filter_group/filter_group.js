define(function (require, exports, module) {
	let FieldBase = require('crm-modules/action/field/field').C.Base
	let util = require('crm-modules/common/util');
	let FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
	var helper = require('crm-modules/setting/ownership/helper');

	
	let Filter = FieldBase.extend({

		render: function () {
			this.apiname = this.model.get('apiname');
			this.init();
		},

		// // 检查field_values是否都带有_y后缀
		// checkFieldValuesHasYSuffix: function(fieldValues) {
		// 	if (!Array.isArray(fieldValues) || fieldValues.length === 0) return false;
		// 	return fieldValues.every(value => String(value).endsWith('_y'));
		// },

		init: function () {
			let me = this;
			let defaultValue = me.get('data').filter_group;
			let needDepartmentChildren = false;
			let filterApiname = [];
			if (this.apiname === 'LeadsObj') {
				filterApiname = ['completion_rate','life_status','leads_status', 'transform_time', 'assigner_id', 'assigned_time', 'assigned_time', 'expire_time', 'remaining_time', 'last_modified_by', 'owner', 'remind_days', 'resale_count', 'returned_time', 'owner_department', 'last_modified_time', 'account_id', 'contact_id', 'new_opportunity_id', 'opportunity_id','out_tenant_id', 'extend_days'].concat(CRM.get('isOpenPRM') ? [] : ['out_owner', 'partner_id'])
				// if(FS.util.getls('sfa_vcrm_leadpool_department_includechildren')){
				// 	needDepartmentChildren = true;
				// 	// 处理defaultValue中的级联选择
				// 	if (defaultValue && Array.isArray(defaultValue)) {
				// 		defaultValue.forEach(item => {
				// 			if (item.filters && Array.isArray(item.filters)) {
				// 				item.filters.forEach(filter => {
				// 					if (filter.is_cascade && filter.field_values && Array.isArray(filter.field_values)) {
				// 						filter.field_values = filter.field_values.map(value => {
				// 							if (typeof value === 'string' && !value.endsWith('_y')) {
				// 								return value + '_y'; // 添加_y后缀
				// 							}
				// 							return value;
				// 						});
				// 					}
				// 				});
				// 			}
				// 		});
				// 	}
				// 	console.log("[][][]",defaultValue);
				// 	// 创建本地helper副本
				// 	let localHelper = Object.assign({}, helper, {
				// 		changeDepartmentConfig(child,options,compare) {
				// 			if(compare.value == 13 || compare.value == 14){
				// 				child.groupIncludeChildrenStatus = 1;
				// 				child.needDepartmentChildren = true;
				// 			}
				// 			return child;
				// 		}
				// 	});
				// 	// 使用本地helper
				// 	helper = localHelper;
				// }
			} else if (this.apiname === 'AccountObj') {
				filterApiname = ['completion_rate','life_status','back_reason', 'lock_status', 'owner', 'owner_department', 'created_by', 'remaining_time', 'last_modified_by', 'lock_status', 'data_own_department', 'account_status', 'fax', 'url', 'recycled_reason', 'total_refund_amount', 'last_deal_closed_amount', 'filling_checker_id', 'is_remind_recycling', 'owner_modified_time', 'expire_time', 'returned_time', 'claimed_time', 'completed_field_quantity', 'remind_days', 'extend_days', 'account_path', 'industry_ext'].concat(CRM.get('isOpenPRM') ? [] : ['out_owner', 'partner_id'])
			} else if ( this.apiname === 'NewOpportunityObj') {
                filterApiname = ['completion_rate', 'life_status', 'owner', 'owner_department', 'created_by', 'remaining_time', 'last_modified_time', 'last_modified_by', 'lock_status', 'data_own_department', 'owner_modified_time', 'expire_time', 'returned_time', 'claimed_time', 'remind_days', 'extend_days'].concat(CRM.get('isOpenPRM') ? [] : ['out_owner', 'partner_id']);
            }
			
			this.widget = new FilterGroup({
				$wrapper: this.$el,
				title: $t("且(AND)"),
				width: 750,
				AND_MAX: 10,
				addBtnName: $t('新增或关系'),
				apiname: me.apiname,
				selectone_multiple: true,	//单选变多选
				// needDepartmentChildren: needDepartmentChildren,
				defaultValue: defaultValue,
				filterType: ['url', 'time', 'image', 'file_attachment', 'percentile', 'count', 'quote', 'formula', 'signature', 'employee_many', 'department_many', 'html_rich_text', 'object_reference_many'],
				filterApiname: filterApiname,
				helper: helper,
				parseCompare(compares, field) {
					if (CRM.get('isOpenPRM') && field.api_name === 'out_owner') {
						compares = compares.filter((item) => {
							return ['IS', 'ISN'].indexOf(item.value1) != -1
						})
					}
					return compares;
				}
			});
		},

		getValue: function () {
			let result = [];
			let data = this.widget.getValue();
			data = JSON.parse(data);

			let wheres = data;
			let connector = data.type;

			wheres = wheres.map((condition) => {
				let filters = condition.filters.map((filter) => {
					return {
						operator: filter.operator,
						operator_name: filter.operator_name,
						field_name: filter.field_name,
						field_values: filter.field_values,
						isIndex: false,
						fieldNum: 0,
						connector: 'AND',
						isObjectReferencea: false
					};
					// if (this.checkFieldValuesHasYSuffix(newFilter.field_values)) {
					// 	newFilter.is_cascade = true;
					// }
					// return newFilter;
				})
				return {
					filters: filters,
					connector: 'OR'
				}
			})

			this.hideError();
			if (!this.widget.valid()) {
				this.showError();
				return null;
			}

			return wheres;
		},

		show() {
			this.$el.show();
		},

		hide() {
			this.$el.hide();
		},

		showError: function () {
			util.showErrmsg(this.$el, $t('请填写筛选值!'));
		},

		hideError: function () {
			util.hideErrmsg(this.$el);
		},

		destroy: function () {
			this.widget && this.widget.destroy();
		}
	});

	module.exports = Filter;

});
