
.pickselfobject_cpq_style2{

	.displayNone{
		display: none;
	}

	.draggable-container__main{
		padding: 8px;
	}

	.t-wrap{
		overflow: unset;
		display: flex;
	}

	.cpq_style2_container{
		display: flex;
		height: 100%;
        .dialog-btns {
            .fx-button{
                // padding: 0 16px;
                span{
                    padding: 0 !important;
                    margin: 0 !important;
                    line-height: inherit;
                }
            }
            .b-g-btn,
            .b-g-btn-cancel {
                border-radius: 8px;
            }
        }
	}

	.cpq_style2_container_right{
		flex: 1;
	}

	.resize-right{
		z-index: 9;
	}

	.cpq_style2_container_left{

		.draggable-container__main{
			overflow: auto;
		}

		.draggable-container__right-close-btn{
			z-index: 10;
		}
	}

	.closed{
		.cpq_style2_container_table{
			display: none;
		}

	}


	.cpq_style2_container_table{
		//padding: 8px;
		//border: 1px solid @table-border-color;
		box-sizing: border-box;
		overflow: hidden;
		min-width: 270px;

		.dt-caption{
			height: auto;

			.bom_style2_pricebook{
				float: left;
				width: 48%;
				height: 28px;
				margin-right: 8px;
				margin-bottom: 8px;

				.el-input__inner{
					height: 28px;
				}
				.fx-select{
					width: 100%;
				}
				.fx-icon-arrow-up{
					&:before{
						font-size: 14px;
						color: #333;
					}
					&:after{
						font-size: 14px;
						color: #333;
					}
				}
			}

			.category-wrap{
				width: 48%;
				height: 28px;
				margin-bottom: 8px;
			}

			.crm-a-category-hand{
				width: 100%;
				height: 100%;
				box-sizing: border-box;
				padding: 5px 8px;
				border-radius: 4px;
				border: 1px solid #c1c5ce;
			}

			.j-dt-sc-box{
				width: 48%;
				float: right;
				margin-bottom: 8px;
				margin-right: 0;
			}

			.search-float-left{
				float: left;
				margin-left: 0;
			}

			.filter-btn-wrap{
				float: right;
				margin-right: 8px;
				margin-bottom: 8px;

				.term-filter-btn{
					//width: 28px;
					height: 28px;
					box-sizing: border-box;
					border-radius: 4px;
					&:before{
						padding: 0 1px;
					}
				}
				.term-filter-name{
					display: none;
				}
			}

			.el-input--suffix {
				.el-input__inner{
					padding-right: 8px;
				}
			}

			.cpq_style2_attrFilter{
				box-sizing: border-box;
				display: inline-block;
				float: right;
				width: 32px;
				height: 28px;
				line-height: 28px;
				border-radius: 4px;
				border: 1px solid @border-color;
				font-size: 12px;
				text-align: center;
				margin-left: 8px;
				cursor: pointer;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			.cpq_style2_attrFilter:hover{
				color: var(--color-primary06);
				border-color: var(--color-primary05);
			}

		}

		.cpq_style2_bomIcon{
			margin-right: 4px;
			font-size: 14px;

			&:before{
				color: #C1C5CE
			}
		}


		.dt-page {
			width: 100%!important;
			position: unset;
			z-index: 1000;
			height: 46px;
			padding: 0;
			margin-top: 6px;
			border: none;
		}

		.dt-batchoperate-wrapper{
			position: absolute;
			bottom: 0;
			left: 0;
		}

		.fix-start-b{
			//width: 100% !important;
		}

		.crm-table{

			.tr-current.tr-selected{
				background-color: #FFF5E6;
			}

			.tr-hover{
				background-color: #FFF5E6 !important;
			}


			.batch-term{
				display: none;
			}

			.dt-term-batch{
				margin-top: 8px !important;
			}

			.dt-tit{
				display: none;
			}

			td, th{
				border: none !important;
			}

			.header{
				display: none;
			}

			.fix-start-b{
				border: none !important;
			}

			.dt-term-batch{
				.item{
					.line, .item-tit{
						display: none;
					}
				}
			}

			.crm-w-table-search{
				width: 200px;
				border-radius: 4px;

				.fx-select, .s-line, .font-search{
					display: none;
				}
			}

			.dt-term-batch{
				.dt-control-btns{
					margin-left: 0;
					text-align: center;
					border: 1px solid @border-color;
					width: 28px;
					height: 28px;
					border-radius: 4px;
					box-sizing: border-box;

					.fx-icon-set{
						margin: 0;
					}
				}
			}

		}

		.cpq_style2_tableRender{
			float: right;
			color: var(--color-primary06, #ff8000);
			display: none;
			font-weight: 700;

		}

		.tr-selected{
			background-color: #fff;

			.cpq_style2_tableRender{
				display: inline-block;
			}

		}

        &.cpq_style2_container_table_single {
            .crm-table {
                .main-scroll{
                    .j-sfixed-th{
                        width: 0;	
                    }
                }
    
                .fix-start-b{
                    display: none
                }
            }
        }

	}

	.cpq_style2_container_right{

		.cpq_style2_container_right_box{
			flex: 1;
			height: 100%;
		}

		.cpq_style2_container_right_detail{
			display: none;
		}
		.cpq_style2_container_right_main{
			position: relative;
			height: calc(~"100% - 50px");
			border-top: 1px solid @table-border-color;
			box-sizing: border-box;
		}
		.cpq_style2_container_right_table{
			height: 100%;
			display: none;
		}

		.draggable-container__right-close-btn{
			z-index: 11;
			top: calc(~"40% - 10px");

		}

		.p-unicode{
			top: calc(~"40% + 4px");
		}

		//.dialog_attribute{
		//	display: none;
		//}

		.dialog_selectbom_attrConstraint{
			padding: 0;

		}

		.draggable-container__main{
			padding: 0;
			border: none;
		}

		.attrConstraint_back{
			border:none;
		}

		.dialog_selectbom_attrRange{
			padding: 16px;
			box-sizing: border-box;

		}

		.attrRange_back{
			border: none;
		}

		.rootAttrBoxForQuoter_bottomLine{
			bottom : 0;
		}

		.rootAttrBoxForQuoter{

			.attrRange_back{
				border-bottom: 1px solid #dee1e6;
			}

		}
		.rootAttrClosed{
			.attrRange_back{
				border: none;
			}

			.rootAttrBoxForQuoter_bottomLine{
				bottom : -12px;
			}
		}


		.cpq_style2_container_right_header{
			width: 100%;
			height: 50px;
			line-height: 50px;

			.cpq_style2_container_right_productInfoBtn_wrap{
				display: none;
				cursor: pointer;
				float:right;
				line-height: 50px;
   				margin-right: 16px;
				color: #0C6CFF;
				font-size: 12px;
			}

			.cpq_style2_container_right_productInfoBtn{
				vertical-align: text-bottom;
				font-size: 16px;
				&:before{
					color: #0C6CFF;
					// font-size: 16px;
				}
			}
		}

		.cpq_style2_container_right_title{
			font-size: 14px;
			font-weight: 500;
			margin-left: 16px;
			margin-right: 16px;

		}

		.cpq_style2_container_right_detail{
			color: #0C6CFF;
			cursor: pointer;

		}

		.cpq_style2_container_right_version{
			color: #0C6CFF;
			float: right;
			margin-right: 16px;
			cursor: pointer;

			.fx-icon-switch{
				&:before {
					color: #0C6CFF;
				}
			}
		}

		.cpq_style2_versionDisabled{
			cursor: not-allowed;
			color: #636363;
			.fx-icon-switch{
				&:before {
					color: #636363;
				}
			}
		}

		.ruleBtn{
			font-size: 12px;
		}

		.dialog_selectbom_attrTitle{
			font-size: 14px;
			font-weight: 500;
			line-height: 20px;
			padding-bottom: 16px;
		}

		.cpq_style2_attrRange{
			overflow-x: hidden;
			overflow-y: auto;

		}

		.draggable-container__main{
			.dialog_selectbom_attrRange{
				padding: 0;
			}
		}

		.fx-icon-switch{

		}

		.crm_style2_bomAttr_noData{
			text-align: center;
			position: absolute;
			font-size: 16px;
			color: #9a9a9a;
			top: 38%;
			box-sizing: border-box;
			width: 100%;
			padding: 0 16px;
		}

	 

		.cpq_style2_container_right_productInfo{
			display: none;
			height: 100%;
			width: 350px;
			border-left: 1px solid @table-border-color;
			box-sizing: border-box;
			overflow: auto;
			overflow-x: hidden;
			padding: 16px;
			position: relative;

			.cpq_style2_container_right_productInfo_content_image_img{
				width: 100%;
				height: 100%;
				object-fit: cover;
				cursor: pointer;
			}

			.crm_style2_bomAttr_noData_productInfo{
				display: none;
				width: 90%;
				top: 42%;
				text-align: center;
				position: absolute;
				font-size: 16px;
				color: #9a9a9a;
			}

			.cpq_style2_container_right_productInfo_attribute{
				margin-top: 8px;
			}

			.cpq_style2_container_right_productInfo_header{
				margin-bottom: 8px;
			}

			.cpq_style2_container_right_productInfo_title{
				font-weight: 500;
				font-size: 14px;
				line-height: 20px;

			}

			.cpq_style2_container_right_productInfo_tip{
				.cpq_style2_container_right_productInfo_tip_icon{
					cursor: pointer;
					&:before{
						font-size: 14px;
					}
				}
			}

			// .cpq_style2_container_right_productInfo_close{
			// 	font-size: 16px;
			// 	cursor: pointer;
			// 	float:right;
			// 	&:before{
			// 		color: #0C6CFF;
			// 	}
			// }

			.cpq_style2_container_right_productInfo_content_box{
				display: flex;
			}

			.cpq_style2_container_right_productInfo_content_image{
				width: 120px;
				height: 120px;
				margin-right: 12px;
			}

			.cpq_style2_container_right_productInfo_content_info{
				flex: 1;
			}

			.cpq_style2_container_right_productInfo_content_info_item{
				font-weight: 400;
				font-size: 13px;
				line-height: 18px;
				letter-spacing: 0px;
				margin-bottom: 8px;
			}

			.cpq_style2_container_right_productInfo_content_info_name{
				color:#91959E;
				margin-right: 8px;
			}

			.cpq_style2_container_right_productInfo_content_info_value{
				color:#181C25;

			}
		}

		.cpq_style2_container_right_productInfo_attribute_noData{
			font-size: 12px;
			color: #9a9a9a;
			margin-top: 8px;
		}
	}

	.cpq_style2_footer_extend{
		display: inline-block;
	}

	.cpq_style2_quantityBox{
		overflow: hidden;
		vertical-align: middle;
		float: left;
		margin-top: 4px;

		span{
			margin: 0;
			padding: 0!important;
			height: unset;
			line-height: unset;
			border-radius: unset;
		}

		.cpq_style2_quantityTitle, .cpq_style2_quantity, .cpq_style2_priceTitle, .cpq_style2_price,.cpq_style2_quoterTrial{
			vertical-align: middle;
		}

		.cpq_style2_quantity,.cpq_style2_quoterTrial{
			display: inline-block;
		}

		.cpq_style2_priceTitle{
			margin-left: 16px;
			margin-right: 8px;
		}

	}

	.cpq_style2_price, .cpq_style2_totalPrice{
		position: relative;
		font-size: 16px !important;
		font-weight: 500;
		color: var(--color-primary06, #ff8000);
		display: inline-block;
		min-width: 24px;
		min-height: 24px;
		text-align: center;

	}

	.cpq_style2_totalPriceBox{
		line-height: 38px;
		margin-left: 16px;
		min-width: 120px;

		span{
			vertical-align: middle;
		}

		.cpq_style2_totalPrice{
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			vertical-align: middle;
		}

	}

	.cpq_style2_loading{
		position: absolute;
		width: 100%;
		height: 100%;
		text-align: center;

		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: center;

		.crm-loading-anima{
			width: 14px;
			height: 14px;
			display: inline-block;
			border-color: #b5b5b5;
			border-style: dashed;
		}

	}

	//.cpq_style2_notShowBom{
	//	.dialog_selectbom_right, .draggable-container__right-close-btn,.resize-right, .p-unicode{
	//		display: none;
	//	}
	//
	//	.dialog_selectbom_left, .draggable-container__main{
	//		width: 100%!important;
	//
	//	}
	//
	//}

	.cpq_style2_hasPriceBook{
		.dt-term-batch{
			.category-wrap{
				float: right;
			}
		}
	}

	.cpq_style2_otherBtnBox{
		vertical-align: middle;
		display: inline-block;
		
	}

}
.bom_style2_settingClass{
	.field_setting_header, .column-item, .footer_btn, .recover-default{
		display: none !important;
	}
}
.cpq-style2-attrFilter{
	.j-fold{
		display: none;
	}
}

.crm-style2-attribute-container {
	background: #fff;
	border-radius: 4px;

	.crm-style2-attribute-group {
		margin-bottom: 16px;
	  }
	  
	  .crm-style2-group-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 8px 0;
		cursor: pointer;
		border-radius: 4px;
	  }
	  
	  .crm-style2-group-title-wrapper {
		display: flex;
		align-items: center;
		// gap: 8px;
	  }
	  
	  .crm-style2-group-title {
		
		font-weight: 400;
		font-size: 12px;
		line-height: 18px;
		letter-spacing: 0px;

	  }
	  
	  .crm-style2-toggle-icon {
		font-size: 16px;
		color: #666;
		transition: transform 0.3s ease;
	  }
	  
	  .crm-style2-group-content {
		 
	  }
	  
	  .crm-style2-attribute-item {
		// margin: 8px 0;
		display: flex;
		line-height: 26px;
		font-size: 13px;
		letter-spacing: 0px;
		margin-bottom: 8px;
		border-bottom: 1px solid #DEE1E8;
		overflow: hidden;

	  }
	  
	  .crm-style2-attribute-name {
		display: inline-block;
		color: #91959E;
		margin-right: 8px;
		min-width: 88px;

	  }
	  
	  .crm-style2-attribute-value {
		display: inline-block;
		color: #181C25;
	  }
  }
.cpq_style2_shoppingCart_btnBox {
    display: inline-block;
    vertical-align: middle;
    .confirm-shopping-cart-btn {
        padding-right: 8px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
    .shopping-cart-num-btn {
        padding-left: 12px;
        padding-right: 12px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        border-left: 1px solid #FFA64C;
    }
}
.shoppingCartList {
    display: flex;
    flex-direction: column;
    gap: 12px;
    font-family: "Source Han Sans CN";
    .item {
        display: flex;
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 16px;
        font-size: 13px;
        display: flex;
        img {
            width: 88px;
            height: 88px;
            margin-right: 12px;
        }
        .content {
            flex: 1;
            overflow: hidden;
            .name {
                display: flex;
                font-size: 14px;
                font-weight: 700;
                line-height: 20px;
                margin-bottom: 8px;
                color: var(--Text-H1, #181C25);
                h6 {
                    flex: 1;
                }
                .del {
                    float: right;
                    margin-left: 8px;
                    cursor: pointer;
                    font-size: 16px;
                    color: var(--color-special02, #737c8c);
                    &:hover {
                        color: var(--color-primary06);
                    }
                }
            }
            .attr-wrap {
                margin-bottom: 8px;
                overflow: hidden;
                .attr {
                    line-height: 18px;
                    color: var(--Text-H1, #181C25);
                    display: flex;
                    label {
                        color: var(--Text-H3, #91959E);
                        margin-right: 8px;
                    }
                    p {
                        margin: 0;
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        word-break: break-all;
                    }
                    .more-btn {
                        cursor: pointer;
                        color: var(--color-info06);
                    }
                    
                    &.showMore {
                        p {
                            overflow: visible;
                            white-space: unset;
                        }
                    }
                }
                .fold-btn {
                    cursor: pointer;
                    float: right;
                    color: var(--color-info06);
                }
            }

        }
        .op {
            display: flex;
            align-items: center;
            gap: 0px 8px;
            justify-content: end;
            .price {
                font-weight: 700;
                line-height: 18px;
                color:var(--color-primary06);
            }
        }
        
    }
}
.cpq_style2_shoppingCart_dialog {
    .el-dialog__body {
        height: 100%;
    }
    .cpq_style2_shoppingCart_total {
        display: inline-block;
        float: left;
        line-height: 32px;
        color: var(--Text-H2, #545861);
        font-family: "Source Han Sans CN";
        font-size: 14px;
        font-weight: 500;
        .money {
            color: var(--color-primary06);
            font-family: "Source Han Sans CN";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            margin-left: 8px;
        }
    }
}
.sfa-crm-quoter-trial-popover{
	width: 262px;
	padding:16px;
	.quoter-trial-wrapper{
		.title{
			font-size: 16px;
			font-weight: 400;
			line-height: 24px;
			margin-bottom: 4px;
		}
		.quoter-trial-content{
			display: flex;
  			flex-wrap: wrap; 
			.quoter-trial-item{
				flex: 1 1 50%;                    
				box-sizing: border-box;
				display: flex;
				align-items: center;
				line-height: 18px;
				margin-top:4px;
			}
		}
	}
	.quoter-trial-close{
		float:right;
		border-radius: 6px;
		margin-top: 16px;
	}
	.quoter-trial-close{
		color: var(--color-primary06);
	}
}
  
 