/**
 * @desc: bom特殊逻辑，改pickselfobject
 * @author: wangshaoh
 * @date: 2020-06-02
 */

/**
 * options:{
 *     extendBomParam:{},  // 扩展bom 选配页的方法
 *
 * }
 *
 *
 */
define(function (require, exports, module) {
	var util = CRM.util;
    var mixin = require('crm-modules/buscomponents/pickself_bom_util/pickself_bom_util');
	return function (PickSelfObject) {
		return {
            ...mixin,
            // validAccountId() {
            //     // 某些对象的特殊字段，newmdTreeTable组件在用的时候需要统一。
			// 	let fieldMapping = Object.assign({},{
			// 		form_account_id: 'account_id',
			// 		form_partner_id: 'partner_id',
			// 		form_mc_currency: 'mc_currency',
			// 	}, this.options.fieldMapping || {});
			// 	let masterData = this.options.master_data || this.options.object_data;
				// let accountId = masterData[fieldMapping.form_account_id] || this.options.accountId || masterData[fieldMapping.form_account_id];
				// if(!accountId && !this.options.noAccount) {
				// 	util.alert($t('请先选择客户'));
				// 	util.hideLoading_new();
				// 	return;
				// }
            //     return true;
            // },

			renderBomCoreObj(id, from) {
                // if (!this.validAccountId()) return;
                let me = this;
                this._extendRootData(this.checkedData[id]?.subBomData?.newRootData, this.currentRow);
				// 打开配置，传报价器参数
				const quoterParam = this.getQuoterParam();
                // 打开配置
				require.async('crm-modules/buscomponents/pickselfobject_bomcore/pickselfobject_bomcore', async (bomCore) => {
					let config = {
						product_id: id,
						currentRow: me.currentRow,
						apiname: me.apiname,
						selectBomParams: {
							extendData: me.checkedData[id]?.subBomData?.data,
							extendRootData: me.checkedData[id]?.subBomData?.newRootData || me.currentRow,
							...quoterParam
						},
						bomCoreParams: {
							subBomData: me.checkedData[id]?.subBomData,
							dataId: me.checkedData[id]?._id,
							...quoterParam,
                            ...(me.options.bomCoreParams || {})
						},
						from,
						...me.options,
						dialogEnter: me.bomCoreEnter.bind(me, id),
					};

					me.pickBomCore = await bomCore(config)

				});
			},

			bomCoreEnter(productId, checkedBomCoreList) {
				let noCoreVersion = this.options.noCoreVersion;
				if(noCoreVersion && !checkedBomCoreList.length){
					this._setCheckedRow(this.currentRow._id);
					return;
				}
				// 目前仅支持单选，返回一条数据
				checkedBomCoreList.forEach(async obj => {
					let {_id, core_version, subBomData } = obj;
					this.checkedData[productId] = obj;
					if (subBomData) {
						this.updatePricebookId(subBomData, this.currentRow); // 可能切换了价目表，更新当前产品的价目表id等信息
						this.updateRootAttr(subBomData.newRootData || subBomData.rootData, this.currentRow); // 更新产品的 属性 和 非标属性
						this.supplementAttrToData(this.currentRow);// 属性产品，补全默认选中属性值
						this.setRowNotShowAttribute(this.currentRow, subBomData.hasAttributeConstraint)
					}else{
						await this.checkHasAttrConstraint(_id, this.currentRow)
					}
					this._setCheckedRow(this.currentRow._id);// 配置完，设置选产品行勾选;
				})
			},

			// 校验产品包，是否有属性约束规则
			async checkHasAttrConstraint(coreId, currentRow){
				if(!this._hasAttrConstraint) return false;
                let r = await this.isExistConstraint({coreId});
                let notShow = r?.exist;
				this.setRowNotShowAttribute(currentRow, notShow)
			},

			// 设置行数据是否展示属性
			setRowNotShowAttribute(data, status){
            	data = Array.isArray(data) ? data : [data];
            	data.forEach(item => {
					item.notShowAttribute = status;
				})
			},

			/**
			 * @desc 选中产品复选框，弹出配置BOM
			 * @param status
			 * @param $target
			 * @param table
			 */
			async afterCheckboxClick(status, $target, table, isCheckedAll) {
				// 单选 bom
				if (!isCheckedAll) {
					let index = $target.parents('.tr').data("index");
					let data = table.getCurData().data[index];
					if (status && util.isBom(data).isPackage) {
						let id = this.getDataId(data);
						// let price = this.getPrice(data);
						this.currentRow = data;
						$target.removeClass('tb-checkbox-selected');
						this.table.reduceRemberData([this.currentRow]);
						this.renderBomCoreObj(id, 'checkbox');
					}
				}
				PickSelfObject.prototype.afterCheckboxClick.apply(this, arguments);
			},
			getDataId(data) {
                return util.isBom(data).productId;
				// return data.product_id || data._id;
			},

			renderTable(options) {
				let _this = this;
				_this.checkedData = {};
				this.options._from = 'bom';
				this.options.checkboxclick_before = this.checkboxclick_before.bind(this);
				this.options.renderListCompleteHandle_after = this.renderListCompleteHandle_after.bind(this);
				// 获取价目表配置是否开启,如果开启，展示选产品
				util.getPriceListConfig(async function () {
                    try {
                        let r = await _this.isExistConstraint();
                        _this._hasAttrConstraint = r?.exist;
                    } catch(e) {
                        console.error(e)
                    }
					PickSelfObject.prototype.renderTable.call(_this, options)
				})
			},
            // 判断是否有产品属性约束对象数据
            isExistConstraint(param = {}) {
                CRM.util.showLoading_tip();
                return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/bom/service/existConstraint',
                        data: param,
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            reject();
                            util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                        },
                        complete: function () {
                            CRM.util.hideLoading_tip();
                        }
                    }, {
                        errorAlertModel: 1
                    })
                })
            },
			// 获取属性信息
			getAttribute(data){
				let res = {};
				if(data.selectedAttr){
					_.each(data.selectedAttr,(val, key) => {
						res[key] = val.value_ids[0].id;
					})
				} else if(data.attribute){
					 _.each(data.attribute, item => {
					 	let key = item.id;
					 	let d = _.find(item.attribute_values, a => a.is_default == '1');
					 	if(d) res[key] = d.id;
					 })
				}
				return res;
			},

			// 配置完，设置行勾选;
			_setCheckedRow:function(id){
				// 选产品table
				let checked = this.table.getRemberData() || [];
				let findPb = _.find(checked, item => item._id === id);
				if(!findPb) {
					this.table.setCheckedRow('_id', [{'_id':id}]);
				};
				// 获取是否开启属性产品
				if(CRM._cache.openAttribute) this.table.showAttribute(this.currentRow);
			},

			// 可能切换了根结点的节目表，需要更新
			// updatePricebookId: function (obj, currentRow) {
			// 	let rootData = obj.newRootData;
			// 	if (currentRow.pricebook_id && rootData && currentRow.pricebook_id !== rootData.pricebook_id) {
			// 		currentRow.pricebook_id = rootData.pricebook_id;
			// 		currentRow.pricebook_id__r = rootData.pricebook_id__r;
			// 		currentRow._id = rootData._id;
			// 		currentRow.name = rootData.name;
			// 	}
			// },

			/**
			 * @desc 如果该产品是包，则查询默认勾选的子产品，或者添加上修改后的子产品勾选项 ；
			 * @param data 当前选产品页选中的产品包
			 * @param cb
			 */
			parseValue: function (data, cb) {
				let p_dataList = data;
				let isArr = true;
				let me = this;
				let bomList = [];
				let ids = [];
				let noBomCoreId = [];
				if (!_.isArray(data)) {
					isArr = false;
					p_dataList = [data]
				}
				_.each(p_dataList, function (proData, index) {
					let id = proData.product_id || proData._id;
					let isPackage = util.isBom(proData).isPackage;
					if (!proData.rowId) proData.rowId = util.uniqueCode();
					proData.newestPrice = proData.pricebook_sellingprice || proData.price;

					// 将子产品添加到对应的产品包 checkedData = {id: bomCheckedData}
					if (isPackage) {
                        var subData = proData.subBomData;
                        if (me.checkedData[id]) {
                            let {_id, name, core_version, category, subBomData, sale_strategy} = me.checkedData[id] || {};
                            // 如果没有bom版本id
                            subData = subBomData;
                            proData.core_id = _id;
                            proData.core_id__r = name;
                            proData.node_bom_core_type = category;
                            proData.node_bom_core_version = core_version;
							proData.sale_strategy = sale_strategy;
                        }
                        if (!proData.core_id) {
                            noBomCoreId.push(`【${me.getProductName(proData)}】`);
                        }
						if (subData) {
							// 已经手动选择了价目表明细，存到数据中；
							if (subData.newRootData) {
								proData._pbInfo = subData.newRootData;
							}
							var num = 0;
							if (subData.hasOwnProperty('totalMoney')) proData.newestPrice = subData.totalMoney;
							if (subData.hasOwnProperty('totalSingleSetPrice')) proData.totalSingleSetPrice = subData.totalSingleSetPrice;
							_.each(subData.data, function (item) {
								item.pid = proData.rowId;
								item.isChecked = false;
								num++;
							});
							// 给产品增加子产品
							proData.children = subData.data;
							proData.bom_id = subData.rootDataBomId;
							proData._updateRootPrice = true;
						} else {
							bomList.push(proData);
							ids.push(id);
						}
					}
				});
				if (noBomCoreId.length && !this.options.noCoreVersion) {
					util.alert($t(`${noBomCoreId.join(',')}${$t('未选择BOM版本')}`));
					return;
				}
				if (bomList.length && !this.options.noFetchBom) {
					// 批量查选中的bom产品默认选中项
					me.fetchAllBomList(ids, bomList, function () {
						let r = me.checkNsAttr(bomList);
						if(!r) return;
                        // 不进配置页，需要校验子件属性是否都有值
						let r2 = util.validAttribute(bomList);
						if(!r2) {
							CRM.util.alert($t('子件')+ $t("每个属性至少选择一个属性值"));
							return;
						}
						let r3 = me.checkBOMVersion(bomList);
						if(!r3) return;
						cb && cb(isArr ? p_dataList : p_dataList[0])
					});
					return
				}
				cb && cb(isArr ? p_dataList : p_dataList[0])
			},


			//属性产品，补全默认选中属性值
			// supplementAttrToData:function(data, key = 'attribute_values'){
			// 	let attrObj={},
			// 		attrG = {},
			// 		attrTxt="";
			// 	if(data.selectedAttr || !data.attribute) return;
			// 	data.attribute.forEach(a => {
			// 		let defAttr = a[key].find(i => i.is_default == '1');
			// 		attrObj[a.id] = {
			// 				name: a.name,
			// 				value_ids: [{
			// 					id: defAttr.id,
			// 					name: defAttr.name
			// 				}]
			// 			};
			// 		attrTxt+=a.name+":"+defAttr.name+";";
			// 		attrG[a.id] = defAttr.id;
			// 	});
			// 	data.selectedAttr=attrObj;
			// 	data.attribute_json = attrG;
			// 	data.attribute = attrTxt.slice(0, attrTxt.length - 1);
			// 	return data;
			// },

			getPriceField: function (value) {
				if (value.hasOwnProperty('pricebook_sellingprice')) {
					return value.pricebook_sellingprice
				}
				return value.price
			},

			/**
			 * @desc 补子产品
			 * @param id
			 * @param value
			 */
			addChildren: function (id, value) {
				var me = this;
				var subData = me.checkedData[id]?.subBomData;
				var subList = [];
				if (subData) {
					value.newestPrice = subData.totalMoney;
					value.totalSingleSetPrice = subData.totalSingleSetPrice;
					_.each(subData.data, function (item) {
						_.each(item, function (list, index) {
							list.rowId = util.uniqueCode();
							list.pid = id;
							list.isChecked = false;
							subList.push(list)
						})
					});
					value.children = subList;
				}
			},
			// 选产品，确认
			async confirm (e) {
				var me = this;
				var value = me.table.getValue(true);

				if (!value) {
					util.alert($t("请选择一条数据") + '!');
					return;
				}

				//属性值选择有错误
				if (value == "attributeError") {
					return;
				}
				// 校验产品约束关系
				let r = await me.validSelectValueBySelf(value, me.table);
				if(!r) return;

				// pwc 插件自定义校验
				if(this.options.validResult){
					let r1 = await me.options.validResult({data: value});
					if(r1 && !r1.result) return;
				}

				function _fn(res) {
					// 触发父组件监听的产品组件选中事件，返回选中项
					me.trigger('select', res, res);
					if (me.submit) {
						me.submit(res, me, e);
						return;
					}
					me.destroy();
				}

				// 开启cpq || 开启固定搭配，且不是退货单对象，需要补齐产品包的子产品
				if (
					(CRM._cache.fixedCollocationOpenStatus || CRM._cache.cpqStatus) &&
					me.options.source_api_name !== "ReturnedGoodsInvoiceProductObj"
				) {
					me.parseValue(value, function (res) {
						_fn(res)
					});
				} else {
					_fn(value)
				}
			},

			changeParam: function (param) {
				param.otherBtns = this.addOtherBtns.bind(this);
			},
			// 添加"配置"按钮;
			addOtherBtns: function (data, index) {
				if (util.isBom(data).isPackage) {
					return ` <a data-index="${index}" data-id="" tb-action-type="showBomCoreConfig" href="javascript:;" class="j-config">${$t('配置')}</a>`
				}
				return '';
			},

			// 监听配置按钮点击事件；
			addTableEventsListen: function (table) {
				let me = this;
				table.on('showBomCoreConfig', function ($target) {
					var index = $target.data().index;
					let data = table.getCurData()[index];
					let id = me.getDataId(data);
					// let price = me.getPrice(data);
					// 开了报价器，格式化属性
					const quoterParam = me.getQuoterParam();
					this.curQuoterFilter = CRM.util.getQuoterParam(quoterParam.extraData);
					CRM.util.setDefaultAttr(data, this.curQuoterFilter);
					me.currentRow = me.extendRowByRemberData(data);
					me.renderBomCoreObj(id, 'configBtn');
				});
			},

			// 根据记忆数据替换行数据
			extendRowByRemberData(row){
				let rememberData = this.table.getRemberData();
				if(rememberData?.length){
					let f = rememberData.find(item => item._id && item._id === row._id);
					if(f) return f;
				}
				return row;
			},

			// 处理属性展示
			checkboxclick_before(data){
				if(!data) return;
				data = Array.isArray(data) ? data : [data];
				// 如果产品包有属性约束，不展示属性;
				if(CRM._cache.cpqStatus && this._hasAttrConstraint) {
					data.forEach(item => {
						if(CRM.util.isBom(item).isPackage){
							this.setRowNotShowAttribute(item, true)
						}
					})
				}
			},
            /**
             * 列表渲染完成的回调
             * @param {array} data 选中的数据
             * @returns 
             */
			renderListCompleteHandle_after(data){
				if(!data) return;
				data = Array.isArray(data) ? data : [data];
				// 如果产品包有属性约束，不展示属性;
				if(CRM._cache.cpqStatus && this._hasAttrConstraint) {
					data.forEach(item => {
						if(CRM.util.isBom(item).isPackage && !item.hasOwnProperty('notShowAttribute')){
							this.setRowNotShowAttribute(item, true)
						}
					})
				}
                // 如果有bom，禁用全选复选框
                let dataList = this.table.getCurData().data;
                let disabledCheckAll = !!dataList.find(item => CRM.util.isBom(item).isPackage);
                if (disabledCheckAll) {
                    this.$('.header .j-all-checkbox').addClass('checkbox-item-disabled');
                } else {
                    this.$('.header .j-all-checkbox').removeClass('checkbox-item-disabled');
                }

			},


			destroy: function () {
				PickSelfObject.prototype.destroy.apply(this);
				// this.selectBom && this.selectBom.destroy && this.selectBom.destroy();
				this.pickBomCore && this.pickBomCore.destroy();
			}

		}
	}
})
