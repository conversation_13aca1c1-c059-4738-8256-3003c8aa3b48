define(function (require, exports, module) {
	function getFunction(name, args) {
		return function (args) {
			return new Promise(resolve => {
				require.async(`./${name}/index`, component => {
					if (component instanceof Function) {
						resolve(component());
						return;
					}
					if (component instanceof Promise) {
						component.then(resolve);
						return;
					}
				});
			});
		};
	}

	const componentsList = ['accountobjbquery', 'bquery', 'remind', 'recorder', 'treetable'];

	// ae1a4907fca4ea7bc10dbe882c2bff698aa5f334 为此commit添加注释
	// 之前的写法会直接将所有的组件加载到内存中，这样会导致调用顺序出错多语模块未取到 1383965 
	//【【异步任务监控-设计器】筛选操作符没多语】
	// https://www.tapd.cn/********/bugtrace/bugs/view/11********001383965
	const ret = new Proxy({}, {
		get(target, name) {
			if (name in target) {
				return target[name]();
			}
		}
	})

	componentsList.forEach(components => {
		ret[components] = getFunction(components);
	});

	module.exports = ret;
});
