/**
 * @desc 部门
 * 
 */
define(function(require, exports, module) {

	var Common = require('./common'),
		util = require('crm-modules/common/util'),
		PersonInput = require('crm-widget/selectorV2/selectorV2');

	module.exports = Common.extend({

		render: function() {
			var person = new PersonInput(this.getOptions());
			this._widgets['person'] = person;
		},

		getOptions: function() {
			var $box = $('.input-box', this.$el);

			return {
				$wrap: $box,
				parentNode: $box,
				group: {
					company: true,
          chooseType: this.options.fname === 'data_own_organization' ? 'organization' : 'department'
				},
				stopDepartment:this.options.stopDepartment,
				single: !this.options.isMultiple,
				label: this.options.fname === 'data_own_organization' ? $t('选择组织') : $t("crm.选择部门"),
				foldInput: true,
				groupIncludeChildrenStatus: this.options.groupIncludeChildrenStatus,
        groupIncludeChildrenStatusCascade: this.options.groupIncludeChildrenStatusCascade
			};
		},

		setValue: function(val) {
			if(this.options.needDepartmentChildren){
			// 检查是否所有值都有_y后缀
			const allHasYSuffix = val && val.length > 0 && val.every(v => typeof v === 'string' && v.endsWith('_y'));
			// 如果都有_y后缀，设置groupIncludeChildrenChecked为2
			if (allHasYSuffix) {
				// 使用$nextTick确保组件已挂载
				this._widgets['person']._selector.$nextTick(() => {
					const selectorComponent = this._widgets['person']._selector.$children[0];
					if (selectorComponent) {
						selectorComponent.groupIncludeChildrenChecked = true;
					}
				});
				
				// 创建新数组去掉_y后缀，不修改原始数据
				val = val.map(v => v.slice(0, -2));
				}
			}
			var t = this.options.stopDepartment;
			if(t){
				var stop = [];
				var group = [];
				val.forEach(function (id) {
					if(!Fx.contacts.getCircleById(id)){
							Fx.contacts.getCircleByIdSync(id,function(dep){
									if(Fx.contacts.isStop(dep)){
										stop.push(id);
									}
							});
					}else{
						group.push(id)
					}
				})
				this._widgets['person'].setValue({
					group: val || [],
					stopDepartment: stop || []
				});
			}else{
				this._widgets['person'].setValue({
					group: val || []
				});
			}
		},

		getValue: function() {
			var data = this._widgets['person'].getValue();
			const status = this._widgets['person'].getIncludeGroupChildrenStatus()
			var result = status ? data.group.map(v => {
				return v+(this.options.valSubfix?this.options.valSubfix:'_y')
			}) : data.group

			if(data.stopDepartment){
				result = result.concat(data.stopDepartment);
			}
			return result
		},

		setDisable: function(status) {
			var $mask;
			if(status) {
				$mask = $('<div class="mask"></div>');
				this.$el.append($mask);
			}else{
				$mask = $('.mask', this.$el);
				$mask.remove();
			}
			this.$el.toggleClass('comp-mask', !!status);
			this.clear();
		},

		clear: function() {
			this._widgets['person'].clearAll && this._widgets['person'].clearAll();
		}

	});
})
