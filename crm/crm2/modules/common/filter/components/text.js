/**
 * @description  文本
 */
define(function(require, exports, module) {
	var util = require('crm-modules/common/util');
    var Common = require('./common'),
        MInput = require('crm-widget/table/condition/input');
    

	module.exports = Common.extend({

		render: function() {
            var ph = this.options.useMInput;
			this.$el.off('click', 'span');
			this.cacheValueIds = [];
			this.valueIdsInitial = false;

            if (this.options.apiname == 'AccountObj' && this.options.fname == 'name') { // 客户名称单独处理
                $('.input-box', this.$el).addClass('accountobj-name');
                $('.input-box', this.$el).html('<input type="text" class="b-g-ipt"/><span>+</span>');
                this.$el.on('click', 'span', $.proxy(this._pickCustomer, this));
            } else {
                $('.input-box', this.$el).removeClass('accountobj-name');
                $('.input-box', this.$el).html(ph ? this._initInput() : '<input type="text" class="b-g-ipt"/>');
            }
			if(this.options.inputDisabled) {
				$('.input-box input', this.$el).attr('readonly', true);
			}
			// $('input', this.$el).attr('disabled', !this._type).toggleClass('input-disabled', !this._type);
			CRM.util.fixInputEvent($('input', this.$el), _.bind(this.changeInput, this));
        },
        
        // 属于、不属于对应的分号；拆分输入框
        _initInput: function() {
            var $ipt = $('<input type="text" class="b-g-ipt" style="padding-right:32px"/>')
                    .attr('placeholder', $t('多个值用;隔开'))
                    .after('<span class="text-batch-ico"></span>');
            var $first = $ipt.first();

            $ipt.last().on('click', function() {
                if ($first.attr('readonly')) {return false;}
                var mInput = new MInput({
                    val: $first.val()
                });
                mInput.on('enter', function(val) {
                    $first.val(val);
                    $first.trigger('input');
                })
                mInput.show();
            });
            return $ipt;
        },

        // 选择客户
        _pickCustomer: function() {
			var me = this;
			const supportMultiple = [13,14].includes(me.options.compareVal); // 属于、不属于支持多选
			require.async('crm-modules/components/pickselfobject/pickselfobject', function(PickSelf) {
				
				const opts = me.options?.initOptions?.extra?.lookupPickDataOptions || {};
                me._pickobject = new PickSelf();
                me._pickobject.render({
					dataId:me.cacheValueIds,
					apiname: 'AccountObj',
					filters: me.getCustomerFilters(),
					...opts,
					isMultiple:opts.isMultiple && supportMultiple
                });
                me._pickobject.on('select', function(data) {
					const value = Array.isArray(data) ? data.map(item=>item.name).join(';') : data.name;
					me.cacheValueIds = Array.isArray(data) ? data : [data];
					me.setValue(value);
                    me.trigger('change', {
                        value: value
                    });
                });
            })
		},

		getCustomerFilters: function () {
			let filters = [],
				model = this.options.model,
				apiname = 'data_own_organization';
			if (!model || model.attributes.apiname !== 'PromotionObj') return filters;
			let val = model.getData(apiname);
			if (val) {
				filters.push({
					field_name: apiname,
					field_values: val,
					operator: "IN",
					value_type: 0
				})
			}
			return filters
		},

		changeInput: function(e) {
			var $target = $(e.target);
			this.value = $target.val();
			this.validate();
			$('.input-box', this.$el).attr('title', this.value || '');
			this.trigger('change', {
				value: this.value
			});
		},

		validate: function() {
			var $input = $('input', this.$el);
			var me = this;
			switch(this.options.ftype) {
				case 'number':
				case 'currency':
				case 'percentile':
				case 'count':
					if(isNaN(+this.value) && !/^\$(.*?)\$$/g.test(this.value)){
						me.showErrmsg($input, $t("只允许填写数字") + (this.options.isRelate ? $t("或同类型字段") : '') + '！');
						return;
					}
					break;
			}
			me.hideErrmsg($('input', this.$el));
		},

		showErrmsg: function ($el, errmsg, flag) {
			var $parent = $el.parent(),
				$errInfoEl, left;
			if ($parent.find('.fm-err').length > 0) {
				$errInfoEl = $parent.find('.fm-err');
			} else {
				$errInfoEl = $('<span class="fm-err crm-ico-error"></span>');
			}
			left = $el.offset().left - $parent.offset().left;
			$errInfoEl.css({
				'padding-left': left,
				'color': '#f5715f',
				'display': 'block'
			});
			$errInfoEl.html(errmsg);
			!flag && $parent.addClass('crm-error-wrap').append($errInfoEl);
		},

		hideErrmsg: function ($el) {
			var $parent = $el.parent().removeClass('crm-error-wrap'),
				$errInfoEl = $parent.find('.fm-err');
			$errInfoEl.length && $errInfoEl.remove();
		},

		setValue: function(value) {
			this.checkInitData(value);
			var $input = $('input', this.$el);
			if(!this.options.useMInput && /^\$(.*?)\$$/g.test(value)) {
				this.setInputValue($input, value);
			}else{
				$input.val(value);
			}
			$('.input-box', this.$el).attr('title', value || '');
			this.value = value;
		},

		setDisable: function(status) {
			if(status) {
				this.showMask();
			}else{
				this.hideMask();
			}
			this.clear();
		},

		getValue: function() {
			return this.value.trim();			
		},

		clear: function() {
			this.setValue('');
		},

		destroy: function(){
			this._pickobject && this._pickobject.destroy();
            this._pickobject = null;
			CRM.util.offInput($('input', this.$el), this);
			Common.prototype.destroy.apply(this);
		},

		checkInitData:async function(value){
			const isAccountName = this.options.apiname == 'AccountObj' && this.options.fname == 'name'
			if(this.valueIdsInitial || !isAccountName || !value) return;
			await this.fetchAccountData(value);
			this.valueIdsInitial = true;
			
		},
		fetchAccountData:async function(dataStr){
			const data = dataStr.split(';');
			const filters =[{
				field_name:'name',
				field_values:data,
				operator:'IN',
			}]
			const searchParam	= {
				limit:data.length,
				filters,
				offset:0,
			}
			const res = await CRM.util.fetchObjRelatedList(
				"AccountObj",
				{
					associated_object_describe_api_name: "AccountObj",
					include_describe: false,
					search_query_info:JSON.stringify(searchParam),
				},
				true
			)
			this.cacheValueIds = res?.dataList ||[]
		},

	});

});
