/**
 * @desc 集成组件，跟随type切换组件类型
 */
define(function(require, exports, module) {
	var	components = require('./components/components');

	function Wrapper(opts) {
		this.options = opts;
        this.apiname = opts.apiname || '';
		this._widgets = {};
		this.curType = '';
		this.curComp = null;
		this.crmAreaV3 = CRM.util.getUserAttribute("crmAreaV3");
		this.bindFunc();
	}

	Wrapper.prototype = _.extend({

		constructor: Wrapper,

		renderComp: function (obj, compareVal) {

			var type = obj ? (['employee_many', 'department_many'].indexOf(obj.type) >= 0 ? obj.type.replace('_many', '') : obj.type) : '';

			//单选字段时，属于、不属于比较符设置为多选，需求文档：http://wiki.firstshare.cn/pages/viewpage.action?pageId=151296323
			if (obj && type === 'selectone' && (compareVal === 13 || compareVal === 14)) {
				obj = JSON.parse(JSON.stringify(obj));//不影响原始数据
				type = 'selectmany';
			}
			// 筛选判断是否是灰度使用新的国家省市区组件
			const dMapping = ["country", "province", "city", "district"];
			const dMappingIndex = dMapping.findIndex(
                (item) => obj && obj.ftype && item === obj.ftype
            );

			const isArea = obj && obj.ftype && dMapping.includes(obj.ftype);
			if (this.crmAreaV3 && isArea && dMappingIndex >= 0) {
                type = "area";
                obj = JSON.parse(JSON.stringify(obj)); //不影响原始数据
                obj.field = {
					fieldAreaLabels: [$t("国家"), $t("省"), $t("市"), $t("区")].slice(0, dMappingIndex + 1),
					type: obj.ftype,
				};

				// 属于不属于改为多选
				if (compareVal === 13 || compareVal === 14) {
					obj.isMultiple = true;
				}

				// 新国家省市区组件compareVal为9,10时设置组件禁用
				if (compareVal === 9 || compareVal === 10) {
					obj.disabled = true;
				}
            }
			var me = this;
			if (me.curComp) me.curComp.off();
			if(!(obj&&type)) {
				me.curComp.setDisable(true);
				return;
			}
			
			me.data = obj;
			// 缓存不会渲染 UI 组件, 导致 UI 组件的检验规则还是上一个 UI组件的校验, 注释掉
			// if(!obj.isReInit && me.curType == type) {
			// 	me.curComp.setDisable(false);
			// 	me.resetOptions(obj.options);
			// 	return;
			// }
			
			var getComponents = me.options.helper && me.options.helper.getComponents;
			var Comp = (getComponents && getComponents(type, obj, components[type])) || components[type],
				comp;

			var from = me.options.extra && me.options.extra.from;
			if(from == 'datashare') {
				if(type=='employee' && obj.fname=='out_owner'){
					Comp = components['outemployee'];
				}
			}
				
			// me.curComp && me.curComp.destroy();
			comp = new Comp({
				wrapper: this.options.$el,
				$parent: this.options.$parent,
				width: this.options.width,
				model: this.options.model,
				needDepartmentChildren: obj.needDepartmentChildren,
				initOptions: this.options,
				options: obj.options,
        		props: type === 'cascader' ? obj.props : null,
				extra: obj.extra||this.options.extra,
        		useMInput: obj.useMInput,
				isRelate: type.indexOf('-relate') > -1,
				inputDisabled: type == '-relate',
				isMultiple: obj.isMultiple,
				ftype: obj.ftype,
				apiname: this.apiname,
				fname: obj.fname || '',
				level: this.options.level || 0,
				relatedName: obj.relatedName || '',
				targetApiName: obj.targetApiName || '',
				dimension_type: obj.dimension_type || '',
				stopDepartment: obj.stopDepartment,
				groupIncludeChildrenStatus: obj.groupIncludeChildrenStatus,
				groupIncludeChildrenStatusCascade: obj.groupIncludeChildrenStatusCascade,
				valSubfix: obj.valSubfix,
				compareVal: compareVal,
				fieldDesc: obj.field || {},
				disabled: obj.disabled || false,
			});
			comp.on('change', (val) => {
				me.trigger('change', val, me);
			});
			me.curType = type.replace('—relate', '');
			me.curComp = comp;
		},

		bindFunc: function() {
			var me = this;
			_.each(['getValue', 'setValue', 'setDisable', 'clear', 'resetOptions'], function (item) {
				me[item] = function() {
					return me.curComp[item].apply(me.curComp, arguments);
				}
			});
		},

		destroy() {
			this.curComp.off();
			this.curComp.destroy.apply(this.curComp, arguments);
			this.options = this.curComp = null;
		},

	}, Backbone.Events)

	module.exports = Wrapper;
})
