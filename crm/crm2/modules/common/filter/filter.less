.crm-w-cascade{
	position: relative;
	.filter-title{
        color: #919eab;
		display: inline-flex;
        align-items: center;
        line-height: 32px;

        .el-icon-remove{
        font-size: 16px;
        color: #f5715f;
        margin-right: 2px;
        cursor: pointer;
        }
    }
	.filter-list{
		display: inline-block;
		margin-left: 10px;
		vertical-align: middle;
	}
	.filter-item,
	.filter-item-last{
		position: relative;
		//height: 32px;
		padding-left: 35px;
		margin: 13px 0;
	}
	.none-borderleft{
		.filter-item-border{
			border-left: none;
		}
	}
	.filter-item-border{
		position: absolute;
		top: 15px;
		left: 0;
		display: inline-block;
		width: 19px;
		height: 100%;
		padding-bottom: 13px;
		border-left: 1px solid #ddd;
		border-top: 1px solid #ddd;
		border-radius: 3px;
	}
	.filter-item-last{
		.filter-item-border{
			border-left: none;
		}
	}
	.el-date-editor--datetimerange.el-input, .el-date-editor--datetimerange.el-input__inner{
		width: auto;
	}
	.filter-item-box{
		display: inline-block;
		width: 100%;
		.btn-add{
			position: relative;
			display: inline-block;
			width: 18px;
			height: 18px;
			vertical-align: -14px;
			border: 1px solid #ddd;
			#css3 .radius(18px);
			&:before,
			&:after{
				position: absolute;
				content: '';
				display: inline-block;
				background-color: #ccc;
			}
			&:before{
				top: 4px;
				left: 8px;
				width: 2px;
				height: 10px;
			}
			&:after{
				top: 8px;
				left: 4px;
				width: 10px;
				height: 2px;
			}
			&:hover{
				background-color: #3487e2;
				border-color: #3487e2;
				&:before,
				&:after{
					background-color: var(--color-neutrals01);
				}
			}
		}
		.comp{
			position: relative;
			display: inline-block;
			//height: 34px;
			margin-right: 8px;
            margin-bottom: 8px;
			vertical-align: top;
			box-sizing: border-box;
			&:last-child{
				margin-right: 28px;
			}
		}
		.input-disabled{
			background-color: #f2f2f2;
			border-color: #ddd;
			cursor: not-allowed;
		}
		input.b-g-ipt{
			width: 100%!important;
			box-sizing: border-box;
			height: 32px!important;
		}
		input, .el-input{
			width: 100%!important;
			box-sizing: border-box;
			// height: 32px;
		}
		
		.crm-comp-area{
			line-height: 30px;
			input{
				height: 30px;
				display: block;
			}
		}
		.el-date-editor {
			input {
				height: 30px;
			}
		}
		.input-box,
		.relate-box{
			box-sizing: border-box;
			//height: 34px;
			vertical-align: top;
		}
		.input-box{
			display: inline-block;
            position: relative;
			// .el-input{
			// 	height: 32px;
			// }
            &.accountobj-name {
                input {
                    padding-right: 40px;
                }
                span {
                    position: absolute;
                    top: 1px; right: 1px;
                    width: 30px;
                    height: 30px;
                    border-left: 1px solid #ddd;
                    font-size: 22px;
                    text-align: center;
                    line-height: 30px;
                    cursor: pointer;
                    color: var(--color-special02);
                }
			}
			.datepicker2-date-box {
				line-height: 30px;
            }
            .text-batch-ico {
                display: block;
                width: 20px;
                height: 20px;
                .img-retina("@{imgUrl}/table/new.png", "@{imgUrl}/table/new_2x.png");
                background-position: -20px 0;
                position: absolute;
                right: 10px; top: 6px;
                cursor: pointer;
            }
		}
		.relate-box{
			display: none;
		}
		.comp-relate{
			display: inline-block;
			box-sizing: border-box;
			// height: 32px;
			vertical-align: text-bottom;
			input{
				border-bottom-right-radius: unset;
				border-top-right-radius: unset;
			}
			.relate-add{
				display: inline-block;
				position: relative;
				width: 30px;
				height: 32px;
			    vertical-align: -2px;
			    border: 1px solid #ddd;
			    border-left: none;
			    cursor: pointer;
			    background-color: var(--color-neutrals01);
			    &:before,
				&:after{
					position: absolute;
					content: '';
					display: inline-block;
					background-color: #ddd;
				}
				&:before{
					top: 8px;
				    left: 14px;
				    width: 2px;
				    height: 14px;
				}
				&:after{
					top: 14px;
				    left: 8px;
				    width: 14px;
				    height: 2px;
				}
				&:hover{
					&:before,
					&:after{
						background-color: #3487e2;
					}
				}
				.title{
					height: 35px;
    				border-bottom: 1px solid #eee;
				}
				span{
					border-bottom: 2px solid #fc923f;
					color: #333;
					padding: 0 10px 17px;
				}
			}
			.relate-panel{
				display: none;
			    position: absolute;
			    top: 43px;
			    left: -220px;
			    width: 270px;
			    padding: 15px;
			    border-radius: 3px;
			    background-color: var(--color-neutrals01);
			    box-shadow: 0 0 10px 0 rgba(0,0,0,.15);
			    z-index: 999;
			    &:before{
			    	content: '';
			    	position: absolute;
			    	top: -5px;
			    	left: 228px;
			    	display: inline-block;
			    	width: 14px;
			    	height: 14px;
			    	background-color: var(--color-neutrals01);
			    	-webkit-box-shadow: -2px -2px 5px 0 rgba(0,0,0,.03);
				    box-shadow: -2px -2px 5px 0 rgba(0,0,0,.03);
			    	#css3 .transform(rotate(45deg));
			    }
			}
			.relate-list{
				padding-top: 10px;
				max-height: 160px;
			}
			.relate-item{
				padding: 0 15px;
		    	height: 32px;
				line-height: 32px;
				&:hover{
					background-color: #f2f2f2;
				}
		    }
			.relate-box{
				display: inline-block;
			}
		}
		.mask{
			display: none;
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 998;
		}
		.no-data{
			padding: 30px 0;
			text-align: center;
			color: #999;
		}
		.comp-mask{
			.mask{
				display: block;
				background-color: transparent !important;
			}
			*{
				background-color: rgba(238,238,238,.5) !important;
				color: #999;
			}
			.u-date,
			.u-time{
				display: none;
			}
		}
	}
	.show-del{
		.del-icon,
    .del-l-item{
			display: block;
		}
	}
	.del-icon{
		display: none;
    position: absolute;
    top: 6px;bottom: 0;right: 0;
    width: 20px;
    height: 20px;
    .img-retina("@{imgUrl}/del.png", "@{imgUrl}/del_2x.png", 80px, 40px);
    background-repeat: no-repeat;
    cursor: pointer;
    &:hover {
        background-position: -20px 0;
    }
	}
  // .del-l-item{
	// 	display: none;
  //   height: 32px;
  //   line-height: 32px;
  //   cursor: pointer;
  //   font-size: 16px;
  //   color: var(--color-special02);
  //   position: absolute;
  //   top: 0;right: 0;
  //   &:hover{
  //     color:#f5715f;
  //   }
  // }
	.el-cascader{
		// margin-top:-4px;
	}

	.shortcuts-value{
		background-color: var(--color-neutrals01);
		line-height: 30px;
		cursor: pointer;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 20px;
		right: 24px;
	}
}

// 级联组件样式自定义，适配文字过长的场景，
// 此自定义 class 还用在 buscomponents/action_field/components/reactive_form
// 使用场景 1. 价格政策筛选器选聚合规则 2. 价格政策每满选聚合规则 3. 返利等
.el-popper.filter-cascader-popper {
	.el-cascader-panel {
		max-width: 590px;

		.el-cascader-node {
			height: auto;
			line-height: 20px;
			padding: 7px 10px 7px 20px;

			.el-cascader-node__label {
				white-space: normal;
				word-break: break-word;
			}
		}

		.el-cascader-extra{
			padding: 0 20px;
			text-align: right;
			line-height: 24px;
			font-size: 13px;
		}

		.el-cascader-menu {
			max-width: 300px;
		}
	}

	.el-cascader__suggestion-panel {
		max-width: 590px;

		.el-cascader__suggestion-item {
			height: auto;
			line-height: 34px;
			white-space: normal;
		}
	}

}

.crm-c-filter.flex-mode {
    display: flex;
    flex-wrap: wrap;
    .filter-list {
        flex: 1;
        .filter-item-box {
            display: flex;
            gap: 8px;
            .comp {
                flex: 1;
                // min-width: 125px;
                margin-right: unset;
                &:last-child{
                    margin-right: 28px;
                }
                .input-box{
                    inset: unset!important;
                    width: 100%;
                }
                &.comp-relate {
                    .input-box {
                        width: calc(100% - 32px);
                    }
                }
            }
        }
    }
}
