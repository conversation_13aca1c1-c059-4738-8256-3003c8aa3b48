define(function (require, exports, module) {

	var helper = require('crm-modules/common/filtergroup/helper'),
		util = require('crm-modules/common/util'),
		Filter = require('crm-modules/common/filter/filter');

	let FieldFilter = Backbone.View.extend({
		options: {
			$wrapper: '',
			title: $t("默认"),
			level: 3,
			max: 0, //最大限制
			maxErrTip: $t("达到最大量"), //超过最大量的文字提示
			width: 600, //宽度
			apiname: '', //哪个对象的
			fields: null,
			origin: null,
			isInitFilter: true,
			isSupportCascadingCheck: true, //支持级联单选校验
			datetimeOprateMore:false,//时间字段支持更多比较符号
			lastSelectCanEmpty: false,
			filterType: [
				'object_reference',
				// 'formula',
				'group',
				'image',
				'file_attachment',
				'master_detail',
				'auto_number',
				'signature',
				'quote',
				'embedded_object_list',
				'multi_level_select_one',
				'tree_path',
				'employee_many',
				'department_many',
				'html_rich_text',
				'object_reference_many',
				'big_file_attachment',
			], //过滤的字段类型
			filterApiname: [], //过滤的字段apiname
			isRelate: false,
			disabled: false, // 是否禁用
			defaultValue: null, //默认值
			parseCompare: function (compare, field) {  // 处理比较符的函数
				return compare;
			},
			parseFields: function (fields) {                  		// 处理字段
				return fields
			},
			formatFields: function (fields) {
				return fields;
			},
			helper: helper
		},
		initialize() {
			this.init()
		},
		init() {
			let me = this;
			let opts = me.options;

			this.cpcd = null; // 国家省市区选项
			this.todoList = []; //需要等待执行的操作

			util.getCountryAreaOptions().then(function (res) {
				me.cpcd = res;
				setTimeout(function () {
					if (opts.isInitFilter) {
						me.render();
					}
				}, 0);
			})
		},

		render: function () {
			var me = this;
			if (me.options.fields) {
				me.fields = me.options.fields;
				me._setOptions();
				me.initFilter();
			} else {
				me.fetchFields(function () {
					me._setOptions();
					me.initFilter();
				});
			}
		},

		fetchFields: function (cb) {
			var me = this;
			var api_name = me.options.apiname;
			if (!api_name) return;
			util.getFieldsByDescribe(api_name, true).then((res) => {
				var fields = CRM.util.deepClone(CRM.get('describeFields.' + api_name));
				if (api_name == 'OpportunityObj') { // 测试没时间测试  只做商机
					var o = {};
					_.each(fields, function (v, k) {
						if (v.is_index || v.define_type == 'custom') {
							o[k] = v;
						}
					});
					me.fields = o;
				} else {
					me.fields = me.options.parseFields(fields);
				}
				// 灰度了新版国家省市区数据做处理
				me.fields = CRM.util.parseAreaFields(me.fields);
				me.fields = me.options.formatFields(me.fields);
				me.fieldsAll = fields.fields; // 记录一次全局段
				cb && cb();
			})
		},

		//获取参数
		_setOptions: function () {
			var me = this,
				opts = me.options;
			me.allowFields = me._filterFields(me.fields);
			opts.options = me._getFieldOptions(me.allowFields);
		},

		//过滤无法筛选的字段
		_filterFields: function (list) {
			// 默认过滤的字段类型
			var defaultFilterFieldType = [
				'rich_text', // 协同富文本
			];
			var fields = [],
				opts = this.options,
				mustFilter = this.options.helper.getFieldMustFilter(),
				groupField = this._getGroupFields();
			_.each(list, function (field) {
				// 默认过滤的字段类型
				if (_.contains(defaultFilterFieldType, field.type)) {
					return;
				}
				//过滤组字段
				if (groupField[field.api_name]) {
					return;
				}
				//过滤类型
				if (_.contains(opts.filterType, field.type)) {
					return;
				}
				//过滤apiname
				if (_.contains(opts.filterApiname, field.api_name)) {
					return;
				}
				if (_.contains(mustFilter, field.api_name)) {
					return;
				}
				//过滤禁用字段
				if (field.is_active === false) {
					return;
				}
				// 屏蔽 1、计算结果为时间类型的计算字段（组件不支持计算字段选择时间）；2、不落库的计算字段（无法筛选）
				if ((field.type == 'formula' && _.contains(['date_time', 'date', 'time'], field.return_type)) || (field.type == 'formula' && !field.is_index)) {
					return;
				}
				fields.push(field);
			});
			return fields;
		},

		//过去组类型的字段
		_getGroupFields: function (fields) {
			var NOT = {};
			fields = fields || this.fields;
			_.each(fields, function (field) {
				if (field.type == 'group') {
					NOT[field.api_name] = 1;
					_.each(field.fields, function (val, key) {
						field.group_type !== 'area' && (NOT[val] = 1);//地区定位组件里字段不过滤
					});
				}
			});
			return NOT;
		},

		_getFieldOptions: function (fields) {
			var me = this;
			return _.map(fields, function (field) {
				return {
					label: field.label,
					value: field.api_name,
					depend: me.options.isSupportCascadingCheck ? field.cascade_parent_api_name : '',
					child: {
						type: 'selectone',
						options: me._getCompareOptions(field)
					}
				}
			});
		},
        // 格式化字段类型
        _formatFieldType: function (field) {
            let _t = field.type;
            // 引用类型字段
            if (field?.quote_field_type) {
                _t = field.quote_field_type; // 关联字段的类型
            }
            if (this.options.formatFieldType) {
                _t = this.options.formatFieldType(field) || _t;
            }
            return _t;
        },
		_getCompareOptions: function (field) {
			var me = this;
            field.type = this._formatFieldType(field);
			var compares = this.options.helper.getCompare(field.type);
			compares = me.options.parseCompare(compares, field);
			var type = this.options.helper.getType(field.type, me.options.isRelate, me.options.needLookUp);

			return _.map(compares, function (compare) {
				var child = {
					type: me._getChildType(compare, field.optionType || type, field.optionType || field.type),
					ftype: field.type,
					fname: field.api_name,
					field: field,
					options: me._getValueOptions(field),
					isReInit: type == 'employee' || type == 'department' || type == 'exemployee',
					relatedName: field.target_related_list_name || '',
					targetApiName: field.target_api_name,
                    props: field.optionType === 'cascader' ? {
                        multiple: _.contains([13, 14], compare.value), // 操作符为属于或不属于时，支持多选
                        ...(me.options.setCascaderProps ? me.options.setCascaderProps(field, compare) : {})
                    } : null
				};
				if ((_.contains(['employee', 'department', 'record_type', 'exemployee', 'area'], type) && _.contains([7, 8, 13, 14], compare.value)) 
                    || (type == 'lookup' && _([13, 14]).contains(compare.value))
                    || (child.ftype === 'object_reference_many' && _([7, 8, 25, 26]).contains(compare.value))
                    ) {
					if( type == 'department' && me.options.helper.changeDepartmentConfig){
						child = me.options.helper.changeDepartmentConfig(child,me.options,compare);
					}
					child.isMultiple = true;
                }
                if (me.options.needMInput && child.type == 'text' && _.contains([13, 14], compare.value)) {
                    child.useMInput = true;
                }

				return {
					label: compare.name,
					value: compare.value,
					optionTip: compare.optionTip,
					child: child
				}
			})
		},

		_getChildType: function (compare, type, ftype) {
			if (!compare||compare.value == 9 || compare.value == 10) {
				return '';
			}

			if (this.options.helper.getTypeByCompare) {
				let v = this.options.helper.getTypeByCompare(compare, type, ftype)
				if (v === 'diasbled') {
					return ''
				}
				return v || type;
			}

			return type;
		},

		isEmpField: function (apiname) {
			return _.contains(['last_modified_by', 'last_follower', 'created_by', 'assigner_id'], apiname);
		},

		_getValueOptions: function (field) {
			var me = this,
				temp = [],
				opts = me.options;
			if(field.type === 'mycascader' || field.optionType === 'cascader'){
				return opts.getCascaderOptions ? opts.getCascaderOptions(field) : field.options;
			}
			switch (field.type) {
				case 'select_many':
				case 'record_type':
					temp = _.filter(field.options, function (item) {
						item.extendConfig = item.config || {};
						return item.is_active !== false;
					});
					break;
				case 'select_one':
					temp = _.filter(field.options, function (item) {
						item.extendConfig = item.config || {};
						return item;
					});
					break;
				case 'date':
				case 'date_time':
				case 'time':
					temp = _.filter(me.allowFields, function (item) {
						if (item.api_name !== field.api_name) {
							//同类型
							if (item.type == field.type) {
								return true;
							}
							//日期类型允许选择日期时间类型
							if (field.type == 'date' && item.type == 'date_time') {
								return true;
							}
						}
						return false;
					});
					break;
				case 'true_or_false':
					if (field.options) {
						temp = _.map(field.options, function (item) {
							return {
								label: item.label,
								value: item.value ? 'true' : 'false'
							};
						});
					} else {
						temp = [{
							label: $t("是"),
							value: 'true'
						}, {
							label: $t("否"),
							value: 'false'
						}];
					}
					break;
				case 'number':
				case 'currency':
				case 'percentile':
					temp = _.filter(me.allowFields, function (item) {
						if (item.api_name !== field.api_name) {
							//特殊处理
							if (me.isEmpField(item.api_name)) {
								return false;
							}
							if (_.contains(['number', 'currency', 'percentile'], item.type)) {
								return true;
							}
						}
						return false;
					});
					break;
				case 'object_reference':
					temp = [];
					if (opts.isRelate && opts.origin) {
						temp = _.filter(opts.origin.allowFields, function (item) {
							if (_.contains(opts.filterApiname, item.api_name)) {
								return false;
							}
							if (item.type == 'object_reference') {
								if (item.target_api_name == field.target_api_name &&
									item.target_api_name !== opts.apiname) {
									return true;
								}
							}
							return false;
						});
					}
					break;
				case 'country':
					temp = me.cpcd.country.options;
					break;
				case 'province':
					temp = me.cpcd.province.options;
					break;
				case 'city':
					temp = me.cpcd.city.options;
					break;
				case 'district':
					temp = me.cpcd.district.options;
					break;
				default:
					temp = [];

			}
			return _.map(temp, function (item) {
				return {
					extendConfig: item.extendConfig || null,
					label: item.label || item.name,
					value: item.value || item.api_name
				}
			});
		},

		initFilter: function () {
			var me = this;
			var filter = me.filter;
			filter && filter.destroy();
			filter = new Filter(_.omit(me.options, 'defaultValue'));
			me.filter = filter;
			me.copyFilterEvents();
			filter.render();
			me.setData(me.options.defaultValue);
		},

		copyFilterEvents: function () {
			var me = this;
			_.each(['add.item', 'del.item', 'change', 'render'], function (item) {
				var fun = item.replace('.', '') + 'Handle';
				me.filter.on(item, function () {
					var arr = Array.prototype.slice.call(arguments);
					me[fun] && me[fun].apply(me, arr);
					arr.unshift(item);
					me.trigger.apply(me, arr);
				});
			});
		},

		changeHandle: function (data, comp) {

		},

		validateNumber: function (val) {
			return !isNaN(+val);
		},

		_setCPCDOptions: function (data, arr) {
			var me = this;
			var parent_field = me.fields[data.depend];
			var son_field = me.fields[data.value];
			var parent_opts = me.cpcd[parent_field.type].options;
			var item = _.findWhere(parent_opts, { value: arr[2] });
			var son_opts = [];
			if (item.child_options && item.child_options.length > 0) {
				son_opts = item.child_options[0][son_field.type];
				son_opts = _.filter(me.cpcd[son_field.type].options, function (item) {
					return _.contains(son_opts, item.value);
				});
			}
			_.each(data.child.options, function (item) {
				item.child.options = son_opts;
			});
		},

		_setSonOptions: function (data, arr) {
			var parent = this.fields[arr[0]];
			var son = this.fields[data.value];
			var parent_options = _.findWhere(parent.options, { value: arr[2] }) || _.findWhere(parent.options, { api_name: arr[2] });
			if (parent_options) {
				var options = [];
				_.each(son.options, function (item) {
					if (_.contains(parent_options.child_options[0][data.value], item.value)) {
						options.push({
							label: item.label || item.name,
							value: item.value
						});
					}
				});
				_.each(data.child.options, function (item) {
					item.child.options = options;
				});
			}
		},

		getData: function () {
			var me = this;
			let data =  me.filter ? me.filter.getData() : [];
			let filter = [];

			for (let i = 0; i < data.length; i++) {
				let item = data[i];
				if (_.every(item, (v) => { return v === '' })) continue;  // 如果值都是空, 那么放弃掉
				var field = me.fields[item[0]];
				if (field) {
					// 比较符的数字代号（唯一）
					let operatorNum = item[1];
					// 比较符的英文字符（不唯一）
					item[1] = me._parseOperator(item[1]);
					item[2] = me._parseFieldValue(field, item[1], item[2], operatorNum);
					item.push(field ? field.type : '');
					this.options.formatGetItem && this.options.formatGetItem(item, field);
				}
				filter.push(item);
			}
			return filter
		},

		_parseFieldValue(field, operator, value, operatorNum) {
            let type = field.type;

			// if (this.options.optionType === 'cascader') {
                let _type = this.options.helper.getType(type, this.options.isRelate, this.options.needLookUp);
                let compares = this.options.helper.getCompare(type);
                    compares = this.options.parseCompare(compares, field);
                let compare = _.findWhere(compares, {value1: operator});

			if (this.options.needMInput && this._getChildType(compare, _type, type) == 'text' && _.contains(['IN', 'NIN'], operator)) {
                    return _.isString(value) && _.filter(value.replace(/；/g, ';').split(';'), item => item.trim());
                }

			if (this.options.needLookUp && (type === 'object_reference' || type === 'object_reference_many')) {
                    return _(value).pluck('id');
                }

			// }

			if (['date', 'time', 'date_time'].indexOf(field.type) != -1) {
				if (['BETWEEN', 'CUSTOM'].indexOf(operator) != -1) {
					if (_.isString(value) && _.contains(value, '|')) {
						return value.split('|')
					}
				}
				if ([27, 28, 29, 30, 31, 32, 33, 34].indexOf(operatorNum) != -1) {
					switch(operatorNum) {
						case 27: 
						case 28:
						case 31:
						case 32:
							return ['day', value];
							break;
						case 29:
						case 30:
							return ['month', value];
							break;
						case 33:
						case 34:
							return ['week', value];
							break;
					}
				}
				if(this.options.datetimeOprateMore){
					let resultOpratorValue = (this.options.helper.moreTimeOpratorGet && this.options.helper.moreTimeOpratorGet(type)) || []
					if (resultOpratorValue.indexOf(operatorNum) != -1) {
						let resultVlaue = this.options.helper.moreTimeOpratorGetValue && this.options.helper.moreTimeOpratorGetValue(operatorNum, value)
						if(resultVlaue){
							return resultVlaue
						}
					}
				}
            }

			if (_.isArray(value)) {
				return value;
			} else {
				return [value];
			}
			return value;
		},

		setData: function (data) {
			var me = this;
			if (!data) {
				return;
			}
			if (!(me.fields && me.cpcd)) {
				me.todoList.push({
					fn: 'setData',
					args: Array.prototype.slice.apply(arguments)
				});
				return;
			}

			data = _.map(data, function (item) {
				var field = me.fields[item[0]];
				field = field || _.findWhere(me.fields, { api_name: item[0] }) || {};
				var compareConfig = me.options.helper.getCompare(field.old_type || field.type);
				compareConfig = me.options.parseCompare(compareConfig, field);
				var compare;
				if (me.options.helper.getCompareInfo) {
					compare = me.options.helper.getCompareInfo(item[1], item[2], field.type, compareConfig)
				} else {
					compare = _.findWhere(compareConfig, {
						value1: item[1]
					})
				}
				var result = [item[0], compare ? compare.value : ''];
				var operatorNum = compare ? compare.value : '';
				var operator = compare ? compare.value1 : '';
				var field_value = item[2];

				let value = me.setFieldValue(field, operator, field_value, operatorNum);

				result.push(value)
				return result;
			});
			this.filter.setData(data);
		},

		setFieldValue(field, operator, value, operatorNum) {
			let type = field.type;
            let compare = _.findWhere(this.options.helper.compare, {value1: operator});
			let gType = this.options.helper.getType(type, this.options.isRelate, this.options.needLookUp);
            let childType = this._getChildType(compare, gType, type)

            if (this.options.needMInput && childType == 'text' && _.contains([13, 14], compare.value)) {
                return value.join(';');
            }
			if (['date', 'time', 'date_time'].indexOf(type) != -1) {
				if ([27, 28, 29, 30, 31, 32, 33, 34].indexOf(operatorNum) != -1) {
					return value[1];
				} else if (['LT', 'GT'].indexOf(operator) != -1) {
					return value[0];
				} else if(this.options.datetimeOprateMore){
					// 开启更多比较符号
					let resultOpratorValue = (this.options.helper.moreTimeOpratorGet && this.options.helper.moreTimeOpratorGet(type)) || []
					if (resultOpratorValue.indexOf(operatorNum) != -1) {
						return value[1];
					} 
				}
			}
			if (['auto_number', 'text', 'long_text', 'phone_number', 'email', 'url', 'location'].indexOf(type) != -1) {
				if (['IN', 'NIN', 'STARTWITH', 'ENDWITH'].indexOf(operator) != -1) {
					return value[0];
				}
			}
			if (['employee', 'department', 'select_many', 'object_reference_many'].indexOf(type) != -1) {
				return value;
			}
			if (['EQ', 'N', 'LIKE', 'NLIKE', 'GT','GTE','LT','LTE'].indexOf(operator) != -1) {
				return value[0];
			}
			return value;
		},

		renderHandle: function () {
			var me = this;
			_.each(me.todoList, function (item) {
				me[item.fn] && me[item.fn].apply(me, item.args);
			});
			me.todoList = [];
		},

		_parseOperator: function (val) {
			var compare = _.findWhere(this.options.helper.getCompareConfig(), {
				value: parseInt(val)
			});
			return compare ? compare.value1 : '';
		},

		getPreviewData: function (data) {
			var me = this;
			var compares = this.options.helper.getCompareConfig();
			data || (data = me.getData());
			return _.map(data, function (item) {
				var field = me.fields[item[0]];
				var result = ['--', '--', '--'];
				if (field) {
					result[0] = field.label;
					var compare = _.findWhere(compares, { value1: item[1] });
					result[1] = compare.name;
					var ftype = field.type.replace('_', '');
					var funcName = field.type.split('_');
					funcName = _.map(funcName, function (item) {
						return item.charAt(0).toUpperCase() + item.slice(1)
					});
					funcName = '_format' + funcName.join('');
					if (item[2]) {
						if (_.contains(['last_modified_by', 'last_follower', 'created_by'], field.api_name)) {
							result[2] = me._formatEmployee(item[2], field);
						} else if (/^\$(.*?)\$$/g.test(_.isArray(item[2]) ? item[2][0] : item[2])) {
							result[2] = me._formatVar(item[2], field);
						} else if (me[funcName]) {
							if (_.isArray(item[2])) {
								result[2] = item[2].reduce(function (memo, it) {
									return memo + me[funcName](it, field);
								}, '');
							} else {
								result[2] = me[funcName](item[2], field);
							}
						} else {
							result[2] = item[2];
						}
					} else {
						result[2] = item[2];
					}
				}
				return result;
			});
		},

		_formatTrueOrFalse: function (val) {
			return val == 'true' ? $t("是") : $t("否");
		},

		_formatRecordType: function (val, field) {
			var enumItem = _.findWhere(field.options, { api_name: val });
			return enumItem ? enumItem.label : '--';
		},

		_formatTime: function (val, field) {
			return FS.moment(+val,null,null,null,true).format('HH:mm');
		},

		_formatDateTime: function (val, field) {
			return FS.moment(+val,null,null,null,true).format('YYYY-MM-DD HH:mm');
		},

		_formatDate: function (val, field) {
			return FS.moment(+val,null,null,null,true).format('YYYY-MM-DD');
		},

		_formatDepartment: function (val, field) {
			var dep = util.getCircleById(+val);
			return dep ? dep.name : '--';
		},

		_formatEmployee: function (val, field) {
			var emp = util.getEmployeeById(+val);
			return emp ? emp.name : '--';
		},

		_formatSelectOne: function (val, field) {
			var item = _.findWhere(field.options, { value: val });
			return item ? item.label : '--';
		},

		_formatSelectMany: function (val, field) {
			if (!_.isArray(val)) {
				val = [val]
			}
			val = _.filter(val, function (v) {
				return v !== 'undefined' && !!v;
			});
			return _.map(val, function (v) {
				if (v == 'undefined' || !v) {
					return '';
				}
				var item = _.findWhere(field.options, { value: v });
				return item.label || '--';
			}).join(',');
		},

		_formatCountry: function (val, field) {
			var country = _.findWhere(this.cpcd[field.type].options || [], { value: val });
			return country ? country.label : '--';
		},

		_formatProvince: function (val, field) {
			var province = _.findWhere(this.cpcd[field.type].options || [], { value: val });
			return province ? province.label : '--';
		},

		_formatCity: function (val, field) {
			var city = _.findWhere(this.cpcd[field.type].options || [], { value: val });
			return city ? city.label : '--';
		},

		_formatDistrict: function (val, field) {
			var district = _.findWhere(this.cpcd[field.type].options || [], { value: val });
			return district ? district.label : '--';
		},

		_formatVar: function (val, field) {
			var target;
			if (_.isArray(val)) {
				val = val[0];
			}
			if (field.type == 'object_reference') {
				target = this.options.origin.fields[val.slice(1, -1)];
			} else {
				target = this.fields[val.slice(1, -1)];
			}
			return target ? target.label : '--';
		},

		isExitNull: function () {
			var data = this.getData();
			for (var i = 0, len = data.length; i < len; i++) {
				var temp = data[i];
				if (!(temp[0] && temp[1])) {
					return true;
				}
				if (temp[1] != 'IS' && temp[1] != 'ISN' && !temp[2]) {
					return true;
				}
			}
			return false;
		},

		destroy: function () {
			this.filter && this.filter.off();
			this.filter && this.filter.destroy();
			this._countryAjax && this._countryAjax.abort()
		}
	})

	module.exports = FieldFilter;
})
