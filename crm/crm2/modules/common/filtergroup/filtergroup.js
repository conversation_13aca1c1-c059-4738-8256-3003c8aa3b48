define(function (require, exports, module) {
	
	let tpl = require('./template/tpl-html');
	let itemTpl = require('./template/item-html')
	let FieldBase = require('crm-modules/action/field/field').components.Base
	let helper = require('./helper')
	let Filter = require('crm-modules/common/filtergroup/fieldfilter')
	let CascaderFilter = require('crm-modules/common/filtergroup/cascaderfilter')

	let FilterGroup = FieldBase.extend({
		tpl: tpl,
		itemTpl: itemTpl,
		options: {
			AND_MAX: 5,
			OR_MAX: 5,
			OR_MIN: 1,	// 最小的或数量
			level: 3,
			width: 600,
			OR_KEY: 'filters',	//server端下发的不统一 , 这里动态的可修改
			apiname: null,
			selectone_multiple: false, 	// 是否把单选组件都变为多选
			addBtnName: $t('继续添加筛选器'),
			optionType: 'alone',  //第一级选择是级联还是单选 alone or cascader,
            needLookUp: false, // 是否需要放开object_reference对应的lookup组件
            needMInput: false, // 是否需要放开text组件‘属于、不属于’使用分号‘；’拆分
			filterCusOptions: null,  // 自定义筛选组件的参数
			parseDefualtData: null,  // 根据自己的数据结构处理默认值
			lastSelectCanEmpty:false,//最后一项时可清空筛选项
			datetimeOprateMore:false,//时间字段支持更多比较符号
			needDepartmentChildren: false, // 部门字段是否需要包含子部门
		},
        events: {
            'click .j-add-filter': 'addFilterHandle',
            'click .j-delete-filter': 'deleteFilterHandle',
		},
		
		initialize() {
            let opts = this.options;

            if (opts.optionType === 'cascader') { // 级联类型忽略needLookUp, needMInput
                opts.needLookUp = opts.needMInput = true;
            }
			this.apiname = this.options.apiname;
			this.$el = this.options.$wrapper;
			this.helper = this.options.helper || helper;
			this.Filter = this.options.Filter || Filter;
			this.CascaderFilter = this.options.CascaderFilter || CascaderFilter;
			this.render();
			
		},

		render() {
			let opts = this.options;
			this.filters = [];
			this.$group = $('.crm-filter-group', this.$el);
			
			this.$el.html(this.tpl({
				addBtnName: this.options.addBtnName
			}))

			if (opts.defaultValue && opts.defaultValue.length) {
				for (let i = 0; i < opts.defaultValue.length; i++) {
					let condition = opts.defaultValue[i][opts.OR_KEY];
					let defaultValue = this.formatValue(condition)
					this.addFilter(defaultValue);
				}
			} else {
				this.addFilter();
			}
		},

		resetRender(defaultValue) {
			this.removeAllFilters();
			
			if (defaultValue) {
				for (let i = 0; i < defaultValue.length; i++) {
					let condition = defaultValue[i][this.options.OR_KEY];
					this.addFilter(this.formatValue(condition));
				}
			} else {
				this.addFilter();
			}
		},

		addFilter(defaultValue) {
			let me = this;
			let config = {
				parseCompare: me._parseCompare.bind(me)
			}
			let opts = this.options;
			let $item = $(this.itemTpl());
			let $wrapper = $item.find('.filter-item-wrap');
			this.$('.j-filter-list').append($item);

			if (opts.parseCompare) {
				config.parseCompare = opts.parseCompare;
			}
			if (opts.fields) {
				config.fields = opts.fields;
			}

			let options = $.extend(true, {
				$wrapper: $wrapper,
				width: opts.width,
				title: '<span class="del-span j-delete-filter"></span><span style="color:#919eab">' + $t("且（AND）") + '</span>',
				max: opts.AND_MAX,
				apiname: opts.apiname,
				objects: CRM.util.deepClone(opts.objects),
				props: opts.props,  //cascader情况下传给第一级选择框cascader的props
                needLookUp: opts.needLookUp,
                needMInput: opts.needMInput,
				level: opts.level,	//层级 default = 3
				filterApiname: opts.filterApiname,	//通过字段 apiname 过滤
				filterType: opts.filterType,	//通过字段类型过滤
				defaultValue: defaultValue,
				helper: this.helper,
				formatFields(fields) {
					var data = me.helper.formatFields(fields, opts);

					return opts.formatFields ? opts.formatFields(data) : data;
				},
				formatGetItem: opts.formatGetItem,  //对获取的数据进行个性化定制
                layoutMode: opts.layoutMode,
                getCustItemWidth: opts.getCustItemWidth,
				lastSelectCanEmpty:opts.lastSelectCanEmpty,//最后一项是否可清空
				datetimeOprateMore: opts.datetimeOprateMore, //时间字段支持更多比较符号
                getCompStyle: opts.getCompStyle,
				showObjectsCascader: opts.showObjectsCascader, // cascader 如 objects 数据仅为一组时，也展示完整的级联关系
                formatFieldType: opts.formatFieldType,
				needDepartmentChildren: opts.needDepartmentChildren,
				// 需要增加筛选的配置可以继续在这里增加
			}, config, opts.filterCusOptions);

			_.map(options, function(val, key) {
				_.isUndefined(val) && (delete options[key]);
			})

			let map = {
				cascader: me.CascaderFilter,
				alone: me.Filter,
			}
			let Constructor = map[opts.optionType] || Filter;

			// 扩展options，依赖其他option
			if (opts.afterParseFilterOptions) {
				options = opts.afterParseFilterOptions(options);
			}
			var filter = new Constructor(options);
			filter.__$parent = $item;
			this.filters.push(filter);


			filter.on('render', function () {
				// 如果只有一个或, 隐藏第一个的删除按钮
				me.isShowDelBtn();

				me.trigger('render', me.filters)
			})

			filter.on(['add.item', 'del.item', 'change'].join(' '), function() {
				me.trigger('change', me.filters, ...arguments);
			})

			filter.on('change.objects', (rst) => me.trigger('change.objects', rst));
			filter.on('change.props', (rst) => me.trigger('change.props', rst));
			
			
			// 达到最大数量小之后, 隐藏添加按钮
			this.isShowAddBtn();

			return filter;
		},

        removeFilter: function(filter) {
            if (!filter) return;
            var filters = [];
            _.each(this.filters, function(a, index) {
                if (a === filter) {
                    filter.destroy();
                    filter.__$parent && filter.__$parent.remove();
                    return;
                }
                filters.push(a);
            })
            this.filters = filters;
			
			this.isShowDelBtn();
			this.isShowAddBtn();
        },

        removeAllFilters: function() {
            _.each(this.filters, function(filter) {
                filter.destroy && filter.destroy();
            })
            this.filters = [];
            this.$('.j-filter-list').empty();
			this.isShowAddBtn();
		},
		
		isShowAddBtn() {
			$('.j-add-filter', this.$el)[this.filters.length === this.options.OR_MAX ? 'hide' : 'show']();
		},

		isShowDelBtn() {
			$('.j-delete-filter', this.$el).css('visibility', this.filters.length == this.options.OR_MIN ? 'hidden' : 'visible');
        },

		getValue: function() {
			if (!this.valid()) {
				return JSON.stringify([]);
			}

			return this._getValue();
		},

		_getValue: function () {
			let me = this;
			let opts = this.options;
			let wheres = this.filters;
			let data = []
			for (let i = 0; i < wheres.length; i++) {
				let filters = wheres[i];

				let filterlist = filters.getData();

				if (filterlist.length === 0) continue;

				if (opts.optionType == 'cascader') {
					filterlist = _.map(filterlist, (filter) => {
						return _.extend({}, filter, {
							operator_name: me.helper.getNameByValue(filter.operator, filter.type),
						})
						// return {
						// 	object_api_name: filter[4],
						// 	// field_name_type: '',
						// 	field_name: filter[0],
						// 	operator: filter[1],
						// 	operator_name: me.helper.getNameByValue(filter[1], filter[3]),
						// 	// field_value_type: '',
						// 	field_values: filter[2],
						// 	connector: 'AND',
						// 	type: filter[3]
						// }
					})
				} else {
					filterlist = filterlist.map((filter) => {
						const list = {
							field_name: filter[0],
							operator: filter[1],
							operator_name: me.helper.getCompareName ? me.helper.getCompareName(filter[1], filter[2], filter[3]) : me.helper.getNameByValue(filter[1], filter[3]),
							field_values: filter[2],
							connector: 'AND',
							type: filter[3],
							value_type: me.helper.getValueType && me.helper.getValueType(filter[1], filter[2], filter[3]),
						}
						if (this.options.formatValue) {
							this.options.formatValue(list, filter);
						}
						return list
					})
				}

				data.push({
					[opts.OR_KEY]: filterlist,
					connector: 'OR'
				})
			}

			return JSON.stringify(data);
        },
        
        dataAllEmpty: function() {
            let opts = this.options;
            let wheres = JSON.parse(this._getValue());

			for (let i = 0; i < wheres.length; i++) {
				let filters = wheres[i][opts.OR_KEY];

				for (let j = 0; j < filters.length; j++) {
                    let filter = filters[j];

                    if (filter.field_name 
                        || filter.operator_name 
                        || !(_.isNull(filter.field_values) 
                            || _.isNaN(filter.field_values) 
                            || _.isUndefined(filter.field_values) 
                            || (_.isArray(filter.field_values) && filter.field_values.length === 0) 
                            || (_.isArray(filter.field_values) && filter.field_values.length > 0 && filter.field_values[0] === ''))
                        ) {
                        return false;
                    }
				}
			}
            return true;
        },

		valid() {
			let opts = this.options;
			let wheres = JSON.parse(this._getValue());
			for (let i = 0; i < wheres.length; i++) {
				let filters = wheres[i][opts.OR_KEY];

				for (let j = 0; j < filters.length; j++) {
                    let filter = filters[j];
					// level 层级不一样, 校验也不一样 todo
					if (opts.level === 1) {
						if (!filter.field_name) return false;
					} else {
						if (!filter.field_name || !filter.operator) return false;
						if (['IS','ISN','SAME','NSAME'].indexOf(filter.operator) != -1) continue;
						if (CRM.util.isEmptyValue(filter.field_values)) {
							return false;
						}
					}
				}
			}
			return true;
		},
		
		formatValue(condition) {
			console.log(condition,"condition");
			let data = [];
			let me = this;
			for (let i = 0; i < condition.length; i++) {
				let filter = _.clone(condition[i]);
				let field_name = filter.field_name;
                if (me.options.needLookUp) {
                    // lookup组件数据回填前重写field_values
					if (filter.type == 'object_reference' || filter.type == 'object_reference_many') {
						let values = _.zip(filter.field_values, _.isString(filter.field_values__s) && filter.field_values__s.split(',') || []);
						filter.field_values = _.map(values, val => ({id: val[0], value: val[1]}));
                    }
                }
				if (me.options.optionType === 'cascader'){
                    field_name = filter.field_name.split('.');
					if (me.options.parseDefualtData && _.isFunction(me.options.parseDefualtData)) {
						field_name = me.options.parseDefualtData(filter, me);
					}
				}
				data.push([field_name, filter.operator, formatValue(filter)])
			}
			function formatValue(filter) {
				// 开启了needDepartmentChildren 并且有is_cascade,并且类型为部门并且比较符为属于或者不属于 需要加上_y后缀
				if ( me.options.needDepartmentChildren && filter.type == 'department' && (filter.operator == 'IN' || filter.operator == 'NIN') && filter.is_cascade){
					return filter.field_values.map(value => `${value}_y`);
				}
				if (filter.operator === 'CUSTOM' || filter.operator === 'BETWEEN') {
					return filter.field_values.join('|')
				} else {
					return filter.field_values;
				}
			}
			return data;
		},

        _switchHandle: function(e) {
            var $target = $(e.currentTarget);
            if ($target.hasClass('mn-selected')) return;
            $target.data('value') === 'hasCondition' ? (this.$scopeWrap.show(), this.addFilter()) : this.removeAllFilters();
        },

		deleteFilterHandle: function (e) {
			let index = $(e.currentTarget).closest('.filter-item').index();
            this.removeFilter(this.filters[index]);
        },

        addFilterHandle: function() {
            this.addFilter();
		},
		
        _hideError: function() {
            this.hideError();
		},
		
		_parseCompare: function(compares, field) {

			compares = this._parseCompareHandle(compares, field);

			if (this.options.parseCompare) {
				compares = this.options.parseCompare(compares, field);
			}

			return compares;
		},

		_parseCompareHandle: function(compares, field) {

			if (field.api_name === 'owner_department') {
				compares = _.filter(compares, function(item) {
					return _.contains([13, 14, 9, 10], item.value);
				});
			}

			return compares;
		},

        destroy: function() {
            _.each(this.filters, function(filter) {
				filter.off();
                filter.destroy && filter.destroy();
                filter = null;
            })
			this.filters = null;
			this.undelegateEvents();
        }
	});

	FilterGroup.helper = helper;
	
	return FilterGroup;
})
