define(function (require, exports, module) {

	let util = require('crm-modules/common/filtergroup/util')

	module.exports = {
		compare: [{
			value: 1,
			name: $t("等于"),
			value1: 'EQ'
		}, {
			value: 2,
			name: $t("不等于"),
			value1: 'N'
		}, {
			value: 3,
			name: $t("大于"),
			value1: 'GT'
		}, {
			value: 4,
			name: $t("大于等于"),
			value1: 'GTE'
		}, {
			value: 5,
			name: $t("小于"),
			value1: 'LT'
		}, {
			value: 6,
			name: $t("小于等于"),
			value1: 'LTE'
		}, {
			value: 7,
			name: $t("包含"),
			value1: 'LIKE'
		}, {
			value: 8,
			name: $t("不包含"),
			value1: 'NLIKE'
		}, {
			value: 9,
			name: $t("为空"),
			value1: 'IS'
		}, {
			value: 10,
			name: $t("不为空"),
			value1: 'ISN'
		}, {
			value: 11,
			name: $t("早于"),
			value1: 'LT'
		}, {
			value: 12,
			name: $t("晚于"),
			value1: 'GT'
		}, {
			value: 13,
			name: $t("属于"),
			value1: 'IN'
		}, {
			value: 14,
			name: $t("不属于"),
			value1: 'NIN'
		}, {
			value: 17,
			name: $t('时间段'),
			value1: 'BETWEEN'
		},  {
			// value: 18,
			// name: $t('介于'),
			// value1: 'BETWEEN'
		}, {
			value: 19,
			name: $t("自定义"),
			value1: 'CUSTOM'
		}, {
			value: 21,
			name: $t("相同"),
			value1: 'SAME'
		}, {
			value: 22,
			name: $t("不相同"),
			value1: 'NSAME'
		}, {
			value: 23,
			name: $t("开始于"),
			value1: 'STARTWITH'
		}, {
			value: 24,
			name: $t("结束于"),
			value1: 'ENDWITH'
		},{
			value: 25,
			name: $t("属于"),
			value1: 'HASANYOF'
		}, {
			value: 26,
			name: $t("不属于"),
			value1: 'NHASANYOF'
		},{
			value: 27,
			name: $t("前N天"),
			value1: 'LTE',
			optionTip: $t('前N天(不含当天)，比如当前是5月8号，前3天是指5.7、5.6、5.5')
		},{
			value: 28,
			name: $t("后N天"),
			value1: 'GTE',
			optionTip: $t('后N天(不含当天)，比如当前是5月8号，后3天是指5.9、5.10、5.11')
		},{
			value: 29,
			name: $t("前N月"),
			value1: 'LTE',
			optionTip: $t('前N月：指从当天所在月的前一个月开始，向前N个月；前1月=上月；前2个月=上月+上上月')
		},{
			value: 30,
			name: $t("后N月"),
			value1: 'GTE',
			optionTip: $t('后N月：指从当天所在月的下个月开始，向后N个月；后1月=下月；后2月=下月+下下月')
		},{
			value: 31,
			name: $t("N天前"),
			value1: 'LT',
			optionTip: $t('N天前(当天算1天)，比如当前日期是0508，3天前，是指0505的23:59:59之前')
		},{
			value: 32,
			name: $t("N天后"),
			value1: 'GT',
			optionTip: $t('N天后(当天算1天)，比如当前日期是0508，3天后，是指0511的00:00:00之后')
		},{
			value: 33,
			name: $t("N周前"),
			value1: 'LT',
			optionTip: $t('N周前(当周算1周)，比如当前是2020年第7周，3周前，是指2020年第4周周日23:59:59之前')
		},{
			value: 34,
			name: $t("N周后"),
			value1: 'GT',
			optionTip: $t('N周后(当周算1周)，比如当前是2020年第7周，3周后，是指2020年第10周周一00:00:00之后')
		}],
		getType: function (type, isRelate, needLookUp) {
			switch (type) {
				case 'select_one':
					return 'selectone';
				case 'select_many':
					return 'selectmany';
                case 'cascader':
                    return 'cascader';
				case 'country':
				case 'province':
				case 'city':
				case 'district':
				case 'true_or_false':
				case 'record_type':
					return 'selectone';
				case 'date':
					return 'date' + (isRelate ? '-relate' : '');
				case 'date_time':
					return 'datetime' + (isRelate ? '-relate' : '');
				case 'time':
					return 'time' + (isRelate ? '-relate' : '');
				case 'employee':
				case 'department':
					return type;
				case 'object_reference':
				case 'object_reference_many':
					if (needLookUp) return 'lookup';
				// 	return '' + (isRelate ? '-relate' : '');
				case 'number':
				case 'currency':
				case 'percentile':
					return 'text' + (isRelate ? '-relate' : '');
				default:
					return 'text';
			}
		},
		getCompareNums(type) {
			var enums;
			switch (type) {
				//自增编号、单行文本、多行文本、手机、邮箱、网址
				case 'auto_number':
				case 'text':
				case 'long_text':
				case 'phone_number':
				case 'email':
				case 'url':
					enums = [1, 2, 7, 8, 13, 14, 23, 24, 9, 10];
					break;

				// 查找关联、主从关系
				case 'object_reference': // 查找关联
				case 'master_detail':
					enums = [1, 2, 7, 8, 13, 14, 9, 10];
					break;
                case 'object_reference_many': // 查找关联
                    enums = [7, 8, 25, 26, 9, 10];
                    break;

				//单选、不支持筛选、国家、省、市、区 
				case 'select_one':
				case 'record_type':
				case 'country':
				case 'province':
				case 'city':
				case 'district':
					enums = [1, 2, 13, 14, 9, 10];
					break;
				
				// 布尔值
				case 'true_or_false':
					enums = [1, 2, 9, 10];
					break;

				// 多选
				case 'select_many':
					enums = [1, 2, 7, 8, 25, 26, 9, 10];
					break;

				// 人员、部门
				case 'employee':
				case 'department':
					enums = [13, 14, 9, 10];
					break;

				// 数字字段、金额字段、百分数、统计字段
				// TODO 介于
				case 'count':
				case 'number':
				case 'currency':
				case 'percentile':
				case 'formula':
					enums = [1, 2, 3, 4, 5, 6, 9, 10];
					break;

				// 时间
				case 'time':
					enums = [1, 2, 11, 12, 9, 10, 17];
					break;

				// 日期、日期时间
				// TODO 不早于、不晚于、前N天、后N天、前N月、后N月
				case 'date':
				case 'date_time':
					enums = [1, 2, 11, 12, 9, 10, 17];
					break;

				// 定位
				case 'location':
					enums = [7, 8, 9, 10, 23, 24];
					break;

				// 附件、签名、图片、没匹配上的字段类型
				default:
					enums = [9, 10];
			}
			return enums;
		},
		getCompare: function (type) {
			var me = this;
			var enums = this.getCompareNums(type);

			return _.map(enums, function (item) {
				return _.find(me.compare, function (obj) {
					return obj.value === item
				})
			});
		},
		getCompareConfig: function () {
			return this.compare;
		},
		getFieldMustFilter: function() {
    		return [
    			'version',
                'package',
                'tenant_id',
                'object_describe_api_name',
                'object_describe_id',
                '_id',
                'extend_obj_data_id',
                // 'sales_process_id',
                'sales_process_name',
                'oppo_stage_id',
                'is_deleted',
                'pin_yin',
                'filling_checker_id',
                'product_group_id',
                'high_seas_id',
                'total_refund_amount',
                'is_remind_recycling',
                'Address',
                // 'owner_department',
                'SalesOrderProductObj',
                'lock_user',
                'lock_rule',
                'life_status_before_invalid',
                'active_status',
				'relevant_team',
				'order_by',
				'refresh_duplicated_version',
				'enterprise_wechat_user_id',
				'remind_days',
				'completed_field_quantity',
				'poi_information', //客户字段-poi信息
                'parent_prod_package_id',
                'parent_prod_pkg_key',
                'prod_pkg_key',
                'root_prod_pkg_key',
                'parent_gift_key',
				'account_path',
				'industry_ext',
				'mc_functional_currency',
				'mc_exchange_rate_version',
				'mc_currency',
				'mc_exchange_rate',
				'town',
				'extend_days',
				'last_deal_closed_amount',
    		];
    	},
		getTypeByCompare(compare, type, ftype) {
			if (compare.value === 18) {
				return 'between';
			} else if (['select_one'].indexOf(ftype) != -1 && [1, 2].indexOf(compare.value) != -1) {
				return 'selectone';
			} else if (['select_one'].indexOf(ftype) != -1 && [13, 14].indexOf(compare.value) != -1) {
				return 'selectmany';
			}  else if ([17, 19].indexOf(compare.value) != -1) {
				let compType = ['date', 'datetime', 'time'];
				let index = compType.indexOf(type);

				if (index > -1) {
					return compType[index] + '2';
				}
			}
		},
		// 获取比较符名称 
		// todo-（废弃）
		getNameByValue(value1, type) {
			if (type && ['date', 'time', 'date_time'].indexOf(type) != -1) {
				if (value1 == 'LT') return $t('早于');
				if (value1 == 'GT') return $t('晚于');
			}
			let item = _.findWhere(this.compare, { value1 });
			return item && item.name;
		},
		formatFields(fields, opts = {}) {
			for (const key in fields) {
				let field = fields[key];
				if (opts.selectone_multiple && ['select_one', 'record_type'].indexOf(field.type) != -1) {
					field.type = 'select_many'
				}
				// 负责人主属部门
				if (field.api_name === 'owner_department') {
					field.type = 'department';
				}
			}
			return fields;
		},
		formatFieldsValue(field, value, compare, originValue) {
			// 日期字段从东八区转换为个人时区
			var compares = ['IS', 'ISN'];

			if (field.type === 'date' && !compares.includes(compare)) {
				value = value.map(item => FS.util.convertTimestampFromTZ8(+item).toString());
				originValue = value.join('|');
			}

			return util.formatFieldValue(field, value, compare, originValue);
		},
		// 获取比较符名称
		getCompareName(operator, value, ftype, compares) {
			compares = compares || this.getCompare(ftype);
			let compare = this.getCompareInfo(compares, operator, value, ftype)
			return compare && compare.name;
		},
		// 获取比较符信息
		getCompareInfo(operator, value, ftype, compares) {
			// N天前，前N天等的比较符获取
			compares = compares || this.getCompare(ftype);
			let compare = _.findWhere(compares, {
				value1: operator
			});
			if (_.contains(['date', 'date_time'], ftype) && _.contains(['day', 'week', 'month'], value[0])) {
				switch(operator){
					case 'LT':
						compare = _.findWhere(compares, {
							value: {day: 31, week: 33}[value[0]]
						});
						break;
					case 'GT': 
						compare = _.findWhere(compares, {
							value: {day: 32, week: 34}[value[0]]
						});
						break;
					case 'LTE':
						compare = _.findWhere(compares, {
							value: {day: 27, month: 29}[value[0]]
						});
						break;
					case 'GTE':
						compare = _.findWhere(compares, {
							value: {day: 28, month: 30}[value[0]]
						});
						break;
				}
			}
			return compare || {};
		},
		getValueType(operator, value, ftype) {
			let value_type;
			if(operator == 'BETWEEN' && (!_.isArray(value) || (_.isArray(value) && value.length < 2))) {
				value_type = 3;
			} else if(_.contains(['date', 'date_time'], ftype) && _.contains(['day', 'week', 'month'], value[0])) {
				value_type = 3;
			}
			return value_type;
		},
	}
})

