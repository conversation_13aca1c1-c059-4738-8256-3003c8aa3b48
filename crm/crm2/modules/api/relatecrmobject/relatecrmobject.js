/**
 * @desc 关联crm对象
 * @param param   {Object} 
 *      @param param.methods {Objec} 方法集合
 *           @param  param.methods.loadingComplete 加载完成
 *           @param  param.methods.loadingComplete 加载完成
 *           @param  param.methods.destroy 销毁
 *           @param  param.methods.success 确认
 *           @param  param.methods.cancel  取消
 * <AUTHOR>
 */
define(function (require, exports, module) {
    var util   = CRM.util,
        Dialog = require("crm-widget/dialog/dialog"),
        Table  = require('./table/table'),
        tpl =  require('./template/tpl-html'),
        itemtpl =  require('./template/item-html'),
		ShowHistory = false;

    var CrmObject = Dialog.extend({
        attrs: {
            className: 'crm-c-crmobject',
            title: $t("关联CRM业务模块"),
            content: '<div class="crm-loading"></div>',
            showBtns:  true,
            showScroll: false,

            selectLimit:   200,      // 最大添加模块数
            maxNum:        0,        // 每个对象下最多选择的数据个数 0 表示不限制
            dataMaxCount:  0,        // 最多允许的数据总条数 0 表示不限制
            lineCheck: true,         // 选中表格行的时候，是否默认处理成选中
            curObjectType: 0,        // 当前选中的对象
            curTerms: [],            // 当前对象对应的场景
            curSelectedTermType: '', // 当前选中的场景
            keywords: '',            // 所搜框文本
            showAsText: true,        // 是否显示作为文本插入，不显示，则asText为false
            asText: false,           // 作为文本插入正文
            single: false,           // 是否为单选
            disabledData: [],
			selectedItems: {},       // 选中的对象列表  key: 对象类型  value: {ids: []}
			latestRelatedObjects: [], //最近使用的对象列表
			objectList:[],
            allObjects: [],    // 用户存储所有的对象类型列表，可传入，传入则表示：会在传入的所有objectType中筛选出负责当前用户权限的对象类型（传入的为希望展示的objectType数组）
            reRenderDtFlag: true, // 如果类型发生了变化，则表头变化，则重绘表格，否则刷新表格数据即可
            defaultFilter: {
                type: 0,
                termType: 0,
                keywords: ''
            },
            zIndex: 890,
            relationType: 0,    // 当前场景是否关联了对象，0：没有关联，其他：对应关联对象的type
            curObjectSelectedItems: [],  // 每种类型的表格下，选中的数据缓存（selectedItems：所有类型选中的缓存数据）
            excludeObjectTypes:[], // 增加黑名单（基础对象用objectType,自定义对象用apiName） add by hgl
            defaultRelationDataIds: null, // 手动传入客户，适配联系人中已选客户的联系人  add by hgl { 2: ['customerId'] }
			defaultObjectFilters: null ,  // 各类型下默认的选择项, add by hgl { 3: '3002', }
			showAccountByAddr: false, // 根据客户地址选择相同客户 -- add by hgl
            parseTableOptions: null, // 右侧table组件配置项
            singleSelectObjApiNameMap: {}, // eg: { AccountObj: true } 代表 客户对象是单选
            objectListUrl: '/EM1HNCRM/API/v1/object/describe/service/findCrmObjectList'
        },

        events: {
            'click .j-astext':  '_asTextChangeHandle',
            'click .j-item':    '_switchTypeHandle',
            'click .j-latest':    '_showLatestHandle',
            'click .b-g-btn-cancel':  '_cancel',
            'click .b-g-btn':   '_enter',
			'click .j-add':     '_onAdd',
        },

        initialize: function () {
            var result = CrmObject.superclass.initialize.apply(this, arguments),
                me = this;
            this.after("show", function () {
                me.widgets && me.widgets.dt && me.widgets.dt.resize();
            });
            $(window).on('resize', this._onResize = function() {
                me.$('.content:first').height(me._calHeight());
            })
            return result;
        },

        _calHeight() {
            let num = 128; //64 * 2
            if($(window).width() < 1280) {
                num = 32; //16 * 2
            }
            return $(window).height() - num - 116;
        },

        render: function () {
            var result = CrmObject.superclass.render.call(this);
            this.widgets = {};
            this.ajax = {};
			this._preRender();
            return result;
        },

        _preRender: function () {
            if (this.get('showAsText')) {
                this.$('.b-g-btn').before('<div class="mn-checkbox-box footer-left fn-left"><i class="mn-checkbox-item j-astext"></i><label>' + $t("作为文本信息插入正文") + '<label></div>');
            }
            var allObj = this.get('allObjects') || []; // @宏伟增加
            if (allObj.length  === 1) {
                this.$('.left-nav').hide().siblings('.dt-content').css({
                    left: 0
                });
            }
		},
		
		/**
		 * initSelectRelateCrmObjectWrap
		 * @param {*} objData 
		 */
		initSelect: function(){
			var me = this;
			me.selectObject = FxUI.create({
                wrapper: this.$('.j-select-warp')[0],
                template: `<fx-input
								placeholder="${$t('搜索对象')}"
								suffix-icon="el-icon-search"
								size="small"
								clearable
								@input="change"
								v-model="value">
							</fx-input>`,
                data: function() {
                    return {
						value: "",
						options: []
					  }
				},
				computed:{
					
				},
				created:function(){
					this.options = me.get('allObjects');
				},
                methods: {
					filterMethod:function(val){
						// this.options = [
						// 	apiName: "MarketingEventObj",
						//  displayName: "市场活动20"
						// 	}];
					},
					change:function(kw){
						console.log("change:",kw);
						if(kw==''){
							me.$('.j-latest').show();
						}else{
							me.$('.j-latest').hide();
						}
						var list = [];
						me._reFormatData();
						var allObjects = me.get('allObjects');
						if(kw==''){
							list = allObjects;
						}else{
							_.each(allObjects, function(item){
								if(item.displayName.indexOf(kw)>=0){
									list.push(item);
								}
							});
						}
						me.$('.j-nav-list').html(itemtpl({
							list: list
						}));
						if(list.length==0){
							me.$('.no-result').show();
						}else{
							me.$('.no-result').hide();
						}
					},
					setValue:function(){

					},
					getValue:function(){

					},
					getOptions:function(){

					},
                },
                watch: {
                }
            });
		},

        /**
         * @desc 获取有权限查看的对象列表
         */
        _fetch: function () {
            var me = this;
            var objectData = me.get('objectData');
            var customFetch = me.get('customFetch');
            var successHandler = (objectList) => {
                if (objectList.length) {
                    me.set('objectList',objectList);
                    me._formatData(objectList);

                    var _allObjects = me.get('allObjects');
                    console.log("_allObjects:",_allObjects);
                    me.setContent(tpl({
                        list: _allObjects,
                        ShowHistory:ShowHistory,
                        height: me._calHeight()
                    }));
                    me.$('.j-nav-list').html(itemtpl({
                        list: _allObjects
                    }));

                    if(ShowHistory){
                        me._showLatestHandle();
                    }else{
                        // 渲染第一个列表
                        _allObjects.length && me.set('curObjectType', _allObjects[0].apiName);
                    }
                    //me.resizedialog();
                    me.initSelect();
                } else {
                    util.alert($t("没有可查看的CRM业务模块"), function () {
                        me.hide();
                    });
                }
            };

            if (customFetch) {
                this.ajax.fetchXhr = customFetch();
                this.ajax.fetchXhr.then(objectList => {
                    successHandler(objectList);
                });
                return;
            }
            
            this.ajax.fetchXhr = util.FHHApi({
                url: objectData && objectData.object_describe_api_name ? '/EM1HNCRM/API/v1/object/' + objectData.object_describe_api_name + '/controller/CRMObjectList' : me.get('objectListUrl'),
                data: {
                    isIncludeFieldDescribe: false,
                    isIncludeSystemObj: true,
                    isIncludeUnActived: false,
                    packageName: "CRM",
                    objectData: objectData
                },
                success: function (resData) {
                    if (resData.Result.StatusCode == 0) {
                        successHandler(resData.Value.objectList)
                    } else {
                        util.alert(resData.Result.FailureMessage);
                    }
                }
            }, {errorAlertModel: 1});
        },

        /**
         * @desc 格式化左侧导航数据
         * allObjects {{Array Object}} 所有左侧导航数据
         */
        _formatData: function (objects) {
            var optsAllObjects = this.get('allObjects'),
                excludeObjectTypes = this.get('excludeObjectTypes'),
                len = optsAllObjects.length,
                selectedItems = this.get('selectedItems'),
                allObjects = [];
            _.each(objects || [], function (obj) {
                if (!len || (len && optsAllObjects.indexOf(obj.apiName) > -1)) {
                    if(excludeObjectTypes && excludeObjectTypes.length && excludeObjectTypes.indexOf(obj.apiName) > -1) return;

                    var selData = (selectedItems[obj.apiName] || {}).data || [];

                    allObjects.push({
                        apiName: obj.apiName,
                        objectType: obj.apiName,
                        displayName: obj.displayName,
                        name:       obj.displayName,
                        selectedNum: selData.length
                    });
                }
            });

            this.set('allObjects', allObjects);
		},
        /**
         * @desc 重新计算左侧导航数据
         * allObjects {{Array Object}} 所有左侧导航数据
         */
        _reFormatData: function () {
            var allObjects = this.get('allObjects'),
                selectedItems = this.get('selectedItems'),
                _allObjects = [];
            _.each(allObjects || [], function (obj) {
                    var selData = (selectedItems[obj.apiName] || {}).data || [];

                    _allObjects.push({
                        apiName: obj.apiName,
                        objectType: obj.objectType,
                        displayName: obj.displayName,
                        name:       obj.name,
                        selectedNum: selData.length
                    });
            });

            this.set('allObjects', _allObjects);
		},
		_formatLatestData: function (latestObjects) {

		},

        /**
         * 当前选中的对象发生变化后的回调
         * 1、保存了当前表格已选中的数据 2、重置了场景(筛选)【并对场景进行了缓存】
         * @param newValue
         * @param oldValue
         */
        _onChangeCurObjectType: function (newValue, oldValue) {
            var me = this,
                curObject = me._getObjByObjectType(newValue);
            this._setSelectedItems(newValue, oldValue);
            this.$('.left-nav-inner .list-item').removeClass('selected').filter('[data-type="' + newValue + '"]').addClass('selected');
            if (!_.isNumber(newValue)) {
                me._initSelfTable(newValue, curObject.name || curObject.displayName);
                return;
            }
            if (curObject.terms) {
                this._resetTermSelect(curObject.terms);
            }
        },

        /**
         * 1、销毁并重建表格之前，先表格中的rememberData的数据缓存当前对象下的所有选中数据到selectedItems中
         * 2、并把selectedItems中对应的下一个要渲染的类型的选中数据放到curObjectSelectedItems中，供表格渲染完成就自动选中缓存的数据
         * @param curObjectType
         * @param preObjectType
         * @private
         */
        _setSelectedItems: function (curObjectType, preObjectType) {
            var items = this.get('selectedItems'),
                rememberData = [];

            if (this.widgets.dt) {
                rememberData = this.widgets.dt.getRemberData();
            }
            if (rememberData &&　rememberData.length && preObjectType!=0) {
                items[preObjectType] = this._formatRememberData(preObjectType, rememberData);
            } else { 
                if((this.widgets.dt && items[preObjectType])||preObjectType==0){ // 处理快速点击时，dt没有初始化，造成初始值失效 @add by hgl
                    try {
                        delete items[preObjectType];
                    } catch (e) {
                    }
                }
			}
			if(curObjectType!=preObjectType){
				this.set('curObjectSelectedItems', items[curObjectType]);
			}
        },

        /**
         * @desc 外部调用接口
         * @param {Object} opts
         */
        show: function (opts) {
            var result = CrmObject.superclass.show.call(this);
            _.each(opts, function (value, key) {
                this.set(key, value, {silent: true});
            }, this);
			this._fetch();
            return result;
        },

        /**
         * @desc 自定义对象的table
         */
        _initSelfTable: function(apiName, name) {
            var me = this;
            var checked = (me.get('curObjectSelectedItems') || {}).data || [];
            me.widgets.dt && me.widgets.dt.destroy();
            $('.dt-content', me.element).html('<div class="self-obj-wrap"></div>');
            me.widgets.dt = new Table({
                el: $('.self-obj-wrap', me.element),
                lineCheck: me.get('lineCheck'),
                trHandle: true,
                single: me.get('single') || me.get('singleSelectObjApiNameMap')[apiName],
                arrowResetSingle: true,
                tableOptions:{
                    arrowResetSingle: true,
                },
                disabledcfg: {
                    idKey: '_id',
                    data: _.map(me.get('disabledData'), function(item) {item._id = item.id; return item;})
                },
                checked: {
                    idKey: '_id',
                    data:  _.map(checked, function(item) {item._id = item.id; return item;}),
                    maxNum: me.get('maxNum')
                },
                zIndex: me.get('zIndex'),
                parseRequest: me.get('parseRequest'),
                parseTableOptions: me.get('parseTableOptions'),
                selectedItems: me.get('selectedItems'),
				showAccountByAddr: (apiName === 'AccountObj' && me.get('showAccountByAddr')),
                isFromPaas: me.get('isFromPaas'),
                trigger_info: me.get('trigger_info'),
                pluginOptions: me.get('pluginOptions'),
			    listType: 'selected'
            });
            me.widgets.dt.on('checkbox.click', function () {
                me._setSelectedNum(me.widgets.dt.getRemberData());
            });
            me.widgets.dt.on('dt.refresh', function (data, id) {
                me.widgets.dt.table.setParam({}, true);
            });
            me.widgets.dt.render([null, apiName], {
                name: name,
                id: me.get('selectedItems').AccountObj && me.get('selectedItems').AccountObj.data ? _.pluck(me.get('selectedItems').AccountObj.data, 'id') : []
            });
        },

        _setSelectedNum: function (rememberData) {
            if(this.get('forceSingle')) {
                this.attrs.selectedItems.value = {};
                this.$('.j-nav-list .selected-num').html('');
            }
            var len = (rememberData || []).length,
                lenStr = len ? '(' + len + ')' : '',
                curObjectType = this.get('curObjectType'),
                $num = this.$('.j-item[data-type=' + curObjectType + '] .selected-num');
            $num.text(lenStr);
			$num.toggleClass('data-selected', len > 0);
			
			this._setSelectedItems(curObjectType,curObjectType);
        },
        
        /**
         * 底部是否作为文本插入正文的监听函数
         * @param e
         * @private
         */
        _asTextChangeHandle: function (e) {
            var $target = $(e.currentTarget);
            $target.toggleClass('mn-selected');
            this.set('asText', $target.hasClass('mn-selected'));
            e.stopPropagation();
        },
        /**
         * 左侧对象列表选择的回调函数，同时设置curObjectType的值，从而触发后续一系列的联动处理
         * @param e
         * @private
         */
        _switchTypeHandle: function (e) {
            var $target = $(e.currentTarget);
            if (!$target.hasClass('selected')) {
                this.set('curObjectType', $target.data('type'));
            }
		},
        /**
         * 左侧对象列表选择"最近使用"的回调函数，同时设置curObjectType的值，从而触发后续一系列的联动处理
         * @param e
         * @private
         */
		_showLatestHandle: function(){
			var me = this;
			this.set('curObjectType', 0);
			this.$('.left-nav-inner .list-item').removeClass('selected');
			this.$('.j-latest').addClass('selected');
			this.$('.dt-content').empty().html('<div class="j-latest-checkbox-warp latest-checkbox-warp"></div>');
			me.selectObject = FxUI.create({
                wrapper: this.$('.j-latest-checkbox-warp')[0],
				template: `<div>
								<fx-checkbox-group v-model="checkedLatestRelatedObjects" @change="handleCheckedLatestRelatedObjectsChange">
									<fx-checkbox v-for="obj in latestRelatedObjects" :label="obj.value" :key="obj.key" class="latest_related_objects" :disabled="isDisabled(obj.value)" border>
									<div v-html="createlabel(obj.value)"></div>
									</fx-checkbox>
								</fx-checkbox-group>
								<div class="latest_related_objects_tips">{{createTips()}}~</div>
							</div>
							`,
                data: function() {
                    return {
						latestRelatedObjects: [],
						checkedLatestRelatedObjects: []
					  }
				},
				computed:{
					
				},
				created:function(){
					this.latestRelatedObjects = me.get('latestRelatedObjects')||this.latestRelatedObjects;
					
					if(!this.latestRelatedObjects||this.latestRelatedObjects.length==0){
						//如果有外部传入的数据，内部不需要再请求一次，这个接口很慢。
						this.getLatestRelatedObjects();
					}else{
						this.resetCheckedLatestRelatedObjects();
					}
				},
                methods: {
					createTips:function(){
						return this.latestRelatedObjects.length>0?$t('没有更多了'):$t('无数据')
					},
					isDisabled:function(str){
						var obj = JSON.parse(str);
						return obj&&obj.isDisabled;
					},
					createlabel:function(str){
						var obj = JSON.parse(str);
						return `<span class="select-relate-crm-object-select-tag-obj-name">[${_.escape(obj.objectDisplayName)}]</span><span class="select-relate-crm-object-select-tag-obj-dataname">${_.escape(obj.objectDataName)}</span>`;
					},

					getLatestRelatedObjects:function(){
						//拉取最近的数据
						var fxme = this;
						util.FHHApi({
							url: '/EM1HNCRM/API/v1/object/FeedRelation/service/getLatestRelatedObjects',
							data: {apiName:me.get('apiName')},
							success: function (res) {
								if (!res.Error) {
									if(res.Value&&res.Value.customTagList){
										var latestRelatedObjects = [];
										res.Value.customTagList.forEach(function(item){
											latestRelatedObjects.push({
												value:JSON.stringify({
													objectApiName: item.objectApiName,
													objectDataId: item.objectDataId,
													objectDisplayName: item.objectDisplayName,
													objectDataName: item.objectDataName,
													isDisabled:false
												}),
												label:`[${item.objectDisplayName}]${item.objectDataName}`,
												key:`${item.objectApiName}|${item.objectDataId}`,
												objectApiName:item.objectApiName,
												objectDataId:item.objectDataId,
											})
										})
										fxme.latestRelatedObjects = latestRelatedObjects;
										fxme.resetCheckedLatestRelatedObjects();
									}
								} else {
								}
							}
						});
					},
					handleCheckedLatestRelatedObjectsChange:function(val){
						console.log('handleCheckedLatestObjsChange:', val);
						this.resetSelectedItems();
					},
					//和外部交互，外部操作重制内部的已选数据
					resetCheckedLatestRelatedObjects:function(){
						var fxme = this;
						var selectedItems = me.get('selectedItems');
						var _checkedLatestRelatedObjects = [];
						fxme.latestRelatedObjects.forEach(function(item){
							// item.key
							if(selectedItems[item.objectApiName]){
								var selectedItem = _.findWhere(selectedItems[item.objectApiName].data,{id:item.objectDataId});
								if(selectedItem){
									_checkedLatestRelatedObjects.push(item.value);
									selectedItem.inLatest = true;
								}
							}
						});
						me.set('selectedItems',selectedItems);
						console.log('resetCheckedLatestRelatedObjects',selectedItems);
						fxme.checkedLatestRelatedObjects = _checkedLatestRelatedObjects;
					},
					//和外部交互，重置外部的已选数据
					resetSelectedItems:function(){
						var fxme = this;
						var selectedItems = me.get('selectedItems');
						//过滤掉已选中的最近数据
						var keys = _.keys(selectedItems);
						 _.each(keys,function(k){
							var item = selectedItems[k];
							item.data = _.filter(item.data,function(i){
								return i.inLatest!=true;
							});
							selectedItems[k] = item;
						})
						me.$('.selected-num').text('');
						fxme.checkedLatestRelatedObjects.forEach(function(item){
							var obj = JSON.parse(item);
							selectedItems[obj.objectApiName] = selectedItems[obj.objectApiName]||{
								data:[],
								displayName:obj.objectDisplayName
							};
							if(!_.findWhere(selectedItems[obj.objectApiName].data,{id:obj.objectDataId})){
								selectedItems[obj.objectApiName].data.push({
									id: obj.objectDataId,
									isDisabled: obj.isDisabled,
									name: obj.objectDataName,
									inLatest:true
								});

								// var len = selectedItems[obj.objectApiName].data.length,
								// 	lenStr = len ? '(' + len + ')' : '',
								// 	$num = me.$('.j-item[data-type=' + obj.objectApiName + '] .selected-num');
								// $num.text(lenStr);
								// $num.toggleClass('data-selected', len > 0);
							}

						});
						me.set('selectedItems',selectedItems);
						me._reFormatData();

						me.$('.j-nav-list').html(itemtpl({
							list: me.get('allObjects')
						}));

						console.log('resetSelectedItems',selectedItems);
					},
                },
                watch: {
                }
            });
		},
        /**
         * 会在选中的对象类型发生变化时，被调用
         * 重置场景(筛选)，同时设置标志位，让表格销毁重建
         * @param terms
         * @param needCache
         * @private
         */
        _resetTermSelect: function (terms, needCache) {
            var defaultItem = _.find(terms, function (item) {
                return item.isDefault;
            });
            defaultItem = defaultItem || terms[0];

            this.set('reRenderDtFlag', true);
            needCache && this._cacheCurTerms(terms);
            this.set('relationType', defaultItem.relation);
            this.set('curSelectedTermType', defaultItem.value);
        },

        _formatSelectedData: function (data) {
			var me = this;
			
            _.map(data, function (item, type) {
                item.objectType = item.objectType || type;
                item.isMyobject = item.isMyobject || !_.isNumber(type);
                item.name = item.name || item.displayName || me._getObjByObjectType(type).name;
            });

            return data;
        },

        _formatRememberData: function (type, rememberData) {
            if (rememberData.length) {
                var temp = {},
                    obj = this._getObjByObjectType(type);
                temp.objectType = type;
                temp.name = obj.name;
                temp.isMyobject = !_.isNumber(type); // 标记自定义对象
                temp.data = _.map(rememberData, function (_item) {
                    var _item_data = {};
                    _.each(_item, function(__item, __key) {
                        if (!temp.isMyobject) {
                            _item_data[__key] = __item ? (__item.FieldDisplayValue || __item.name || '') : '';
                        } else {
                            _item_data[__key] = __item || '';
                        }
                    });
                    return {
                        name: _item.__really_name || _item.name,
                        id: _item.id || _item._id,
                        data: _item_data
                    }
                });
                return temp;
            } else {
                return [];
            }
        },
        
        /**
         * 根据对象类型从allObjects中获取对象的相关信息
         * @param objectType
         * @returns {*|{}}
         * @private
         */
        _getObjByObjectType: function (objectType) {
            return _.find(this.get('allObjects'), function (item) {
                    return item.objectType == objectType;
                }) || {};
        },
  
        /**
         * 获取应该返回调用者的数据
         * @returns {{isAsText: *, selectedObj: *}}
         * @private
         */
        getSelectedData: function () {
            var curObjectType = this.get('curObjectType');
            this._setSelectedItems(curObjectType, curObjectType);
            return {
                isAsText: this.get('showAsText') && this.get('asText'),
                selectedItems: this._formatSelectedData(this.get('selectedItems')) || {}
            }
        },

        _cancel: function (e) {
            this.trigger('cancel');
            this.hide();
        },
        
        /**
         * @desc 新建
         */
        _onAdd: function() {
            var me = this;
            require.async(util.getCrmFilePath(me.get('curObjectType'), 'action'), function(Action) {
                me.addObj && me.addObj.destroy();
                me.addObj = new Action();
                me.addObj.on('refresh', function(data, id) {
                    me.widgets.dt.setParam({}, true);
                })
                me.addObj.add({
                    logType:    'fs',
                    logSubMod:  'crmobject',
                    From: 2,
                    zIndex: 10 * me.get('zIndex')
                });
            });
        },
        
        _enter: function (e) {
            var data = this.getSelectedData();
			var dataLen = 0;
			var selectedItemsKeys = _.keys(data.selectedItems);
			var _selectedItems = {};
			_.each(selectedItemsKeys, function(k){
				if(data.selectedItems[k].data.length>0){
					_selectedItems[k] = data.selectedItems[k];
					dataLen += data.selectedItems[k].data.length;
				}
			})
			data.selectedItems = _selectedItems;
            if ($('.left-nav .data-selected', this.$el).length > this.get('selectLimit')) {
                util.alert($t('最多添加业务模块个数', {
                    selectLimit: this.get('selectLimit')
                }));
                return;
            }
            if (this.get('dataMaxCount') && dataLen > this.get('dataMaxCount')) {
                util.alert($t("最多添加数据条数",{
                    dataMaxCoun:this.get('dataMaxCount')
                }));
                return;
            }
            console.log(data);
            this.trigger('success', data);
            this.hide();
        },

        hide: function() {
            var result = CrmObject.superclass.hide.call(this);
            this.destroy();
            return result;
        },

        destroy: function () {
            this.trigger('destroy');
            this._onResize && $(window).off('resize', this._onResize);
            this.addObj && this.addObj.destroy();
            _.each(this.widgets, function (widget) {
                widget && widget.destroy && widget.destroy();
            });
            return CrmObject.superclass.destroy.call(this);
        }
    });

    return function(param) {
        var instance = new CrmObject(param);

        instance.on('success', function(data) {
            param.methods.success && param.methods.success(data);
        });

        instance.on('cancel', function() {
            param.methods.cancel && param.methods.cancel();
        });

        instance.on('destroy', function() {
            param.methods.destroy && param.methods.destroy();
            param.methods.end && param.methods.end();
        });

        instance.show();
    }
});
