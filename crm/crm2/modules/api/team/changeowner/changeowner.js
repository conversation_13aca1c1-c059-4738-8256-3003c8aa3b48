/**
 * @desc 更换负责人，单例
 * @param {Object} param
 *      @param param.apiname      apiname
 *      @param param.dataList     对象数据
 *      @param param.excludeIds   排除人员列表
 * <AUTHOR>
 */
define(function(require, exports, module) {
    var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog'),
        Selector = require('crm-widget/selector/selector'),
        sharedInternals = require('../shared'),
        tpl = require('./template/content-html'),
        instance;

    var ChooseOwner = Dialog.extend({

        attrs: {
            className: 'crm-c-changeowner',
            title: $t("更换负责人"),
            labelTitle: $t("变更负责人为"),
            selectTitle: $t("选择员工"),
            errorText: $t("crm.请选择人员"),
            singleCked: true,
            showScroll: false,
            showBtns: true,
            remind: true,
            content: '',
            from: 'detail',
            cluePoolMembers: []
        },

        _isShowClean(field, apiname) {
            return field && field.is_required === false && /__c$/.test(apiname || '');//负责人字段为非必填时可以显示清空选项 当前仅支持自定义对象
        },

        // 设置label标题
        // setLableTitle: function(title) {
        //     $('.j-select-tit', this.element).html(this.get('labelTitle'));
        // },

        _show: function(data) {
            var me = this;
            var result = ChooseOwner.superclass.show.call(this);
            var apiname = data.apiname;
            var preOwnerSetting = CRM.getLocal('preOwnerSetting') && CRM.getLocal('preOwnerSetting')[apiname] || 1;
            var owner = (data.dataList && data.dataList[0] && data.dataList[0].owner__r) ? (data.dataList[0].owner__r.name || '') : '';
            var objName = _.escape(data.describe ? data.describe.display_name : '');
            var info = {
                team: $t("移出该{{name}}团队", {
                    name: objName
                }),
                // 支持自定义角色后，这里统一走团队成员
                // general: $t("变更为{{name}}普通成员", {
                //     name: objName
                // }),
                // 支持自定义角色后，这里统一走团队成员
                // generalTip: $t("普通成员是指该{{name}}协作人", {
                //     name: objName || '数据'
                // }),
                group: $t("变更为{{name}}团队成员", {
                    name: objName
                }),
                groupTip: $t("团队成员是指该{{name}}协作人", {
                    name: objName || $t('数据')
                })
            };
            var fw = data.describe && data.describe.fields && data.describe.fields["owner"];
            var showClean = this._isShowClean(fw, apiname);
            var ownerSearchQueryInfo = fw && fw.wheres || [];        
            var hasOwnerRange = ownerSearchQueryInfo.length && data.source!=='list' && data.dataList && data.dataList.length==1;
            this.setTitle($t("更换{{name}}负责人", {
                name: objName
            }));
            this.set('data', data);
            this.set('hasOwnerRange', hasOwnerRange);

            var ext = (data.objectDescribeExt || data.describe.objectDescribeExt)
            var hideTeam = (ext && ext.is_related_team_enabled === false);
            // this.setLableTitle();
            //客户更换负责人判断是否开启新商机
            if (apiname === "AccountObj") {
                
                util.getNewoppoConfig().done(function(status) {
					util.getSaleContractObj().done(function(isNewContract) {
						let objectList = me.getObjectList(status, isNewContract);
						me.$('.dialog-con').html(tpl({
							apiname: apiname,
							isNewOppo: status,
							preOwnerSetting: preOwnerSetting,
							owner: owner,
							info: info,
							objectList: objectList,
							showClean: showClean,
							hideTeam: hideTeam,
							roleList: data.roleList,
							hideOther: CRM.util.isGrayScale('CRM_CHANGEOWNER_EXCLUDE_OTHER'),
						}));
						if (hasOwnerRange) {
							me.fetchEmpRange(ownerSearchQueryInfo).then(empList => {
								me._initSelectBar(empList);
							})
						} else {
							me._initSelectBar();
						}
					})
                    
                })
            } else if (apiname === 'LeadsObj') {
                // 线索、线索池更换负责人 过滤线索池成员
                me.$('.dialog-con').html(tpl({
                    apiname: apiname,
                    preOwnerSetting: preOwnerSetting,
                    owner: owner,
                    info: info,
                    showClean: showClean,
                    hideTeam: hideTeam,
                    roleList: data.roleList,
                }));
                util.getPoolMembersByID(data.dataList[0].leads_pool_id).then(function(res) {
                    me.set('cluePoolMembers', res.employee || []);
                    if (hasOwnerRange) {
                        me.fetchEmpRange(ownerSearchQueryInfo).then(empList => {
                            me._initSelectBar(empList);
                        })
                    } else {
                        me._initSelectBar();
                    }
                })
            }  else if (apiname === 'PartnerObj') {
                // 合作伙伴更换负责人
                util.getNewoppoConfig().done(function(status) {
					util.getSaleContractObj().done(function(isNewContract) {
						let partneobjectList = me.getPartneObjectList(status, isNewContract);
						me.$('.dialog-con').html(tpl({
							apiname: apiname,
							preOwnerSetting: preOwnerSetting,
							owner: owner,
							info: info,
							objectList: partneobjectList,
							showClean: showClean,
							hideTeam: hideTeam,
							roleList: data.roleList,
						}));
						if (hasOwnerRange) {
							me.fetchEmpRange(ownerSearchQueryInfo).then(empList => {
								me._initSelectBar(empList);
							})
						} else {
							me._initSelectBar();
						}
					})
                })
            }else {
                me.$('.dialog-con').html(tpl({
                    apiname: apiname,
                    preOwnerSetting: preOwnerSetting,
                    owner: owner,
                    info: info,
                    showClean: showClean,
                    hideTeam: hideTeam,
                    roleList: data.roleList,
                }));
                if (hasOwnerRange) {
                    me.fetchEmpRange(ownerSearchQueryInfo).then(empList => {
                        me._initSelectBar(empList);
                    })
                } else {
                    me._initSelectBar();
                }
            }

            this._doLogs(data);
            return result;
        },

        show: async function(data) {
            const roleList = await sharedInternals.fetchRoleList(data.apiname);
            this._show({
                ...data,
                roleList,
            });
        },

        getPartneObjectList(isNewOppo, isNewContract) {
            let relatedObject = CRM.getLocal(`changeOwnerPartnerObject_${CRM.ea}`) || [];
            let objectList = [{
                label: $t("crm.销售线索"),
                value: 'LeadsObj',
                selected: _.contains(relatedObject, 'LeadsObj'),
            }, {
                label: $t("crm.客户"),
                value: 'AccountObj',
                selected: _.contains(relatedObject, 'AccountObj'),
            }, {
                label: $t("crm.联系人"),
                value: 'ContactObj',
                selected: _.contains(relatedObject, 'ContactObj'),
            },{
                label: $t("crm.销售订单"),
                value: 'SalesOrderObj',
                selected: _.contains(relatedObject, 'SalesOrderObj'),
            }]
            if (isNewOppo) {
                objectList.unshift({
                    label: $t("crm.商机2.0"),
                    value: 'NewOpportunityObj',
                    selected: _.contains(relatedObject, 'NewOpportunityObj'),
                })
            } else {
                objectList.unshift({
                    label: $t("crm.商机"),
                    value: 'OpportunityObj',
                    selected: _.contains(relatedObject, 'OpportunityObj'),
                })
            }
            if(isNewContract) {
              objectList.push({
                label: $t("销售合同"),
                value: 'SaleContractObj',
                selected: _.contains(relatedObject, 'SaleContractObj'),
              })
            } else {
              objectList.push({
                label: $t("crm.合同"),
                value: 'ContractObj',
                selected: _.contains(relatedObject, 'ContractObj'),
              })
            }
            return objectList;
        },

        getObjectList(isNewOppo, isNewContract) {
            let relatedObject = CRM.getLocal(`changeOwnerRelatedObject_${CRM.ea}`) || [];
            let objectList = [{
                label: $t("crm.联系人"),
                value: 'ContactObj',
                selected: _.contains(relatedObject, 'ContactObj'),
            }, {
                label: $t("crm.销售订单"),
                value: 'SalesOrderObj',
                selected: _.contains(relatedObject, 'SalesOrderObj'),
            }]
            if(isNewContract) {
              objectList.push({
                label: $t("销售合同"),
                value: 'SaleContractObj',
                selected: _.contains(relatedObject, 'SaleContractObj'),
              })
            } else {
              objectList.push({
                label: $t("crm.合同"),
                value: 'ContractObj',
                selected: _.contains(relatedObject, 'ContractObj'),
              })
            }
            if (isNewOppo) {
                objectList.unshift({
                    label: $t("crm.商机2.0"),
                    value: 'NewOpportunityObj',
                    selected: _.contains(relatedObject, 'NewOpportunityObj'),
                })
            } else {
                objectList.unshift({
                    label: $t("crm.商机"),
                    value: 'OpportunityObj',
                    selected: _.contains(relatedObject, 'OpportunityObj'),
                })
            }
            return objectList;
        },

        // 埋点统计
        _doLogs: function(data) {
            if (data.objectIds && data.objectIds.length == 1) {
                util.uploadLog(data.apiname, this.get('from'), {
                    eventId: 'changeowner',
                    eventType: 'cl'
                });
            }
        },

        fetchEmpRange: function (ownerSearchQueryInfo) {
            let me = this;
            return {
				then: function (resolve) {
                    var ids = [];
                    var offset = 0;
                    function _fetch() {
                        util.waiting($t('数据加载中') + '...');
                        util.FHHApi({
                            url: '/EM1HNCRM/API/v1/object/PersonnelObj/controller/RelatedList',
                            data: {
                                associated_object_describe_api_name: "PersonnelObj",
                                include_associated: true,
                                include_describe: false,
                                include_layout: false,
                                object_data: me.get('data').dataList[0],
                                relatedFieldName: "owner",
                                field_projection: ["user_id"],
                                search_query_info: JSON.stringify({
                                    limit:1000,
                                    offset,
                                    wheres: ownerSearchQueryInfo || []
                                })
                            },
                            success: function(res) {
                                if (res.Result.StatusCode !== 0) {
                                    util.waiting(false);
                                    util.alert(res.Result.FailureMessage);
                                    return;
                                }

                                ids = ids.concat(_.pluck(res.Value.dataList, "user_id"));

                                if (res.Value && res.Value.dataList && res.Value.dataList.length > 999) {
                                    offset = offset + 1000;
                                    _fetch();
                                } else {
                                    util.waiting(false);
                                    resolve(me.formatEmps(ids));
                                }
                            }
                        }, {
                            errorAlertModel: 1
                        });
                    }
                    _fetch();
                }
            }
        },

        formatEmps: function (ids) {
            var list = [];
			_.each(ids, function (id) {
				var item = util.getEmployeeById(id);
				item && list.push(item);
			})

			return list;
		},

        /**
         * @desc 初始化选人弹层
         */
        _initSelectBar(empList) {
            var me = this;
            var data = this.get('data');
            var actionFrom = (data && data.apiname) ?
                [data.apiname, data._from || 'detail', 'changeowner'].join('_').toLowerCase() :
                '';

            let departmentId = FS.util.getUserAttribute('crmOrgByLonginer') && FS.contacts.getMainOrganizationIdByEmpId(CRM.curEmpId);//根据当前登录人归属组织显示
            me.selectbar = new Selector({
                $wrap: $('.select-box', me.element),
                member: departmentId ? {departmentId} : true,
                excludeItems: {
                    member: me.excludeEmp(data, empList)
                },
                zIndex: 2000,
                single: me.get('singleCked'),
                label: me.get('selectTitle'),
                defaultSelectedItems: me.getDefaultData(),
                action_from: actionFrom
            });

            me.selectbar.on('addItem', function() {
                util.hideErrmsg($('.select-box', me.element));
            });

            me.resizedialog();
        },

        /**
         * @desc 排除员工
         */
        excludeEmp: function(data, empList=[]) {
            var allEmployees = FS.contacts.getAllEmployees();
            var excludeItems = _.map(data.excludeIds || data.execIds || [], function(item) {
                return item * 1;
            });
            let range = this.get('cluePoolMembers').length ? this.get('cluePoolMembers') : empList; //人员范围
            var exclude = this.get('hasOwnerRange')?_.reject(allEmployees, function (a) { //将不在人员范围的id传入选人组件 否则构建组件树比较耗费性能
                return _.find(range, (b) => b.id===a.id);
            }).map(item => item.id*1):[];
            return excludeItems.concat(exclude);
        },

        /**
         * @desc 获取默认值
         */
        getDefaultData: function() {
            var ids = this.get('data').defaultIds || [];
            return {
                member: ids
            };
        },

        events: {
            'click .b-g-btn-cancel': 'hide',
            'click .b-g-btn': 'onEnter',
            'click .j-set-pre-owner .mn-radio-item': '_setPreOwnerHandle',
            'click .j-check-all-objs': '_checkAllObjs',
            'click .general-detail .disabled-selected': '_checkDisabled',
            'click .other-obj .right-con .mn-checkbox-item': '_checkObj',
            'click .j-switch .mn-radio-item': '_switchHandle'
        },

        _switchHandle: function(e) {
            var $target = $(e.currentTarget);
            e.stopPropagation();

            if($target.hasClass('mn-selected')) return;

            var $box = $target.closest('.mn-radio-box');
            $box.find('.mn-selected').removeClass('mn-selected');
            $target.addClass('mn-selected');

            this.__cleanOwner = $target.hasClass('hidden-inner')
            this.$('.j-inner').toggle(!this.__cleanOwner);


            //this.resizedialog();
        },

        _checkDisabled: function(e) {
            e.stopPropagation();
        },

        _checkObj: function(e) {
            var $currentTarget = $(e.currentTarget);
            var len = e.currentTarget.parentNode.parentNode.children.length;
            $currentTarget.toggleClass('mn-selected');

            this.$('.check-all .j-check-all-objs').html(this.$('.other-obj .right-con .mn-checkbox-item.mn-selected').length < len ? $t("全选") : $t("反选"));
            e.stopPropagation();
        },
        _checkAllObjs: function(e) {
            var $currentTarget = $(e.currentTarget);
            if ($currentTarget.html() === $t("全选")) {
                this.$('.other-obj .mn-checkbox-item').addClass('mn-selected');
                $currentTarget.html($t("反选"))
                e.stopPropagation();
                return
            } else if ($currentTarget.html() === $t("反选")) {
                this.$('.other-obj .mn-checkbox-item').removeClass('mn-selected');
                $currentTarget.html($t("全选"))
                e.stopPropagation();
                return
            }
        },

        _setPreOwnerHandle: function(e) {
            var $target = $(e.currentTarget),
                $box = $target.closest('.mn-radio-box');
            e.stopPropagation();
            if ($target.hasClass('mn-selected')) {
                return;
            }
            $box.find('.mn-selected').removeClass('mn-selected');
            $target.addClass('mn-selected');
            this.$('.general-detail').toggle(this.$('.j-to-general').hasClass('mn-selected'));
            this.resizedialog();
        },

        _getOldOwnerOperationType: function() {
            return this.$('.j-set-pre-owner .mn-selected').closest('.radio-item').data('type');
        },

        _getOldOwnerTeamMemberTypeList: function() {
            var types = [];
            this.$('.general-detail .role .mn-selected').each(function(index, el) {
                types.push($(el).closest('.checkbox-item').data('type') + '');
            });
            return types;
        },

        _getOldOwnerPermissionType: function() {
            return this.$('.general-detail .auth .mn-selected').closest('.radio-item').data('type');
        },

        _getRelationList: function() {
            var list = [];
            this.$('.other-obj .right-con .mn-selected').each(function(index, item) {
                list.push($(item).attr('data-type'));

            });
            return list;
        },

        // 提交事件
        onEnter: function(e) {
            if(this.__cleanOwner) return this.handleClean(e);

            var me = this;
            var data = this.get('data');
            var apiname = data.apiname;
            var defaultIds = data.defaultIds || [];
            var ids = this.selectbar.getValue('member');
            var strategy = me._getOldOwnerOperationType() + '';
            var roleList = me._getOldOwnerTeamMemberTypeList();
            // var roleList = _.contains(['AccountObj', 'OpportunityObj', 'NewOpportunityObj'], apiname) ? me._getOldOwnerTeamMemberTypeList() : ['4'];

            if (ids.length == 0) {
                util.showErrmsg(this.$('.select-box'), this.get('errorText'));
                return;
            }

            if (defaultIds.join(',') == ids.join(',')) { // 没有修改
                this.hide();
                return;
            }

            // 支持自定义角色
            if (strategy === '2' && !roleList.length) {
                util.alert($t('paas.crm.api.team.least_one_role', null, '至少选择一个团队角色'));
                return;
            }

            // if (strategy === '2' && _.contains(['AccountObj', 'OpportunityObj', 'NewOpportunityObj'], apiname) && !roleList.length) {
            //     util.alert($t('至少选择一个团队角色'));
            //     return;
            // }

            var local = {};
            local[apiname] = strategy;
            CRM.setLocal('preOwnerSetting', _.extend({}, CRM.getLocal('preOwnerSetting'), local));

            var param = {
                Data: _.map(data.dataList, function(a) {
                    return {
                        objectDataId: a._id,
                        ownerId: ids
                    }
                }),
                oldOwnerStrategy: strategy,
                oldOwnerTeamMemberPermissionType: me._getOldOwnerPermissionType() + '',
                oldOwnerTeamMemberRoleList: roleList,
                relateObjectApiNames: me._getRelationList()
            }
            if(apiname == 'PartnerObj') {
                CRM.setLocal(`changeOwnerPartnerObject_${CRM.ea}`, param.relateObjectApiNames);
            } else {
                CRM.setLocal(`changeOwnerRelatedObject_${CRM.ea}`, param.relateObjectApiNames);
            }

            var url = '/EM1HNCRM/API/v1/object/' + apiname + '/action/' + (data.async_button_apiname || 'ChangeOwner');

            util.FHHApi({
                url: url,
                data: param,
                success: function(res) {
                    if (me.destroyed) return;
                    me.hide();
                    if (res.Result.StatusCode === 0) {
                        if (data.async_button_apiname) {
                            data.jobId = res.Value.jobId;
                            CRM.api.list_batchbtn_operate_query(data);
                        } else {
                            me._successHandle(res.Value, url, param, data);
                        }
                    } else {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                errorAlertModel: 1,
                submitSelector: $(e.target)
            })
        },

        //830新增清空负责人
        handleClean(e) {
            var me = this;
            var data = this.get('data');
            var url = '/EM1HNCRM/API/v1/object/' + data.apiname + '/action/' + (data.async_button_apiname || 'ChangeOwner');
            var param = {
                Data:  _.map(data.dataList, function(a) {
                    return {
                        objectDataId: a._id,
                        ownerId: []
                    }
                })
            }
            util.FHHApi({
                url: url,
                data: param,
                success: function(res) {
                    if (me.destroyed) return;
                    me.hide();
                    if (res.Result.StatusCode === 0) {
                        if (data.async_button_apiname) {
                            data.jobId = res.Value.jobId;
                            CRM.api.list_batchbtn_operate_query(data);
                        } else {
                            me._successHandle(res.Value, url, param, data);
                        }
                    } else {
                        util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                errorAlertModel: 1,
                submitSelector: $(e.target)
            })
        },

        //745增加功能 更换负责人触发后动作，决定是否能更换成功
        _successHandle: function(obj, url, param, data) {
            var me = this;
            var ov = obj.validationMessage;
            if(ov && ov.nonBlockMessages && ov.nonBlockMessages.length) { //不阻断，还可以继续保存
                me._showValidTips(ov.nonBlockMessages, url, param, data);
                return;
            }

            if(ov && ov.blockMessages && ov.blockMessages.length) { //阻断 不允许更换
                me._showValidTips(ov.blockMessages);
                return;
            }

            
            util.remindSuccess();
            data.success();
        },

        _showValidTips: function(messages, url, param, data) {
            var me = this;
            var strs = ['<div class="crm-api-ruletip">'];
            var type = param ? 'orange' : 'red';
            _.each(messages, function(content) {
                strs.push('<div class="tip-item"><span class="tip-icon tip-icon-' + type + '">!</span><div class="tip-content">' + _.escape(content) + '</div></div>');
            })
            strs.push('</div>');
            var html = strs.join('');

            if(param) {
                var confirm = util.confirm(html, $t('提示'), function(e) {
                    //继续保存不再走验证规则
                    param.skipPreAction = true;
                    util.FHHApi({
                        url: url,
                        data: param,
                        success: function(res) {
                            confirm.destroy();
                            if (res.Result.StatusCode === 0) {
                                util.remindSuccess();
                                data.success();
                            } else {
                                util.alert(res.Result.FailureMessage);
                            }
                        }
                    }, {
                        errorAlertModel: 1,
                        submitSelector: confirm.$('.b-g-btn')
                    })
                }, {
                    btnLabel: {
                        confirm: $t('继续保存')
                    }
                })
            } else {
                util.alert(html);
            }
        },

        /**
         * @desc 隐藏
         */
        hide: function() {
            var result = ChooseOwner.superclass.hide.call(this);
            this.destroy();
            return result;
        },

        // 销毁
        destroy: function() {
            this.selectbar && this.selectbar.destroy();
            this.selectbar = null;
            return ChooseOwner.superclass.destroy.call(this);
        }
    });

    return function(param) {
      	new ChooseOwner().show(param);
    }
});
