/**
 * @desc  添加相关团队成员，单例
 * @param {Object} param
 *      @param apiname      apiname
 *      @param dataList     对象数据
 *      @param ownerId      员工id
 * <AUTHOR>
 */
define(function (require, exports, module) {
	var util = require("crm-modules/common/util"),
		Dialog = require("crm-widget/dialog/dialog"),
		Selector = require("crm-widget/selector/selector"),
		sharedInternals = require('../shared'),
		tpl = require("./template/add-html");

	// require('icmanage/app.js');

	var SelectorConfig = {};
	var paasCRMTeamLimit = CRM.util.getUserAttribute('paasCRMTeamLimit');
	if(paasCRMTeamLimit) {
		SelectorConfig.maxNum = paasCRMTeamLimit;
	}

	var AddDialog = Dialog.extend({
		attrs: {
			title: $t("添加团队成员"),
			className: "crm-c-salesteam-add-dialog",
			showBtns: true,
			showScroll: false,
			memberType: 1,
			objectType: "",
			content: "",
			btnName: {
				save: $t("添 加"),
			},
		},
		events: {
			"click .b-g-btn": "_enter",
			"click .b-g-btn-cancel": "_cancel",
			"click .disabled-selected": "_disabledSelected",
			"click .j-check-all-objs": "_checkAllObjs",
			"click .other-obj .right-con .mn-checkbox-item": "_checkObj",
		},
		render: function () {
      var me = this;
			var isOut =  CRM.util.isConnectApp() ? true : me.get("isOut"); //如果互联应用则操作外部员工
      var result = AddDialog.superclass.render.call(me);
      var apiname = me.get("apiname");
	
      console.log("添加相关团队2.0版本");

			// 是否包含子部门
			me.is_cascade = false;

			me.showLoading();
			sharedInternals.fetchRoleList(apiname, isOut).done((roleList) => {
				me.hideLoading();
				util.getNewoppoConfig().done(function (status) {
					util.getSaleContractObj().done(function(isNewContract) {
						if (me.destroyed) return;
						me.isOut = isOut;
						me.$(".dialog-con").html(
							tpl({
								apiname: apiname,
								isNewOppo: status,
								roleList: roleList,
								isNewContract: isNewContract,
								isOut: me.isOut,
								// 历史原因，隐藏外部相关团队受crmChangeOwnerExcludeOther灰度项控制，与小程序不一致
								// 新增外部相关团队受crmChangeOutOwnerExcludeOther灰度项控制，以便和小程序保持一致
								// 未来把灰度项改为后台配置，实现两端一致
								hideOther: me.get("isOut") ? CRM.util.isGrayScale('CRM_ADDSALESTEAM_EXCLUDE_OTHER') || CRM.util.isGrayScale('CRM_CHANGE_OUT_OWNER_EXCLUDE_OTHER') : CRM.util.isGrayScale('CRM_ADDSALESTEAM_EXCLUDE_OTHER'),
							})
						);
						me.widgets = {};
						me.ajax = {};
						me.isOut ? me._initOutWidget() : me._initWidget();
						me.initDependPartnerBtn(me.isOut);
						return result;
					});
				});

			});
    },
		initDependPartnerBtn(isOut) {
			let wrapper = this.$('.related-team-btn');
			if (!isOut || !wrapper.length) {
				return;
			}

			this.widgets.dependPartnerBtn = FxUI.create({
				wrapper: wrapper[0],
				template:`
					<fx-tooltip class="item" :effect="effectType" :content="title" placement="top">
						<fx-switch v-model="value" :disabled="isCkeck"   size="mini"></fx-switch>
					</fx-tooltip>
				`,
				data: function() {
					return {
						value: false,
						isCkeck: true,
						effectType:'dark',
						title:$t("请先勾选其他相关团队")
					}
				},
				methods: {
					getValue() {
						return this.value;
					}
				}
			});
		},

		_checkObj: function (e) {
			var $currentTarget = $(e.currentTarget);
			$currentTarget.toggleClass("mn-selected");

			this.$(".check-all .j-check-all-objs").html(
				this.$(".other-obj .right-con .mn-checkbox-item.mn-selected")
					.length < 4
					? $t("全选")
					: $t("反选")
			);
			if(this.widgets.dependPartnerBtn) {
				this.widgets.dependPartnerBtn.isCkeck = this.$(".other-obj .right-con .mn-checkbox-item.mn-selected").length > 0 ? false : true;
				if(!this.$(".other-obj .right-con .mn-checkbox-item.mn-selected").length > 0 ) {
					this.widgets.dependPartnerBtn.value = false;
				} 
			}
			e.stopPropagation();
		},

		_checkAllObjs: function (e) {
			var $currentTarget = $(e.currentTarget);
			if ($currentTarget.html() === $t("全选")) {
				this.$(".other-obj .mn-checkbox-item").addClass("mn-selected");
				$currentTarget.html($t("反选"));
				if(this.widgets.dependPartnerBtn) {
					this.widgets.dependPartnerBtn.isCkeck =false;
				}
				e.stopPropagation();
				return;
			} else if ($currentTarget.html() === $t("反选")) {
				this.$(".other-obj .mn-checkbox-item").removeClass(
					"mn-selected"
				);
				$currentTarget.html($t("全选"));
				if(this.widgets.dependPartnerBtn) {
					this.widgets.dependPartnerBtn.isCkeck = true;
					this.widgets.dependPartnerBtn.value = false;
				}
				e.stopPropagation();
				return;
			}
		},
		_initWidget: function () {
			let me = this;
			let departmentId = FS.util.getUserAttribute('crmOrgByLonginer') && FS.contacts.getMainOrganizationIdByEmpId(CRM.curEmpId);//根据当前登录人归属组织显示
			let paasDisableTeamRoleUsergroup = FS.util.getUserAttribute('paasDisableTeamRoleUsergroup'); // 隐藏角色和用户组
			var ownerId = this.get("ownerId");
			this.widgets.selectBar = new Selector({
				$wrap: this.$(".select-bar"),
				label: $t("选择员工"),
				member:departmentId ? {
					departmentId,
				} : true,
				group: departmentId ? {
					departmentId,
					canSelectRoot: true
				} : true,
				role: paasDisableTeamRoleUsergroup ? false : true,
				usergroup: paasDisableTeamRoleUsergroup ? false : true,
				groupIncludeChildrenStatus: me.is_cascade ? 2 : 1,
				excludeItems: {
					member: ownerId ? [ownerId] : [],
				},
				single: false,
				...SelectorConfig
				// zIndex: 2000
			});
			this.widgets.selectBar.on('includeGroupChildrenStatusChange', function (status) {
				me.is_cascade = status;
			})

			this.resetPosition();
		},
		//添加外部
		_initOutWidget: function () {
			const me = this;
			// if (CRM.get("outTeamMember")) {
			// 	me._initOutWidgetHandle();
			// } else {
			// 	me._getOutTeamData().done(() => {
			// 		me._initOutWidgetHandle();
			// 	});
			// }
			me._getOutTeamData().done(() => {
				me._initOutWidgetHandle();
			});
		},
		_initIcSelectorV2() {
			var me = this;
			var ownerId = me.get('ownerId');
			seajs.use("icmanage-modules/icselectorV2/icselector", function (mod) {
				const SelectWarp = Vue.extend({
					template:
						`<icSelector v-bind="icsProps" @change="icsChange" :zIndex="${me.get('zIndex')}"></icSelector>`,
					components: {
						icSelector: mod.icSelectorInput,
					},
					data() {
						return {
							icsProps: {
								selectorOpts: {
									addBtnLabel: $t("选择互联用户"),
									tabs: CRM.util.getUserAttribute('paasCRMOuterIds') ? ["outerUids"] : [
										"outerUids",
										"outerTenantIds",
										"outerTgroupIds",
										"outerRoleIds"
									],
									excludeItems: {
										outerUids: ownerId ? [ownerId] : [],
										outerRoleIds: ['er_enterprise', 'er_personal'],
									},
									...SelectorConfig
								},
							},
							icsChange(data) {
								var allData = me.widgets.selectBar.$children[0].getValueOfLine() || {};
								me.outData = data;
								me.outDataEas = allData.outerUids || [];
							},
						};
					},
				});
				me.widgets.selectBar = new SelectWarp().$mount(this.$(".select-bar")[0]);
			});
		},
		_initOutSelector() {
			const me=this,
                ownerId = this.get('ownerId'),
                outTeamMemberTree=CRM.get('outTeamMember').outTeamMemberTree,
                outTeamMemberList=CRM.get('outTeamMember').outTeamMemberList;

			//业务侧临时处理，过滤掉tree结果里掉负责人数据
			let parsedTreeData = [];
			_.each(outTeamMemberTree, (p) => {
				let arr = _.filter(p.children, (d) => {
					return d.outUserId !== ownerId
				})
				let item = _.extend({}, p, {
					children: arr
				})
				parsedTreeData.push(item)
			})

            this.widgets.selectBar = new Selector({
                $wrap: this.$('.select-bar'),
                label: $t("选择外部团队成员"),
                excludeItems: {
                    'outTeamMember': ownerId ? [ownerId] : []
                },
                tabs:[{
                    id: 'outTeamMember',
                    type: 'switch',
                    data:[{
                        title: $t("按人员查看"),
                        type: "list",
                        selectAll: true,
                        data:outTeamMemberList,
                    },{
                        title: $t("按公司查看"),
                        type: "tree",
                        cascade: true,
                        map: function () {},
                        selectAll: true,
                        data:{
                            id:'0',
                            name:$t('外部团队'),
							children: parsedTreeData
                        }
                    }]
                }],
				...SelectorConfig
            });
		},
		_initOutWidgetHandle() {
			if (CRM.util.isConnectApp()) {
				this._initOutSelector();
			} else {
				this._initIcSelectorV2();// v2版互联选人组件
			}
			this.resetPosition();
		},

		// 获取外部企业及成员
		getOutMember: function () {
			var me = this;
			return new Promise((resolve, reject) => {
				CRM.util.getOuterContacts().then(function (data1, data2) {
					resolve(data1, data2);
				});
			});
		},

		//
		_getAuth: function () {
			var $auth = this.$(".auth .mn-selected");
			return $auth.closest(".radio-item").data("type");
		},
		_getRoles: function () {
			var roles = [];
			this.$(".role .mn-selected").each(function (index, el) {
				roles.push($(el).closest(".checkbox-item").data("type") + "");
			});
			return roles;
		},
		_getOtherObjects: function () {
			var objs = [];
			this.$(".other-obj .right-con .mn-selected").each(function (
				index,
				el
			) {
				var $el = $(el);
				objs.push($el.closest(".checkbox-item").data("type"));
			});
			return objs;
		},
		_disabledSelected: function (e) {
			e.stopPropagation();
			return false;
		},
		_validator: function (reqData) {
			const me = this;
			var params = reqData || {};
			var emps = params.teamMemberInfos;
			if (!emps || !emps.length) {
				util.alert($t("请选择要添加的员工"));
				return false;
			}

			const roles = this._getRoles();
			if (!roles.length) {
				util.alert($t("请设置团队成员的角色"));
				return false;
			}
			return true;
		},

		_enter: function (e) {
			const me = this;
			var reqData = this._getAddPostData();
			var successFn = this.get("success");
			if (!me._validator(reqData)) return;
			util.FHHApi(
				{
					url:
						"/EM1HNCRM/API/v1/object/" +
						me.get("apiname") +
						"/action/" +
						(_innerParam.async_button_apiname || "AddTeamMember"),
					data: reqData,
					success: function (resData) {
						if (me.destroyed) return;
						
						if (resData.Result.StatusCode == 0) {
							me.hide();
							if (_innerParam.async_button_apiname) {
								_innerParam.jobId = resData.Value.jobId;
								CRM.api.list_batchbtn_operate_query(
									_innerParam
								);
							} else {
								util.remindSuccess();
								successFn();
							}
						} else {
							util.alert(resData.Result.FailureMessage);
						}
					},
				},
				{
					errorAlertModel: 1,
					submitSelector: me.$(".b-g-btn"),
				}
			);
		},

		_getAddPostData() {
			const me = this;
			const types = me.isOut
				? ['outerUids','','','','outerRoleIds', 'outerTenantIds','outerTgroupIds']
				: ["member", "usergroup", "group", "", "role"]; //index对应teamMemberType
			let teamMemberInfos = [];
			types.map((type, index) => {
				if (!type) {
					return;
				}
				let list = [];
				if (me.isOut) {
					if (CRM.util.isConnectApp()) {
						let arr=CRM.get('outTeamMember').outTeamMemberList;
						if (index == 0) {
							var selectData = me.widgets.selectBar.getValue('outTeamMember');
							_.each(selectData,(data)=>{
								let item=_.findWhere(arr,{id:data});
								list.push({
									userId: item.id+"",
									outTenantId: item.tenantId+""
								})
							})
						}
					}else {
						if (index !== 0 || me.outDataEas && me.outDataEas.length) {
							if (me.outData && _.isArray(me.outData[type])) {
								list = me.outData[type].map((id, ix) => {
									let item;
									id = id + '';
									if (index == 0) {
										item = me.outDataEas[ix];
									}else if(index == 5) {
										item = {
											groupId: id
										}
									}
									return {
										userId: id,
										outTenantId: item ? item.groupId+"" : ''
									}
								});
							}
						}
					}
				}else {
					list = me.widgets.selectBar.getValue(type);
				}
				if (list && list.length) {
					teamMemberInfos.push({
						// dataIDs: _.pluck(me.get('dataList'), '_id'),
						teamMemberEmployee: me.isOut ? [] : list,
						teamMemberPermissionType: me._getAuth() + "",
						teamMemberRole: "", // 写死
						teamMemberRoleList: me._getRoles(),
						teamMemberType: index,
						outTeamMemberEmployee: me.isOut ? list : [],
						teamMemberDeptCascade: type == 'group' ? (me.is_cascade ? "1" : "0") : void 0
					});
				}
			});
			return {
				teamMemberInfos,
				otherObjects: me._getOtherObjects(),
				dataIDs: _.pluck(me.get("dataList"), "_id"),
				dependPartner: this.widgets.dependPartnerBtn && this.widgets.dependPartnerBtn.getValue()
			};
		},

		_getOutTeamData: function () {
			return new Promise((resolve,reject)=>{
                CRM.api.get_outemployees({
					pageSize: CRM.util.isConnectApp() ? 1000 : void 0,
                    callback(outTeamMember) {
                        outTeamMember && resolve(outTeamMember);
                    },
                    error(res) {
                        res && util.alert(res.Result.FailureMessage);
                    }
                })
            })
		},

		_cancel: function (e) {
			this.hide();
		},

		hide: function () {
			var result = AddDialog.superclass.hide.call(this);
			this.destroy();
			return result;
		},
		destroy: function () {
			_.each(this.widgets, function (widget) {
				widget && widget.destroy && widget.destroy();
			});
			return AddDialog.superclass.destroy.call(this);
		},
	});

	var _innerParam;
	return function (param) {
		_innerParam = param;
		new AddDialog(param).show();
	};
});
