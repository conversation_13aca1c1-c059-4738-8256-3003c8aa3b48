/*
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2023-03-08 10:38:55
 * @LastEditors: LiAng
 * @LastEditTime: 2025-07-16 10:54:23
 */
define(function(require, exports, module) {
    return function(params) {
        let {instances, loggers, events} = params;

        return {
            /**
             * 通过appId获取pluginService实例
             *
             * @param {string} appId 
             * @returns 
             */
            getInstance(appId) {
                return instances[appId];
            },
    
            /**
             * 获取全部pluginService实例
             *
             * @returns 
             */
            getAllInstance() {
                Object.keys(instances).forEach(key => {
                    let instance = instances[key];

                    if (instance && instance.destroyed === true) {
                        delete instances[key];
                    }
                });

                return instances;
            },
    
            /**
             * 获取第一个pluginService实例
             *
             * @returns 
             */
            getFirstInstance() {
                return instances[Object.keys(instances)[0]];
            }
        }
    }
})