/*
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2023-03-08 10:38:55
 * @LastEditors: LiAng
 * @LastEditTime: 2025-07-15 18:13:38
 */
define(function(require, exports, module) {
    return function(params) {
        let {instances, loggers, events} = params;
        let EVENT_NAME = 'polling.sfa_send_user.change';

        return {
            /**
             * 初始化日志
             *
             * @param {string} appId 
             * @returns 
             */
            async initLog(opts) {
                let {appId} = opts;
                let logConfig = await this.getLogConfig(false, opts);
                let log = await CRM.api.pluginLog(logConfig);
                let logger = log && log.getLogger(appId);

                loggers[appId] = logger;
                this.addEvent(appId, opts);

                return logger;
            },

            /**
             * 获取日志配置
             * @param {boolean} noCache 不取缓存，通过接口获取
             * @returns 
             */
            async getLogConfig(noCache, opts) {
                const KEY = 'send_user';
                const DB_CONFIG = {
                    name: this.getLogDBName() // indexedDB名称
                };

                let parseConfig = data => {
                    let result = {
                        isSendLog: false,
                        sendLogFilter: [],
                        isRecordLog: !!opts.isRecordLog,
                        isPrintLog: !!opts.isPrintLog
                    };

                    try {
                        let obj = JSON.parse(data);

                        // mock
                        // obj = {
                        //     allUser: true,
                        //     user: ['79337'],
                        //     filters: [
                        //         {
                        //             "field_name": "createTime",
                        //             "operator": "LTE",
                        //             "field_values": 1674893789003
                        //         }
                        //     ],
                        //     id: 1674893789003
                        // }

                        // 防止重复上报，有id的配置可以作为上报日志的过滤条件，id值为时间戳
                        if (obj.id) {
                            let key = 'crm_send_user';
                            let _id = localStorage.getItem(key);

                            _id = typeof _id === 'string' ? +_id : _id;
                            obj.id = typeof obj.id === 'string' ? +obj.id : obj.id;

                            if (obj.id !== _id) {
                                obj.filters = this.formatFilters(obj.filters);

                                let state = obj.allUser 
                                    || obj.user.includes(CRM.curEmpId + '') 
                                    || ($.cookie('EROuterUid') && obj.user.includes($.cookie('EROuterUid') + ''));

                                result = Object.assign({}, result, {
                                    isSendLog: state,
                                    sendLogFilter: obj.filters
                                });

                                localStorage.setItem(key, obj.id);
                            }
                        }
                    }
                    catch(e) {}

                    return result;
                }

                if (!noCache && CRM._cache && CRM._cache.crmAllConfig && CRM._cache.crmAllConfig.length) {
                    let config = CRM._cache.crmAllConfig.filter(item => item.key === KEY)[0];

                    if (config) {
                        return Object.assign({}, parseConfig(config.value), {
                            dbConfig: DB_CONFIG,
                            objectApiName: opts.objectApiName
                        });
                    }
                }

                let value;

                try {
                    value = await CRM.util.getConfigValue(KEY);
                }
                catch(e) {
                    value = '{"allUser":false,"user":[],"filter":[]}';
                }

                return Object.assign({}, parseConfig(value), {
                    dbConfig: DB_CONFIG,
                    objectApiName: opts.objectApiName
                });
            },

            /**
             * 获取DB名称
             */
            getLogDBName() {
                const isCross = this.isCross();
                // 上游取enterpriseAccount，下游取ERUpstreamEa
                const ea = isCross ? $.cookie('ERUpstreamEa') : CRM?.curEmp?.enterpriseAccount;
                // 上游取employeeId，下游取EROuterUid
                const empId = isCross ? $.cookie('EROuterUid') : CRM?.curEmp?.employeeId;

                return `__CRM__${ea}_${empId}`;
            },

            // 是否下游
            isCross() {
                return window?.PAAS?.app?.isCross();
            },

            /**
             * 格式化日志筛选条件数据
             *
             * @param {array} filters 
             * @returns 
             */
            formatFilters(filters) {
                filters.forEach(item => {
                    // 确保创建时间的值为number
                    if (item.field_name === 'createTime') {
                        item.field_values = +item.field_values;
                    }
                })

                return filters;
            },

            /**
             * 上报日志
             * @param {string} appId 
             */
            async uploadLog(appId, opts) {
                let logger = this.getLogger(appId);

                if (logger) {
                    let logConfig = await this.getLogConfig(true, opts);

                    logger.uploadLog(logConfig);
                }
            },

            /**
             * 通过appId获取log实例
             *
             * @param {string} appId 
             * @returns 
             */
            getLogger(appId) {
                return loggers[appId];
            },

            /**
             * 监听polling
             */
            addEvent(appId, opts) {
                if (appId in events) {
                    return;
                }

                events[appId] = FS.MEDIATOR.always(EVENT_NAME, (data, mode) => {
                    this.uploadLog(appId, opts);
                    mode.saveToStorage();
                });
            },

            /**
             * 移除监听
             */
            removeEvent() {
                FS.MEDIATOR.off(EVENT_NAME);
            },

            /**
             * 设置是否打印日志
             * @param {Boolean} state 
             */
            setPrintLog(state) {
                let instances = this.getAllInstance();

                Object.keys(instances).forEach(key => {
                    instances[key].options.isPrintLog = state;

                    if (instances[key].api && instances[key].api.log) {
                        instances[key].api.log.options.isPrintLog = state;
                    }
                });

                window.Plugin_Service_PrintLog = !!state;
            },

            /**
             * 设置是否记录日志
             * @param {Boolean} state 
             */
            setRecordLog(state) {
                let instances = this.getAllInstance();

                Object.keys(instances).forEach(key => {
                    instances[key].options.isRecordLog = state;

                    if (instances[key].api && instances[key].api.log) {
                        instances[key].api.log.options.isRecordLog = state;
                    }
                });

                window.Plugin_Service_RecordLog = !!state;
            }
        }
    }
})
