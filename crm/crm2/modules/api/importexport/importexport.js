define(function (require, exports, module) {
    var waiting = FS.crmUtil.waiting;
    const objectImport = FS.util.getUserAttribute('paasObjectImport');
    const importV3 = require('./importv3/importv3');
    var fns = {
       import: function(param) {
			if(param.apiname === "CoinAccountDetailObj") {//今币明细导入使用这个导入弹窗
                waiting();
				FS.MEDIATOR.trigger('appCheckin.getModule', {
					moduleInfo:{
						isEs6:false,
						url:'app-checkin-modules/common/importin/importin'
					},
					isNeedCheckAdmin:false,
					complete: function(Import) {
						waiting(false);
						var downUrlDomain = FS.APP_CHECKIN_MODULE.ASSETS_PATH.replace(/^http(s)?:\/\/([^\/]+)?\/(.*)?\/html/,'/html');
                        var importIn = new Import({
                            title: $t("今币明细导入"),
                            tabs:[{
                                title: $t("今币明细"),
                                downUrl: downUrlDomain + '/xls/coin-template.xlsx',
                                importUrl: '/EM1HINTE/coin/coinImport',
                            }],
                            importObj: 'CheckinObj',
                            parseServerSuccCb: function (data, importComp) {
                                if (data.Result.StatusCode == 0 && data.Value && data.Value.data) {
                                    if (data.Value.code == 0 && data.Value.data.isComplete) {
                                        // importComp.trigger('success');
                                        importComp.hide();
                                        FS.util.confirm($t('导入可能需要较长时间，导入结果后会在企信的文件助手中推送，请注意查收'), '', function(){this.hide();}, {withoutCancelBtn: true});
                                    } else {
                                        importComp._hideLoading();
                                        importComp.hide();
                                        FS.util.alert(data.Value.data.message || $t('服务器返回错误!'));
                                    }
                                } else {
                                    importComp._hideLoading();
                                    importComp.hide();
                                    FS.util.alert(data.Result.FailureMessage || $t("服务器返回错误!"));
                                }
							}
                        });
                        // importIn.on('success', function () {
                        //     param.success();
						// });
						importIn.show();
					}
				})
				return;
			}
			fns.import_async(param);
        },
        import_sync: function(param) {
            waiting();
            require.async('./importsync/import', function(Import) {
                waiting(false);
                var comp = new Import(param);
                comp.on('success', function() {
                    param.success();
                })
                comp.show();
            })
        },

        import_async: function(param) {
            if(CRM.util.getUserAttribute('paasCRMImportV3') || objectImport) return importV3(param);//启用v3版本

            waiting();
            function innerFn() {
                require.async('./importasync/import', function(Import) {
                    waiting(false);
                    var comp = new Import(param);
                    try {
                        // 互联专用，非互联侧需求请【勿】使用，如有修改请告知互联跟进
                        if (param.icmgOnImporterMount) param.icmgOnImporterMount(comp);
                    } catch (e) {
                        // empty
                    }
                    comp.on('success', function() {
                        param.success();
                    })
                })
            }
            if (objectImport) {
                require.async('./importasync/importdialog', function(ImportDialog) {
                    waiting(false);
                    new ImportDialog(param).on('confirm', function () {
                        // 判断是否是灰度对象，如果是灰度对象
                        // 灰度对象判断，以__c结尾为自定义对象，是灰度，或者_.contains(objectImport, apiname)灰度
                        if (param.apiname.indexOf('__c') != -1 || _.contains(objectImport, param.apiname)) {
                            waiting();
                            // 导入埋点
                            try {
                                window.logger.action({
                                    eventId: 's-paasobj_list_import',
                                    str1: param.apiname
                                })
                            } catch(e) {}
                            
                            require.async('paas-vui/sdk', function (VuiSdk) {
                                VuiSdk.getImportExportRecords().then(function (Import) {
                                    waiting(false);
                                    Import.initImport({ param });

                                    FS.util.getUserAttribute('crmGrayImportTeamMember') && CRM.api.show_guide({
                                        key: 'import_guide_900ii',
                                        title: '',
                                        content: $t('crm.import.team.tip')
                                    })
                                })
                            })
                        } else {
                           innerFn();
                        }
                    })
                })
            } else {
                innerFn();
            }
        },

        export: function(param) {
            param.dataList = [];
            this.export_selected(param);
        },

        export_selected: function(param) {
            if (!param.queryParam) {
                //导出可配置参数
                if (!param.queryParam && (param.setColumnsParams|| param.relevantTeamParams)
                    ) {
                        FS.util.alert($t('按钮')+$t('配置错误'));
                }
                return;
            }
            waiting();
            require.async('./export/export', function (Export) {
                waiting(false);
                new Export(param).show();
            });

			CRM.util.uploadLog('paasobj', 'export', {
				operationId: param.dataIds && param.dataIds.length ? 'doExportSelectedData' : 'doExportListData',
				object_api_name: param.apiname
			});
        },

        // 导出图片附件
        export_file: function(param) {
            param.dataList = [];
            fns.export_file_selected(param);
        },

        // 导出选中的图片附件
        export_file_selected: function(param) {
            if (!param.queryParam) return;
            waiting();
            require.async('./exportfile/exportfile', function(ExportFile) {
                waiting(false);
                new ExportFile(param).show();
            });
        }
    }

    return function(param) {
        var name = param.__name;
        delete param.__name;
        return fns[name](param);
    }
})
