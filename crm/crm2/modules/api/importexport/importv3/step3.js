define(function(require, exports, module) {
    return function(wrapper, helper) {
        return FxUI.create({
            wrapper,
            template: `
                <div style="margin-top:32px;">
                    <h5><span class="i-line">${$t('第num步', {num:3})}：${$t('添加数据')}</span></h5>
                    <div style="margin-top:12px" class="mt20">
                        <p v-show="dFileList.length" class="p-tip mb8">
                            <span class="el-icon-info"></span>
                            <span style="margin-left:2px">${$t('paas.crm.import.fileinfo', null, '仅可上传一个文件，删除后可重新上传')}</span>
                        </p>
                        <div class="fitem mb8" v-show="!dFileList.length">
                            <p class="info15">
                                ${$t('paas.crm.import.fileimport', null, '第一次导入或者更换业务类型时，需要下载并使用模板进行导入')}
                            </p>
                            <fx-button @click="handleDownloadTpl" :disabled="!dEnable" style="margin-left:8px" type="primary" plain size="small">${$t('下载数据模板')}</fx-button>
                        </div>
                        <div style="display:flex;align-items:flex-end">
                            <div 
                                v-show="!dFileList.length"
                                class="upload-dragger" 
                                @dragover.prevent="" 
                                @drop.prevent="handleDrag"
                                @dragenter.prevent=""
                                @click="handleUpload"
                                draggable="true"
                            >
                                <input style="display:none" ref="fileInput" @change="handleChange" type="file" accept=".xls,.xlsx">
                                <div class="upload-text">
                                    <i class="fx-icon-upload"></i>
                                    <span class="info19">${$t('上传文件')}</span>
                                    <span class="info15">
                                        ${$t("将文件拖到此处，或")}
                                        <em style="color: #0C6CFF;">${$t('点击上传')}</em>
                                    </span>
                                    <p class="p-info">
                                        {{dLimitStr}}
                                    </p>
                                    <p class="p-info">${$t('paas.crm.importext.tip')}xlsx/xls</p>
                                    <p v-if="dTextLimit" class="p-info">${$t('paas.crm.importext.textlimit')}</p>
                                    <p v-if="dTipStr" class="p-info">
                                        {{dTipStr}}
                                    </p>
                                </div>
                            </div>
                            <div v-show="dFileList.length" class="table-wrapper" ref="table"></div>
                            <div v-show="dMappingErrors.length" class="x-mb">
                                <fx-tooltip effect="light" placement="bottom">
                                    <div class="crm-importv3-errortip" slot="content">
                                        <div v-for="error in dMappingErrors">
                                            <span class="fx-icon-f-cuowu crm-importv3-icon-warining"></span>
                                            <span>{{error.label}}：</span>
                                            <span class="info">{{error.info}}</span>
                                        </div>
                                    </div>
                                    <span class="fx-icon-warning crm-importv3-icon-warining"></span>
                                </fx-tooltip>
                                <span>${$t('paas.crm.import.mappingtip')}</span></span>
                                <fx-button @click="handleMapping" style="margin-left:12px" size="small">${$t('字段映射')}</fx-button>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            data() {
                let app = CRM.util.getConnectAppInfo();
                return {
                    dUrl: `/FSC/EM/File/UploadByStream${app && app.appId ? '?appid=' + app.appId : ''}`,
                    dSize: 50 * 1024 * 1024,
                    dFileList: [],
                    dLimitStr: $t('crm.导入上限300000大小50M'),
                    dMappingErrors: [],
                    dTipStr: '',
                    dTextLimit: false,
                    dEnable: false
                }
            },

            watch: {
                dFileList(v) {
                    this.$nextTick(() => this.table && (this.table.doStaticData(v), this.table.resize()));
                    helper.updateFileInfoAndMapping(v[0]).then(() => {}).catch((message) => {this.showError(message)})
                }
            },

            mounted() {
                helper.on('change:currentImportObject', (item) => {
                    let tipStr = '', val = item.changed.currentImportObject.objectCode;
                    if(val === 'DepartmentObj' && FS.util.getUserAttribute('crmImportExportDepRepeat')) {
                        tipStr = $t('部门重复灰度提示');
                    } else if(val === 'PersonnelObj' && FS.util.getUserAttribute('crmImportExportPersonnelRepeat')) {
                        tipStr = $t('crm.import.personnel.repeat', null, '企业已灰度人员名称可重复，人员字段需填写人员编码值！');
                    }

                    this.dTipStr = tipStr;

                    this.dEnable = !!val;

                    this.dFileList = [];

                    let isTextureImport = !!item.changed.currentImportObject.IsTextureImport;

                    this.dTextLimit = isTextureImport;

                    this.dSize = isTextureImport ? 100 * 1024 * 1024 : 50 * 1024 * 1024;
                })
                helper.on('change:radioValues', (item) => {
                    let r = item.changed.radioValues;
                    this.setAddfileTip(r);
                })
                helper.on('change:IsCheckedDeails', () => {
                    this.dFileList = [];
                });
                helper.on('change:detailArgs', () => {//从对象变化
                    this.dFileList = [];
                });
                helper.on('change:mappingErrors', item => {
                    this.dMappingErrors = item.changed.mappingErrors || [];
                })

                this.renderTable();
            },

            methods: {
                //页面点击触发上传
                handleUpload() {
                    $(this.$refs.fileInput).click();
                },

                //input上传
                handleChange(e) {
                    this.createFileUpload(e.target.files);
                    this.$refs.fileInput.value = '';
                },

                //拖动上传
                handleDrag(e) {
                    this.createFileUpload(e.dataTransfer.files);
                    e.stopPropagation();
                },

                //创建文件上传实例，提交时调用
                createFileUpload(files) {
                    if(files.length > 1) {
                        this.showError($t('附件最多只能上传{{num}}个!', {
                            num : 1
                        }));
                        return;
                    }
                    let file = files[0];

                    let fileExt = CRM.util.getFileExtText(file.name) || '';
                    if (!_.contains(['xls', 'xlsx'], fileExt.toLowerCase())) {
                        this.showError($t('请选择excel格式的文件'));
                        return;
                    }

                    if (file.size > this.dSize) {
                        this.showError(this.dLimitStr);
                        return;
                    }

                    this.dFileList = [file];
                },

                //清除文件
                handleRemove() {
                    this.dFileList = [];
                    helper.set('fileInstance', null);
                },

                //字段映射
                handleMapping() {
                    helper.trigger('showMapping');
                },

                //下载数据模板
                handleDownloadTpl() {
                    helper.downloadTpl().then().catch(message => this.showError(message));
                },

                showError(message) {
                    helper.showError(message);
                },

                setAddfileTip(r) {
                    let tip;
                    if(r.IsWorkFlowEnabled) {
                        tip = $t('crm.导入上限1000大小2M');
                        if(r.IsApprovalFlowEnabled) {
                            tip = tip.replace('1000', '500');
                        }
                    } else if(r.IsTextureImportEnabled) {
                        tip = $t('crm.导入上限300000大小50M').replace('50', '100');
                    } else {
                        tip = $t('crm.导入上限300000大小50M');
                    }

                    this.dLimitStr = tip;
                },

                renderTable() {
                    require.async('crm-widget/table/table', Table => {
                        let columns = [{
                            data: 'name',
                            width:326,
                            dataType: 'text',
                            title: $t('文件名称'),
                            render(data, type, full) {
                                return `<span class="link">${_.escape(full.name)}</span>`;
                            }
                        }, {
                            data: 'size',
                            dataType: 'text',
                            title: $t('文件大小'),
                            width: 110,
                            render(data) {
                                return CRM.util.getFileSize(data);
                            }
                        }, {
                            data: null,
                            width: 100,
                            title: $t("操作"),
                            lastFixed: true,
                            render(data, type, full) {
                                return '<span class="j-del link">' + $t('删除') + '</span>';
                            }
                        }]
                        this.table = new Table({
                            $el: $(this.$refs.table),
                            showSize: false,
                            showMultiple: false,
                            showFilerBtn: false,
                            showTerm: false,
                            showManage: false,
                            showPage: false,
                            searchTerm: null,
                            postData: {},
                            height: 'auto',
                            noSupportLock: true,
                            columns: columns
                        });

                        this.table.on('trclick', (cellData, $tr, $target) => {
                            if($target.hasClass('j-del')) {
                                this.handleRemove();
                            }
                        })

                        this.table.render();
                    })
                }
            }
        })
    }
})