.crm-c-importv3 {
    position:fixed;inset:0;z-index:1000;background:#fff;

    overflow: auto;
    .inner {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        overflow: hidden;
    }
    h3 {
        font-size: 18px;
        font-weight: 500;
    }
    h5 {
        font-size: 16px;
        font-weight: 500;
    }
    .p-info {
        color: var(--color-neutrals11);
    }
    .info15 {
        color: var(--color-neutrals15);
    }
    .mt20 {
        margin-top: 20px;
    }
    .mb8 {
        margin-bottom: 8px;
    }
    .fitem {
        display: flex;
        align-items: center;
    }
    .info19 {
        font-size: 14px;
        font-weight: 400;
        color: var(--color-neutrals19);
    }
    .p-tip {
        display: inline-block;
        padding: 2px 4px;
        border-radius: 4px;
        background-color: var(--color-special01);
        font-size: 14px;
        font-weight: 400;
        color: var(--color-neutrals19);
    }
    .tit {
        display: flex;
        height: 48px;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        background-color: var(--color-neutrals03);
    }
    .content {
        flex: 1;
        overflow: auto;
        padding: 20px 24px;
    }
    .i-line {
        border-left: 4px solid var(--color-primary06);
        padding-left: 8px;
    }
    .type-btns {
        margin: 20px 0 16px -24px;
        button {
            margin-left: 24px;
        }
        .fx-icon-ok-2 {
            display: none;
            position: absolute;
            right: -1px;
            top: 0;
            z-index: 10;
            transform: scale(0.6);

            &::before {
                color: #fff !important;
            }
        }
        .el-button--primary {
            &::after {
                content: "";
                position: absolute;
                top: -1px;
                right: -1px;
                width: -1px;
                height: 0;
                border-color: var(--color-primary06) var(--color-primary06) transparent transparent;
                border-style: solid;
                border-width: 12px
            }
            .fx-icon-ok-2 {
                display: block;
            }
        }
    }
    .i-form-item {
        margin-bottom: 6px;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .el-form-item {
        .el-form-item__label {
            width: initial !important;
            height: 32px !important;
        }
        .el-form-item__content {
            margin-left: 0 !important;
        }
    }
    .i-button {
        position:relative;
        padding:18px 32px;
        background-color:#fff !important;
    }
    .upload-text {
        display: flex;
        height: 100%;
        flex-direction: column;
        justify-content: center;
    }
    .upload-dragger {
        width: 800px;
        height: 230px;
        border: 1px dashed var(--color-neutrals05);
        border-radius: 4px;
        text-align: center;
        &:hover {
            cursor: pointer;
            border-color: var(--color-primary06);
        }
    }
    .upload-hide {
        .el-upload-dragger,.el-upload--text {
            display: none;
        }
    }
    .fx-icon-upload::before {
        color: var(--color-special02) !important;
        font-size: 24px;
    }
    .fx-upload-list {
        width: 540px;
    }
    .x-mb {
        transform: translate(12px, 2px);
    }

    .i-group {
        width:840px;
        margin: 0 0 10px 20px;
        background: var(--color-neutrals03);
        padding-top: 8px;
        .fx-form-item {
            margin-bottom: 6px !important;
        }
    }
    .i-horiz {
        display: flex;
        .fx-form-item {
            flex: 1;
        }
    }
    .i-vertival {
        .el-form-item__label {
            float: none !important;
        }
        .fx-form-item {
            margin-left: 10px !important;
            margin-bottom: 0 !important;
        }
    }
    .link {
        color: var(--color-info06);
        &:hover {
            text-decoration: underline;
            cursor: pointer;
        }
    }
    .table-wrapper {
        width: 540px;
        border: 1px solid var(--color-neutrals05);
        border-top: none;
        border-bottom: none;
        overflow: hidden;
        .dt-term-batch {
            display: none;
        }
    }

    .mm {
        background-color: var(--color-neutrals02);
        padding: 16px;
        margin-left: 10px;
    }

    .fx-icon-question {
        cursor: pointer;
        margin-left: 4px;
    }

    .mmc {
        display: flex;
        flex-wrap: wrap;
        gap: 12px 36px;
        font-size: 14px;
        .el-form-item {
            margin-bottom: 0;
            width: 100%;
        }
        .label-wrapper {
            display: flex;
            align-items: center;
            width: 112px;
            min-width: 112px;
        }
        .label-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .el-select {
            width: 100%;
        }
        &>div {
            display: flex;
            align-items: center;
            width: calc(33.333% - 24px);
            gap: 12px;
            box-sizing: border-box;
        }
    }
    .ss {
        display: flex;
        height: 32px;
        align-items: center;
        margin-left: 10px;
        margin-bottom: 6px;
        color: var(--color-neutrals15);
        font-size: 14px;
    }
}

.crm-api-importv3__childobject {
    width: 480px;
    padding: 0;
    cursor: pointer;
    .mode-input {
        position: relative;
        width: 100%;
        min-height: 32px;
        box-sizing: border-box;
        border: 1px solid var(--color-neutrals07);
        border-radius: 4px;
        padding: 4px;
        overflow: auto;
        li {
            line-height: 24px;
            margin-top: 4px;
            &:last-child{
                margin-top: 4px;
            }
            &:first-child{
                margin-top: 0px;
            }
            &:nth-child(2) {
                margin: 0px;
            }
        }
        .p-label {
            display: inline-block;
            background: var(--color-special01);
            border-radius: 3px;
            padding: 0px 8px;
            margin: 0;
        }
        i {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            width: 30px;
            justify-content: center;
            font-size: 12px;
            &::before {
                color: var(--color-neutrals07);
            }
        }
    }
    .mode-active {
        border-color: var(--color-primary06);
        i {
            transform: rotate(-180deg);
        }
    }
    .panel {
        display: flex;
        width: 100%;
        box-sizing: border-box;
        border: 1px solid var(--color-neutrals05,#dee1e8);
        border-radius: 4px;
        ul {
            padding: 6px 0;
            width: 50%;
            height: 170px;
            overflow: auto;
        }
        .p-left {
            border-right: 1px solid var(--color-neutrals05,#dee1e8);
        }
        .p-label {
            flex: 1;
            margin: 0 4px;
            .nowrap();
        }
        .el-checkbox {
            margin-right: 4px;
        }
        li {
            display: flex;
            align-items: center;
            padding: 0 8px;
            height: 34px;
            line-height: 34px;
            &:hover {
                background-color: var(--color-special01);
            }
        }
    }
    .focus {
        .p-label,.ir:before,.ic::before {
            color: var(--color-primary06);
            font-weight: bold;
        }
        .ic::before {
            visibility: visible;
        }
    }
    .ic::before {
        visibility: hidden;
    }
    .ec:before {
        color: #c0c4cc;
        cursor: pointer;
    }
    .ec:hover:before {
        color: #000;
    }

    .popper__arrow {
        transform: translateX(-200px);
    }
}
.crm-importv3-errortip {
    max-height: 400px;
    overflow: auto;
    div {
        margin-bottom: 8px;
        &:last-child {
            margin-bottom: 0;
        }
    }
    .info {
        color: var(--color-primary06);
    }
}
.crm-importv3-icon-warining {
    margin-right: 8px;
    font-size: 14px;
    cursor: pointer;
}