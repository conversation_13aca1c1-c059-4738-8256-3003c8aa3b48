define(function(require, exports, module) {
    const showDocument = require('../importasync/showdocument');
    return function(wrapper, helper) {
        return FxUI.create({
            wrapper,
            template: `
                <div style="margin-top:16px;">
                    <h5>
                        <span class="i-line">${$t('第num步', {num:2})}：${$t('请选择导入方式')}</span>
                        <fx-link style="font-size:12px;margin-left:4px" type="standard" :underline="false" @click="handleImportInfo">${$t('导入格式说明')}</fx-link>
                    </h5>
                    <div class="type-btns">
                        <fx-button @click="handleImportType('add')" :disabled="disabledAdd || !dImportType" :type="dImportType === 'add' ? 'primary' : 'default'" class="i-button" size="small" plain icon="fx-icon-add-2">
                            ${$t('添加新数据')}
                            <span class="fx-icon-ok-2"></span>
                        </fx-button>
                        <fx-button @click="handleImportType('update')" :disabled="disabledUpdate || !dImportType" :type="dImportType === 'update' ? 'primary' : 'default'" class="i-button" plain size="small" icon="fx-icon-refresh">
                            ${$t('更新现有数据')}
                            <span class="fx-icon-ok-2"></span>
                        </fx-button>
                    </div>
                    <fx-form v-show="dImportType" style="margin-left:-8px" size="small">
                        <template v-for="item in dOptions">
                            <fx-radio-group
                                v-if="item.radios"
                                v-show="!item.hide"
                                :disabled="item.disabled"
                                class="i-form-item"
                                v-model="dRadioValues[item.key]"
                                @change="handleChange(item.key, $event)"
                                :label="item.label"
                            >
                                <fx-radio v-for="radio in item.radios" :label="radio.value">
                                    {{radio.label}}
                                    <fx-tooltip v-if="radio.tip" :content="radio.tip" placement="top">
                                        <span class="fx-icon-question"></span>
                                    </fx-tooltip>
                                    <fx-select
                                        style="display:inline-block;margin-bottom:0"
                                        v-if="radio.hasAny"
                                        @change="handleSpcChange"
                                        :disabled="!dRadioValues.specifiedField"
                                        :options="radio.anyFields"
                                        :value="dRadioValues.specifiedField || ''"
                                        filterable
                                    ></fx-select>
                                </fx-radio>
                            </fx-radio-group>
                            <fx-form-item v-show="dRadioValues[item.parentKey]" class="i-group" v-else-if="item.groups">
                                <template v-for="gitem in item.groups">
                                    <fx-radio-group
                                        v-show="!gitem.hide"
                                        v-if="gitem.radios"
                                        v-model="dRadioValues[gitem.key]"
                                        @change="handleChange(gitem.key, $event)"
                                        :label="gitem.label"
                                    >
                                        <fx-radio v-for="radio in gitem.radios" :label="radio.value">
                                            {{radio.label}}
                                            <fx-tooltip v-if="radio.tip" :content="radio.tip" placement="top">
                                                <span class="fx-icon-question"></span>
                                            </fx-tooltip>
                                        </fx-radio>
                                    </fx-radio-group>
                                    <div v-show="!gitem.hide" v-else-if="gitem.checks" :class="gitem.isVertival ? 'i-vertival' : 'i-horiz'">
                                        <label class="el-form-item__label">
                                            <span>{{gitem.label}}</span>
                                            <fx-tooltip v-if="gitem.tips"placement="top">
                                                <div slot="content">
                                                    <p v-for="tip in gitem.tips">{{tip}}</p>
                                                </div>
                                                <span class="fx-icon-question"></span>
                                            </fx-tooltip>
                                            <fx-link 
                                                @click="toggleCheckAll(gitem)" 
                                                style="margin-left:8px" 
                                                v-if="gitem.supportAll" 
                                                type="standard" 
                                                :underline="false">
                                                <span>{{gitem.checks.length === dRadioValues[gitem.key].length ? $t('反选'): $t('全选')}}</span>
                                            </fx-link>
                                        </label>
                                        <fx-checkbox-group v-model="dRadioValues[gitem.key]" @change="handleChange(gitem.key, $event)">
                                            <fx-checkbox 
                                                v-for="ggitem in gitem.checks" 
                                                :label="ggitem.value"
                                            >
                                                {{ggitem.label}}
                                            </fx-checkbox>
                                        </fx-checkbox-group>
                                    </div>
                                </template>
                            </fx-form-item>
                            <div class="ss" v-else-if="item.flowControl">
                                <span>
                                    ${$t('paas.crm.import.flowset')}
                                </span>
                                <fx-link style="margin-left:12px" type="standard" :underline="false" @click="setFlowStatus(item)">
                                    {{item.hide ? $t('展开') : $t('收起')}}
                                    <span :class="item.hide ? 'fx-icon-arrow-down' : 'fx-icon-arrow-up'"></span>
                                </fx-link>
                            </div>
                            <div v-else-if="item.controlMappings">
                                <div class="ss">
                                    <span>
                                        ${$t('paas.crm.import.types')}
                                    </span>
                                    <fx-tooltip content="${$t('paas.crm.import.mangetip')}" placement="top">
                                        <span class="fx-icon-question"></span>
                                    </fx-tooltip>
                                    <fx-link style="margin-left:12px" type="standard" :underline="false" @click="setCMStatus(item)">
                                        {{item.hide ? $t('展开') : $t('收起')}}
                                        <span :class="item.hide ? 'fx-icon-arrow-down' : 'fx-icon-arrow-up'"></span>
                                    </fx-link>
                                </div>
                                <div v-show="!item.hide" class="mm">
                                    <h5>
                                        ${$t('paas.crm.import.fields')}
                                        <fx-tooltip content="${$t('vui.myobject.import.unique_identification_field_tooltip')}" placement="top">
                                            <span style="font-size:14px" class="fx-icon-question"></span>
                                        </fx-tooltip>
                                    </h5>
                                    <div v-show="!mitem.hide" style="margin-top:20px" v-for="mitem in item.controlMappings">
                                        <div style="color:var(--color-neutrals15);margin-bottom:12px">{{mitem.title}}</div>
                                        <div class="mmc">
                                            <div v-for="field in mitem.fields">
                                                <div class="label-wrapper">
                                                    <span class="label-text">{{field.label}}</span>
                                                    <fx-tooltip :content="field.tip" placement="top">
                                                        <div slot="content">{{field.tips[0]}}<br/>{{field.tips[1]}}</div>
                                                        <span class="fx-icon-question"></span>
                                                    </fx-tooltip>
                                                </div>
                                                <fx-select disabled :value="field.value || ' '"></fx-select>
                                                <fx-tooltip v-show="!field.value" content="${$t('paas.crm.import.warning')}" placement="top">
                                                    <span style="cursor:pointer" class="fx-icon-jingshi"></span>
                                                </fx-tooltip>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <fx-form-item v-else style="margin-bottom:10px;margin-left:10px">
                                <p class="p-info">
                                    {{item.tips}}
                                    <fx-tooltip v-if="item.question" :content="item.question" placement="top">
                                        <span class="fx-icon-question"></span>
                                    </fx-tooltip>
                                </p>
                            </fx-form-item>
                        </template>
                        
                    </fx-form>
                </div>
            `,
            data() {
                return {
                    dImportType: '',
                    dRadioValues: {},
                    dOptions: [],
                    disabledUpdate: false,
                    disabledAdd: false
                }
            },
            mounted() {
                helper.on('change:currentImportObject', (item) => this.handleImportObjectChange(item.changed.currentImportObject));
                helper.on('change:IsCheckedDeails', (item) => {
                    let citem = helper.get('currentImportObject');
                    if(!/__c$/.test(citem.objectCode)) {
                        helper.resetObjectInfo();
                        return;
                    };

                    this.disabledUpdate = !!item.changed.IsCheckedDeails;

                    if(this.dImportType === 'update') {
                        this.dImportType = '';
                        this.reset();
                    }

                    this.setControlMappings();
                });

                helper.on('xxxx', (item) => {//从对象变化
                    this.reset();
                    this.setControlMappings();
                });
                helper.on('resetMappinfo', (item) => {
                    this.setControlMappings();
                });
            },

            watch: {
                dImportType(v) {
                    helper.set('importType', v);
                }
            },

            methods: {
                setCMStatus(item) {
                    item.hide = !item.hide;
                    this.handleChange('isExpendMapping', this.dRadioValues.isExpendMapping = !item.hide);
                },
                setFlowStatus(item) {
                    item.hide = !item.hide;
                    _.each(this.dOptions, r => {
                        if(r.key === 'IsWorkFlowEnabled' || r.key === 'IsApprovalFlowEnabled' || r.key === 'IsTextureImportEnabled') {
                            r.hide = !r.hide;
                        }
                    })
                    this.handleChange('isExpendFlow', this.dRadioValues.isExpendFlow = !item.hide);
                },
                setControlMappings() {
                    let detailArgs = helper.get('detailArgs') || [];
                    let IsCheckedDeails = helper.get('IsCheckedDeails');
                    _.find(this.dOptions, (r) => {
                        if(!r.controlMappings) return;
                        _.each(r.controlMappings, rr => {
                            if(!rr.isMaster) {
                                rr.hide = !(IsCheckedDeails && _.findWhere(detailArgs, {value: rr.objectApiName}));
                            }
                        })
                        return true;
                    });
                },

                handleChange(key, value) {
                    let dr = this.dRadioValues;
                    if(key === 'IsEmptyValueToUpdate' && value) {
                        let flag;
                        let confirm = CRM.util.confirm($t('paas.crm.import.emptytip'), $t('提示'), () => {
                            flag = true;
                            confirm.hide();
                            
                        }, {
                            hideFn: () => {
                                if(flag) {
                                    this._hackSome(key, value);
                                    helper.set('radioValues', {...dr})//值同步出去
                                } else {//将值还原回去
                                    dr.IsEmptyValueToUpdate = false;
                                }
                            }
                        });
                        return;
                    }

                    if(key === 'IsUpdateOwner' && !value) {
                        dr.otherObjects && (dr.otherObjects = []);
                        dr.rolePermission && (dr.rolePermission = []);
                        dr.permissionType && (dr.permissionType = 1);
                        if(dr.strategy !== 1) {
                            dr.strategy = 1;
                            this._hackSome('strategy', 1);
                        }
                    } else if(key === 'strategy' && value === 2) {
                        let tt = _.findWhere(this.dOptions, {parentKey: 'IsUpdateOwner'});
                        tt = _.findWhere(tt.groups, {key: 'rolePermission'});
                        tt = tt && _.find(tt.checks, a => a.value == 4);
                        if(tt) {
                            dr.rolePermission = _.union(dr.rolePermission || [], [tt.value]);
                        }
                        dr.permissionType = 1;
                    }

                    this._hackSome(key, value);

                    helper.set('radioValues', {...dr})//值同步出去

                    if(key === 'MatchingType') {
                        helper.set('MatchingType', value);//对象映射依赖此变化
                    }
                },

                handleSpcChange(v) {
                    this.handleChange('specifiedField', this.dRadioValues.specifiedField = v || void 0);
                },

                //处理一些选项之间的特殊关系
                _hackSome(key, value) {
                    if(key === 'MatchingType') {
                        if(value == 4) {
                            if(!this.dRadioValues.specifiedField) {
                                let item = helper.get('currentImportObject');
                                this.dRadioValues.specifiedField = item.anyFields[0].value;
                            }
                        } else {
                            this.dRadioValues.specifiedField = void 0;
                        }
                    } else if(key === 'IsEmptyValueToUpdate') {
                        let tt = _.findWhere(this.dOptions, {key: 'emptyTips'});
                        if(tt) {
                            tt.tips = value ? $t('更新相关团队成员将会覆盖原有') : $t('相关团队导入列都为空时才会判为空');
                        }
                    } else if(key === 'IsApprovalFlowEnabled') {
                        if(value) {
                            this.dRadioValues.IsWorkFlowEnabled = true;
                            let tt = _.findWhere(this.dOptions, {key: 'IsWorkFlowEnabled'});
                            if(tt) {
                                tt.disabled = true;
                            }
                        } else {
                            let tt = _.findWhere(this.dOptions, {key: 'IsWorkFlowEnabled'});
                            if(tt && !tt.forceDisabled) {
                                tt.disabled = false;
                            }
                        }
                    } else if(key === 'strategy') {
                        _.each(this.dOptions, (v, k) => {
                            _.each(v.groups, vv => {
                                if(vv.key === 'rolePermission' || vv.key === 'permissionType') {
                                    vv.hide = value !== 2;
                                }
                            })
                        })
                    }

                    this._setWorkTips(key);
                },

                _setWorkTips(key) {
                    if(key === 'IsWorkFlowEnabled' || key === 'IsApprovalFlowEnabled') {
                        let tt = _.findWhere(this.dOptions, {key: 'workTip'});
                        let IsWorkFlowEnabled = this.dRadioValues.IsWorkFlowEnabled;
                        if(IsWorkFlowEnabled) {
                            let str = $t('当前导入数据上限为1000条');
                            if(this.dRadioValues.IsApprovalFlowEnabled) {
                                str = $t('paas.crm.import.approval.tip', null,'如选择触发审批流则导入数据上限为500条');
                            }
                            tt.tips = str;
                        } else {
                           tt.tips = ''; 
                        }
                    }
                },

                handleImportType(type) {
                    let item = helper.get('currentImportObject');
                    if(!item) {
                        helper.showError($t('请选择业务对象'));
                        return;
                    }

                    if(item.importUniquenessRule && !item.importUniquenessRule.IsEnabled && type === 'update') {
                        if (item.importUniquenessRule.RuleSettingRight) {//有设置唯一规则权限 跳转预对象设置页面
                            let confirm = CRM.util.confirm(item.objectName + $t('crm.尚未开启唯一性规则'), $t('提示'), () => {
                                helper.trigger('destroy');
                                confirm.hide();
                                CRM.control.navigate('#crmmanage/=/module-sysobject');
                            }, {
                                btnLabel: {
                                    confirm: $t('去开启'),
                                    cancel: $t('取 消')
                                }
                            });
                        } else {//无权限联系管理员
                            CRM.util.getCrmManagers((crmManagers) => {
                                let confirm = util.confirm(tip, $t("提示"), function () {
                                    helper.trigger('destroy');
                                    confirm.hide();
                                    FS.tpl.event.trigger("qxOpenChat", crmManagers[0].EmployeeID, 'employee');
                                }, {
                                    btnLabel: {
                                        confirm: $t('联系管理员开启'),
                                        cancel: $t('取 消')
                                    }
                                });
                            })
                        }
                    } else {
                        this.dImportType = type;
                        this.reset();
                    }
                },

                //显示导入说明
                handleImportInfo() {
                    showDocument();
                },

                //导入对象发生变化时，根据默认数据更新导入方式
                handleImportObjectChange (item) {
                    let importType = item.defalutValues.ImportType;
                    this.dImportType = importType !== void 0 ? importType == 1 ? 'update' : 'add' : '';
                    this.disabledUpdate = false;
                    this.reset(item.defalutValues);
                },

                //选中所有check选项
                toggleCheckAll(item) {
                    let key = item.key;
                    let values = this.dRadioValues[key];
                    if(item.checks.length === values.length) {
                        this.dRadioValues[key] = [];
                    } else {
                        this.dRadioValues[key] = _.pluck(item.checks, 'value');
                    }
                },

                //导入方式发生变化后，充值所有选项
                reset(defalutValues = {}) {
                    let item = helper.get('currentImportObject');
                    let options = [];
                    let radios = [{
                        value: true,
                        label: $t('是')
                    }, {
                        value: false,
                        label: $t('否')
                    }]

                    let st = item.supportType; // 0 不支持更新导入 3不支持新建导入 默认是1
                    if(st === 0) {
                        this.disabledUpdate = true;
                        this.disabledAdd = false;
                    } else if(st === 3) {
                        this.disabledAdd = true;
                        this.disabledUpdate = false;
                    } else {
                        this.disabledAdd = false;
                        this.disabledUpdate = false;
                    }

                    if(helper.get('IsCheckedDeails')) {//主从导入默认禁止更新导入
                        this.disabledUpdate = true;
                        this.dImportType = '';
                    }

                    if(this.disabledAdd && this.disabledUpdate) {
                        this.dImportType = '';
                        return;
                    }

                    if(this.disabledAdd) {
                        this.dImportType = 'update';
                    }

                    this.dImportType || (this.dImportType = 'add');

                    let radioValues = {};//当前导入对象支持的导入项
                    let isAdd = this.dImportType === 'add';//是否是新增导入
                    if(item) {
                        let _tips = [];
                        let _tip = '';
                        let importUniquenessRule = item.importUniquenessRule;
                        let supportMTypes = item.matchingTypes && item.matchingTypes[isAdd ? 'insert_import' : 'update_import']
                        // 唯一性规则提示
                        if(importUniquenessRule && importUniquenessRule.IsEnabled) {
                            if (item.uniqueTip) {
                                // 已经取过之后就直接获取
                                _tips.push(item.uniqueTip);
                            } else {
                                _.each(importUniquenessRule.RuleFieldList, item => {
                                    _tip += $t("、") + item.FieldCaption;
                                })
                                _tip = _tip.substr(1).length ? _tip.substr(1) + $t('不重复'): '';
                                _tip && (_tips.push(_tip), item.uniqueTip = _tip);
                            }
                        }

                        if (isAdd && item.tip) {
                            _tips.push(item.tip);
                        }

                        if(_tips.length) {
                            options.push({
                                key: 'tip',
                                tips: _tips.join($t('、'))
                            })
                        }

                        if(!isAdd) {
                            options.push({
                                label: $t('导入数据的字段有空值时，是否更新现有字段数据'),
                                key: 'IsEmptyValueToUpdate',
                                radios
                            })

                            radioValues.IsEmptyValueToUpdate = false;

                            if(item.objectCode !== 'GoalValueObj') {
                                options.push({
                                    key: 'emptyTips',
                                    tips: $t("相关团队导入列都为空时才会判为空")
                                })
                            } else if(item.goal_type === 'PersonnelObj') {
                                options.push({
                                    key: 'emptyTip',
                                    tips: $t('bi.oldGoal.tips')
                                })
                            }

                            if(_.contains(['LeadsObj', 'AccountObj', 'ContactObj'], item.objectCode)) {
                                let tt = importUniquenessRule && $t('按{{tip}}确定数据唯一性进行更新导入', {tip: _.map(importUniquenessRule.RuleFieldList, a => a.FieldCaption).join($t('、'))})
                                if(_.contains(supportMTypes, 1)) {
                                    options.push({
                                        key: 'ssss',
                                        tips: $t("管理员关闭了唯一性规则，按照唯一性ID进行更新导入"),
                                        question: $t("获取唯一性ID请到数据列表页进行导出操作")
                                    })
                                }
                                if(_.contains(supportMTypes, 3) && tt) {
                                    options.push({
                                        key: 'bbbb',
                                        tips: tt
                                    })
                                }
                            }
                            
                            if(item.IsUpdateOwner) {
                                options.push({
                                    label: $t('是否更新负责人'),
                                    key: 'IsUpdateOwner',
                                    radios
                                })

                                radioValues.IsUpdateOwner = false;

                                let {labelInfo, oldOwnerTeamMember, roleOptions, sameUpdateOptions} = item.ownerUpdateInfo

                                let groups = [{
                                    label: $t('将原负责人'),
                                    key: 'strategy',
                                    radios: [{
                                        value: 1,
                                        label: labelInfo.team
                                    }, {
                                        value: 2,
                                        label: labelInfo.group,
                                        tip: labelInfo.generalTip
                                    }]
                                }, {
                                    label: $t('团队权限'),
                                    key: 'rolePermission',
                                    checks: roleOptions,
                                    hide: true
                                }, {
                                    label: $t('权限'),
                                    key: 'permissionType',
                                    hide: true,
                                    radios: [{
                                        value: 1,
                                        label: $t('只读')
                                    }, {
                                        value: 2,
                                        label: $t('读写')
                                    }] 
                                }]

                                radioValues.strategy = oldOwnerTeamMember.strategy;
                                radioValues.rolePermission = [];
                                radioValues.permissionType = 1;

                                if(sameUpdateOptions) {
                                    groups.push({
                                        label: $t('变更以下对象与客户同负责人的数据'),
                                        key: 'otherObjects',
                                        isVertival: true,
                                        className: 'i-vertival',
                                        supportAll: true,
                                        checks: sameUpdateOptions,
                                        tips: [
                                            `1.${$t("一并变更的数据仅是负责人为当前客户的负责人不是该客户负责人的数据不会变更。")}`,
                                            `2.${$t("已完成商机2.0（输赢单无效的商机2.0）、生命状态为未生效、变更中、作废的数据都不会转移给新的客户负责人。")}`,
                                            `3.${$t("已完成商机（输赢单无效的商机）、生命状态为未生效、变更中、作废的数据都不会转移给新的客户负责人。")}`,
                                        ]
                                    })

                                    radioValues.otherObjects = [];
                                }

                                options.push({
                                    groups,
                                    parentKey: 'IsUpdateOwner'
                                })
                            }
                        }

                        if(supportMTypes && supportMTypes.length && !item.controlMappings) {
                            let t = {
                                1: {
                                    label: $t('唯一性ID'),
                                    tip: $t('当主属性重复时，可按唯一性ID导入，获取唯一性ID请到数据列表页进行导出操作') 
                                },
                                2: {
                                    label: $t('主属性')
                                },
                                3: {
                                    label: $t('唯一性规则')
                                },
                                4: {
                                    label: $t('任意字段'),
                                    hasAny: true
                                }
                            }

                            let radios = [];
                            _.each(supportMTypes, mt => {
                                let tt = t[mt];
                                if(mt == 4 && !item.anyFields) return;
                                if(tt) {
                                    radios.push({
                                        value: mt,
                                        ...tt,
                                        anyFields: mt == 4 ? item.anyFields : void 0
                                    })
                                }
                            })

                            options.push({
                                label: $t('请选择数据导入识别方式'),
                                key: 'MatchingType',
                                radios
                            })

                            radioValues.MatchingType = _.findWhere(radios, {value: 2}) ? 2 : radios[0].value;
                            _.findWhere(radios, {value: 4}) && (radioValues.specifiedField = void 0);
                        }

                        if(item.IsOpenWorkFlow) {
                            let flag = item.hasThruster && isAdd;
                            options.push({
                                hide: false,
                                label: flag ? $t('是否触发工作流和阶段推进器') : $t('是否触发工作流'),
                                key: 'IsWorkFlowEnabled',
                                radios
                            })

                            radioValues.IsWorkFlowEnabled = false;
                        }

                        if(item.IsApprovalFlow && isAdd) {
                            options.push({
                                hide: false,
                                label: $t('是否触发审批流'),
                                key: 'IsApprovalFlowEnabled',
                                radios
                            })

                            radioValues.IsApprovalFlowEnabled = false;
                        }

                        if(item.IsTextureImport) {
                            options.push({
                                hide: false,
                                label: $t('paas.object.picture.import.type'),
                                key: 'IsTextureImportEnabled',
                                radios: [{
                                    value: true,
                                    label: $t('paas.object.texture.import'),
                                    tip: $t('paas.object.import.pic.tip')
                                }, {
                                    value: false,
                                    label: $t('paas.object.picture.path.import'),
                                    tip: $t('paas.object.import.path.tip')
                                }]
                            })

                            radioValues.IsTextureImportEnabled = false;
                        }

                        if(item.IsCheckOutOwner) {
                            options.push({
                                label: $t('是否校验外部负责人'),
                                key: 'IsCheckOutOwner',
                                radios
                            })

                            radioValues.IsCheckOutOwner = false;
                        }

                        if(item.IsRemoveOutTeamMember && !isAdd) {
                            options.push({
                                label: $t('是否移除外部相关团队成员'),
                                key: 'IsRemoveOutTeamMember',
                                radios
                            })

                            radioValues.IsRemoveOutTeamMember = false;
                        }
                    }

                    this.dImportType && helper._parseByImportConfig(isAdd, options, defalutValues);

                    this.dOptions = options;

                    if(!_.isEmpty(defalutValues)) {//默认值更新到选项上
                        _.each(radioValues, (v, k) => {
                            let vv = defalutValues[k];
                            if(vv !== void 0) {
                                radioValues[k] = vv;
                            }
                        })
                    }

                    let ss = isAdd ? item.insertImport : item.updateImport;
                    if(ss && (ss.triggerWorkFlow?.readOnly || ss.triggerApprovalFlow?.readOnly || ss.textureImport?.readOnly)) {//开启后台管控
                        radioValues.isExpendFlow = defalutValues.isExpendFlow || false;
                        options.push({
                            flowControl: true,
                            hide: !radioValues.isExpendFlow //默认收起
                        })
                        //拿出来放options的后面
                        _.find(options, (r, index) => {
                            if(r.key === 'IsWorkFlowEnabled') {
                                r.hide = !radioValues.isExpendFlow;
                                options.push(options.splice(index, 1)[0]);
                                return true;
                            }
                        })
                        _.find(options, (r, index) => {
                            if(r.key === 'IsApprovalFlowEnabled') {
                                r.hide = !radioValues.isExpendFlow;
                                options.push(options.splice(index, 1)[0]);
                                return true;
                            }
                        })
                        _.find(options, (r, index) => {
                            if(r.key === 'IsTextureImportEnabled') {
                                r.hide = !radioValues.isExpendFlow;
                                options.push(options.splice(index, 1)[0]);
                                return true;
                            }
                        })
                    }
                    if(item.controlMappings) {//后台管控导入方式
                        _.each(item.controlMappings, r => {
                            _.find(r.fields, rr => {
                                if(item.objectApiName === rr.targetObjectApiName) {
                                    let sf = rr.specifiedUniqueFieldApiName;
                                    if(sf === '_id') {
                                        radioValues.MatchingType = 1;
                                    } else if(sf === 'name') {
                                        radioValues.MatchingType = 2;
                                    } else {
                                        radioValues.MatchingType = 4;
                                        radioValues.specifiedField = sf;
                                    }
                                    return true;
                                }
                            })
                        })

                        radioValues.isExpendMapping = defalutValues.isExpendMapping === void 0 ? true : defalutValues.isExpendMapping;
                        options.push({
                            hide: !radioValues.isExpendMapping,//默认展开
                            controlMappings: item.controlMappings
                        })
                    } else if(isAdd && radioValues.MatchingType == 4) {//新建时无其它字段，应该是后台管控带入的值，需要去掉
                        let r = _.findWhere(options, {key: 'MatchingType'});
                        radioValues.MatchingType = r && r.radios[0].value;
                        radioValues.specifiedField = void 0;
                    }

                    options.push({
                        key: 'workTip',
                        tips: ''
                    })

                    this.dRadioValues = radioValues;

                    this.dRadioValues.strategy || (this.dRadioValues.strategy = 1);

                    _.each(this.dRadioValues, (v, k) => {
                        this._hackSome(k, v);
                    })
                    
                    helper.set('radioValues', {...radioValues})//值同步出去
                }
            }
        })
    }
})