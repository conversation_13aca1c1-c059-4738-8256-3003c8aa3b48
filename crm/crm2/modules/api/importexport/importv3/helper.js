define(function(require, exports, module) {
    const config = require('../importasync/config');
    const util = CRM.util;
    const paasCRMImportV3 = ['CostAdjustmentNoteObj', 'DeliveryNoteObj', 'GoodsReceivedNoteObj', 'OutboundDeliveryNoteObj', 'RequisitionNoteObj'].concat(util.getUserAttribute('paasCRMImportV3') || []);
    return Backbone.Model.extend({
        initialize(param) {
            let config1 = config.paramConfig;
            let config2 = param.paramConfig || {};
            let paramConfig = {};
            _.each(config1, (obj, apiname) => {
                paramConfig[apiname] = {...obj, ...config2[apiname]}
            })
            _.each(config2, (obj, apiname) => {
                paramConfig[apiname] || (paramConfig[apiname] = {...obj})
            })

            this.set({
                paramConfig:  paramConfig,
                objectCode: param.pageApiname || param.apiname,//大多数时候是和objectApiName保持统一的
                currentImportObject: null,
                importObjectParam: param.importObjectParam || {},
                importRecordType: param.importRecordType,
                importType: '',//导入类型add update
                radioValues: {},
                objectList: [],//objectList中objectCode唯一，objectApiName不唯一
                detailArgs: [],
                fileInfo: null,
                mappingErrors: null,

                ...param.customImportConfig,

                importConfig: param.importConfig,
                roleOptions: param.roleOptions,
                extendAttribute: param.extendAttribute,
                icmgOnImporterMount: param.icmgOnImporterMount,
                importSuccssCb: param.importSuccssCb,
                success: param.success

            })
            // objectList: [],
            // importType: '',
            // pageCode: '',
            // objectCode: '',
			// apiName: '',
            // curObject: {},
            // IsEmptyValueToUpdate: 0,
            // IsWorkFlowEnabled: 0,
            // IsApprovalFlowEnabled: 0,
            // paramConfig: {},
            // importObjectParam: {},
            // detailObjectApiNames: [], //从对象
        },
        initConfigs() {
            return new Promise((resolve, reject) => {
                Promise.all([this.fetchObjectList()]).then(res => {
                    let objectCode = this.get('objectCode');
                    let objectList = res[0];
                    
                    if(objectCode) {
                        if(!_.findWhere(objectList, {objectCode})) {//如果没有找到
                            let tt = _.findWhere(objectList, {objectCode: config.codeAdaptor[objectCode]}) || _.findWhere(objectList, {objectApiName: objectCode});
                            this.set('objectCode', tt && tt.objectCode);
                        }
                    }
                    this.set('objectList', objectList);
                    resolve();
                }).catch(reject)
            })
        },

        getImportItem(objectCode) {
            return _.findWhere(this.get('objectList'), {objectCode: objectCode || this.get('objectCode')});
        },

        loading(flag) {
            this.trigger('loading', flag);
        },

        showError(msg) {
            this.trigger('showError', msg || $t('服务器返回错误!'));
        },

        /**
         * 获取导入对象
         */
        fetchObjectList() {
            return new Promise((resolve, reject) => {
                let account = FS.getAppStore('contactData');
                CRM.util.FHHApi({
                    url: '/EM1HCRMImptExpt/API/v1/object/object_import/service/get_import_object_list',
                    data: {
                        ea: account ? account.enterpriseAccount : 'admin',
                        ...this.get('importObjectParam')
                    },
                    success: res => {
                        if (res.Result.StatusCode === 0 && res.Value) {
                            resolve(this.formatObjectList(res.Value.importObjectList || []));
                        } else {
                            this.showError(res.Result.FailureMessage);
                            reject();
                        }
                    },
                    error: res => {
                        this.showError(res && res.errorMessage);
                        reject([]);
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        /**
         * 处理导入对象数据
         */
        formatObjectList(list) {
            let paramConfig = this.get('paramConfig');
            let objectCode = this.get('objectCode');
            let customList = config.customObjectList();
            let relList = [];
            [].push.apply(list, customList);
            list = _.map(list, item => {
                let _item = {
                    IsEmptyValueToUpdate: 0,
                    hasThruster: item.describeType == 'custom' || _.contains(config.stageThruster, item.objectCode),
                    ...item,
                    ...paramConfig[item.objectCode]
                }
                if(!_item.isNotSupportSaleEvent) {
                    relList.push({
                        value: _item.objectApiName,
                        label: _item.objectName
                    })
                }
                if(_item.showRelation) {
                    _item.relList = relList;
                }

                return _item;
            })
            let apinameFilters = config.apinamefilter();
            list = _.filter(list, item => {
                let flag = _.contains(apinameFilters, item.objectCode) && item.objectCode !== objectCode;
                return !flag;
            })

            let parseObjectList = this.get('parseObjectList');
            return parseObjectList ? parseObjectList(list) : list;
        },

        //过滤掉缓存里不合理的数据
        _filterCahce(data, tt) {
            if(data.MatchingType == 4) {//指定字段
                let anyFields = tt.anyFields || [];
                if(!_.findWhere(anyFields, {value: data.specifiedField})) {//没有找到
                    if(anyFields[0]) {
                        data.specifiedField = anyFields[0].value;
                    } else {
                        delete data.MatchingType;
                        delete data.specifiedField;
                    }
                }
            }
            if(data.recordType && !_.findWhere(tt.recordTypes, {value: data.recordType})) {
                delete data.recordType;
            }
            if(data.detailArg) {
                data.detailArg = _.filter(data.detailArg, d => {
                    return _.findWhere(tt.detailDescribes, {api_name: d.detailApiName});
                })
            }
            if(data.rolePermission) {
                data.rolePermission = _.filter(data.rolePermission, d => {
                    return _.findWhere(tt.ownerUpdateInfo && tt.ownerUpdateInfo.roleOptions || [], {value: d});
                })
            }
            if(data.otherObjects) {
                data.otherObjects = _.filter(data.otherObjects, d => {
                    return _.findWhere(tt.ownerUpdateInfo && tt.ownerUpdateInfo.sameUpdateOptions || [], {value: d});
                })
            }

            return data;
        },
        
         //根据外部传入的importConfig对审批流工作流参数进行再处理
         _parseByImportConfig(isAdd, options, defalutValues) {
            let importItem = this.get('currentImportObject');

            
            // if(importItem.supportType !== void 0) {// 0 不支持更新导入 3不支持新建导入 默认是1
            //     if(importItem.supportType == 3) {
            //         delete importItem.matchingTypes.insertImport;
            //     } else if(importItem.supportType == 0) {
            //         delete importItem.matchingTypes.updateImport;
            //     }
            // }
            //目标对象特殊规则
            if(importItem.IsEmptyValueToUpdate) {
                defalutValues.IsEmptyValueToUpdate = true;
                if(importItem.objectApiName === 'GoalValueObj' && importItem.goal_type == 'PersonnelObj') {
                    (_.findWhere(options, {key: 'IsEmptyValueToUpdate'}) || {}).disabled = true;
                }
            }

            let config = isAdd ? importItem.insertImport : importItem.updateImport;
            
            let importConfig = (this.get('importConfig') || {})[isAdd ? 'add' : 'update'] || {};//某些企业强制写死传进来的导入配置
            let workFlow = _.findWhere(options, {key: 'IsWorkFlowEnabled'})//工作流
            if(workFlow) {
                //let flag = item.hasThruster && isAdd;
                workFlow.disabled = config?.triggerWorkFlow?.readOnly;
                workFlow.disabled && (workFlow.forceDisabled = true);
                if(workFlow.disabled) {//开启管控
                    defalutValues.IsWorkFlowEnabled = !!(config?.triggerWorkFlow?.value);
                }

                if(importConfig.workFlow) {
                    if(importConfig.workFlow.disabled) {
                        workFlow.disabled = true;
                    }
                    if(importConfig.workFlow.value) {
                        defalutValues.IsWorkFlowEnabled = true;
                    }
                }
            }

            let approvalFlow = _.findWhere(options, {key: 'IsApprovalFlowEnabled'})//审批流
            if(approvalFlow) {
                approvalFlow.disabled = config?.triggerApprovalFlow?.readOnly;
                if(approvalFlow.disabled) {
                    defalutValues.IsApprovalFlowEnabled = !!(config?.triggerApprovalFlow?.value);
                }

                if(importConfig.approveFlow) {
                    if(importConfig.approveFlow.disabled) {
                        approvalFlow.disabled = true;
                    }
                    if(importConfig.approveFlow.value) {
                        defalutValues.IsApprovalFlowEnabled = true;
                    }
                }
            }

            let textTureImport = _.findWhere(options, {key: 'IsTextureImportEnabled'})//审批流
            if(textTureImport) {
                textTureImport.disabled = config?.textureImport?.readOnly;
                if(textTureImport.disabled) {
                    defalutValues.IsTextureImportEnabled = !!(config?.textureImport?.value);
                }

                if(importConfig.textTureImport) {
                    if(importConfig.textTureImport.disabled) {
                        textTureImport.disabled = true;
                    }
                    if(importConfig.textTureImport.value) {
                        defalutValues.IsTextureImportEnabled = true;
                    }
                }
            }

            //人员部门特殊逻辑
            //判断 是否是部门更新导入，且灰度了部门名称允许重复
            //识别方式为两种：其他-部门编码(默认选中且禁用)、唯一性ID
            //默认识别方式默认选中其它字段，默认选项为部门编码(dept_code)/人员编码(employee_number)，且不可选择其它选项
            let grayKey = isAdd || {DepartmentObj: 'isGrayDepartmentRepeatNameImport', PersonnelObj: 'isGrayPersonelRepeatNameImport'}[importItem.objectApiName];
            if(grayKey && util.getUserAttribute(grayKey)) {
                let mkey = {DepartmentObj: 'dept_code', PersonnelObj: 'employee_number'}[importItem.objectApiName];
                let mt = _.findWhere(options, {key: 'MatchingType'});
                let radio = mt && _.findWhere(mt.radios, {value: 4});
                if(radio) {
                    radio.anyFields = _.filter(radio.anyFields, a => a.value === mkey);
                    defalutValues.MatchingType = 4;
                    defalutValues.specifiedField = mkey;
                }
            }
        },

        parseControlMappings(referenceFieldMapping, objectApiName) {
            let rs = [];
            _.each(referenceFieldMapping, r => {
                let tt = _.findWhere(rs, {objectApiName: r.objectApiName});
                let flag = r.objectApiName === objectApiName;
                if(!tt) {
                    tt = {
                        objectApiName: r.objectApiName,
                        title: flag ? $t('本对象') : $t('从对象') + '：' + r.objectLabel,
                        fields: [],
                        isMaster: flag,
                        hide: !flag
                    }
                    if(flag) {
                        rs.unshift(tt);
                    } else {
                        rs.push(tt);
                    }
                }
                tt.fields.push({
                    ...r,
                    value: r.specifiedUniqueFieldLabel,
                    label: r.objectReferenceFieldLabel,
                    tips: [`${$t('字段名称')}：${r.objectReferenceFieldLabel}`, `${$t('所属对象')}：${r.targetObjectLabel}`],
                })
            })

            return rs.length ? rs : void 0;
        },

        /**
         * @description 更新导入对象信息
         */
        updateImportObject(item) {
            return new Promise(resolve => {
                Promise.all([this.fetObjectInfo(item), this.fetchRecordType(item), this.fetchOwnerUpdateIfo(item), this._getLocalStore(item.objectCode)]).then(res => {
                    if(!res[0].importObject) {
                        res[0].importObject = {};
                    }

                    let mt = res[0].importObject.matchingTypes;
                    this.set('objectCode', item.objectCode);
                    let tt = {
                        isSupportFieldMapping: !_.contains(paasCRMImportV3, item.objectApiName),
                        controlMappings: this.parseControlMappings(res[0].importObject.referenceFieldMapping, item.objectApiName),
                        ...res[0].importObject,
                        ...item,
                        ...this.get('paramConfig')[item.objectCode],
                        isError: _.isEmpty(res[0]),//当前导入对象出现错误
                        detailDescribes: res[0].detailDescribes,
                        recordTypes: res[1],
                        ownerUpdateInfo: res[2]
                    }

                    let inner = () => {
                        tt.defalutValues = this._filterCahce(res[3], tt);
                        resolve(tt);
                    }

                    if(mt && _.contains(mt.update_import || [], 4)) {
                        CRM.util.FHHApi({
                            url: `/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName`,
                            data: {
                                describe_apiname: item.objectApiName,
                                get_label_direct: true,
                                include_buttons: false,
                                include_layout: false,
                                include_related_list: false,
                                include_describe_extra: false,
                                layout_type: "detail" 
                            },
                            success(res) {
                                if (res.Result.StatusCode === 0) {
                                    let options = [];
                                    const {whitelist = [], blacklist = []} = config.anyFieldConfig[item.objectApiName] || {};
                                    _.each(res.Value.objectDescribe.fields, a => {
                                        if(!a.is_active) return;//禁用字段

                                        if (whitelist.includes(a.api_name)) {
                                            options.push({
                                                label: a.label,
                                                value: a.api_name
                                            });
                                        } else if(a.is_unique && a.api_name !== 'name' && _.contains(['text', 'auto_number', 'url', 'email', 'phone_number'], a.type) && !blacklist.includes(a.api_name)) {
                                            options.push({
                                                label: a.label,
                                                value: a.api_name
                                            })
                                        }
                                    })
                                    if(options.length) {
                                        tt.anyFields = options;
                                    }
                                }
                                inner(tt);
                            },
                            error() {
                                inner(tt);
                            }
                        }, {
                            errorAlertModel: 1
                        })
                    } else {
                        inner(tt);
                    }
                })
            })
        },

        fetchOwnerUpdateIfo(item) {
            return new Promise(resolve => {
                Promise.all([
                    util.getNewoppoConfig(),
                    util.getSaleContractObj(),
                    this.fetchCustomRoles(item.objectApiName),
                ]).then(res => {
                    let name = item.objectCode == 'HighSeasObj' ? '' : item.objectName
                    let labelInfo = {
                        team: $t("移出该{{name}}团队", {
                            name
                        }),
                        general: $t("变更为{{name}}普通成员", {
                            name
                        }),
                        group: $t("变更为{{name}}团队成员", {
                            name
                        }),
                        generalTip: $t("普通成员是指该{{name}}协作人", {
                            name: name || $t('数据')
                        }),
                        groupTip: $t("团队成员是指该{{name}}协作人", {
                            name: name || $t('数据')
                        })
                    };
                    let [status, isNewContract, roleOptions] = res;

                    resolve({
                        labelInfo,
                        sameUpdateOptions: this._hackSameUpdateOptions(item.objectCode, isNewContract, status),
                        oldOwnerTeamMember: {},
                        roleOptions
                    })
                })
            })
        },

        //客户对象变更负责人需要同时变更一些对象
        _hackSameUpdateOptions(apiname, isNewContract, isNewOppo) {
            if(apiname === 'AccountObj' && !util.isGrayScale('CRM_CHANGEOWNER_EXCLUDE_OTHER')) {
                let tt = this.getImportItem('ContactObj') || {};
                let objectList = [{
                    label: tt.objectName || $t('crm.联系人'),
                    value: 'ContactObj',
                },{
                    label: (this.getImportItem('SalesOrderObj') || {}).objectName || $t('crm.销售订单'),
                    value: 'SalesOrderObj',
                }]

                if(isNewContract) {
                    objectList.push({
                        label: (this.getImportItem('SaleContractObj') || {}).objectName || $t('销售合同'),
                        value: 'SaleContractObj'
                    })
                } else {
                    objectList.push({
                        label: (this.getImportItem('ContractObj') || {}).objectName || $t('crm.合同'),
                        value: 'ContractObj'
                    })
                }
                if (isNewOppo) {
                    objectList.unshift({
                        label: (this.getImportItem('NewOpportunityObj') || {}).objectName || $t('crm.商机2.0'),
                        value: 'NewOpportunityObj',
                    })
                } else {
                    objectList.unshift({
                        label: (this.getImportItem('OpportunityObj') || {}).objectName || $t('crm.商机'),
                        value: 'OpportunityObj',
                    })
                }

                return objectList;
            }
        },

        //获取自定义角色
        fetchCustomRoles(apiname) {
            return new Promise(resolve => {
                // 自定义的优先级最高
                let roleOptions = this.get('roleOptions');
                if (roleOptions) return resolve(roleOptions);

                Promise.all([util.supportCustomRole(apiname), CRM.util.fetchTeamRoleList(apiname)]).then(res => {
                    let supportCustomRole = res[0];
                    if(!supportCustomRole) resolve(config.getRoleOptions(apiname));

                    let ordinaryMember = 4; //普通成员默认选中
                    resolve(_.map(res[1] || config.getRoleOptions(apiname), role => ({...role, seleted: role.value == ordinaryMember})))
                })
            })
        },

        fetObjectInfo(item) {
            return new Promise(resolve => {
                let fetchImportInfo = this.get('fetchImportInfo');
                if(fetchImportInfo) {
                    fetchImportInfo(item).then((res) => {
                        resolve(res)
                    });
                    return;
                };

                CRM.util.FHHApi({
                    url: `/EM1HCRMImptExpt/API/v1/object/${item.objectApiName}/controller/ImportObject`,
                    data: {objectCode : item.objectCode},
                    success: (res) => {
                        if (res.Result.StatusCode !== 0) {
                            this.showError(res.Result.FailureMessage);
                        }

                        resolve(res.Value || {});
                    },
                    error: () => {
                        this.showError();
                        resolve({});
                    }
                }, {
                    errorAlertModel: 1
                });
                
            })
        },

        setDetailArgs(newDetailArgs) {
            let detailArgs = this.get('detailArgs') || [];
            if(JSON.stringify(detailArgs) !== JSON.stringify(newDetailArgs)) {
                this.set('detailArgs', newDetailArgs);
                let v1 = _.pluck(newDetailArgs, 'value');
                let v2 = _.pluck(detailArgs, 'value');
                if(v1.length !== v2.length || _.union(v1, v2).length !== v1.length) {
                    this.resetObjectInfo();
                }
            }
        },

        resetObjectInfo() {
            //选了从对象之后，需要对对象的导入信息进行更新
            let citem = this.get('currentImportObject');
            if(/__c$/.test(citem.objectCode)) {
                this.trigger('resetMappinfo');
                return;
            }
            if(!this._resetObjectInfo) {
                this._resetObjectInfo = _.debounce((item) => {
                    let detailObjectApiNames = this.get('IsCheckedDeails') ? _.pluck(this.get('detailArgs'), 'value') : [];

                    this.xxAjax && this.xxAjax.abort();
                    this.xxAjax = CRM.util.FHHApi({
                        url: `/EM1HCRMImptExpt/API/v1/object/${item.objectApiName}/controller/ImportObject`,
                        data: {objectCode : item.objectCode, detailObjectApiNames},
                        success: (res) => {
                            this.xxAjax = null;
                            if (res.Result.StatusCode === 0 && this.get('currentImportObject') === item) {
                                _.extend(item, res.Value.importObject);
                                this.trigger('xxxx');
                            }
                        },
                        error: () => {
                            this.xxAjax = null;
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }, 60);
            };

            this._resetObjectInfo(citem);
        },

        fetchRecordType(item) {
            return new Promise(resolve => {
                let list = [{
                    label: $t('全部字段'),
                    value: 'all'
                }];
                let describeApiName = item.objectApiName;
                if(item && (describeApiName === 'SPUObj' || (item.unionImportApiNameList && item.unionImportApiNameList[1]))) {
                    resolve(list);
                    return;
                }
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + describeApiName + '/controller/ValidRecordType',
                    data: {
                        describeApiName,
                        is_only_active: true,
                        is_import: true
                    },
                    success: (res) => {
                        if(res.Result.StatusCode === 0) {
                            _.each(res.Value.record_list, a => list.push({label: a.label, value: a.api_name}))
                        } else {
                            //this.showError(res.Result.FailureMessage);
                        }
                        resolve(list);
                    },
                    error: () => {
                        //this.showError();
                        resolve(list);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        //文件发生变化时，更新文件信息，更新文件字段报错映射信息
        updateFileInfoAndMapping(file) {
            return new Promise((resolve, reject) => {
                if(!file) {
                    this.set('fileInfo', null);
                    this.set('mappingErrors', null);
                    this.set('fieldMapping', null);
                    return;
                }

                this.loading(true);

                CRM.api.upload_file({
                    file,
                    successCb: (res) => {
                        this.loading();
                        file.filePath = res.filePath;
                        let fileInfo = {
                            filePath: res.filePath,
                            fileExt: CRM.util.getFileExtText(file.name),
                            fileName: file.name || ''
                        }
                        this.set('fileInfo', fileInfo);
                    },
                    failCb: () => {
                        this.loading();
                        this.set('fileInfo', null);
                        this.set('mappingErrors', null);
                        this.set('fieldMapping', null);
                        reject($t('数据上传失败！'));
                    }
                })
            })
        },

        //更新字段映射报错信息，更新字段映射基本信息
        updateFieldMapping() {
            return new Promise((resolve, reject) => {
                this.loading(true);
                this.set('fieldMapping', null);
                this.fetchImportView().then(objectInfo => {
                    this.loading();
                    let mappingErrors = [];
                    let infoLabel = $t('CRM对象中必填字段未映射');
                    function generateError(info, label) {
                        _.each(info.fields, field => {
                            //必填没有找到，则显示错误信息
                            if(field.is_required && !_.findWhere(info.fieldMapping, {apiName: field.api_name})) {
                                mappingErrors.push({
                                    label: label + '【' + field.label + '】',
                                    info: infoLabel,
                                    apiName: info.apiName
                                });
                            }
                        })
                    }

                    if(this.get('importType') !== 'update') {//更新导入不验证必填
                        generateError(objectInfo.masterInfo, $t('主对象'));
                        _.each(objectInfo.detailInfo, info => generateError(info, $t('从对象')));
                    }
                    
                    
                    this.set('mappingErrors', mappingErrors);
                    this.set('mappingInfo', objectInfo);

                    if(!mappingErrors.length) {//无错误信息
                        this.set('fieldMapping', {
                            masterInfo: {
                                apiName:  objectInfo.masterInfo.apiName,
                                fieldMapping: _.map(objectInfo.masterInfo.fieldMapping, c => {
                                    return {
                                        colIndex: c.colIndex,
                                        apiName: c.apiName,
                                        importFieldMark: _.findWhere(objectInfo.masterInfo.fields, {api_name: c.apiName})?.importFieldMark
                                    }
                                })
                            },
                            detailInfo: _.map(objectInfo.detailInfo, info => {
                                return {
                                    apiName: info.apiName,
                                    fieldMapping:  _.map(info.fieldMapping, c => {
                                        return {
                                            colIndex: c.colIndex,
                                            apiName: c.apiName,
                                            importFieldMark: _.findWhere(info.fields, {api_name: c.apiName})?.importFieldMark
                                        }
                                    })
                                }
                            }),
                            supportFieldMapping: true
                        });
                    }
                    resolve({objectInfo, mappingErrors});
                }).catch((message) => {
                    this.loading();
                    this.set('mappingErrors', null);
                    this.set('mappingInfo', null);
                    reject(message);
                })
            })
        },
        
        //获取映射字段相关信息
        fetchImportView() {
            return new Promise((resolve, reject) => {
                let fileInfo = this.get('fileInfo');
                let importItem = this.get('currentImportObject');
                if(!fileInfo || !importItem) return reject();
                if(!importItem.isSupportFieldMapping) return reject();

                let param = this.parseQueryParam();
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + importItem.objectApiName + '/controller/ImportView',
                    data: {
                        filePath: fileInfo.filePath,
                        ImportType: param.ImportType,
                        MatchingType: param.MatchingType,
                        IsTextureImportEnabled: param.IsTextureImportEnabled,
                        objectInfo: {
                            detailInfo: _.map(param.unionImportApiNameList.slice(1), apiName => ({apiName})),
                            masterInfo: {
                                apiName: importItem.objectApiName
                            }
                        }
                    },
                    success:(res) => {
                        if(res.Result.StatusCode === 0) {
                            this.importViewInfo = {};
                            resolve(res.Value.objectInfo);
                        } else {
                            reject(res.Result.FailureMessage);
                        }
                    },
                    error() {
                        reject($t('网络错误，请重试！'));
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * 下载模板
         */
        downloadTpl() {
            return new Promise((resolve, reject) => {
                let errorInfo = this.validate();
                if(errorInfo) return reject(errorInfo);

                let downloadTpl = this.get('downloadTpl');
                if(downloadTpl) {
                    downloadTpl().then(() => resolve()).catch(message => {
                        reject(message);
                    })
                    return;
                }

                let param = this.parseQueryParam();
                let importItem = this.get('currentImportObject');

                let PREFIX = '/EM1HCRMImptExpt/API/v1/object/' + importItem.objectApiName;
                let defaultUrls = {
                    add: PREFIX + '/action/InsertImportTemplate',
                    update: PREFIX + '/action/UpdateImportTemplate',
                    union: PREFIX + '/action/UnionInsertImportTemplate' //联合导入
                }

                let importType = this.get('importType');

                util.FHHApi({
                    url: importItem.downloadUrl || (param.unionImportApiNameList.length ? defaultUrls['union'] : defaultUrls[importType]),
                    data: {
                        ...param,
                        ...importItem.downlaodParam,
                        supportFieldMapping: importItem.isSupportFieldMapping
                    },
                    success: (res) => {
                        if (res.Result.StatusCode === 0) {
                            let rv = res.Value;
                            let url = '';
                            let displayName = importItem.objectName + (param._child_display_name ? ',' + param._child_display_name : '');
                            displayName = $t('{{displayName}}对象{{typeText}}导入模板', {displayName, typeText: $t(importType === 'update' ? '更新' : '新建').toLowerCase()}) + '.xlsx';
                            if (rv.path) {
                                url = CRM.util.getFscTempFileUrl(rv.path, displayName, true);
                            } else {
                                url = CRM.util.getFscLinkByToken(rv.TokenID || rv.Url, 'down');
                            }
                            let app = CRM.util.getConnectAppInfo();
                            if(app && app.appId) { //下游互联应用
                                url = url + '&appid=' + app.appId;
                            }

                            location.href = url;
                            resolve();
                        } else {
                            reject(res.Result.FailureMessage || $t('下载失败'));
                        }
                    },
                    error: () => {
                        reject($t('网络错误，请重试！'));
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        validate() {
            let importItem = this.get('currentImportObject');

            if(!importItem) return $t('请选择业务对象');

            let importType = this.get('importType');
            if(!importType) return $t('请选择导入方式');

            let detailArgs = this.get('detailArgs') || [];
            if(this.get('IsCheckedDeails') && !detailArgs.length) return $t('paas.crm.api.importasync.select_sub_obj', null, '请选择子业务对象');

            let relatedApiNameList = this.get('relatedApiNameList') || [];
            if(importItem.showRelation && !relatedApiNameList.length) return $t('导入销售记录至少选择一个关联对象');
        },

        //处理参数
        parseQueryParam() {
            let radioValues = this.get('radioValues');
            let importItem = this.get('currentImportObject');
            let queryParam = {
                objectCode: importItem.objectCode,
                MatchingType: radioValues.MatchingType,
                ImportType: this.get('importType') === 'update' ? 1 : 0,
                unionImportApiNameList: importItem.unionImportApiNameList || [],
                IsTextureImportEnabled: radioValues.IsTextureImportEnabled
            };
            
            if(this.get('IsCheckedDeails')) {//同时导入从对象
                let detailArgs = this.get('detailArgs') || [];
                queryParam.unionImportApiNameList = [importItem.objectApiName, ..._.pluck(detailArgs, 'value')];
                queryParam._child_display_name = _.pluck(detailArgs, 'label').join(',');
                queryParam.detailArg = _.map(detailArgs, item => {
                    let detailRecordType = item.children.value;
                    return {
                        detailApiName: item.value,
                        detailRecordType: detailRecordType === 'all' ? '' : detailRecordType || ''
                    }
                })
            }

            //销售记录同时导入关联对象
            if(importItem.showRelation) {
                let relatedApiNameList = this.get('relatedApiNameList') || [];
                queryParam.relatedApiNameList = relatedApiNameList;
                queryParam.Type  = 1;
            }

            let recordType = this.get('recordType');
            if(recordType && recordType !== 'all') {
                queryParam.recordType = recordType;
            }

            return queryParam;
        },

        /**
         * 导入
         */
        doImport(param) {
            return new Promise((resolve, reject) => {
                let error = this.validate();
                if(error) return reject(error);

                let fileInfo = this.get('fileInfo');
                if(!fileInfo) return reject($t('请添加您要导入的数据'));
                
                let radioValues = this.get('radioValues');
                if(radioValues.strategy == 2 && !(radioValues.rolePermission || []).length) return reject($t('请选择团队权限'));

                let importItem = this.get('currentImportObject');
                let fieldMapping = this.get('fieldMapping');
                if(importItem.isError || (importItem.isSupportFieldMapping && !fieldMapping)) return reject($t('页面存在错误信息'));

                if(radioValues.IsTextureImportEnabled && (fileInfo.fileExt || '').toLowerCase() !== 'xlsx') {
                    return reject($t('paas.crm.importext.textlimit'));
                }

                let data = {
                    timezone: Date.timezone,
                    templateId: 'crm_001',
                    jobType: 1,
                    apiName: importItem.objectApiName,
                    recordType: '',
                    apiFullName: importItem.objectName,
                    notifies: this.get('notifies'),
                    queryParam: {
                        isNoBatch: importItem.isNoBatch,
                        IsEmptyValueToUpdate: false,
                        IsWorkFlowEnabled: false,
                        IsApprovalFlowEnabled: false,
                        extendAttribute: this.get('extendAttribute'),
                        importMethod: this.get('importType') === 'add' && importItem?.insertImport?.importMethod || void 0,
                        IsUnionDuplicateChecking: !!this.get('IsUnionDuplicateChecking'),
                        
                        ...this.get('fieldMapping'),
                        ...this.parseQueryParam(),
                        ...radioValues,
                        ...fileInfo,

                        oldOwnerTeamMember: {
                            otherObjects: radioValues.otherObjects,
                            roleList: radioValues.strategy == 2 ? radioValues.rolePermission : void 0,
                            strategy: radioValues.IsUpdateOwner ? radioValues.strategy : void 0,
                            permissionType: radioValues.strategy == 2 ? radioValues.permissionType : void 0,
                        }
                    }
                }

                let doImport = this.get('doImport');
                if(doImport) {
                    doImport(data).then(() => resolve()).catch(error => reject(error));
                    return;
                }

                //记忆
                this._setLocalStore(data);

                data.strategy = data.otherObjects = data.rolePermission = data.permissionType = void 0;
                data.queryParam.referenceFieldMapping = importItem.referenceFieldMapping;

                util.FHHApi({
                    url: "/EM1HJobCenter/inputJobCenter/createJob",
                    data,
                    success: (res) => {
                        if (res.Result.StatusCode == 0) {
                            if (res.Value.code == -1) {
                                reject(res.Value.message);
                            } else {
                                let importSuccssCb = this.get('importSuccssCb');
                                if(importSuccssCb) {
                                    importSuccssCb();//自定义导入成功回调(如后台管理人员列表导入成功跳转后台管理菜单页面)
                                    return resolve();
                                }

                                let app = CRM.util.getConnectAppInfo();
                                if(app && app.appId) {
                                    util.alert($t('paas.crm.api.importasync.connectapp_promptly', null, '操作成功，导入结果会以业务通知形式下发，请及时关注'));
                                    return resolve();
                                };
    
                                if (window.location.hash.indexOf('#crm/iorecords') > -1) {
                                    let success = this.get('success');
                                    success && success();
                                } else {
                                    //特殊企业不走新页签打开
                                    if(_.contains(['fktest087', 'fktest299', 'cdnlight2021', 'dthznq578881'], CRM.ea)) {
                                        CRM.control.navigate('#crm/iorecords');
                                    } else {
                                        window.open('#crm/iorecords');
                                    }
                                }

                                resolve();
                            }
                        } else {
                            reject(res.Result.FailureMessage);
                        }
                    },
                    error: () => {
                        reject($t('网络错误，请重试！'));
                    },
                }, {
                    errorAlertModel: 1,
                    submitSelector: param && param.$target
                });
            })
        },

        //按对象记忆缓存数据
        _setLocalStore(_params) {
            try {
                FS.store.setItem(`crm.import.${_params.queryParam.objectCode}.cache`, JSON.stringify({notifies: _params.notifies, ..._params.queryParam}));
            } catch(e) {}
        },

        //按对象objectCode得到缓存的数据
        _getLocalStore(objectCode) {
            return new Promise(resolve => {
                try {
                    FS.store.getItem(`crm.import.${objectCode}.cache`).then(content => {
                        let cache = (content && JSON.parse(content)) || {};
                        resolve(cache);
                    })
                } catch(e) {
                    resolve({});
                }
            })
        }
    })
})