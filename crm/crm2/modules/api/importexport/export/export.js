
define(function(require, exports, module) {
    var Dialog = require('crm-widget/dialog/dialog');
    var config = require('./config');
    var contentTpl = require('./template/content-html');
    var Model = require('./model');
    var util = require('crm-modules/common/util');


    var crmObjectExport = !FS.util.getUserAttribute('crmObjectExport1');//TODO: 970删除

    var Export = Dialog.extend({

        attrs: {
            className: 'crm-c-export',
            width: 800,
            title: $t("导出"),
            showScroll: false,
            content:'',
            apiname: '',
            displayName: '',
            showUniqueID: true,  //唯一性id
            showExportfield: true,  //字段选择
            showExportRelTeam:true,
            async: true,
            fieldApinameList: [], //列设置展示的字段,
            setColumnsParams: {}, //列设置可传参 控制显示 隐藏  勾选
            objectDescribeExt: {},
            // recordType: '',
            // queryParam: '',
            // dataList: [],
            // getTokenUrl: '',
            // getTokenParam: {},
            // tabs: {
            //     type: 'checkbox',
            //     title: '',
            //     layout: '',
            //     list: [{
            //         title: '',
            //         name: '',
            //         value: '',
            //         disabled: false,
            //         checked: true,
            //     }]
            // }
        },

        ajaxs: {},

        events: _.extend(Dialog.superclass.events, {
            'click .mn-checkbox-box p': 'checkboxHandle',
            'click .j-export': 'onExport',
            'click .j-retry': 'onExport',
            'click .j-close': 'hide'
        }),

        initialize: function (options) {
            var me = this;
            let { setColumnsParams, relevantTeamParams, objectDescribeExt = {} } = options;

            var _opts = _.extend({}, me.attrs, config.options[options.pageApiname || options.apiname], options);
            _.contains(config.unsupportUniqueIDList, _opts.apiname) && (_opts.showUniqueID = false);
                //导出支持列设置配置
            let apiParams = { setColumnsParams, relevantTeamParams };
            _opts.funcParams = {};
            Object.keys(apiParams).forEach(v => {
                if (v && apiParams[v]) {
                    _opts.funcParams[v] = me.getSetColParams(apiParams[v]);
                }
            })
            
            let showExportfield = (_opts.queryParam && (_opts.queryParam.search_template_id || _opts.queryParam.ignore_search_template_id || _opts.queryParam.is_full_field_search)) && (setColumnsParams ? !_opts.funcParams?.setColumnsParams.hidden: _opts.showExportfield);
            me.isV2 = showExportfield && crmObjectExport;
            //默认不隐藏
            _opts.content = contentTpl({
                showUniqueID: _opts.showUniqueID,
                showExportfield: showExportfield,
                tabs: _opts.tabs,
                showExportRelTeam: me.isV2 ? false : objectDescribeExt.is_related_team_enabled == false ? false : relevantTeamParams ? !_opts.funcParams?.relevantTeamParams.hidden : true,
                crmObjectExport: crmObjectExport
            });
            _opts.showExportfield = showExportfield;
            _opts.title = options.title || $t("crm.导出")+ ' ' + _.escape(_opts.displayName || '');
            _opts.immediately = !((_opts.tabs && _opts.tabs.list.length) || _opts.showUniqueID || showExportfield);
            _opts.describe = _opts.describe || {};
            me.initModel(_opts);
            Export.superclass.initialize.call(this, _opts);
            me.$el = me.element;
        },

        _renderGuide() {},

        initModel: function(data) {
            var me = this;
            data.dataList && data.dataList.length && (data.dataIdList = _.pluck(data.dataList, '_id'));
            data._show_field_list&&data._show_field_list.length && (data.fieldApinameList =_.pluck(data._show_field_list, 'data') )
            data.recordType && data.recordType !== 'all' && (data.recordType_apiName = data.recordType);

            me.cmodel = new Model(_.extend({
                dataIdList: data.dataIdList,
                recordType_apiName: data.recordType_apiName,
                field_api_name_list: data.fieldApinameList,
                new_field_api_name_list: []
            }, data.queryParam));

            if (data.tabs && data.tabs.list) {
                var _list = _.filter(data.tabs.list, function(item) {
                    return item.checked;
                });
                me.cmodel.set(data.tabs.name, _.pluck(_list, 'value'));
            }
        },

        //校验数据
        getSetColParams(data = {}) {
            let params = {
                hidden: false,
                readOnly: false,
                according_to_list: false,
                no_export_relevant_team: false
            };
            Object.keys(data).forEach(v => {
                params[v] = typeof data[v] == 'boolean' ? data[v] : params[v];
            })
            return params;
        },

        checkboxHandle: function(e) {
            var me = this;
            e.stopPropagation();
            var $tg = $(e.currentTarget);
            var $value = $tg.find('.mn-checkbox-item');
            var $wrapper = $tg.closest('.mn-checkbox-box');
            var _value = $value.data('value');
            if ($value.hasClass('disabled-selected')) return;
            $value.toggleClass('mn-selected');

            var flag = $value.hasClass('mn-selected');
            var key = $wrapper.data('key');

            if ($wrapper.find('p').length > 1) {
                var _v = me.cmodel.get(key) || [];
                if (flag) {
                    _v.push(_value);
                } else {
                    _v = _.filter(_v, function(__v) {
                        return __v !== _value;
                    });
                }
                me.cmodel.set(key, _v);
                return ;
            }

            me.cmodel.set(key, flag);

            if(key === 'export_relevant_team') {
                CRM.util.setCache('export_relevant_team_config', flag);
            }
        },

        getParam: function() {
            var me = this;

            // 当为选中导出时，需要过滤掉query里面的数据
            if (me.cmodel.get('dataIdList') && me.cmodel.get('dataIdList').length) {
                var queryinfo = JSON.parse(me.cmodel.get('search_query_info'));
                // 线索池和公海 此过滤条件需保留
                var leads_pool_id = _.findWhere(queryinfo.filters, {field_name: 'leads_pool_id'}) || _.findWhere(queryinfo.filters, {field_name: 'leads_pool_id.name'});
                var high_seas_id = _.findWhere(queryinfo.filters, {field_name: 'high_seas_id'}) || _.findWhere(queryinfo.filters, {field_name: 'high_seas_id.name'});
                queryinfo.filters = leads_pool_id ? [leads_pool_id] : (high_seas_id ? [high_seas_id] : []);
                queryinfo = JSON.stringify(queryinfo);
                me.cmodel.set('search_query_info', queryinfo);
            }

            var _params = _.extend({}, me.cmodel.toJSON(), me.get('getTokenParam'));

            if(this.mdInstance && this.mdInstance.getDetailArg) {
                _.extend(_params, this.mdInstance.getDetailArg());
            }

            //是否同时导出从对象
            if(_params.detail_object_api_names && _params.detail_object_api_names.length) {
                _params.detail_arg = {
                    detail_object_api_names: _params.detail_object_api_names,
                    include_id: !!_params.include_md_id,
                    detail_info: _params.detail_info
                }
            }
            _params.include_md_id = _params.detail_object_api_names = _params.detail_info =void 0;


            //添加灰度 如果未勾选根据列设置导出 删除according_field_list
            // 有打印模板不校验
            let isTemplate = me.cmodel.get('bulk_export_type') !== "default";
            if (!isTemplate && crmObjectExport && me.optionCheckItem) {//v2版本
                var _isDeleteFieldList = _params.according_to_list_select
                _params.field_api_name_list = _params.new_field_api_name_list;
                // 修改不论是否根据列设置导出都传参field_api_name_list
                // _isDeleteFieldList&& delete _params.field_api_name_list
                if (!_isDeleteFieldList && !_params.field_api_name_list.length) {
                    // 未开启根据列设置导出且未选择导出字段，错误提示
                    FxUI.Message({
                        isMiddler: true,
                        duration: 1500,
                        message: $t('请选择导出字段'),
                        type: 'error'
                    })
                    return false;
                }
            } else {
                var _isDeleteFieldList = _params.according_to_list
                _isDeleteFieldList&& delete _params.field_api_name_list
            }

            delete _params.export_relevant_team;
            delete _params.according_to_list_select;
            delete _params.new_field_api_name_list;

            // _params.no_export_relevant_team =  !me.$('.j-ert').hasClass('mn-selected');
            //关闭相关团队时不导出相关团队 no_export_relevant_team
            _params.no_export_relevant_team = (me.get('objectDescribeExt') || {}).is_related_team_enabled == false ? true : me.$('.j-ert').length ? !me.$('.j-ert').hasClass('mn-selected') : _params.no_export_relevant_team;
            // _params.export_fileAt
            _params.list_component && delete _params.list_component;
            _params.pageSizeOption && delete _params.pageSizeOption;

            if(!isTemplate && me.isV2) {//v2版本，根据字段补齐相关团队属性 和 附件图片字段签名字段
                me._hackV2Param(_params);
            }

            me._setLocalStore(_params);

            if(me.get('thirdExportParam')) {
                _params = _.extend(_params, me.get('thirdExportParam'));
            }

            return _params;
        },

        _hackV2Param(_params) {
            let fields = (this.get('describe')|| {}).fields || {};
            
            if(this.optionItem && this.optionItem.export_fileAttachment) {
                let file_fields = [];
                _.each(_params.field_api_name_list, a => {
                    let field = fields[a] || {};
                    let types = ["image", "file_attachment", "signature"];
                    if(_.contains(types, field.quote_field_type) || _.contains(types, field.type)) {
                        file_fields.push(a);
                    }
                })

                _params.file_fields = file_fields;
            }

            _params.no_export_relevant_team = true;
            _.find(_params.field_api_name_list, a => {
                if(a === 'relevant_team') {//需要导出相关团队
                    _params.no_export_relevant_team = false;
                    return true;
                }
            })
            
            delete _params.print_template_id;

            return _params;
        },

        _setLocalStore(_params) {
            if(!this.isV2) return; //v2版本才记忆
            try {
                let cache = {};
                //记忆1 是否根据excel模板导出
                cache.bulk_export_type = _params.bulk_export_type === 'printTemplate';

                if(cache.bulk_export_type) {
                    _.extend(cache, this._cacheData, {bulk_export_type: true});
                    cache.print_template_id =  _params.print_template_id;
                } else {
                    cache.print_template_id = this._cacheData.print_template_id;

                    //记忆2 导出表格中是否包含字段唯一性ID
                    cache.include_id = !!_params.include_id;

                    //记忆3 是否根据列设置导出
                    cache.export_by_column = this.optionCheckItem.isShowcolset;

                    //记忆4 当前选择导出的字段
                    cache.field_api_name_list = _params.field_api_name_list;

                    let detail_arg = _params.detail_arg || {};
                    //记忆5 已选从对象
                    cache.md = detail_arg.detail_object_api_names || [];

                    //记忆6 从对象是否包含字段唯一性ID
                    cache.md_include_id = !!detail_arg.include_id;

                    //记忆7 从对象是否导入图片附件签名
                    cache.detail_info = detail_arg.detail_info || [];

                    //记忆8 是否导出图片附件
                    cache.export_file = !!(this.optionItem && this.optionItem.export_fileAttachment);

                    //记忆9 从对象是否导出图片附件
                    cache.md_export_file = !!(this.mdInstance && this.mdInstance.export_fileAttachment);
                }

                FS.store.setItem(`crm.export.${this.get('apiname')}.cache`, JSON.stringify(cache));

            } catch(e) {}
        },

        _getLocalStore() {
            return new Promise(resolve => {
                let apiname = this.get('apiname');
                if(!this.isV2) return resolve();
                try {
                    FS.store.getItem(`crm.export.${apiname}.cache`).then(content => {
                        let cache = (content && JSON.parse(content)) || {};
                        this._cacheData = cache;
                        resolve(cache);
                    })
                } catch(e) {
                    resolve();
                }
            })
        },

        parseParam: function(param) {
            var me = this;
            return {
                apiName: param.object_describe_api_name,
                apiFullName: me.get('displayName'),
                templateId: 'crm_002',
                queryParam: param,
                jobType: 2,
            };
        },

        onExport: function(e) {
            var me = this;
            if (this.invalidate) return;
            var params = me.getParam();
            if (!params) return;
            e && e.preventDefault();
            me.showLoading();
            if (me.get('async')) {
                me.exportAsync(params);
            } else {
                me.exportSync(params);
            }

            // 导出埋点
            try {
                window.logger.action({
                    eventId: 's-paasobj_list_export',
                    str1: params.object_describe_api_name
                })
            } catch(e) {}
        },

        exportSync: function(params) {
            var me = this;
            me.cmodel.getToken.abort = false;
            me.ajaxs.export = me.cmodel.getToken(me.get('getTokenUrl'), params)
            .done(function(data) {
                me.showResult('success', data);
            })
            .fail(function(msg) {
                me.showResult('fail', msg);
            })
            .always(function() {
                me.ajaxs.export = null;
                me.hideLoading();
            })
        },

        exportAsync: function(params) {
            var me = this;
            params = me.parseParam(params);
            me.ajaxs.export = me.cmodel.exportAsync(params)
            .then(function(data) {
                if (data.status < 2) {
                    setTimeout(function(){
                        me.queryJobStatus(data);
                    }, 2000);
                } else {
                    // todo 是否返回别的状态，是否需要全部处理一遍
                }
            })
            .fail(function(msg) {
                me.showResult('fail', msg);
                // me.hideLoading();
            })
            .always(function() {
                me.ajaxs.export = null;
            })
        },

        queryJobStatus: function(data) {
            var me = this;
            var _tips = window.PRM || Fx.imAppStatus === 'hidden' ? $t("导出数据可能需要较长时间，已自动转入后台导出，完成后系统会发送业务通知") : $t("导出数据可能需要较长时间，已自动转入后台导出，完成后系统会在企信的文件助手中提示");
            var _count = 0;
            query();
            function query() {
                _count++;
                if (_count > 4) {
                    me.showResult('tips', _tips);
                    return;
                }
                if(!me.cmodel) return;
                
                me.ajaxs.query = me.cmodel.queryJob({
                    jobId: data.id,
                })
                .then(function(res) {
                    if (res.status === 2) {
                        me.showResult('success', {
                            path: res.url,
                            file_name: res.fileName || $t('{{displayName}}对象{{typeText}}结果_{{moment}}', {
                                displayName: me.get('displayName'),
                                moment: FS.moment.unix(res.startTime  / 1000).format('YYYYMMDD'),
                                typeText: $t('导出')
                            }),
                            ext: res.fileExt,
                        });
                    } else {
                        setTimeout(query, 2000);
                        // me.showResult('tips', _tips);
                    }
                })
                .fail(function() {
                    me.showResult('tips', _tips);
                })
                .always(function() {
                    me.ajaxs.query = null;
                });
            }
        },

        showResult: function(key, data) {
            var me = this;
            var $result = $('.export-result', me.$el);
            var _herf = '';
            $result.attr('data-show', key);
            me.hideLoading();
            switch(key) {
                case 'success':
                    if (data.path) _herf = CRM.util.getFscLink(data.path, data.file_name + '.' + data.ext, true);
                    $('.export-success .j-download-excel', me.$el).attr('href', _herf);
                    break;
                default:
                    $('.export-' + key + ' .export-result-tip', me.$el).html(data)
                    break;
            }
        },

        showLoading: function() {
            var me = this;
            var $process = $('.export-process span', me.$el);
            var percent = 0;
            me.showResult('loading');
            me.processTimer = setInterval(function() {
				percent++;
				if (percent == 95) {
                    clearInterval(me.processTimer);
                    me.processTimer = null;
				}
				$process.css('width', percent + '%');
			}, 50);
        },

        hideLoading: function() {
            var me = this;
            var $process = $('.export-process span', me.$el);
            $process.css('width', 0);
            me.processTimer && clearInterval(me.processTimer);
            me.processTimer = null;
        },

        show: function() {
            var me = this;
            var result = Export.superclass.show.call(me);
            me.get('immediately') && me.onExport();

            let $load = FxUI.create({
				wrapper: this.$el[0],
				template: '<div v-loading="true" style="position:absolute;top:0;height:100%;width:100%;z-index:10000;"></div>'
			})

            Promise.all([this.grayPrintExport(), this._getLocalStore(), this.fetchRelations()]).then(res => {
                $load.destroy();

                me._noSupportTemplate = !res[0].supportPrintExport;

                let cache = res[1] || {};

                if(cache.include_id) {
                    me.$el.find('[data-key="include_id"]>p').click();
                }

                me.renderTemplate(cache.bulk_export_type, cache.print_template_id);

                if(me.isV2) {
                    me.renderCheckboxField(cache);
                }

                let {supportDetailFileAttachment, supportFileAttachment, options} = res[2];
                me.renderMDView(options, {supportDetailFileAttachment}, cache);
                me.renderOptionItem({supportFileAttachment, export_file: cache.export_file}); //导出配置项

                me.renderAccordingToList();

                me.resizedialog();
            })
            
            return result;
        },

        renderAccordingToList() {
            let me = this;
            let keys = {
                'relevantTeamParams': 'export_relevant_team' ,
                'setColumnsParams': 'according_to_list'};

            let funcParams = me.get('funcParams') || {};

            Object.keys(funcParams).forEach(key => {
                if (['relevantTeamParams', 'setColumnsParams'].includes(key)) {
                    let attr =  keys[key]
                    let $dom = $(`.mn-checkbox-box[data-key="${attr}"`)
                    let $value = $dom.find('.mn-checkbox-item');
                    let params = funcParams[key];
                    if (!$dom.length) {
                        //隐藏
                        if (params.hidden) {
                            key == 'setColumnsParams' && params.according_to_list && me.cmodel.set('according_to_list', params.according_to_list);

                            key == 'relevantTeamParams'  &&  me.cmodel.set('no_export_relevant_team', params.no_export_relevant_team)
                        }
                    } else {
                            if (key == 'setColumnsParams' && params && params.according_to_list) {
                                $value.trigger('click');
                            } else if (key == 'relevantTeamParams' && params) {
                                /*
                                导出相关团队  false 勾选
                                不导出 true
                                */
                                (!params.no_export_relevant_team) ? $value.removeClass('mn-selected') &&$value.trigger('click') : $value.removeClass('mn-selected');
                            }
                            params&&params.readOnly && $value && $value.addClass('disabled-selected')
                    }

                }
            })


        },

        //获取从对象
        fetchRelations: function() {
            var apiname = this.get('apiname');
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                    url: '/EM1HCRMImptExpt/API/v1/object/object_import/service/getExportDetailObjects',
                    data: {
                        describe_api_name: apiname
                    },
                    success: function(res) {
                        if (res.Result.StatusCode === 0) {
                            const resData = res.Value;
                            resolve( {
                                options:_.map(res.Value.describes, function(a) {
                                return {
                                    label: a.display_name,
                                    value: a.api_name,
                                    children: ((resData.exportDetailObjects||[]).find(v => v.detailApiName == a.api_name) || [])?.fieldDescribes || []
                                }
                            }),
                                supportDetailFileAttachment: resData.supportDetailFileAttachment,
                                supportFileAttachment: resData.supportFileAttachment
                            })
                        } else {
                            resolve({});
                            //CRM.util.alert(res.Result.FailureMessage || $t("服务器返回错误!"));
                        }
                    },
                    error() {
                        resolve({})
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        fetchTemplates: function () {
            var apiname = this.get('apiname');
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                    url: '/EM1HCRMTemplate/printTemplateAdminApi/findList',
                    data: {
                        objDescApiName: apiname,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve((res.Value.result|| []).reduce((pre, cur) => {
                                if (cur.isToWord == 2) {
                                    pre.push({
                                        label: cur.name,
                                        value: cur.templateId
                                    });
                                    return pre;
                                } else {
                                    return pre;
                                }
                            }, []))
                        }
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        grayPrintExport: function () {
            var apiname = this.get('apiname');
            return new Promise(function(resolve) {
                if(!apiname) return resolve({});
                CRM.util.FHHApi({
                    url: '/EM1HCRMImptExpt/API/v1/object/object_export/service/isSupportBatchPrintExport',
                    data: {
                        objectApiName: apiname,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value || {});
                        } else {
                            resolve({});
                        }
                    },
                    error() {
                        resolve({});
                    }
                }, {
                    errorAlertModel: 1
                })
            })

        },

		renderTemplate(isPrint, id) {
			var me = this;
            me.cmodel.set("print_template_id", '');
            me.cmodel.set("bulk_export_type", "default");

            if(me._noSupportTemplate) return;

			me.selectTemplate = FxUI.create({
				wrapper: me.$el.find(".export-template-option")[0],
				template: `
                    <div>
                        <div>
                            <label>${$t('crm.export.template.tip', null, '使用Excel打印模板导出数据')}</label>
                            <fx-switch size="mini" v-model="dValue" @before-change="onBeforeChange"/>
                        </div>
                        <div style="margin-top:12px;" v-show="dValue">
                            <div>${$t('打印模板')}</div>
                            <fx-select
                                style="width:654px"
                                v-model="tValue"
                                :disabled="!templateList.length"
                                :options="templateList"
                                placeholder="${$t('请选择')}"
                                @change="onChange"
                                size="mini"
                            />
                            <div v-show="this.dLoad && !templateList.length" style="color: rgb(245, 113, 95);">{{ $t("该对象暂未添加打印模板提示")}}</div>
                        </div>
                    </div>
                `,
				data() {
                    return {
						templateList: [],
						tValue: '',
                        dValue: false,
                        dLoad: false
					};
				},
				created() {
                    me.fetchTemplates().then(templateList => {
                        this.dLoad = true;
                        this.templateList = templateList;
                        _.findWhere(templateList, {value: id}) && (this.tValue = id);
                    })
                },

                mounted() {
                    this.$nextTick(() => {
                        isPrint && (this.dValue = true);
                    })
                },

                watch: {
                    dValue(v) {
                        me.tValue = v ? '' : ((this.templateList || [])[0] || {}).value;
                        me.cmodel.set({
                            bulk_export_type: v ? 'printTemplate' : 'default'
                        });

                        me.$('.export-default-option')[v ? 'hide' : 'show']();

                        this.$nextTick(() => me.resizedialog());
                    },
                    tValue(v) {
                        me.cmodel.set('print_template_id', v || ''); 
                    }
                }
			});
        },

        //渲染导出图片附件签名选项
        renderOptionItem: function ({ supportFileAttachment=false , export_file}) {
            if (!supportFileAttachment) return;
            var me = this;
            let fieldList = me.cmodel.get('file_fields') || [];
            var columns = me.get('columns')||[];
            me.optionItem = FxUI.create({
                wrapper: me.$el.find(".export-option-item")[0],
                template: `<div >
                <div class="setting-item" style="display:flex;flex-direction:column;">
                    <div>
                        <fx-checkbox v-model="export_fileAttachment" @change="handleCheckBox" class="export_file_attachment">{{ $t('导出图片/附件/签名') }}</fx-checkbox>
                        <span v-if="tipSpan" style="margin-left:-16px" data-pos="top" data-title="${_.escape($t('crm.onlinedoc.exporttip'))}" class="crm-ui-title fx-icon-question"></span>
                    </div>
                    <fx-select
                        style="margin-top:12px;"
                        v-show="dss && export_fileAttachment"
                        :placeholder="$t('请选择')"
                        v-model="file_fields"
                        :options="fieldOpts"
                        size="mini"
                        multiple
                        filterable
                        @change="handleFileAttach"
                    ></fx-select>
                </div>


                </div>`,
                data: function () {
                    return {
                        export_fileAttachment: !!export_file, //是否导出图片、附件
                        file_fields: fieldList,
                        dss: !me.isV2,
                        tipSpan: CRM.util.getUserAttribute('crmOnlineDoc')
                    };
                },
                computed: {
                    //引用的图片？
                    fieldOpts() {
                        let opts = columns.reduce((pre, cur) => {
                            if (
                                ["image", "file_attachment", "signature"].includes(
                                    cur.type
                                ) ||
                                (cur.type == "quote" &&
                                    cur.quote_field_type == "image")
                            ) {
                                pre.push({
                                    label: cur.label,
                                    value: cur.api_name,
                                });
                            }
                            return pre;
                        }, []);
                        return opts;
                    },
                },
                methods: {
                    handleFileAttach(value) {
                        me.cmodel.set("file_fields", value || []);
                    },
                    handleCheckBox(value) {
                        this.file_fields = [];
                        me.cmodel.set("file_fields", []);
                    },
                },
            });
        },


        renderCheckboxField: function (cache) {
            var me = this;
            var columns = me.get('allSetColumns') || [];
            let allSetColumns = [];
            _.each(columns, item => {
                let data = me.get('describe').fields[item.data];
                if (data) {
                    allSetColumns.push(data);
                }
            })
            var fieldApinameList = _.filter(me.get('fieldApinameList') || [], a => {
                if(a === 'crm_tag__c') return;
                let data = me.get('describe').fields[a] || {};
                let t = data.quote_field_type || data.type;
                if(t === 'big_file_attachment') return;

                return true;
            });
            me.optionCheckItem = FxUI.create({
                wrapper: me.$el.find(".export-option-fields-dd")[0],
                template: `<div >
                <div style="font-size:14px;color:#181C25;font-weight:bold" class="setting-item">
                    <label class="sil">
                        <span>{{ $t("根据列设置导出") }}</span>
                    </label>
                    <fx-switch
                        size="mini"
                        :value="isShowcolset"
                        @change="handleTableChange($event, 'dividingLine')"
                    >
                    </fx-switch>
                </div>
                <fx-transfer
                    style="text-align: left; display: inline-block"
                    v-model="value"
                    filterable
                    :titles="[$t('crm.noselect.field', '', '未选的字段'), $t('crm.selected.field', '', '已选的字段')]"
                    @change="handleChange"
                    :data="data"
                    :draggable="true"
                ></fx-transfer>


                </div>`,
                data() {
                    const generateData = _ => {
                        let data = [];
                        data = allSetColumns.map(v => {
                            return {
                                key: v.api_name,
                                label: v.label,
                                type: v.type,
                                quote_field_type: v.quote_field_type
                            }
                        })
                        data = data.filter(v => {
                            if (['crm_tag__c', 'big_file_attachment'].includes(v.quote_field_type || v.type)) {
                                return false;
                            }

                            return v.key != undefined
                        })

                        return data;
                    };
                    let data = generateData();
                    return {
                        data,
                        value: [],
                        isShowcolset: false
                    };
                },



                methods: {
                    handleChange(value, direction, movedKeys) {
                        me.cmodel.set("new_field_api_name_list", value || []);
                        if (this.isShowcolset) {
                            this.isShowcolset = false;
                            me.cmodel.set('according_to_list_select', this.isShowcolset)
                        }
                    },
                    handleTableChange(v, prop) {
                        this.isShowcolset = !this.isShowcolset;
                        me.cmodel.set('according_to_list_select', this.isShowcolset)
                        if (this.isShowcolset) {
                            this.value = [];
                            this.value = fieldApinameList.map(v => {
                                return v
                            })
                            me.cmodel.set("new_field_api_name_list", fieldApinameList || []);
                        } else {
                            this.value = [];
                            me.cmodel.set("new_field_api_name_list", []);
                        }
                    }
                },
                created() {
                    me.cmodel.set('according_to_list', true);
                },
                mounted() {
                    //默认按列设置导出
                    if(cache.export_by_column !== false) {
                        this.$nextTick(() => {
                            this.handleTableChange();
                        })
                    } else {
                        let r = _.filter(cache.field_api_name_list || [], key => _.findWhere(this.data, {key}));
                        if(r.length) {
                            this.handleChange(this.value = r);
                        }
                    }
                }
            });
        },
        parseDetailInfo(value = []) {
            let res = []
            value.forEach(item => {
                let apiname = item.apiName;
               let list =  item.file_fields.map(v => {
                    return [
                        apiname,v
                    ]
               })
                res.push(...list);

            })
            return res;

        },

        renderMDView: function (options, { supportDetailFileAttachment=false}, cache = {}) {
            var me = this;
            if(!options || !options.length) return;
            if(!CRM.util.getUserAttribute('crmNoGrayNewMDExport') && me.isV2) return me.renderNewMDView(options, supportDetailFileAttachment, cache);

            me.mdInstance = FxUI.create({
                wrapper: me.$el.find(".export-md")[0],
                template:
                    '<div style="margin-top:16px;">' +
                    '<label @click="clickHandle" style="margin-top:16px;color:#3487e2;cursor:pointer">+' +
                    $t("选择导出从对象") +
                    '<span v-show="dArrow" class="el-icon-arrow-down"></span><span v-show="!dArrow" class="el-icon-arrow-up"></span></label>' +
                    '<div v-show="!dArrow" style="margin:8px 0 0 16px" class="md-config">' +
                    '<fx-select @change="changeHandle" style="width:100%;" v-model="dValue" size="mini" :placeholder="dPlaceholder" :options="dOptions" :multiple="dMultiple" :clearable="dMultiple"></fx-select>' +
                    '<div style="margin-top:16px;">' +
                    $t("导出配置") +
                    "</div>" +
                    '<div class="mn-checkbox-box mn-checkbox-uniqueid" data-key="include_md_id">' +
                    '<p ref="' + 'inlude_id">' +
                    '<span class="mn-checkbox-item" data-value="1"></span>' +
                    '<span class="checkbox-lb">' +
                    $t("导出的表格中包含“唯一性ID”") +
                    "</span>" +
                    "</p>" +
                    '<em class="crm-ui-help crm-ui-title" data-title="' +
                    $t(
                        "唯一性ID用于标识数据的唯一性，即当主属性重复时，可进行导入数据匹配"
                    ) +
                    '" data-pos="top">?</em>' +
                    "</div>" +
                    `<div class="setting-item" style="display:flex;flex-direction:column;" v-if="show_image">
                        <div>
                            <fx-checkbox
                                v-model="export_fileAttachment"
                                @change="handleCheckboxChange">{{ $t('导出图片/附件/签名') }}
                            </fx-checkbox>
                            <span v-if="tipSpan" style="margin-left:-16px" data-pos="top" data-title="${_.escape($t('crm.onlinedoc.exporttip'))}" class="crm-ui-title fx-icon-question"></span>
                        </div>

                        <fx-cascader
                        v-show="export_fileAttachment"
                        :options="cascadeOpts"
                        v-model="file_fields"
                        :props="config"
                        clearable
                        size="mini"
                        @change="handleCascadeChange"
                        >
                        <template slot-scope="{ node, data }">
                            <span>{{ data.label }}</span>
                        </template>
                        </fx-cascader>
                    </div>    ` +
                    "</div>" +
                    "</div>",
                data: function () {
                    let dm = _.filter(cache.md, value => _.findWhere(options, {value}));
                    return {
                        dArrow: !dm.length,
                        dOptions: options,
                        dValue: dm,
                        dPlaceholder: $t('请选择从对象(可多选)'),
                        dMultiple: true,
                        config: {
                            multiple: true //多选
                        },
                        export_fileAttachment: !!cache.md_export_file,
                        file_fields: [],
                        show_image: supportDetailFileAttachment,
                        tipSpan: CRM.util.getUserAttribute('crmOnlineDoc')
                    };
                },
                mounted() {
                    this.$nextTick(() => {
                        me.resizedialog();
                        if(this.dValue && this.dValue.length) {
                            this.changeHandle(this.dValue); 
                        }
                        if(cache.md_include_id) {
                            $(this.$refs.inlude_id).click();
                        }
                        let r = this.parseServerToDetail(cache.detail_info, options);
                        if(r.length) {
                            this.file_fields = r;
                            this.handleCascadeChange(r);
                        }
                    });
                },
                computed: {
                    cascadeOpts() {
                        let data = this.dOptions.reduce((pre, obj) => {
                            if (this.dValue.includes(obj.value)) {
                                pre.push({
                                    ...obj,
                                    children: (obj.children || []).map(
                                        (v) => ({
                                            label: v.label,
                                            value: v.api_name
                                        })
                                    ),
                                });
                            }
                            return pre;
                        }, []);
                        return data;
                    },
                },
                methods: {
                    handleCheckboxChange: function () {
                        me.cmodel.set("detail_info", []);
                        this.file_fields = [];
                    },

                    clickHandle: function () {
                        this.dArrow = !this.dArrow;
                    },
                    changeHandle: function (v) {
                        me.cmodel.set("detail_object_api_names", v || []);
                    },
                    parseServerToDetail(value, options) {
                        let res = [];
                        _.each(value, a => {
                            let tt = _.findWhere(options, {value: a.apiName});
                            if(tt) {
                                _.each(a.file_fields, b => {
                                    if(_.findWhere(tt.children || [], {api_name: b})) {
                                        res.push([a.apiName, b]);
                                    }
                                })
                            }
                        })

                        return res;    
                    },
                    parseDetailToServer(value) {
                        let res = [];
                        value.forEach((item) => {
                            let object_apiname = item[0],
                                field_apiname = item[1] || "";
                            if (object_apiname && field_apiname) {
                                let findItem = res.find(
                                    (v) => v.apiName == object_apiname
                                );
                                if (findItem) {
                                    findItem.file_fields = [
                                        ...findItem.file_fields,
                                        field_apiname,
                                    ];
                                } else {
                                    res.push({
                                        apiName: object_apiname,
                                        file_fields: [field_apiname],
                                    });
                                }
                            }
                        });
                        return res;
                    },
                    handleCascadeChange(value) {
                        // console.log(value);
                        me.cmodel.set(
                            "detail_info",
                            this.parseDetailToServer(value)
                        );
                    },
                },
            });
        },

        renderNewMDView(options, supportDetailFileAttachment, cache) {
            let me = this;
            let $wrapper = me.$el.find(".export-md");
            $wrapper.css('margin-right', '12px');
            me.mdInstance = FxUI.create({
                wrapper: $wrapper[0],
                template: `
                    <div class="crm-api-export-md" style="margin-top:16px;">
                        <div @click="handleShow" :class="dShow ? 'm-show' : ''" class="m-h">
                            <span class="fx-icon-fold-2 s1"></span>
                            <span>${$t('选择导出从对象')}</span>
                        </div>
                        <div v-show="dShow" class="m-c">
                            <div class="m-ch">
                                <div>
                                    <fx-checkbox v-model="dIncludeId">
                                        <span>${$t('导出的表格中包含“唯一性ID”')}</span>
                                        <span class="crm-ui-help crm-ui-title fx-icon-question" data-title="${$t('唯一性ID用于标识数据的唯一性，即当主属性重复时，可进行导入数据匹配')}" data-pos="top"></span>
                                    </fx-checkbox>
                                </div>
                                <div v-if="dSupportFile">
                                    <fx-checkbox v-model="export_fileAttachment">
                                        <span>${$t('导出图片/附件/签名')}</span>
                                    </fx-checkbox>
                                    <span v-if="tipSpan" style="margin-left:-16px" data-pos="top" data-title="${_.escape($t('crm.onlinedoc.exporttip'))}" class="crm-ui-title fx-icon-question"></span>
                                </div>
                            </div>
                            <div v-loading="dLoading" class="m-t">
                                <div class="m-l">
                                    <div :class="dApiName === md.value ? 'm-o-selected':''" class="m-o" :key="md.value" v-for="md in mdList">
                                        <fx-checkbox @change="handleCheckAllChange($event, md)" :indeterminate="md.checkHalf" v-model="md.checkAll">
                                            
                                        </fx-checkbox>
                                        <span v- @click.prevent="handleObject(md.value)" :title="md.label" class="m-e">{{md.label}}</span>
                                    </div>
                                </div>
                                <div class="m-r">
                                    <fx-transfer
                                        style="text-align: left; display: inline-block"
                                        v-model="dValue"
                                        filterable
                                        draggable
                                        :data="columns"
                                        :titles="[$t('crm.noselect.field', '', '未选的字段'), $t('crm.selected.field', '', '已选的字段')]"
                                        @change="handleChange"
                                    />
                                    <div v-if="dErrorMsg[dApiName]" class="m-err">
                                        {{dErrorMsg[dApiName]}}
                                        <fx-button @click="handleRefresh" size="small" type="text" icon="fx-icon-refresh">${$t('刷新')}</fx-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                data() {
                    return {
                        dShow: cache.md && cache.md.length,
                        dSupportFile: !!supportDetailFileAttachment,
                        mdList: _.map(options, a => ({label: a.label, value: a.value, checkAll: false , checkHalf: false})),
                        columns: [],
                        dCheckItems: {},
                        dIncludeId: !!cache.md_include_id,
                        export_fileAttachment: !!supportDetailFileAttachment && !!cache.md_export_file,
                        dValue: [],
                        dApiName: options[0].value,
                        dLoading: true,
                        dErrorMsg: {},
                        tipSpan: CRM.util.getUserAttribute('crmOnlineDoc')
                    }
                },
                mounted() {
                    this.$nextTick(() => {
                        let length = this.mdList.length;
                        let inner = () => {
                            --length;
                            if(!length) {
                                this._setSomeByCache();
                                this.dLoading = false;
                                this._initComplete = true;
                            }
                        }

                        _.each(this.mdList, a => {
                            let apiname = a.value;
                            this.fetchFields(apiname).then(columns => {
                                this._setColumns(columns, apiname);
                                inner();
                     
                            }).catch((error) => {
                                this.dErrorMsg[apiname] = error;
                                inner();
                            })
                        })
                    });
                },
                methods: {
                    _setSomeByCache() {
                        //根据缓存的数据设置初始值
                        _.each(cache.detail_info, obj => {
                            let apiname = obj.apiName;
                            let columns = this._getColumns(apiname);
                            let md = _.findWhere(this.mdList, {value: apiname});
                            if(md && columns.length) {
                                let value = [];
                                if(obj.fields) {
                                    _.each(obj.fields, fieldName => {
                                        _.findWhere(columns, {key: fieldName}) && value.push(fieldName);
                                    })
                                } else if(cache.md_export_file) {
                                    _.each(columns, c => {
                                        if(!c.isFile || _.contains(obj.file_fields || [], c.key)) {
                                            value.push(c.key)
                                        }
                                    })
                                } else {
                                    value = _.pluck(columns, 'key');
                                }
                                this.dCheckItems[apiname] = value;
                                if(apiname === this.dApiName) {
                                    this.dValue = value;
                                }

                                if(value.length === columns.length) {
                                    md.checkAll = true;
                                } else if(value.length) {
                                    md.checkHalf = true;
                                }
                            }
                        })
                    },

                    _setColumns(columns, apiname) {
                        (this._objectColumns || (this._objectColumns = {}))[apiname] = columns;
                        if(apiname === this.dApiName) {
                            this.columns = columns;
                        }
                    },
                    _getColumns(apiname) {
                        return this._objectColumns && this._objectColumns[apiname] || [];
                    },

                    handleChange(value) {
                        let md = _.findWhere(this.mdList, {value: this.dApiName});
                        if(value.length === this.columns.length) {
                            md.checkAll = true;
                            md.checkHalf = false;
                        } else {
                            md.checkAll = false;
                            md.checkHalf = !!value.length;
                        }
                        this.dCheckItems[this.dApiName] = value;
                    },

                    handleCheckAllChange(isCheck, md) {
                        md.checkAll = isCheck;
                        md.checkHalf = false;
                        let value = []
                        if(isCheck) {
                            value = _.pluck(this._getColumns(md.value), 'key')
                        }

                        if(md.value === this.dApiName) {
                            this.dValue = value;
                        }

                        this.dCheckItems[md.value] = value;
                    },

                    handleShow() {
                        this.dShow = !this.dShow;
                    },

                    handleObject(apiname) {
                        this.dApiName = apiname;
                        this.dValue = this.dCheckItems[apiname] || [];
                        this.columns = this._getColumns(apiname);
                    },

                    handleRefresh() {
                        this.dLoading = true;
                        this.fetchFields(this.dApiName).then(columns => {
                            this.dLoading = false;
                            this._setColumns(columns, this.dApiName);
                            delete this.dErrorMsg[this.dApiName];
                        }).catch(error => {
                            this.dLoading = false;
                            this.dErrorMsg[this.dApiName] = error;
                        }) 
                    },

                    fetchFields(apiname) {
                        return new Promise((resolve, reject) => {
                            let retry = 0;
                            let request = function() {
                                CRM.util.FHHApiCacheFields({
                                    url: '/EM1HNCRM/API/v1/object/' + apiname + '/controller/ListHeader',
                                    data: {
                                        include_layout: false,
                                        include_components: false,
                                        apiname: apiname,
                                        layout_type: "list",
                                        list_type: 'list'
                                    },
                                    success(res) {
                                        if(res.Result.StatusCode === 0) {
                                            let fields = res.Value.objectDescribe.fields;
                                            let columns = []
                                            _.each(res.Value.visibleFields, key => {
                                                let field = fields[key];
                                                if(!field) return;
                                                let type = field.quote_field_type || field.type;
                                                if(type === 'big_file_attachment') return;
    
                                                columns.push({
                                                    key,
                                                    label: field.label,
                                                    isFile: /^(image|file_attachment|signature)$/.test(type)
                                                })
                                            })
    
                                            resolve(columns);
                                            
                                        } else {
                                            if(!retry) {
                                                retry = 1;
                                                request();
                                            } else {
                                                reject(res.Result.FailureMessage);
                                            }
                                        }
                                    },
                                    error() {
                                        if(!retry) {
                                            retry = 1;
                                            request();
                                        } else {
                                            reject($t('网络异常'));
                                        }
                                    }
                                }, {
                                    errorAlertModel: 1
                                })
                            }

                            request();
                        })
                    },

                    getDetailArg() {
                        if(!this._initComplete) {//没初始化完成，直接用缓存的
                            return {
                                detail_info: cache.detail_info || [],
                                detail_object_api_names: cache.md || [],
                                include_md_id: cache.include_md_id
                            }
                        }

                        let detail_info = [];
                        _.each(this.mdList, md => {
                            let apiName = md.value;
                            let fields = this.dCheckItems[apiName];
                            if(!fields || !fields.length) return;
                            let obj = { apiName, fields };
                            
                            if(this.export_fileAttachment) {//导出图片附件
                                let columns = this._getColumns(apiName);
                                obj.file_fields = _.filter(fields, key => _.findWhere(columns, {key, isFile: true}))
                            } else {
                                obj.file_fields = [];
                            }

                            detail_info.push(obj)
                        })

                        return {
                            detail_info,
                            detail_object_api_names: _.pluck(detail_info, 'apiName'),
                            include_md_id: this.dIncludeId
                        }
                    }
                },
                beforeDestroy() {
                    this._objectColumns = null;
                }
            })
        },

        hide: function() {
            var me = this;
            Export.superclass.hide.call(me);
            me.destroy();
            return me;
        },

        destroy: function() {
            var me = this;
            me.processTimer && clearInterval(me.processTimer);
            me.processTimer = null;
            me._cacheData = null;
            _.each(me.ajaxs, function(ajax){
                ajax && ajax.abort();
                ajax = null;
            });
            me.cmodel && (me.cmodel.getToken.abort = true);
            me.mdInstance && (me.mdInstance.destroy(), me.mdInstance = null);
            me.selectTemplate && (me.selectTemplate.destroy(), me.selectTemplate = null);
            me.optionCheckItem && (me.optionCheckItem.destroy(), me.optionCheckItem = null);
            me.optionItem && (me.optionItem.destroy(), me.optionItem = null);
            return Export.superclass.destroy.call(me);
        }
    });
    module.exports = Export;
})
