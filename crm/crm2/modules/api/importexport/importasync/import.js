/**
 * 异步导入工具
 * 2019.4.15 zhongly
 */
 define(function (require, exports, module) {
    var util = CRM.util;
    var Select = require('crm-widget/select/select');
    var Selector = require('crm-widget/selector/selector');
    var H5Uploader = require('base-h5uploader');
    var RelSelect = require('./relselect/relselect');
    var config = require('./config');
    var Model = require('./model');
    var contentTpl = require('./template/content-html');
    var checkTpl = require('./template/check-html');
    var optionsTpl = require('./template/options-html');
    var typeTpl = require('./template/type-html');
    var Validator = require('./validator');
    var businessTpl = require('./template/business-html');
    var updateOwnerTpl = require('./template/updateOwner-html')

    var ChildSelect = require('./childselect/childselect');

    var grayDepRepeat = FS.util.getUserAttribute('crmImportExportDepRepeat');
    var grayChildSelect = true;

    var ImportTool = Backbone.View.extend({
        className: 'crm-c-importasync',

        events: {
            'click .j-import': 'import',
            'click .j-cancel': 'hide',
            'click .j-switch': 'doSwitch',
            'click .j-import-type': 'setImportType',
            'click .mn-radio-box .radio-item': 'radioHandle',
            'click .j-downloadtpl': 'downloadTpl',
            'click .j-child-check': 'childCheckHandle',
            'click .show-document': 'showDocument',
            'click .mn-checkbox-box .checkbox-item': 'checkboxHandle',
            'click .j-check-all-objs': '_checkAllObjs',
            'click .other-obj .right-con .mn-checkbox-item': '_checkObj'
        },

        initialize: function() {
            var me = this;
            var apiName = me.options.pageApiname || me.options.apiname;
            me.widgets = {};
            me.ajaxs = {};
            me.isCache = false;//是否使用缓存
            me.cacheData = {};//缓存数据
            me.model = new Model({
                pageCode: apiName,
                paramConfig: me.options.paramConfig,
                importObjectParam: me.options.importObjectParam,
                roleOptions: me.options.roleOptions,
                importRecordType: me.options.recordType || 'all'
            });
            me.listenTo(me.model, 'change:objectCode', me.objectMediation);
            me.listenTo(me.model, 'change:importType', me.importTypeMediation);
            me.listenTo(me.model, 'change:IsWorkFlowEnabled', me.setAddfileTip);
            me.listenTo(me.model, 'change:MatchingType', me.matchingTypeHandle);
            me.render();
        },

        render: function () {
           
            var me = this;
			me.$el.html(contentTpl({
				title: $t("导入数据")
			}));
			$('body').append(me.$el);
            me.$el.css('zIndex', me.options.zIndex || 1000);

            me.initValidator();

            me.model.getObjectList()
            .then(function(data) {
                me.initChildObjectSelect();
                me.initTypeSelect();
                me.initObjectSelect(me.filterObjects(data));
                me._renderTeamGuide();
            });
            me.initH5Uploader();
            me.initNoticeWay();
        },

        //相关团队提示
        _renderTeamGuide() {
            FS.util.getUserAttribute('crmGrayImportTeamMember') && CRM.api.show_guide({
                key: 'import_guide_900ii',
                title: '',
                content: $t('crm.import.team.tip')
            })
        },

        _setGrayDepRepeatTip(val) {
            let tipStr = {
                DepartmentObj: grayDepRepeat && $t('部门重复灰度提示'),
                PersonnelObj: FS.util.getUserAttribute('crmImportExportPersonnelRepeat') && $t('crm.import.personnel.repeat', null, '企业已灰度人员名称可重复，人员字段需填写人员编码值！')
            }[val]

            if(!tipStr) {
                this._$retips && this._$retips.hide();
                return;
            }

            if(!this._$retips) {
                this._$retips = $(`<div class="import-tip"></div>`);
                this.$('.import-sect-data .import-sect-dd').append(this._$retips);
            }

            this._$retips.html(tipStr).show();
        },

        _renderGuide: function() {
			// let guideRuleKey = 'import_guide_800i';
            // require.async('base-modules/guide/guide', function(Guide) {
			// 		new Guide({
			// 			type: 'onetip',
			// 			key: guideRuleKey,
			// 			data: [{
			// 				$target: $(".import-caption-title"),
			// 				text: $t("部门重复灰度提示"),
            //                 pos: 'right',
            //                 left: 30
                            
			// 			}],
			// 		});
            // });
		},

		// 提供特殊屏蔽对象方法
        filterObjects: function(objs) {
			return objs;
        },

        // 获取具体对象数据
        getObject: function(obj) {
            var me = this;
           
          /*  var def = $.Deferred();
            if (obj.got) {
                def.resolve && def.resolve(obj);
            } else {
                let { objectCode,detailObjectApiNames=[] } = obj
                let data = {
                    objectCode,
                    detailObjectApiNames
                };
                me.ajaxs[obj.objectCode] = me.model.getObject(data).then(function(regObj) {
                    def.resolve && def.resolve(regObj);
                });
            } */
            let detailObjectApiNames  = me.$el.find(".j-child-check").hasClass("mn-selected") ? me.model.get("detailObjectApiNames") : [];
            let { objectCode } = obj
            detailObjectApiNames = obj.detailObjectApiNames ? obj.detailObjectApiNames : detailObjectApiNames;
            let data = {
                objectCode,
                detailObjectApiNames
            };
            return new Promise((resolve, reject) => {
                me.model.getObject(data).then((regObj) => {
                    resolve(regObj)
                });
                
            })
        },

        // 初始化验证
        initValidator: function() {
            var me = this;
            Validator.prototype.showError = function(errors, alert) {
                for (var key in errors) {
                    if(!errors.hasOwnProperty(key)) return;
                    if (alert) return util.alert(errors[key][0]);
                    var _key = key;
                    if (_key === 'relation') _key = 'object';
                    $('[data-key=' + _key + '] .j-errormsg', me.$el).html(errors[key].join(','));
                }
            },
            Validator.prototype.hideError = function(key) {
                if (key === 'relation') key = 'object';
                $('[data-key=' + key + '] .j-errormsg', me.$el).html('');
            },

            me.widgets.validator = new Validator();
            me.widgets.validator.add([{
                key: 'object',
                getValue: function() {
                    return me.model.get('objectCode');
                },
                rule: 'notEmpty',
                msg: $t('请选择业务对象')
            }, {
                key: 'importType',
                getValue: function() {
                    return me.model.get('importType');
                },
                rule: 'notEmpty',
                msg: $t('请选择导入方式')
            }, {
                key: 'data',
                getValue: function() {
                    if (!$('.j-addfile', me.$el).hasClass('import-addedfile')) return;
                    return $('.j-addfile .import-added-text', me.$el).text();
                },
                rule: 'notEmpty',
                msg: $t('请添加您要导入的数据')
            }, {
                key: 'childrenObject',
                getValue: function() {
                    if(me.$('.andchild .mn-checkbox-item').hasClass('mn-selected')) {
                        return me.widgets.childObjectSelect.getValue();
					}
                    return '  ';
                },
                rule: 'notEmpty',
                msg: $t('paas.crm.api.importasync.select_sub_obj', null, '请选择子业务对象')
                },
                {
                    key: 'rolePermission',
                    getValue: function () {
                        if(me.model.get('oldOwnerTeamMember')?.strategy == 2 ) {
                            return me.model.get('oldOwnerTeamMember')?.roleList||[];
                        }
                        return '  ';
                    },
                    rule: 'notEmpty',
                    msg: $t('请选择团队权限')
                }]);

            me.widgets.validator2 = new Validator();
            me.widgets.validator2.add([{
                key: 'relation',
                getValue: function() {
                    return me.widgets.relSelect.getSelected();
                },
                rule: 'notEmpty',
                msg: $t('导入销售记录至少选择一个关联对象')
            }]);
        },

        // 初始化业务对象选择
        initObjectSelect: function(opts) {
            var me = this;
            if (me.widgets.objectSelect) return;

            // objectSelectOptions用来扩展Select组件(如置灰Select下拉框)
            me.widgets.objectSelect = new Select(_.extend({
                $wrap: $('.import-objectselect', me.$el),
                multiple: 'single',
                placeHolder: $t("请选择"),
                onlyoneline: true,
                options: opts,
                space: 3,
                zIndex: 1000,
			}, me.options.objectSelectOptions));

            me.widgets.objectSelect.on('change', function(val, item) {
                // me._resetOptions(item)
                // me._resetOptions(item)
                // me._resetTypeOpts(item);
                if (!item) return;
                if (me.model.get('pageCode') !== item.value) {
                    me.model.set('importRecordType', 'all');
                }
                me.model.set('curObject', item);
                me.model.set('apiName', item.objectApiName);
                me.model.set('objectCode', item.value);

                me._setGrayDepRepeatTip(val);
            });

            // 设置默认值
            me.widgets.objectSelect.setValue(me.model.get('pageCode'), true);
        },
        //回填从对象
        renderChildSelect: function (detailList) {
            let me = this;
            let data = me.cacheData;
            data = JSON.parse(data);
            if (!data||!Object.keys(data).length) return;
            let curObject = this.model.get('curObject');
            
            let ww = me.widgets;
            //回填业务类型
            data.queryParam.recordType && ww.typeSelect && (ww.typeSelect.options.options||[]).find(v=>v.value == data.queryParam.recordType) && ww.typeSelect.setValue(data.queryParam.recordType);

                //联合导入不支持设置从对象
                if (data.queryParam.unionImportApiNameList.length > 1 && curObject.unionImportApiNameList.length==0) {
                    let detailArr = data.queryParam.unionImportApiNameList.slice(1);
                    let recordType = data.queryParam.detailRecordType;
                    let detailArg = data.queryParam.detailArg || [];

                    let selectedDetails = detailArg.length ? detailArg : [{ detailApiName: detailArr[0], detailRecordType: recordType||'all' }]
                    selectedDetails = selectedDetails.reduce((pre, { detailApiName, detailRecordType = 'all' }) => {
                        detailRecordType = detailRecordType ? detailRecordType :'all'
                        if (detailList.find(val => val.value == detailApiName)) {
                                
                                pre.push({ detailApiName,
                                    detailRecordType,
                                    value:detailApiName})
                            }
                        return pre;
                        },[])
                    //过滤掉删除的从对象
                    if (grayChildSelect) {
                        ww.childObjectSelect.intance.parsedOptionsByCache(selectedDetails)  
                        
                    } else {
                   ww.childObjectSelect.parsedOldOptsByCache(selectedDetails) 
                    }
                    // me.isCache = false;
                     
                }
            
        },
        reRenderByCache:  function (data) {
            data = JSON.parse(data)
            let me = this,curObject = me.model.get('curObject');
            me.isCache = true;
            if (!data) return;
            let ww = me.widgets;
            //已启用的对象
            let activeMdArr = ((data.queryParam?.unionImportApiNameList || []).slice(1) || []).filter(v => {
                
                if (curObject.detailDescribes.find(item => item.value == v)) return true;
                return false
            }) || [];
            //如果从对象被删除 不勾选导入从对象
            if (activeMdArr&& activeMdArr.length&& curObject.unionImportApiNameList.length<1) {
                if (me.$el.find(".j-child-check").hasClass("mn-selected")) {
                    me.hideCheck();
                }
                me.showCheck();
                //
                let $check = me.$('.andchild .j-child-check');
               $check.trigger('click')
            } else {
                _.delay(() => {
                     // 回填当前对象业务类型
                data.queryParam.recordType&&ww.typeSelect&& (ww.typeSelect.options.options||[]).find(v=>v.value == data.queryParam.recordType)&& ww.typeSelect.setValue(data.queryParam.recordType);
                    
                },500)
               
                //未勾选从对象 回填选项
                me.renderOptionByCache();
            }
        },
        //option配置选项根据缓存回填
        renderOptionByCache() {
            let me = this;
            let data = me.cacheData;
            data = JSON.parse(data);
            let curObject = this.model.get('curObject');
            let configKeys = {
                IsEmptyValueToUpdate: 'j-radio-update-emptyfield',//导入数据为空时 是否更新现有字段
                IsCheckOutOwner: 'j-radio-outowner',//校验外部负责人
                IsApprovalFlowEnabled: 'j-radio-approval',//是否触发审批流
                IsRemoveOutTeamMember: 'j-radio-removeout-teammember',// 是否移除相关团队
                IsWorkFlowEnabled: 'j-radio-workflow',//是否触发工作流  
                IsUpdateOwner: 'j-radio-update-owner',
                strategy: 'j-set-pre-owner',
                permissionType:'j-radio-permission-type'
            };

            let curKey = {
                IsApprovalFlowEnabled: 'IsApprovalFlow',
                IsCheckOutOwner: 'IsCheckOutOwner',
                IsUnionDuplicateChecking: 'IsEnableUnionDuplicateChecking',
                IsWorkFlowEnabled: 'IsOpenWorkFlow',
                IsRemoveOutTeamMember: 'IsRemoveOutTeamMember',
                matchingTypes: 'matchingTypes',
                /* supportType: 1,// 0 不支持更新导入 3不支持新建导入 默认是1
                */
            }
            let type = data.queryParam.ImportType ? 'update' : 'add';//导入类型
            if ((type == 'update' && curObject.supportType != 0) || (type == 'add' && curObject.supportType != 3)) {
                this.$('.import-type-' + type).removeClass('disabled').click();
                Object.keys(data.queryParam).forEach(key => {
                    if (Object.keys(configKeys).includes(key) && data.queryParam[key]&& curObject[curKey[key]]!=false) {
                        this.$('.' + configKeys[key] + ' .j-radio__first').click();
                    } else if (key == 'MatchingType') {
                        
                        if (data.queryParam[key] == 4) {
                            // 如果是部门对象，不执行识别方式-其它字段的cache逻辑
                            if(this.__isDeptUpdateImport()){
                                return;
                            }
                            //其他字段
                            _.delay(function () {
                                if (me.widgets.anyFieldSelect && data.queryParam.specifiedField ) {
                                    this.$(`.j-radio-uniqueid .mn-radio-item[data-value=${data.queryParam[key]}]`).click();
                                    me.widgets.anyFieldSelect.enable();
                                    (me.widgets.anyFieldSelect.options.options || []).find(v => v.value == data.queryParam.specifiedField)&& me.widgets.anyFieldSelect.setValue(data.queryParam.specifiedField || '', true)
                                }
                            }, 500)  
                        } else {
                            this.$(`.j-radio-uniqueid .mn-radio-item[data-value=${data.queryParam[key]}]`).click();
                        }
                    } else if ('IsUnionDuplicateChecking' == key && data.queryParam[key]&&curObject[curKey[key]]) {
                        this.$('.j-switch').click();  
                    } else if (key == 'oldOwnerTeamMember' && data.queryParam.IsUpdateOwner) {
                        let ownerObj = data.queryParam.oldOwnerTeamMember;
                        _.delay(() => {
                            Object.keys(ownerObj).forEach(item => {
                                if (item == 'strategy') {    
                                        ownerObj.strategy == 2&& this.$('.' + configKeys[item] + ' .j-radio__first').click();
                                    
                                  
                                } else if (item == 'permissionType') {
                                    ownerObj.strategy == 2 && ownerObj.permissionType == 2&& this.$('.' + configKeys[item] + ' .j-radio__first').click();
                                }
                            })
                            
                        },10)
                       
                    }
                })
                
            } 
            //回填通知
            let noticeData = data.notifies;
            me.widgets.notice.clearAll();
            me.widgets.notice.setValue({
                curEmp: [CRM.curEmpId],
				member: _.uniq([...noticeData.map((v) => v.userId)]),
				
			});
            
            me.isCache = false;
        },

        initTypeSelect: function() {
            var me = this;
            if (me.widgets.typeSelect) return;
            me.widgets.typeSelect = new Select({
                $wrap: me.$('.import-typeselect'),
                multiple: 'single',
                placeHolder: $t("请选择业务类型"),
                onlyoneline: true,
                options: [],
                space: 3,
                zIndex: 1000
			});
        },
        
        initChildObjectSelect: function() {
            if(this.widgets.childObjectSelect) return;
            this.widgets.childObjectSelect = new ChildSelect({
                el: this.$('.child-wrap'),
                fetchRecordType: this.model.fetchRecordType
            }).render();
            let me = this;
            this.widgets.childObjectSelect.on('changeChild', function (val, is_cache) {
               /*  if (me.isCache) {
                    return;   
                }  */
                    let newVal = []
                    if (Array.isArray(val)) {
                        newValue = val.map(v => v.value);
    
                    } else {
                        newValue = [val];    
                    }
                    //只区分对象
                let oldValue = me.model.get("detailObjectApiNames");
                if ((oldValue.join("") != newValue.join("") && newValue.length)||is_cache) {
                     //更新
                    me.model.set({
                        detailObjectApiNames: newValue  
                     }
                        
                    ) 
                    me.getObject({
                            objectCode: me.model.get("objectCode")
                        }).then(() => {
                           
                           
                            //  !grayChildSelect&& me.renderOptionByCache();
                            if (is_cache) {
                                me.renderOptionByCache();
                            } else {
                                //旧版选择主从，已回填值此处执行会覆盖掉
                                !_.isEmpty(me.cacheData) ? '':me.resetOptsRender();
                            }
                        }) 
                    }
               
            });
            //回填选项
            this.widgets.childObjectSelect.on('changeOpts', function () {
                // console.log('lllllllll');
                me.renderOptionByCache();
              
            });
        },
        //重置选项
        resetOptsRender() {
            let me = this;
            me.initImportType(); //导入类型
            me.initRelation(); // 选择关联对象
            me.initCheck(); // 查重 
            me.initBusiness(); //工商信息
            me.initAddfile(); //添加数据
            me.setAddfileTip('limit1');
            me.widgets.notice.clearAll();
            me.widgets.notice.setValue({
                curEmp:  _.uniq([CRM.curEmpId])
            })
        },
        _resetTypeOpts: function (item) {
            var me = this;
            var w = me.widgets;
            if(!item) {
                w.typeSelect.resetOptions([], null, true, true);
            } else {
                me.model.fetchOptions(item.objectApiName, function(typeList,  apiname) {
                    if (me.model.get('apiName') !== apiname) return;
                    w.typeSelect.resetOptions(typeList, null, false, true);
                    w.typeSelect.setValue(typeList[0].value);
                }, item)
          
            }
            
        },

        _resetOptions: function (item) {
            var me = this;
            var w = me.widgets;
            w.childObjectSelect.resetOptions();
            if(!item) {
                w.typeSelect.resetOptions([], null, true, true);
            } else {
                // me.model.fetchOptions(item.objectApiName, function(typeList,  apinameList, apiname) {
                    me.model.fetchOptions(item.objectApiName, function(typeList,  apiname) {
                
                    if (me.model.get('apiName') !== apiname) return;
                    w.typeSelect.resetOptions(typeList, null, false, true);
                    let idx = typeList.find(v => v.value === me.model.get('importRecordType'))
                    w.typeSelect.setValue(idx?.value || typeList[0].value);
                        w.childObjectSelect.resetOptions(item.detailDescribes||[]);  
                    
                    if (me.isCache) {
                        me.renderChildSelect(item.detailDescribes|| apinameList);
                    }
                    // /* apinameList.length && !item.unionImportApiNameList?.[1]
                    //     ? (me.showCheck(apinameList), me.afterShowCheck(apiname))
                    //     : me.hideCheck(); */
                    //     apinameList.length
                    //     ? (me.showCheck(apinameList), me.afterShowCheck(apiname))
                    //     : me.hideCheck();
                }, item) 
          
            }
        },

        hideCheck: function() {
            this.$('.andchild').hide().find('.j-child-check').removeClass('mn-selected disabled-selected');
            this.$('.child-wrap').hide();
            //隐藏check的时候重置从对象选项
            var w = this.widgets;
            w.childObjectSelect.resetOptions([], null, true, true);
            w.childTypeSelect?.resetOptions([], null, true, true);
            
        },

        showCheck: function() {
            var $a = this.$('.andchild');
            $a.show(); //.find('.j-child-check');

           

            var $c = $a.find('.j-child-check');
            var flag = $c.hasClass('mn-selected');
            this.$('.import-type-update').toggleClass('disabled', flag);
            //!gray && this.$('.import-sect-type').toggleClass('and-import-child', true); //通过接口下发判断是否支持审批流
        },

        afterShowCheck: function (objectApiName) {
            var $check = this.$('.andchild .j-child-check');
            $check.removeClass('disabled-selected'); // 每次切换取消禁用check

            this.checkedAndDisable(objectApiName, $check);
        },
        checkedAndDisable: function (objectApiName, $check) {
            // 属性对象,默认选中‘同时导入从对象’，并禁用check
            var objectApiNames = ['AttributeObj'];

            if (_.contains(objectApiNames, objectApiName)) {
                if (!$check.hasClass('mn-selected')) {
                    $check.trigger('click', function($target){
                        $target.addClass('disabled-selected');
                    });
                } else {
                    $check.addClass('disabled-selected');
                }
            }
        },

        childCheckHandle: function (e, fn) {
            let me = this;
            var $target = $(e.currentTarget);
            var flag = !$target.hasClass('mn-selected');

            var disabled = $target.hasClass('disabled-selected');
            if (disabled) {
                e.stopPropagation();
                return;
            }
            fn && fn($target);
            
            this.$('.child-wrap').css('display', flag ? 'flex' : 'none');
            this.$('.import-type-update').toggleClass('disabled', flag);// [flag ? 'addClass' : 'removeClass']('disabled');
            // me.model.set("detailObjectApiNames", [])
                 //取消从对象 更新设置
                 !me.isCache&&me.getObject({
                    objectCode: me.model.get("objectCode"),
                    detailObjectApiNames:flag ? me.model.get("detailObjectApiNames"):[]
            }).then(function (data) {
                // me._resetOptions(data) 
                flag&& me._resetChildOptions(data);
                me.resetOptsRender(); 
                this.$('.import-type-update').toggleClass('disabled', flag);
            });
            //旧版从对象手动勾选传从对象
       /*      if (grayChildSelect) {
                
            } else {
                _.delay(() => {
                    // !me.isCache && this._resetChildOptions(this.model.get('curObject')) && 
                    if (!me.isCache) {
                        this._resetChildOptions(this.model.get('curObject'))
                        me.resetOptsRender(); 
                        
                    }
                },50)  
            } */
  
            //取消审批流的隐藏 通过接口判断
        //    this.$('.import-sect-type').toggleClass('and-import-child', flag);
            //切换从对象的时候请求
       /*      flag && this._resetChildOptions(this.model.get('curObject'));
            //更新选项 取消选中则重新渲染选项
            !flag &&  me.getObject({
                objectCode: me.model.get("objectCode"),
            }).then(() => {
                me.resetOptsRender(); 
            })  */
        },
        _resetChildOptions(item) {
            var me = this;
            var w = me.widgets;
             if(item.detailDescribes&&item.detailDescribes.length) {
                 w.childObjectSelect.resetOptions(item.detailDescribes);
            }
        },

        _formatParam: function(_param) {
            var w = this.widgets;
            var vv = w.typeSelect.getValue();
            if(this.$('.andchild .mn-checkbox-item').hasClass('mn-selected')) {
                var a1 = w.childObjectSelect.getValue();
                _param.unionImportApiNameList = [this.model.get('apiName')].concat(a1.objApiNames || []);
                _param._child_display_name = a1.child_display_name;
                _param.detailRecordType = a1.detailRecordType;
                _param.detailArg = a1.detailArg;
            }
            if(vv !== 'all') {
                _param.recordType = vv;
            }
        },

        showDocument: require('./showdocument'),

        // 初始化通知方式
        initNoticeWay: function() {
            var me = this;
			var curEmp = util.getCurrentEmployee();
            curEmp.lock = true;

			me.widgets.notice && me.widgets.notice.destroy();
			me.widgets.notice = new Selector({
				$wrap: $('.j-notice', me.$el),
				parentNode: $('.j-notice', me.$el),
				width: 480,
				label: $t("选择通知范围"),
				member: true,
				/* group: true, */
				zIndex: 1000,
				tabs: [{
					id: 'curEmp',
					type: 'list',
					hidden: true,
					data: [curEmp]
				}],
				excludeItems: {
					member: [CRM.curEmpId]
				},
				defaultSelectedItems: {
					curEmp: [CRM.curEmpId]
				}
			});

            if(util.isConnectApp()) { //下游禁止选择通知的人
                me.widgets.notice.toggleDisable(true);
            }
        },

        // 初始化数据上传
        initH5Uploader: function() {
            var me = this;
            var limit = 50 * 1024 * 1024;
            var app = util.getConnectAppInfo();
			me.widgets.h5ImgUploader = new H5Uploader({
                multiple: false,
				autoPrependPath: false,
				fileInput: $('.j-btn-addfile', me.$el)[0],
				dragDrop: $('.j-addfile', me.$el)[0],
				url: FS.BASE_PATH + '/FSC/EM/File/UploadByStream' + (app && app.appId ? '?appid=' + app.appId : ''),
				timeout: 180,
				filter: function (files) {
                    var passedFiles = [];
                    _.each(files, function(file){
                        if (file.size > limit) {
                            util.alert($t("crm.文件大小50M不超过30w"));
                            return;
                        }
                        if (util.getFileType(file) !== 'xls' && util.getFileType(file) !== 'xlsx') {
                            util.alert($t("请选择excel格式的文件"));
                            return;
                        }
                        passedFiles.push(file);
                    });
                    me.widgets.h5ImgUploader.removeAllFile(); // 清除操作的文件
                    return passedFiles;
				},
				onSuccess: function (file, responseText) {
					// 记录上传返回TempFileName
                    var data = util.json.parse(responseText);
                    me.model.set({
                        'filePath': data.TempFileName,
                        'fileExt': data.FileExtension,
                        'fileName': file.name||''
                        
                    });
				},
				onFailure: function (file) {
                    util.alert($t("数据上传失败！"));
                    me.hideLoading();
				},
				onSelect: function (files) {
                    if (!files.length) return;
                    me.widgets.validator.hideError('data');
                    $('.import-addfile', me.$el).addClass('import-addedfile');
                    $('.import-added-text', me.$el).html(files[0].name);
                    $('.add-btn-ui', me.$el).html($t("重新上传"));
				},
				onComplete: function () {
                    me.doImport();
				}
			});
        },

        // 对象变更后的处理
        objectMediation: function(model) {
            var me = this;
            var curObject = model.get('curObject');
            me.curObject = curObject;
            me.model.set({
                detailObjectApiNames: []  
             })
            me.getObject(me.curObject)
                .then(function (data) {
                    me.getStore(curObject.objectCode).then(cacheData => {
                        me.showChildObjectSelect(data.detailDescribes, data.objectApiName)
                        me._resetOptions(data) 
                        me.resetOptsRender();
                        if (cacheData&&cacheData.length) {
                            me.cacheData = cacheData;
                            //有缓存
                            me.reRenderByCache(cacheData) 
                        } 
                    })
                });

            me.widgets.validator.hideError('object');
        },
        showChildObjectSelect: function (apinameList=[],apiname) {
            let me = this;
            // me.$el.find(".j-child-check").removeClass("mn-selected")&& me.widgets.childObjectSelect.resetOptions(apinameList);
            me.hideCheck()
            //  me.$el.find(".j-child-check").hasClass("mn-selected") && 
                    /* apinameList.length && !item.unionImportApiNameList?.[1]
                        ? (me.showCheck(apinameList), me.afterShowCheck(apiname))
                        : me.hideCheck(); */
                        apinameList.length
                        ? (me.showCheck(apinameList), me.afterShowCheck(apiname))
                        : me.hideCheck();
        },

        // 初始化导入方式
        initImportType: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            me.model.set('importType', '', {silent: true});
            $('.import-sect-type .import-sect-dd').html(typeTpl(_.extend({}, curObject)));
        },

        // 初始化关联对象 （销售记录）
        initRelation: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            if (!curObject.showRelation) {
                $('.import-relationselect', me.$el).html('');
                me.widgets.relSelect && me.widgets.relSelect.destroy();
                me.widgets.relSelect = null;
                return;
            }
            if (!me.widgets.relSelect) {
                me.widgets.relSelect = new RelSelect({
                    wrapper: '.' + me.className,
                    selected: [],
                    list: curObject.relList,
                });
                me.widgets.relSelect.on('change:selected', function() {
                    me.widgets.validator2.hideError('relation');
                });
                $('.import-relationselect', me.$el).html(me.widgets.relSelect.$el);
            }
        },

        // 初始化查重 （销售线索、线索池）
        initCheck: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            var $notice = $('.import-sect-notice', me.$el);
            var $check = $('.import-sect-check', me.$el);
            // IsEnableUnionDuplicateChecking : true 展示查重 （销售线索、线索池）
            if (curObject.IsEnableUnionDuplicateChecking){
                if (!$check.length) {
                    $notice.prepend(checkTpl());
                    me.model.set('IsUnionDuplicateChecking', false);
                }
                return;
            }
            $check.length && $check.remove();
            me.model.unset('IsUnionDuplicateChecking');
        },

        //初始化工商信息
        initBusiness: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            if(me._businessFill) {
                me._businessFill._$business.remove();
                me._businessFill.destroy();
                me._businessFill = null;
            }
            if(_.contains(['LeadsObj', 'AccountObj', 'PartnerObj'], curObject.objectCode)) {
                me.assertIsNoPay(function() {
                    var $notice = $('.j-notice-way', me.$el);
                    var $business = $(businessTpl());
                    $('.j-notice-way', me.$el).before($business);
                    var $bc = $business.find('.business-check');
                    var $html = $bc.html();
                    $bc.html('');
                    me._businessFill = FxUI.create({
						wrapper: $bc[0],
						template: '<fx-checkbox v-model="checked">' + $html + '</fx-checkbox>',
                        data: function() {return {checked: false}},
                        watch: {
                            checked: function(vv) {
                                me.model.set('businessCheck', vv);
                            }
                        }
                    })
                    me._businessFill._$business = $business;
                })

			}
            me.model.set({
                businessEnable: null,
                businessFill: null,
                businessCheck: null
            })
        },

        assertIsNoPay: function(callback) {
            // if(this.noPay) {
            //     callback();
            //     return;
            // }
            // //util.FHHApi({url:})
        },

        // 初始化添加数据
        initAddfile: function() {
            var me = this;
            $('.import-addfile', me.$el).removeClass('import-addedfile');
            $('.import-added-text', me.$el).html('');
            $('.add-btn-ui', me.$el).html($t("点击上传"));
        },

        // 设置导入方式
        setImportType: function(e) {
            var me = this;
            var $tg = $(e.currentTarget);
            if ($tg.hasClass('disabled') || $tg.hasClass('import-type-on')) return;
            if (!me.widgets.validator.validate('object', false, true).result) return;
            var curObject = me.curObject || me.model.get('curObject');
            var _importType = $tg.data('type');
            //如果当前对象支持审批流  则不隐藏
            // 导入 和主从支持审批流
            this.$('.andchild').css('visibility', _importType&&curObject.IsApprovalFlow === 'update' ? 'hidden' : 'visible');

            var doing = function() {
                me.widgets.validator.hideError('importType');
                $('.j-import-type', me.$el).removeClass('import-type-on');
                $tg.addClass('import-type-on');
                $('.import-type-options', me.$el).toggleClass('import-type-options-updateon', _importType === 'update');
                me.model.set('importType', _importType);

            }
            let obj = curObject;

                // 对于返回obj.importUniquenessRule值的对象进行唯一性规则提示判断
                if (obj.importUniquenessRule && _importType === 'update') {
                    if (obj.importUniquenessRule.IsEnabled) {
                        doing();
                    } else {
                        me.showConfirm(obj.objectName + $t('crm.尚未开启唯一性规则'));
                    }
                    return;
                }
                doing();
        },

        // 导入方式发生变更后的 处理
        importTypeMediation: function() {
            var me = this;
            me.initImportOptions();

            me.setAddfileTip();
        },

        // 初始化 选择导入方式之后 的选项
        initImportOptions: function() {
            var me = this;
            var importType = me.model.get('importType');
            var curObject = me.curObject || me.model.get('curObject');
            var triggerApprovalFlow = curObject[importType === 'update' ? 'updateImport' : 'insertImport']?.triggerApprovalFlow;
            var addTriggerWorkFlow = curObject.insertImport?.triggerWorkFlow;
            var updateTriggerWorkFlow = curObject.updateImport?.triggerWorkFlow;
            var $options = $('.import-type-options', me.$el);
            var MatchingType = 2;
            var tips = me.getTips();
            $options.html(optionsTpl(_.extend({
                type: importType,
                tips: tips,
                IsApprovalFlow: false,
                IsApprovalFlowDisabled: triggerApprovalFlow?.readOnly || false,
                AddIsWorkFlowAddDisabled: addTriggerWorkFlow?.readOnly || false,
                UpdateIsWorkFlowAddDisabled: updateTriggerWorkFlow?.readOnly || false,
                importUniquenessRule: void 0,
                IsCheckOutOwner: false,
                IsRemoveOutTeamMember: false,
                IsUpdateOwner: false,
            }, curObject)));
            !$options.children().length && $options.html('');

      
            if (importType === 'update' && curObject.matchingTypes.update_import && !curObject.matchingTypes.update_import.includes(2)) {
                //更新数据 
                MatchingType = curObject.matchingTypes.update_import[0]
                //更新视图
                me.updateMatchingTypeSelected(MatchingType)   
            }
            

            
            (importType === 'add' && curObject.matchingTypes.insert_import && curObject.matchingTypes.insert_import.length === 1) && (MatchingType = curObject.matchingTypes.insert_import[0]);
            me.toggleAnyFieldSelect(importType);
            me.model.set({
                IsEmptyValueToUpdate: curObject.IsEmptyValueToUpdate,
                IsWorkFlowEnabled: 0,
                IsApprovalFlowEnabled: 0,
                IsCheckOutOwner: 0,
                IsUpdateOwner:0,
                IsRemoveOutTeamMember: 0,
                MatchingType: MatchingType
            })
            if (triggerApprovalFlow?.readOnly) {
                var value = triggerApprovalFlow.value == true ? 1 : 0;
                if (value) {
                    var $item = $options.find('.j-radio-approval');
                    $item.find('.mn-selected').removeClass('mn-selected');
                    $item.find('.j-radio__first').addClass('mn-selected');
                    me.model.set({IsApprovalFlowEnabled: value})
                }
            }
            if (addTriggerWorkFlow?.readOnly && importType === 'add') {
                var value = addTriggerWorkFlow.value == true ? 1 : 0;
                if (value) {
                    var $item = $options.find('.j-radio-workflow');
                    $item.find('.mn-selected').removeClass('mn-selected');
                    $item.find('.j-radio__first').addClass('mn-selected');
                    me.model.set({IsWorkFlowEnabled: value})
                }
            }
            if (updateTriggerWorkFlow?.readOnly && importType === 'update') {
                var value = updateTriggerWorkFlow.value == true ? 1 : 0;
                if (value) {
                    var $item = $options.find('.j-radio-workflow');
                    $item.find('.mn-selected').removeClass('mn-selected');
                    $item.find('.j-radio__first').addClass('mn-selected');
                    me.model.set({IsWorkFlowEnabled: value})
                }
            }

            me.__setImportConfig(importType);
            me.__handleDeptUpdateImport();
        },
        updateMatchingTypeSelected(data) {
            let wrapper = this.$(`.j-radio-uniqueid .mn-radio-item[data-value=${data}]`)
            wrapper && wrapper.addClass('mn-selected')
            
        },

        //临时支持大客户需求，根据传入参数禁用审批流工作流
        __setImportConfig: function(importType) {
            var importConfig = this.options && this.options.importConfig && this.options.importConfig[importType];
            if(!importConfig) return;
            var wf = importConfig.workFlow;
            var af = importConfig.approveFlow;
            if(wf) {
                if(wf.value) {
                    this.$('.j-radio-workflow .j-radio__first').click();
                }
                if(wf.disabled) {
                    this.$('.j-radio-workflow .mn-radio-item').addClass('disabled-selected');
                }
            }
            if(af) {
                if(af.value) {
                    this.$('.j-radio-approval .j-radio__first').click();
                }
                if(af.disabled) {
                    this.$('.j-radio-approval .mn-radio-item').addClass('disabled-selected');
                }
            }
        },

        // 判断 是否是部门更新导入，且灰度了部门名称允许重复
        // 910 - 新增人员名称允许重复
        __isDeptUpdateImport(){
            var me = this;
            var importType = me.model.get('importType');
            var apiName = me.model.get('apiName');

            var departmentGray = apiName === 'DepartmentObj' && FS.util.getUserAttribute('isGrayDepartmentRepeatNameImport');
            var personelGray = apiName === 'PersonnelObj' && FS.util.getUserAttribute('isGrayPersonelRepeatNameImport');

            return importType === 'update' && (departmentGray || personelGray);
        },
        // 处理 部门更新导入，且灰度了部门名称允许重复
        // 识别方式为两种：其他-部门编码(默认选中且禁用)、唯一性ID
        __handleDeptUpdateImport: function () {
            var me = this;
            var apiName = me.model.get('apiName');
            var code = apiName === 'DepartmentObj' ? 'dept_code': 'employee_number';
            if (me.__isDeptUpdateImport()) {
                var $wrapper = me.$('.mn-radio-box.j-radio-uniqueid');
                var $value = me.$('.anyfield-wrapper .mn-radio-item');
                var _value = $value.data('value');
                $wrapper.find('.mn-selected').removeClass('mn-selected')
                $value.addClass('mn-selected');
                me.model.set('MatchingType', _value);
                _.delay(function () {
                    if (me.widgets.anyFieldSelect) {
                        me.widgets.anyFieldSelect.disable();
                        me.widgets.anyFieldSelect.setValue(code)
                        me.model.set('specifiedField', code);
                    }
                }, 500)
            }
        },
        //渲染更新负责人配置
        renderUpdateOwner() {
            let me = this;
            let curObject = this.curObject || this.model.get('curObject');
            let name = curObject.objectCode == "HighSeasObj" ? '' :curObject.name
            var labelInfo = {
                team: $t("移出该{{name}}团队", {
                    name
                }),
                general: $t("变更为{{name}}普通成员", {
                    name
                }),
                group: $t("变更为{{name}}团队成员", {
                    name
                }),
                generalTip: $t("普通成员是指该{{name}}协作人", {
                    name: name || '数据'
                }),
                groupTip: $t("团队成员是指该{{name}}协作人", {
                    name: name|| '数据'
                })
            };
            var $options = $('.update-owner-wrapper', me.$el); 
            // util.getNewoppoConfig().done(function(status) {
            Promise.all([
                util.getNewoppoConfig(),
                util.getSaleContractObj(),
                me.model.fetchRoleList(me.model.get('apiName')),
            ]).then(function(res) {
                let [status, isNewContract, roleList] = res;
                $options.html(updateOwnerTpl(_.extend({
                    IsUpdateOwner: me.model.get('IsUpdateOwner'),
                    apiname: curObject.objectApiName,
                    labelInfo,
                    objectList: me.getObjectList(status, isNewContract),
                    isNewOppo: status,
                    oldOwnerTeamMember: {
                        strategy: 1,//默认将原负责人 移除
                    },
                    roleOptions: roleList,
                    hideOther: CRM.util.isGrayScale('CRM_CHANGEOWNER_EXCLUDE_OTHER'),
                })));
            }) 
            me.model.set('oldOwnerTeamMember', {'strategy': 1})
        },
        //整理对象列表
        getObjectList(isNewOppo, isNewContract) {
            let objectList = [{
                label: $t("crm.联系人"),
                value: 'ContactObj',
            }, {
                label: $t("crm.销售订单"),
                value: 'SalesOrderObj',
            }]
            if(isNewContract) {
              objectList.push({
                label: $t("销售合同"),
                value: 'SaleContractObj',
              })
            } else {
              objectList.push({
                label: $t("crm.合同"),
                value: 'ContractObj',
              })
            }
            if (isNewOppo) {
                objectList.unshift({
                    label: $t("crm.商机2.0"),
                    value: 'NewOpportunityObj',
                })
            } else {
                objectList.unshift({
                    label: $t("crm.商机"),
                    value: 'OpportunityObj',
                })
            }
            return objectList;
        },
        //全选
        _checkAllObjs: function(e) {
            var $currentTarget = $(e.currentTarget);
            if ($currentTarget.html() === $t("全选")) {
                this.$('.other-obj .mn-checkbox-item').addClass('mn-selected');
                $currentTarget.html($t("反选"))
                e.stopPropagation();
                return
            } else if ($currentTarget.html() === $t("反选")) {
                this.$('.other-obj .mn-checkbox-item').removeClass('mn-selected');
                $currentTarget.html($t("全选"))
                e.stopPropagation();
                return
            }
        },

        _checkObj: function(e) {
            var $currentTarget = $(e.currentTarget);
            $currentTarget.toggleClass('mn-selected');

            this.$('.check-all .j-check-all-objs').html(this.$('.other-obj .right-con .mn-checkbox-item.mn-selected').length < 4 ? $t("全选") : $t("反选"));
            e.stopPropagation();
        },
        // 获取点击的对象
        _getRelationList: function() {
            var list = [];
            this.$('.import-type-option .other-obj .right-con .mn-selected').each(function(index, item) {
                list.push($(item).attr('data-type'));
            });
            return list;
        },
        matchingTypeHandle: function() {
            var anyFieldSelect = this.widgets.anyFieldSelect;
            if(anyFieldSelect) {
                if(this.model.get('MatchingType') == '4') {
                    if(this.__isDeptUpdateImport()){
                        this.__handleDeptUpdateImport();
                        return;
                    }
                    anyFieldSelect.enable();
                    anyFieldSelect.setFirstValue(true);
                } else {
                    anyFieldSelect.disable();
                    anyFieldSelect.clear(true);
                }
            }
        },

        toggleAnyFieldSelect: function(importType) {
            var me = this;
            var apiname = me.model.get('apiName');
            me.model.set('specifiedField', '');
            if(me.widgets.anyFieldSelect) {
                me.widgets.anyFieldSelect.destroy();
                me.widgets.anyFieldSelect = null;
            }

            if(importType === 'add') return;
           /*  var config = util.getUserAttribute('crmUpdateImport');
            if(!config) return; 
            if(!(/__c$/.test(apiname) || _.contains(config, apiname))) return;*/

            util.getDescribeLayout({apiname: apiname, include_layout: false}).then(function(des) {
                if(importType === 'add' || me.model.get('apiName') !== apiname) return;
                var options = [];
                const {whitelist = [], blacklist = []} = config.anyFieldConfig[apiname] || {};
                _.each(des.objectDescribe.fields, function(a) {
                    if (whitelist.includes(a.api_name)) {
                        options.push({
                            name: a.label,
                            value: a.api_name
                        });
                    } else if(a.is_unique && a.api_name !== 'name' && _.contains(['text', 'auto_number', 'url', 'email', 'phone_number'], a.type) && !blacklist.includes(a.api_name)) {
                        options.push({
                            name: a.label,
                            value: a.api_name
                        })
                    }
                })
                if(!options.length) return;
                var $wrap = me.$('.anyfield-wrapper')
                if(!$wrap.length) return;
                $wrap.css('display', 'inline-block');

                me.widgets.anyFieldSelect = new Select({
                    $wrap: me.$('.anyfield-select'),
                    multiple: 'single',
                    placeHolder: '  ',
                    options: options,
                    space: 3,
                    zIndex: 1000,
                    disabled: true
                });
                me.widgets.anyFieldSelect.on('change', function(val) {
                    me.model.set('specifiedField', val);
                });
            })
        },

        // 获取 选择导入方式之后 的提示
        getTips: function() {
            var me = this;
            var importType = me.model.get('importType');
            var curObject = me.curObject || me.model.get('curObject');
            var _tips = [];
            var _tip = '';

            // 唯一性规则提示
            if(curObject.importUniquenessRule && curObject.importUniquenessRule.IsEnabled) {
                if (curObject.uniqueTip) {
                    // 已经取过之后就直接获取
                    _tips.push(curObject.uniqueTip);
                } else {
                    _.each(curObject.importUniquenessRule.RuleFieldList, function(item) {
                        _tip += $t("、") + item.FieldCaption;
                    })
                    _tip = _tip.substr(1).length ? _tip.substr(1) + $t("不重复"): '';
                    _tip && (_tips.push(_tip), curObject.uniqueTip = _tip);
                }
            }

            if (importType === 'add' && curObject.tip) {
                _tips.push(curObject.tip);
            }

            return _tips;
        },

        // 数据模板下载
        downloadTpl: function(e) {
            var me = this;
            var $tg = $(e.currentTarget);
            var _param = {};
            var curObject = me.curObject || me.model.get('curObject');
            var typeText = me.model.get('importType') === 'add' ? '新建' : '更新';//[ignore-i18n]
            // 验证是否选择了业务对象
            if (!me.widgets.validator.validate('object', false, true).result) return;
            // 验证是否选择了子业务对象
            if (!me.widgets.validator.validate('childrenObject', false, true).result) return;
            // 验证是否选择了关联对象
            if (curObject.showRelation && me.widgets.relSelect) {
                if (!me.widgets.validator2.validate('relation', false, true).result) return;
            }
            // 验证是否选择了导入方式
            if (!me.widgets.validator.validate('importType', false, true).result) return;
            // 展示关联对象的情况增加参数
            if (curObject.showRelation && me.widgets.relSelect) {
                _param = {
                    relatedApiNameList: _.pluck(me.widgets.relSelect.getSelected(), 'value'),
                    Type: 1,
                }
            }

            me._formatParam(_param);
            if (me.options && me.options.icmgOnImporterMount) {
                // 互联专用，非互联侧请【勿】使用，如有修改请告知互联跟进
                // 互联自定义下载模板
                me.model.downloadTpl();
                return;
            };
            me.model.downloadTpl(_param)
            .then(function(reg) {
                var url = '';
                var displayName = curObject.name + (_param.unionImportApiNameList ? ',' +  _param._child_display_name : '');
                displayName = $t('{{displayName}}对象{{typeText}}导入模板', {displayName: displayName , typeText: $t(typeText).toLowerCase()}) + '.xlsx';
                if (reg.path) {
                    url = util.getFscTempFileUrl(reg.path, displayName, true);
                } else {
                    url = util.getFscLinkByToken(reg.TokenID || reg.Url, 'down');
                }
                var app = util.getConnectAppInfo();
                if(app && app.appId) { //下游互联应用
                    url = url + '&appid=' + app.appId;
                }
                location.href = url;
            })
        },

        // 设置添加数据的文案提示 （根据导入方式以及是否触发工作流）
        setAddfileTip: function(key) {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            var _key = _.isString(key) ? key : 'limit2';
            // if (me.model.get('importType') === 'add' && !me.model.get('IsWorkFlowEnabled')) _key = 'limit1';
            //新建和更新导入提示变更
            if ( !me.model.get('IsWorkFlowEnabled')) {
                _key = 'limit1';
            }
            var _tips = {
                limit1: $t("crm.导入上限300000大小50M"),
                limit2: $t("crm.导入上限1000大小2M"),
            }
            me.model.get('IsApprovalFlowEnabled') && (_tips.limit2 = _tips.limit2.replace('1000', '500'));
            $('.import-add-tip', me.$el).html(_tips[_key]);
        },

        // 查重选项事件
        doSwitch: function(e) {
            var me = this;
            var $tg = $(e.currentTarget);
            $tg.toggleClass('import-switch-on');
            var key = $tg.attr('data-key');
            var flag = $tg.hasClass('import-switch-on');
            var $p = $tg.closest('.import-sect-dd');
            if(key === 'businessFill') {
                $p.toggleClass('business-fill', flag);
                if(flag) {
                    $p.find('.j-b-s').addClass('import-switch-on');
                    me.model.set('businessEnable', true);
                } else {
                    me._businessFill.checked = false;
                }
            } else if(key === 'businessEnable') {
                if(!flag) {
                    $p.find('.j-b-f').removeClass('import-switch-on');
                    $p.removeClass('business-fill');
                    me._businessFill.checked = false;
                    me.model.set('businessFill', null);
                }
            }
            me.model.set(key, flag);
        },

        // 导入事件
        import: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            var r1 = me.widgets.validator.validate(void 0, void 0, true).result;
            var r2 = true;
            if (curObject.showRelation) r2 = me.widgets.validator2.validate().result;
            if (!r1 || !r2) return;
            me.setStore(curObject.objectCode,JSON.stringify(this.getParams()||''))
            me.showLoading();
            me.widgets.h5ImgUploader && this.widgets.h5ImgUploader.startUpload();
			
            // 导入埋点
            try {
                window.logger.action({
                    eventId: 's-paasobj_list_import',
                    str1: curObject.objectCode
                })
            } catch(e) {}
        },
        setStore: function (apiName, cacheData) {
            try {
                FS.store.setItem('crm_store_importconfig_' + apiName, cacheData);
            } catch(e) {}
        },
         getStore: function(objApiName) {
            return new Promise(resolve => {
                try {
                    FS.store.getItem('crm_store_importconfig_' + objApiName).then(cacheData => {
                        resolve(cacheData || '')
                    }, () => {
                        resolve()
                    })
                } catch(e) {
                    resolve();
                }
            })
        },

        // 获取通知方式的员工数据
        getMembers: function() {
            var me = this;
            if (!me.widgets.notice) return [];
            var _notifies = [];
            var member = me.widgets.notice.getValue('member') || [];
            var circle = me.widgets.notice.getValue('circle') || [];
            member = member.concat(_.pluck(util.getEmployeesByCircleIds(circle) || [], 'id'));
            member = _.uniq(member);
            _.each(member, function (id) {
                var emp = util.getEmployeeById(id);
                if (id) {
                    _notifies.push({
                        userId: emp.id,
                        userName: emp.name
                    })
                }
            });
            return _notifies;
        },

        // 获取导入的参数
        getParams: function() {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            var mt = me.model.get('MatchingType');
            var _params = {
                apiName: curObject.objectApiName,
                apiFullName: curObject.objectName,
                notifies: me.getMembers(),
                templateId: 'crm_001',
                jobType: 1,
                queryParam: {
                    objectCode: curObject.objectCode,
					ImportType: me.model.get('importType') === 'add' ? 0 : 1,
                    IsEmptyValueToUpdate: !!me.model.get('IsEmptyValueToUpdate'),
                    IsUpdateOwner: !!me.model.get('IsUpdateOwner'),
                    IsUnionDuplicateChecking: !!me.model.get('IsUnionDuplicateChecking'),
                    IsWorkFlowEnabled: !!me.model.get('IsWorkFlowEnabled'),
                    IsApprovalFlowEnabled: !!me.model.get('IsApprovalFlowEnabled'),
                    IsCheckOutOwner: curObject.IsCheckOutOwner ? !!me.model.get('IsCheckOutOwner') : void 0,
                    IsRemoveOutTeamMember: curObject.IsRemoveOutTeamMember ? !!me.model.get('IsRemoveOutTeamMember') : void 0,
                    MatchingType: mt,
                    fileExt: me.model.get('fileExt'),
                    filePath: me.model.get('filePath'),
                    fileName: me.model.get('fileName'),
                    isNoBatch: curObject.isNoBatch,
                    unionImportApiNameList: curObject.unionImportApiNameList,
                    specifiedField: mt == '4' ? me.model.get('specifiedField') : void 0,
                    oldOwnerTeamMember: me.oldOwnerTeamMemberToString(curObject.objectApiName),
                    extendAttribute: me.options.extendAttribute,
                    importMethod: me.model.get('importType') === 'add' && curObject?.insertImport?.importMethod || void 0
                }
            }
            me._formatParam(_params.queryParam);
            if (curObject.showRelation && me.widgets.relSelect) {
                _params.queryParam.relatedApiNameList = _.pluck(me.widgets.relSelect.getSelected(), 'value');
            }
            return _params;
        },
       
        oldOwnerTeamMemberToString(api_name) {
            let ownerData = this.model.get('oldOwnerTeamMember') || {};
            let res = {};
            Object.keys(ownerData).forEach(key => {
                if (key == 'roleList') {
                    res[key] = ownerData[key].toString()?.split(',')
                   
                } else {
                    res[key] = ownerData[key].toString()  
                    
                }
            })
            if(api_name ==  "AccountObj"){
                res['otherObjects'] = this._getRelationList()
            }
          
                return res;
        },


        //是否是下游企业导入
        isConnectApp: function() {
            var app = util.getConnectAppInfo();
            return app && app.appId;
        },

        // 执行导入
        doImport: function() {
            var me = this;
            var params = me.getParams();
            if (me.doing) return;
            me.doing = true;
            me.model.import(params)
            .done(function() {
                me.widgets.h5ImgUploader && me.widgets.h5ImgUploader.removeAllFile(); // 清除操作的文件
                me.hide();
                if (me.options.importSuccssCb) { //自定义导入成功回调(如后台管理人员列表导入成功跳转后台管理菜单页面)
                    me.options.importSuccssCb();
                    return;
                }
                if(me.isConnectApp()) {
                    util.alert($t('paas.crm.api.importasync.connectapp_promptly', null, '操作成功，导入结果会以业务通知形式下发，请及时关注'));
                    return;
                }
                if (window.location.hash.indexOf('#crm/iorecords') > -1) {
                    me.trigger('success');
                } else {
                    //特殊企业不走新页签打开
                    if(_.contains(['fktest087', 'fktest299', 'cdnlight2021', 'dthznq578881'], CRM.ea)) {
                        CRM.control.navigate('#crm/iorecords');
                    } else {
                        window.open('#crm/iorecords');
                    }
                }
            })
            .fail(function(msg) {
                util.alert(msg || $t("服务器返回错误!"));
            })
            .always(function() {
                me.doing = false;
                me.hideLoading();
            })
        },

        //点击checkbox
        checkboxHandle: function (e) {      
            var me = this;
            me.widgets.validator.hideError('rolePermission');
            var $tg = $(e.currentTarget);
            e.stopPropagation();
            var $value = $tg.find('.mn-checkbox-item');
            let oldOwnerTeamMember = this.model.get("oldOwnerTeamMember");
            var _value = $value.data('value');
            if ($value.hasClass('mn-selected')) {
                //取消
                $value.removeClass('mn-selected');
                oldOwnerTeamMember.roleList = (oldOwnerTeamMember?.roleList||[]).filter(v=>v != _value)
            } else {
                //勾选
                $value.addClass('mn-selected');
                oldOwnerTeamMember.roleList&& oldOwnerTeamMember.roleList.push(_value)

            }
            me.model.set('oldOwnerTeamMember', { ...oldOwnerTeamMember});
        },
        radioHandle: function(e) {
            var me = this;
            var $tg = $(e.currentTarget);
            e.stopPropagation();
            var $wrapper = $tg.closest('.mn-radio-box');
            var $value = $tg.find('.mn-radio-item');
            if ($value.hasClass('mn-selected') || $value.hasClass('disabled-selected')) return;
            var _value = $value.data('value');
            $wrapper.find('.mn-selected').removeClass('mn-selected');
            $value.addClass('mn-selected');
            let key = $wrapper.data('key');
            key == 'IsUpdateOwner' && _value&& me.$('.update-owner-wrapper').removeClass('hide')
            if (['strategy','permissionType'].includes(key)) {
             this.setoldOwnerTeamMember(key,_value,$wrapper)
                return;
            }
            me.model.set($wrapper.data('key'), _value);

			//$wrapper.hasClass('j-radio-workflow') && me.$('.workflow-tip').toggleClass('hide', (_value == 0));
            //是否隐藏更换负责人
            if ($wrapper.hasClass('j-radio-update-owner')) {
                this.$('.update-owner-wrapper').addClass('hide');
                if (_value) {
                    this.$('.update-owner-wrapper').removeClass('hide');
                    me.renderUpdateOwner();
                } else {
                    me.model.set('oldOwnerTeamMember', null);
                }
            }
			if ($wrapper.hasClass('j-radio-update-emptyfield')) {
				me.$('.import-tip[data-value]').addClass('hide');
				me.$('.import-tip[data-value=' + _value + ']').removeClass('hide')
			}
            if($wrapper.hasClass('j-radio-approval')) {
                var $radios = me.$('.j-radio-workflow .mn-radio-item');
                if(_value) {
                    $radios.eq(0).addClass('mn-selected disabled-selected');
                    $radios.eq(1).addClass('disabled-selected').removeClass('mn-selected');
                    me.model.set('IsWorkFlowEnabled', _value);
                } else {
                    $radios.removeClass('disabled-selected');
                }
                me.setAddfileTip('limit2');
            }

            me.setWorkTips();
        },
        //设置更换负责人配置内容
        setoldOwnerTeamMember(key,value,$wrapper) {
            let oldOwnerTeamMember = this.model.get("oldOwnerTeamMember");
            if ($wrapper.hasClass('j-set-pre-owner')) {
                this.$('.general-detail').addClass('hide');
                if (value==2) {
                    this.$('.general-detail').removeClass('hide');
                }
            }
            if (key == 'strategy') {
                if (value == 2) {
                    oldOwnerTeamMember = {
                        ...oldOwnerTeamMember, permissionType: 1,
                        roleList: [4]
                    }
                    this.$(`.general-detail .mn-checkbox-item[data-value=4]`).addClass('mn-selected');
                    let detailDom = this.$(`.general-detail .j-radio-permission-type`);
                    detailDom.find('.mn-selected').removeClass('mn-selected');
                    this.$(`.general-detail .j-radio-permission-type .mn-radio-item[data-value=1]`).addClass('mn-selected');
                } else {
                    delete oldOwnerTeamMember.permissionType;
                    delete oldOwnerTeamMember.roleList
                    this.$(`.general-detail .mn-checkbox-item[data-value=4]`).removeClass('mn-selected');
                    
                }
            }
            this.model.set('oldOwnerTeamMember', { ...oldOwnerTeamMember, [key]: value });
        },

        setWorkTips: function() {
            var flag = this.model.get('IsWorkFlowEnabled');
            var str = $t("当前导入数据上限为1000条");
            if(this.model.get('IsApprovalFlowEnabled')) {
                str = str.replace('1000', '500');
            }
            this.$('.workflow-tip').html(flag ? str : '');
        },

        showLoading: function() {
            var me = this;
            $('.j-import', me.$el).addClass('crm-btn-importing');
            me._timer && (clearTimeout(me._timer), me._timer = null);
            me._timer = setTimeout(function() {
                me._timer = null;
                util.waiting(false);
            }, 30000);
            util.waiting($t('正在导入中') + '...');
        },

        hideLoading: function() {
            var me = this;
            $('.j-import', me.$el).removeClass('crm-btn-importing');
            util.waiting(false);
            me._timer && (clearTimeout(me._timer), me._timer = null);
        },

        getCrmManagers: function() {
            var me = this;
            var def = $.Deferred();
            if (me.crmManagers) {
                def.resolve && def.resolve(me.crmManagers);
            } else {
                util.getCrmManagers(function(crmManagers){
                    me.crmManagers = crmManagers;
                    def.resolve && def.resolve(crmManagers);
                });
            }
            return def.promise();
        },

        showConfirm: function(tip) {
            var me = this;
            var curObject = me.curObject || me.model.get('curObject');
            if (curObject.importUniquenessRule && curObject.importUniquenessRule.RuleSettingRight) {//有设置唯一规则权限 跳转预对象设置页面
				var confirmYR = util.confirm(tip, $t("提示"), function () {
					me.hide();
					confirmYR.hide();
					CRM.control.navigate('#crmmanage/=/module-sysobject');
				}, {
					btnLabel: {
						confirm: $t("去开启"),
						cancel: $t("取 消")
					}
				});
            } else {//无权限联系管理员
                me.getCrmManagers()
                .then(function(crmManagers) {
					var confirmNR = util.confirm(tip, $t("提示"), function () {
						me.hide();
						confirmNR.hide();
						// FS.MEDIATOR.trigger('qx.openChat', crmManagers[0].EmployeeID, 'employee');
                        FS.tpl.event.trigger("qxOpenChat", crmManagers[0].EmployeeID, 'employee');
					}, {
						btnLabel: {
							confirm: $t("联系管理员开启"),
							cancel: $t("取 消")
						}
					});
				});
			}
        },

        hide: function() {
            this.destroy();
			this.$el && this.$el.remove();
            this.$el = null;
        },

        destroy: function() {
            _.each(this.widgets, function (widget) {
				widget && widget.destroy && widget.destroy();
				widget = null;
			})
        },
    });
    module.exports = ImportTool;
})
