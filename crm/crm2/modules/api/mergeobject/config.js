define(function (require, exports, module) {    
    var baseFilter = {
        api_name: ['relevant_team'],
        render_type: ['image', 'file_attachment', 'signature', 'quote', 'html_rich_text', 'group', 'big_file_attachment', 'rich_text', 'count', 'big_text', 'formula']
    }
    var filterConfig = {
        baseFilter: baseFilter,
        'AccountObj': _.extend({}, baseFilter, {
            api_name: [
                'poi_information',
            ]
        }),
        'ContactObj': _.extend({}, baseFilter, {
            api_name: [
                'date_of_birth', //生日
            ]
        }),
        'LeadsObj': _.extend({}, baseFilter, {
            api_name: [
                'account_id',
                'contact_id',
                'opportunity_id',
                'new_opportunity_id',
                'resale_count',
                'relevant_team',
            ]
        }),
        'PartnerObj': _.extend({}, baseFilter, {
            api_name: [
                'is_er_enterprise',  // 是否关联对接企业
                'relevant_team',     // 相关团队
                'address',           // 详细地址
                'country',           // 国家
                'province',          // 省
                'city',              // 市
                'district'           // 区
            ]
        }),
        'NewOpportunityObj': _.extend({}, baseFilter, {
          api_name: [
            
          ]
        }),
        'AccountMainDataObj': _.extend({}, baseFilter, {
          api_name: [
            'biz_status',    // 分配状态
            'industry_ext',  // 工商扩展
            'relevant_team',     // 相关团队
          ]
        }),
        'LoyaltyMemberObj': _.extend({}, baseFilter, {
          api_name: [
            'tier_start_time',  // 当前等级开始时间
            'tier_id',          // 会员等级
            'consumer_points',  // 消费积分
            'frozen_grading_points',  // 冻结定级积分
            'grading_points',   // 定级积分
            'frozen_points',    // 冻结积分
            'member_status',    // 会员状态
          ]
        }),
    }
    var baseDisable = {
        render_type: ['auto_number'],
        is_unique: [true],
    }
    var disableConfig = {
        baseDisable: baseDisable,
        'AccountObj' : _.extend({}, baseDisable, {
            api_name: [
                'account_status',       // 状态
                // 'deal_status',          // 成交状态
                'owner',                // 负责人
                'created_by',           // 创建人
                'create_time',          // 创建时间
                'claimed_time',         // 领取时间
                'transfer_count',       // 转手次数
                'last_modified_time',   // 最后修改时间
                'completion_rate',      // 客户资料完善度
                'high_seas_id',         // 所属公海
                'high_seas_name',       // 所属公海（名称）
                'owner_department',     // 负责人主属部门
                'remaining_time',       // 剩余保有时间
                'last_modified_by',     // 最后修改人
                'expire_time',          // 预计收回时间
                // 'last_deal_closed_time', // 最近一次成交时间
                'returned_time',        // 退回/收回时间
                'lock_status',          // 锁定状态
                // 'out_owner',         // 外部负责人   905
                'is_er_enterprise',     // 是否关联对接企业 905
                'extend_days',          // 申请延期天数  905
                'extend_reason',        // 延期原因   905

                'recycled_reason',      // 退回/收回原因
                'life_status',          // 生命状态
                'last_followed_time',   //最后跟进时间
                'owner_modified_time',  // 负责人变更时间
                'biz_status',           //客户状态/分配状态
                'parent_account_id',    //上级客户
                'out_resources',        //外部来源
                'biz_reg_name',         //工商注册
                'account_main_data_id', //客户主数据
                'mc_currency',          //币种
            ]
        }),
        'ContactObj': _.extend({}, baseDisable, {
            api_name: [
                'contact_status',       // 状态
                'life_status',          // 生命状态
                'owner',                // 负责人
                'created_by',           // 创建人
                'create_time',          // 创建时间
                'last_modified_time',    // 最后修改时间
                'out_resources',          //外部来源
                'account_id',            // 客户名称
            ] 
        }),
        'LeadsObj': _.extend({}, baseDisable, {
            api_name: [
                'assigner_id',          //分配管理员
                'biz_status',           //状态
                'expire_time',          //预计收回时间
                'is_overtime',          //是否超时
                'is_remind_recycling',  //是否待回收提醒
                'last_follow_time',     //最后跟进时间
                'last_follower',        //最后跟进人
                'last_modified_by',     //最后修改人
                'leads_status',         //状态（原）
                'lock_status',          //锁定状态
                'owner_change_time',    //负责人变更时间
                'owner_department',     //负责人主属部门
                'remaining_time',       //剩余保有时间
                'transform_time',       //转换时间
                'completed_result',     //处理结果
                'contact_status',       // 状态
                'life_status',          // 生命状态
                'owner',                // 负责人
                'created_by',           // 创建人
                'create_time',          // 创建时间
                'last_modified_time',   // 最后修改时间
                'out_resources',        //外部来源
                'biz_reg_name',         //工商注册
                'leads_pool_id',        //线索池
            ] 
        }),
        'EnterpriseInfoObj': _.extend({}, baseDisable, {
            api_name: [
                'out_owner',            //外部负责人
                'create_time',          //创建时间
                'created_by',           //创建人
                'data_own_department',  //归属部门
                'last_modified_time',   //最后修改时间
                'last_modified_by',     //最后修改人
                'owner_department',     //负责人所在部门
                'owner',                //负责人
                'lock_status',          //锁定状态
                'life_status',          //生命状态
                'record_type',          //业务类型
                'data_own_organization',//归属组织
                'biz_reg_name',         //工商注册
            ] 
        }),
        'PartnerObj': _.extend({}, baseDisable, {
            api_name: [
                'record_type',          //业务类型
                'life_status',          //生命状态
                'lock_status',          //锁定状态
                'owner',                //负责人
                'owner_department',     //负责人所在部门
                'last_modified_by',     //最后修改人
                'last_modified_time',   //最后修改时间
                'data_own_department',  //归属部门
                'created_by',           //创建人
                'create_time',          //创建时间
                'partner_id',           //合作伙伴
                'parent_id',            //上级合作伙伴
                'resources',            //来源
                'data_own_organization',//归属组织
                'out_owner',            //外部负责人
                'leads_id',             //源线索
                'biz_reg_name',         //工商注册
            ] 
        }),
        'NewOpportunityObj': _.extend({}, baseDisable, {
          api_name: [
            'cost_time',            //结单周期
            'owner_department',     //负责人所在部门
            'opp_lines_sum',        //结单周期
            'lock_status',          //锁定状态
            'create_time',          //创建时间
            'last_followed_time',   //最后跟进时间
            'created_by',           //创建人
            'relevant_team',        //相关团队
            'new_opportunity_path', //商机层级
            'data_own_department',  //归属部门
            'opp_discount',         //整单折扣
            'parent_id',            //上级商机2.0
            'price_book_id',        //价目表
            'sales_process_id',     //销售流程
            'data_own_organization',//归属组织
            'sales_status',         //阶段状态
            'leads_id',             //源线索
            'sales_stage',          //商机阶段
            'partner_id',           //合作伙伴
            'stg_changed_time',     //阶段变更时间
            'probability_amount',   //预测金额
            'out_resources',        //外部来源
            'owner',                //负责人
            'amount',               //商机金额
            'last_modified_time',   //最后修改时间
            'life_status',          //生命状态
            'last_modified_by',     //最后修改人
            'out_tenant_id',        //外部企业
            'record_type',          //业务类型
            'account_id',           //客户名称
          ] 
        }),
        'AccountMainDataObj': _.extend({}, baseDisable, {
          api_name: [
            'biz_reg_name',            //工商注册
            'owner_department',        //负责人主属部门
            'data_own_department',     //归属部门
            'data_own_organization',   //归属组织
            'partner_id',              //合作伙伴
            'out_resources',           //外部来源
            'out_owner',               //外部负责人
            'owner',                   //负责人
            'last_modified_time',      //最后修改时间
            'life_status',            //生命状态
            'last_modified_by',        //最后修改人
            'created_by',              // 创建人
            'create_time',             // 创建时间
          ] 
        }),
        'LoyaltyMemberObj': _.extend({}, baseDisable, {
          api_name: [
            'out_owner',               //外部负责人
            'owner',                   //负责人
            'last_modified_time',      //最后修改时间
            'life_status',            //生命状态
            'lock_status',          //锁定状态
            'last_modified_by',        //最后修改人
            'created_by',              // 创建人
            'create_time',             // 创建时间
            'parent_id',             // 直属上级
            'tree_path',             // 会员层级
            'data_own_department',  //归属部门
            'owner_department',     // 负责人主属部门
            'record_type',          //业务类型
          ] 
        }),
    }

    // 未开启成交规则后动作灰度的 保留成交规则的禁用
    if (!CRM.util.isGrayScale('CRM_ACCOUNTOBJ_DEALRULE')) {
        disableConfig.AccountObj.api_name.push('deal_status');
        disableConfig.AccountObj.api_name.push('last_deal_closed_time');
    }


    module.exports = {
        filter: filterConfig,
        disable: disableConfig,
    }
});