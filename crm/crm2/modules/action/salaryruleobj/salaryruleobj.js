define(function (require, exports, module) {
	var util = require('crm-modules/common/util'),
    Myobject = require('crm-modules/action/myobject/myobject'),
    selectType = require('../field/selecttype/selecttype');
	
	/**
	 * 工资规则对象
	 */
	const SalaryRuleObj = Myobject.extend({
		objName: $t("工资规则"),
		
		/**
		 * 获取对象描述信息
		 * @returns {Promise} 返回描述信息Promise
		 */
		getDescribe: function() {
			return CRM.util.FHHApi({
				url: "/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName",
				data: {
					describe_apiname: 'SalaryRuleObj',
					include_layout: false,
					include_related_list: false,
					get_label_direct: true,
					include_describe_extra: false
				}
			}).then(({Value}) => Value.objectDescribe);
		},

		/**
		 * 打开工资规则组件
		 * @param {Object} options 组件配置选项
		 * @param {Boolean} isEdit 是否为编辑模式
		 * @returns {Promise} 返回组件实例Promise
		 */
		openSalaryRuleComponent: function(options = {}, isEdit = false) {
			const me = this;
			util.waiting();
			
			return new Promise((resolve, reject) => {
				seajs.use(['app-checkin-modules/es6/salary/index.js'], (salary) => {
					try {
						// 创建Vue组件实例
						const SalaryRuleComponent = salary.default.salaryruleobj;
						const container = document.createElement('div');
						document.body.appendChild(container);
						
						const instance = new Vue({
							render: h => h(SalaryRuleComponent, {
								props: { ...options, isEdit },
								ref: 'salaryRuleComponent'
							})
						}).$mount(container);
						
						const component = instance.$refs.salaryRuleComponent;
						
						// 打开组件并绑定事件
						component.open();
						component.$on('success', data => me.trigger('refresh', 'add', data));
						component.$on('close', () => {
							document.body.removeChild(container);
							instance.$destroy();
						});
						
						resolve(instance);
					} catch (error) {
						reject(error);
						console.error('初始化工资规则组件失败:', error);
					}
				});
			})
			.catch(error => {
				console.error('加载工资规则模块失败:', error);
				util.error($t('加载工资规则模块失败'));
			})
			.finally(() => util.waiting(false));
		},

		/**
		 * 构建组件选项
		 * @param {Object} data 数据对象
		 * @param {Object} describe 描述信息
		 * @returns {Object} 组件选项
		 */
		buildComponentOptions: function(data, describe) {
			const recordType = data?.record_type || '';
			return {
				allData: data,
				describe: describe,
				record_type: recordType,
				isInterconnection: recordType === 'record_external__c'
			};
		},

		/**
		 * 重写新建
		 * @param {Object} obj 对象参数
		 * @returns {Promise|undefined} 返回Promise或undefined
		 */
		add: function(obj) {
			const me = this;
			
			// 如果已有数据，直接打开编辑组件
			if (obj.data) {
				return this.getDescribe()
					.then(describe => {
						const options = this.buildComponentOptions(obj.data, describe);
						return this.openSalaryRuleComponent(options, false);
					});
			}
			
			// 否则先选择类型
			selectType('SalaryRuleObj', {}, function(type) {
				me.openSalaryRuleComponent({
					record_type: type,
					describe: me.opts.describe,
					isInterconnection: type === 'record_external__c'
				}, false);
				
				// 清理选择器
				this.destroy && this.destroy();
			});
		},
		
		/**
		 * 重写编辑
		 * @param {Object} obj 对象参数
		 * @returns {Promise} 返回Promise对象
		 */
		edit: function(obj) {
			if (!obj || !obj.data) {
				util.error($t('缺少编辑数据'));
				return Promise.reject(new Error('缺少编辑数据'));
			}
			
			const options = this.buildComponentOptions(
				obj.data, 
				obj.allData?.describe
			);
			
			return this.openSalaryRuleComponent(options, true);
		}
	});
	
	module.exports = SalaryRuleObj;
});
