define(function (require, exports, module) {

	var util = require('crm-modules/common/util'),
		Model = require('crm-modules/action/field/field').Model;

	module.exports = Model.extend({
		parse: function (res) {
			const _this = this;
			let obj = Model.prototype.parse.apply(this, arguments);
			// 固定搭配 组合销售类型 字段处理
			let BomTypeComp = {};
			obj.layout.forEach((l) => {
				if (l.components) {
					let len = l.components.length - 1;
					while (len >= 0) {
						const c = l.components[len];
						if (c.api_name === 'bom_type') {
							BomTypeComp = {
								...c,
								is_required: true
							}
							l.components.splice(len, 1);
						}
						len -= 1
					}
				}
			})
			obj._BomTypeComp = BomTypeComp;
			// 固定搭配 end
			//非从列表页新建未缓存describe，需要处理一下
			if (!this.get('describe')) {
				this.set('describe', res.objectDescribe)
			}
			var isEdit = this.get('isEdit');
			//单位相关字段单独分组
			var tempUnitLayout = {
				api_name: 'local_multiunit_section',
				column: 1,
				components: ["", {
					api_name: 'multi_unit_data'
				}],
				label: $t("单位"),
				has_required: true
			},
				isSpecCom = {},
				multiFlag = false;

			//找到多单位所在分组并移动到基本信息分组末位，如果移动后有空分组不显示
			_.each(obj.layout, function (l, i) {
				var tempOriComp = [];
				_.each((l.components||[]), function (c, k) {
					if (c.api_name == "is_multiple_unit") {
						multiFlag = true;
						tempUnitLayout.components[0] = c;

					} else {
						tempOriComp.push(c);
					}
				});
				l.components = tempOriComp;
			})
			obj.layout = obj.layout.filter(l => l.components?.length > 0);
			if (multiFlag) {
				obj.layout.push(tempUnitLayout);
			}
			//独立新建产品
			if (!CRM._cache.productOpenSpu) {
				_.each(obj.layout, function (l, i) {
					if (l.api_name == 'md') {
						obj.layout.splice(i, 1)
					}
				})
				return obj;
			}
			//
			if (!isEdit) {
				var specComp = [{
					description: $t('crm.productobj.specifications'),
					api_name: 'is_spec',
					label: '',
					type: 'true_or_false',
				}, {
					description: $t('crm.productobj.specificationsValues'),
					api_name: 'spec_and_value',
					label: '',
					type: '',
				}, {
					description: $t('商品'),
					api_name: 'spu',
					label: '',
					type: '',
				}];
				//找到商品名称所在分组并移动到规格分组，如果移动后有空分组不显示
				_.each(obj.layout, function (l, i) {
					_.find(l.components, function (c, k) {
						if (c.api_name == 'spu_id') {
							specComp.splice(1, 0, c);
							l.components.splice(k, 1);
						}
					});
					if (l.api_name == 'md') {
						obj.layout.splice(i, 1)
					}
					if (l.components && l.components.length == 0) {
						obj.layout.splice(i, 1)
					}
				})
				var specLayout = {
					api_name: 'spec',
					type: '',
					label: $t("规格"),
					group_type: '',
					components: specComp
				};

				obj.layout.unshift(specLayout);
				_.extend(obj.fields, {
					is_spec: {
						api_name: 'is_spec'
					},
					spec_and_value: {
						api_name: 'spec_and_value'
					},
					spu: {
						api_name: 'spu'
					}
				})
			} else {
				//md对象产品包不显示
				_.each(obj.layout, function (l, i) {
					if (l.api_name == 'md') {
						obj.layout.splice(i, 1)
					}

				})
			}

			if (_.some(obj.layout, lay => lay.groupId == void (0))) {
				_this.__compGroupId = 0;
				_.each(obj.layout, l => {
					_.extend(l, {
						groupId: _this.__getGroupId()
					});
				});
			}

			return obj;
		},

		/*
		 * model里组件数据变化会执行change
		 * 触发目标notify观察者
		 */
		change: function (key, value, data) {
			this.subject.notify(key, value, data)

			switch (key) {
				case 'pricing_mode':
					this.changePricingMode(value);
					break;

			}
		},

		create: function (param, btn, callback) {
			var me = this;
			if (me.createAjax) return;
			me.createAjax = util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + param.object_data.object_describe_api_name + '/action/Add',
				data: param,
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						me.submitSuccess(res, param, btn)
					} else {
						me.submitError(res, param, btn);
					}
				},
				complete: function () {
					me.createAjax = null
				}
			}, {
				submitSelector: btn,
				errorAlertModel: 1
			})
		},
		update: function (param, btn, callback) {
			var me = this;
			if (me.updateAjax) return;

			me.updateAjax = util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + me.get('apiname') + '/action/Edit',
				data: param,
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						me.submitSuccess(res, param, btn)
					} else {
						me.submitError(res, param, btn)
					}
				},
				complete: function () {
					me.updateAjax = null;
				}
			}, {
				submitSelector: btn,
				errorAlertModel: 1
			})
		},
		fetch: function (apiname) {
			if (apiname) {
				this.set('apiname', apiname);
			}
			Model.prototype.fetch.apply(this, arguments);
		},
		getMultipleUnitByProductId(id) {
			const CACHE = this.get('PRODUCT_MULTIPLE_UNIT') || {};
			if (CACHE[id]) {
				return Promise.resolve(CACHE[id])
			} else {
				CRM.util.showLoading_tip();
				return this.getMultipleUnit({
					field_name: 'product_id',
					field_values: [id],
					operator: 'EQ'
				}).then((res) => {
					this.set('PRODUCT_MULTIPLE_UNIT', {
						...CACHE,
						[id]: res
					})
					return res;
				}).finally(() => {
					CRM.util.hideLoading_tip();
				})
			}
		},
		getMultipleUnit(filters = []) {
			return new Promise(function (resolve, reject) {
				const sq = {
					limit: 2000,
					offset: 0,
					filters
				}
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/MultiUnitRelatedObj/controller/List',
					data: {
						object_describe_api_name: 'MultiUnitRelatedObj',
						search_template_id: null,
						search_query_info: JSON.stringify(sq),
					},
					success(res) {
						if (res.Result.StatusCode == 0) {
							var data = res.Value.dataList;
							resolve(data);
						} else {
							util.alert(res.Result.FailureMessage);
							reject();
						}
					},
					error() {
						reject();
					}
				}, {
					errorAlertModel: 1
				});
			})
		},

		//value:true-隐藏组件，false-显示组件
		toggleFieldVisibility: function (field, value, initStatus) {
			const obj = {},
				fields = this.get('fields');

			//字段本身是必填字段时，在隐藏显示时需要强制修改is_required属性
			if (fields[field]) {

				fields[field].is_required = value ? false : initStatus;

				const action = value ? 'hide_field' : 'show_field';
				const obj = { [action]: [field] };

				this.trigger('rule', obj);
			}
		},


		//初始化处理周期性相关字段
		handleCycleInfoInit() {
			this.set("cycleFields", ["pricing_frequency", "settlement_frequency", "whole_period_sale", "pricing_cycle", "settlement_cycle", "settlement_mode"])
			this.cacheCycleFieldsInfo();
			const { pricing_mode } = this.get('data');
			if (pricing_mode) {
				this.changePricingMode(pricing_mode);
			}
		},

		cacheCycleFieldsInfo() {
			const fields = this.get('fields') || {},
				cycleFields = this.get("cycleFields");
			const cycleFieldsInfo = cycleFields.reduce((accMap, field) => {
				if (fields[field]) {
					accMap[field] = fields[field].is_required;
				}
				return accMap;
			}, {})
			this.set('cycleFieldsInfo', cycleFieldsInfo);
		},

		// 周期性相关字段显示和隐藏状态
		changePricingMode(value) {
			const isCycle = value == "cycle";
			const fields = this.get('fields')||{};
			const cycleFields = this.get("cycleFields");
			const cycleFieldsInfo = this.get("cycleFieldsInfo")||{};
			/**
			 * 周期性产品展示时，还原is_required值
			 * 隐藏时，设置is_required为false才可隐藏
			 */
			Object.entries(cycleFieldsInfo).forEach(([key, defaultRequired]) => {
				if (fields[key]) {
					const targetRequired = isCycle ? defaultRequired : false;
					if (fields[key].is_required !== targetRequired) {
						fields[key].is_required = targetRequired;
					}
				}
			})

			// 设置元素的可见性
			this.trigger('rule', { [isCycle ? 'show_field' : 'hide_field']: cycleFields });
		},

	});
})
