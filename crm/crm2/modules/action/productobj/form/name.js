/*
 * @Description: 产品名称放开编辑
 * @Author: sunsh
 * @Date: 2021-04-15 20:09:03
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-22 15:41:34
 */
define(function (require, exports, module) {
	var Text = require('crm-modules/action/field/field').components.text;
	return Text.extend({
		events: function () {
			let events = Text.prototype.events;
			return _.extend({}, _.isFunction(events) ? events() : events, {
				'change .j-f-ipt': '_onChangeHandle',
			});
		},

		_onChangeHandle: function (e) {
			this.nameIsFrozen = $.trim(e.target.value) !== '';
		},

		dataEvents: {
			'change.spec_and_value': 'createProductName',
			'change.spu_id': 'clearName'
		},

		createProductName({value = []}) {
			if (this.nameIsFrozen) {
				return;
			}
			let spuName = this.model.getData('spu_id__r');
			let specs = _(value).pluck('spec_value_name');
			let name = spuName + (specs.length ? `[${specs.map(item => _.unescape(item)).join('-')}]` : '');

			this.setValue(name);
		},

		clearName() {
			// this.setValue('');
		},

		render: function () {
			this.fieldAttr.is_readonly = false;
			Text.prototype.render.apply(this, arguments);
		}
	});
});
