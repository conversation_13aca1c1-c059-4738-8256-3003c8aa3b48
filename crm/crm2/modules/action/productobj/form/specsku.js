/**
 * @description 新建有规格产品选择规格
 * <AUTHOR>
 */
define(function(require, exports, module) {
	var util = require('crm-modules/common/util'),
		template = require('./tpl/specsku-html'),
		View=require('../view/view'),
		Base = require('crm-modules/action/field/field').components.Base;

	module.exports = Base.extend({

		initialize: function() {
			Base.prototype.initialize.apply(this, arguments);
			this.clearCacheData();
			this.initWithData();
		},

		clearCacheData:function(){
			var me=this;
			me.specOptions = []; //当前商品id所有的规格属性
			me.optionsUsed = []; //当前商品id所有的规格属性
			me.specUsed = []; //商品下已使用的产品组合
			me.specDisabeld = []; //不可用的规格组合
			me.curSpec = []; //当前已选规格组合
			me.Largest = 1; //
			me.spuAbled=true;
		},

		initWithData:async function(){
			const me = this;
			const data = this.get('data');
			const {spu_id,	spec_and_value=[]} = data||{};
			if(spu_id){
				await this.spuChange({
					value:spu_id
				})
			}
			if(spec_and_value.length){
				spec_and_value.forEach(function(data) {
					// 找到对应的DOM元素
					var $item = me.$('.cpopt-item[data-id="' + data.spec_value_id+ '"]');
					if ($item.length) {
						var event = {
							target: $item[0] // 模拟点击事件的target
						};
						me.specChange(event);
					}
				});
			}
		},

		dataEvents: {
			'change.is_spec':'switchSpecHandle',
			'change.spu_id': 'spuChange'
		},

		events: {
			'click .j-cpopt-item': 'specChange',
		},

		switchSpecHandle:function(data){
			this.hideError();
			if(data.value){
				this.show();
			}else{
				this.hide();
			}
		},

		spuChange: async function(value) {
			this.hideError();
			const spuId = value.value;

			this.clearCacheData();

			const data = await this.getSpecData(spuId);
			const options = data.optionsData;
			this.optionsUsed = data.opSelected;

			if (!this.spuAbled) {
				const info = `<div class='error-tip'>${$t("crm.productobj.merchandiseNotAvailable")}</div>`;
				$('*[data-apiname="spu_id"]').parent('.f-item').addClass('crm-error-wrap');
				this.$el.html(info);
			} else {
				const skuData = await this.getSkuData(spuId);
				$('*[data-apiname="spu_id"]').parent('.f-item').removeClass('crm-error-wrap');
				this.renderOptions(options);
				this.mapDictoryHandle(skuData);
			}

		},

		specChange: function(e) {
			this.hideError();
			var me = this,
				$item = $(e.target),
				$parent = $item.parents('.options-item-wrapper'),
				pIdx = $parent.index(),
				iIdx = $item.attr('data-index'),
				value = $item.attr('data-flag');

			// 不可点击规格
			if ($item.hasClass('disabled')||$item.hasClass('unactive')) {
				return false
			}

			// 修改dom, 支持反选
			var uncheck = false;
			if ($item.hasClass('active')) {
				uncheck = true;
				$item.removeClass('active');
			} else {
				this.$('.options-item-wrapper:nth-child(' + (pIdx * 1 + 1) + ')').find('.cpopt-item').removeClass('active');
				$item.addClass('active');
			}

			// 修改当前已选规格数组
			var specItem = this.specOptions[pIdx];
			var item = {
				"spec_id": specItem.value,
				"order_field": pIdx,
				"spec_value_id": specItem.options[iIdx].value,
				"spec_value_name": specItem.options[iIdx].name
			}
			var existFlag = false;
			_.find(me.curSpec, function(cs, idx) {
				if (cs.order_field == pIdx) {
					existFlag = true;
					uncheck ? me.curSpec.splice(idx, 1) : me.curSpec.splice(idx, 1, item);
					return
				}
			})
			if (!existFlag) {
				me.curSpec.push(item);
			}
			// 排序
			me.curSpec.sort(function(s1,s2){
				return s1.order_field*1-s2.order_field*1
			})
			// 重置不可选
			this.specDisabeHandle(me.curSpec);
			this.setData([].concat(me.curSpec), me.apiname);
		},

		renderOptions: function(data) {
			var domFragment = template({
				data: data
			})
			this.$el.parent().parent('.f-g-item').addClass('f-g-item-specs');
			this.$el.html(domFragment);
		},

		//已有规格属性的全排列组合
		mapDictoryHandle: function(data) {
			var me = this,
				temp = [];
			_.each(data, function(d) {
				var cur = me.cartesianHandle(d);
				_.each(cur, function(c) {
					var existFlag = false;
					_.find(temp, function(t) {
						if (c.flag == t.flag) {
							t.count++
								existFlag = true;
						}
					})
					if (!existFlag) {
						temp.push(_.extend(c, {
							count: 1
						}))
					}
				})
			})
			this.specUsed = temp;
			this.initSpecDisabeld(temp);
		},

		//初始化时不可用的规格值
		initSpecDisabeld: function(arr) {
			var me = this,
				specDis = [];
			_.each(arr, function(a) {
				if (a.comp.length == 1) {
					var maxLen = 1,
						obj = {},
						idx = a.comp[0].index;
					_.each(me.specOptions, function(o, i) {

						if (i !== idx) {
							maxLen = maxLen * o.options.length;
						}

					})
					if (maxLen == a.count) {
						specDis.push(a.comp[0]);
						obj[a.flag] = a.comp;
						me.specDisabeld = _.extend(me.specDisabeld, obj)
					}
				}
			})

			this.specDisabeld = _.extend(this.specDisabeld, {
				"init": specDis
			})

			this.setBtnDisabled(specDis, true);
		},

		///点击button重新计算不可用的规格值
		specDisabeHandle: function(arr) {
			var me = this,
				len = arr.length * 1 + 1,
				flag = '';

			_.each(arr, function(a) {
				flag += a.spec_value_name
			})
			if (flag in me.specDisabeld) {
				me.setBtnDisabled(me.specDisabeld[flag]);
			} else {
				var disabledArr = [],
					briefArr = [];


				var arrSvn = [];
				_.each(arr, function(a) {
					arrSvn.push(a.spec_value_name)
				})
				var totalComp = me.cartesianHandle(arrSvn);

				_.each(totalComp, function(t) {
					disabledArr = disabledArr.concat(me.specSearchHandle(t));
				})

				_.extend(this.specDisabeld, {
					flag: disabledArr
				})
				me.setBtnDisabled(disabledArr);
			}
		},

		specSearchHandle: function(arg) {
			var me = this,
				key = arg.flag,
				arr = arg.comp,
				len = arr.length * 1 + 1,
				targetObj = {};
			if (key in me.specDisabeld) {
				targetObj = me.specDisabeld[key];
				return me.specDisabeld[key];
			} else {
				var tempArr = [];
				_.each(me.specUsed, function(su) {
					// 使用字符串包含检查替代正则表达式
					var isMatch = su.flag.indexOf(key) !== -1;
					
					// 如果找到匹配，并且组合长度符合要求
					if (isMatch && su.comp.length == len) {
						var maxCount = me.Largest;
						_.each(su.comp, function(sc) {
							maxCount = maxCount / me.specOptions[sc.index].options.length
						})
						//当前组合可被使用次数
						if (su.count == maxCount) {
							var arrSvn = [];
							_.each(arr, function(a) {
								arrSvn.push(a.value)
							})
							_.each(su.comp, function(sc) {
								if (arrSvn.indexOf(sc.value) < 0) {
									tempArr.push(sc)
								}
							})

							targetObj[key] = tempArr
							_.extend(me.specDisabeld, targetObj);
						}
					}
				})
				return tempArr;
			}
		},


		/*
		 * 更新不可用规格值dom
		 * flag:true-初始就不可用,false-点击操作关联不可用
		 */
		setBtnDisabled: function(arr, flag) {
			var me = this;
			me.$('.cpopt-item').removeClass('disabled');
			_.each(arr, function(a) {
				var item = me.$(`.cpopt-item[data-flag="${a.value}"]`);
				item.addClass(flag ? 'init-disabled' : 'disabled');
			})
		},

		cartesianHandle: function(arr) {
			var compArr = [],
				len = arr.length;

			var cpHandle = function(tempArr) {
				var newTemp = [];
				_.each(tempArr, function(c) {
					for (var j = c.temp + 1; j < len; j++) {
						var value = c.value + arr[j];

						var item = {
							value: value,
							comp: c.comp.concat({
								value: arr[j],
								index: j
							}),
							temp: j
						};
						var cItem = {
							flag: value,
							comp: c.comp.concat({
								value: arr[j],
								index: j
							}),
						}
						compArr.push(cItem)
						newTemp.push(item);
					}
				})

				return newTemp;
			};
			var reCa = function(key) {

				if (key <= 0) {
					var temp = [];
					_.each(arr, function(a, idx) {
						temp.push({
							value: a,
							comp: [{
								value: a,
								index: idx
							}],
							temp: idx
						})
						var item = {
							flag: a,
							comp: [{
								value: a,
								index: idx
							}]
						}
						compArr.push(item);
					})
					return temp;
				} else {
					return cpHandle(reCa(key - 1));
				}
			};
			reCa(arr.length);
			return compArr;
		},

		getSpecData: function(id) {
			var me = this,
				dtd = $.Deferred();
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/spu_sku_object/service/search_spec_and_values_by_spu',
				data: {
					"spu_id": id, //  SPU的主键
					"is_include_all": true, // 是否包含所有规格值的标记。如果是true，返回规格下所有规格值，用status区分是否是使用的。如果传入false,则值给返回所有状态是0的规格值。
					"price_book_id": "", //价目表的ID
					"sku_ids_to_filter": [], //用于基于用户已经选择过的sku列表过滤掉规格&规格值
					"is_add_pro_to_price": false
				},
				success: function(res) {
					if (res.Result.StatusCode == 0) {
						var data = res.Value.dataList,
							opSelected = [],
							largest = 1;

						var optionsData = _.map(data, function(d) {

							var options = [],
								selectOp = [];

							_.each(d.specValueList, function(s) {
								let item=_.extend({
									"name": _.escape(s.specValueName),
									"value": s.specValueId,}
									,s)
								//未使用&已禁用的过滤
								if(!(s.status=='0'&&!s.active)){
									options.push(item);
								}
								if (s.status == '1') {
									selectOp.push(item);
								}
							});
							if (selectOp.length >= 1) {
								opSelected.push({
									"name": d.specName,
									"value": d.specId,
									"options": selectOp
								})
							}
							largest = largest * options.length;
							return {
								"name": d.specName,
								"value": d.specId,
								"options": options
							}

						});
						var prasedData = {
							opSelected: opSelected,
							optionsData: optionsData
						};
						me.specOptions = optionsData;

						me.Largest = largest;
						//me.spuAbled=spuabled;

						dtd.resolve(prasedData);
					} else {
						util.alert(res.Result.FailureMessage);
					}
				}
			}, {
				errorAlertModel: 1
			});
			return dtd;
		},
		//编辑——获取已有sku数据
		getSkuData: function(id) {
			var me = this,
				dtd = $.Deferred();

			var sq = {
				"limit": 10000,
				"offset": 0,
				"filters": []
			};

			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/ProductObj/controller/RelatedList',
				data: {
					associate_object_data_id: id,
					associate_object_describe_api_name: "SPUObj",
					associated_object_describe_api_name: "ProductObj",
					associated_object_field_related_list_name: "spu_sku_list",
					include_associated: true,
					search_query_info: JSON.stringify(sq),
					object_data: {
						is_spec: true,
						under_spu_skus_edit: true
					}
				},
				success: function(res) {
					if (res.Result.StatusCode == 0) {

						//格式化table数据
						var skuList = res.Value.dataList;
						var specBrief = [];
						if(skuList.length==me.Largest||skuList.length>=400){
							me.spuAbled=false;
						}

						_.each(skuList, function(d) {
							// if(d._id=='61164feab771f0000138b089'){
							// 	debugger
							// }
							var item = [],
								spec_and_value = d.spec_and_value;

							_.each(spec_and_value, function(spec) {

								//item.push(spec.spec_value_id);
								item.push(spec.spec_value_name);

							});
							specBrief.push(item);
						})
						dtd.resolve(specBrief);
					} else {
						util.alert(res.Result.FailureMessage);
					}
				}
			}, {
				errorAlertModel: 1
			});
			return dtd;
		},
		getValue: function() {
			var me = this,
				length=me.specOptions.length,
				isSpec=me.model.getData('is_spec'),
				data =[];

			_.each(me.curSpec, function(c) {
				data.push( {
					"spec_id": c.spec_id,
					"spec_value_id": c.spec_value_id,
					"order_field": c.order_field + ""
				})
			})
			if (isSpec && data.length < 1) {
				//util.alert($t("产品至少有一个规格值"));
				let errorWrapper = this.$el.find('.error-wrapper');
				if (errorWrapper.length === 0) {
					this.$el.append('<div class="error-wrapper" style="margin-left:80px"></div>');
					errorWrapper = this.$el.find('.error-wrapper');
				}
				this.showError(errorWrapper,$t("产品至少有一个规格值"))
				return [];
			}else{
				return data
			}
		},
		show:function(){
			this.$el.show();
		},
		hide:function(){
			this.$el.hide();
		},
	});
});
