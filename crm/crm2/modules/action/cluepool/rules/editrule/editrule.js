/**
 * <AUTHOR>
 * 线索池分配器规则弹窗
 */

define(function (require, exports, module) {
	var util = CRM.util,
		Dialog = require('crm-widget/dialog/dialog'),
		Model = require('../model/model').Rule,
		tpl = require('./template/tpl-html'),
		FormBox = require('crm-modules/components/formbox/formbox'),
		Radio = FormBox.Radio,
		Selector = require('crm-widget/selector/selector'),
		SelectorV2 = require('crm-widget/selectorV2/selectorV2'),
		tipconTpl = require('./template/tip-html'),
		Filter = require('crm-modules/components/fieldfilter/fieldfilter'),
		FilterGroup = require('crm-modules/common/filtergroup/filtergroup'),
		helper = require('crm-modules/components/helper/helper'),
		helperComp = require('crm-modules/setting/ownership/ownership').helper,
		FilterMc = require('../model/filtermodel'),
		WeightMode = require('./weight/weight');

	var EditRule = Dialog.extend({
		attrs: {
			width: 900,
			height: 600,
			className: 'crm-g-editrule crm-distribution-editrule',
			title: $t("规则设置"),
			showBtns: true,
			showScroll: false
		},

		events: {
			'mouseenter .crm-g-remind-ico': 'enterHandle',
			'mouseleave .crm-g-remind-ico': 'leaveHandle',
			'click .b-g-btn': 'saveHandle',
			'click .b-g-btn-cancel': 'cancelHandle'
		},

		enterHandle: function (e) {
			var target = $(e.target);
			util.remindTip(target, tipconTpl({
				type: target.data('type')
			}));
			$('.crm-g-remind-info').css('zIndex', this.get('zIndex') + 1);
		},

		leaveHandle: function (e) {
			var target = $(e.target);
			util.remindTip(target);
		},

		initialize: function () {
			this.attrs = $.extend(true, {}, EditRule.prototype.attrs, this.attrs);
			return EditRule.superclass.initialize.apply(this, arguments);
		},

		show: function (o) {
				// // 每次调用时都重新引入原始模块，确保没有扩展
				// if (!o.needDepartmentChildren) {
				// 	// 如果没有部门需要添加子部门的灰度，使用原始helper
				// 	helper = require('crm-modules/components/helper/helper');
				// 	helperComp = require('crm-modules/setting/ownership/ownership').helper;
				// } else 
				if (o.needDepartmentChildren && !helper.isDepartmentExtended) {
					// 如果有部门需要添加子部门的灰度，扩展helper
					let copyGetCompare = helper.getCompare;
					helper = $.extend(true, {}, helper, {
						isDepartmentExtended: true,
						getCompare(type){
							let menus = copyGetCompare.call(this, type);
							// 如果是department类型，添加13(属于)和14(不属于)比较符
							if (type === 'department') {
								let additionalCompares = [13, 14].map(value => 
									_.find(helper.compare, obj => obj.value === value)
								);
								menus = menus.concat(_.compact(additionalCompares));
							}
							return menus;
						},
						changeDepartmentConfig(child,compare) {
							if(compare.value == 13 || compare.value == 14){
							  child.groupIncludeChildrenStatus = 1;
							  child.needDepartmentChildren = true;
							}
							return child;
						  }
					});
					
					let originalGetCompare = helperComp.getCompare;
					helperComp = $.extend(true, {}, helperComp, {
						getCompare(type){
							let menus = originalGetCompare.call(this, type);
							// 如果是department类型，添加13(属于)和14(不属于)比较符
							if (type === 'department') {
								let additionalCompares = [13, 14].map(value => 
									_.find(helper.compare, obj => obj.value === value)
								);
								menus = menus.concat(_.compact(additionalCompares));
							}
							return menus;
						},
						changeDepartmentConfig(child,compare) {
							if(compare.value == 13 || compare.value == 14){
							  child.groupIncludeChildrenStatus = 1;
							  child.needDepartmentChildren = true;
							}
							return child;
						  }
					});
				}
			var result = EditRule.superclass.show.call(this);
			// var obj = this.parseObj(o);
			var obj = o;
			this.parseNewFilter(o);
			this.cache = obj; //原始数据

			this.setModel(obj);
			var temp = this.model.toJSON();
			// _.each(temp.RuleFilterList, function (item) {
			// 	item.FieldValue = typeof item.FieldValue == 'string' ? item.FieldValue.split('|') : item.FieldValue
			// });
			this.setContent(tpl(temp));
			this._initFilter();
			this.initRadio();
			this.initOwnerRadio();
			this.initPesonnelFilter();
			return result;
		},

		/*
    	* 	处理多选，把字符串转为数组
    	* */
		// parseObj: function (obj) {
		// 	var ruleList = obj.get("RuleFilterList");
		// 	if (ruleList.length) {
		// 		ruleList = _.map(ruleList, function (item) {
		// 			return _.map(item.filters, (f) => {
		// 				let _fv = f.field_values + '';
		// 				if (_fv.includes('|')) {
		// 					f.field_values = _fv.split('|');
		// 				}
		// 				return f;
		// 			})
		// 			// let strFieldValue = item.FieldValue + '';
		// 			// if (strFieldValue.includes('|')) {
		// 			// 	item.FieldValue = strFieldValue.split('|');
		// 			// }
		// 			// return item
		// 		})
		// 	}
		// 	obj.set("RuleFilterList", ruleList);
		// 	return obj
		// },

		setModel: function (obj) {

			if (obj instanceof Backbone.Model) {
				this.model = obj;
				return;
			}
			this.model = new Model(obj);
		},

		setModelProp: function (key, value) {
			this.model && this.model.set(key, value);
		},

		getModelProp: function (key) {
			return this.model.get(key);
		},

		//新的过滤器更换迁移自定义对象获取字段接口之后 根据原来的数据格式需要转换才能回显
		parseNewFilter: function (obj) {
			var rules = obj.get("RuleFilterList");
			// var temp = [];
			// _.each(rules, function (item) {
			// 	var ind = [];
			// 	var operate = _.findWhere(helper.getCompareConfig(), {value: item.Compare}).value1
			// 	ind.push(item.FieldName, operate, item.FieldValue, item.FieldType)
			// 	temp.push(ind)
			// })

			// var result = []
			// _.each(temp, function (value, index) {
			// 	var fieldValue = value[2];
			// 	result.push({
			// 		fieldName: value[0],
			// 		fieldType: value[3],
			// 		fieldValue: _.isArray(fieldValue) ? fieldValue : [fieldValue],
			// 		operate: value[1],
			// 		ruleOrder: index + 1,
			// 		RuleType: 1
			// 	});
			// });
			// obj.set('RecycleDefault', result);
			// return result
			// _.each(rules, (rule) => {
			// 	_.each(rule.filters, (f) => {
			// 		f.field_values = _.isArray(f.field_values) ? f.field_values : [f.field_values];
			// 	})
			// })
			obj.set('RecycleDefault', rules);
			return rules
		},

		/*初始化过滤器*/
		_initFilter: function () {
			var me = this,
				data = this.model.get('RuleFilterList');//每个实例名字会不同
			var defaultValue = this.model.get('RecycleDefault');
			if (this.model.needDepartmentChildren) {
				defaultValue.forEach(whereItem => {
					if (whereItem.filters && Array.isArray(whereItem.filters)) {
						whereItem.filters.forEach(filter => {
							if (
								filter.type === 'department' &&
								(filter.operator === 'IN' || filter.operator === 'NIN') &&
								filter.is_cascade
							) {
								filter.field_values = filter.field_values.map(value => value + '_y');
							}
						});
					}
				});
			}
			if (!this.filter) {
				this.filter = new FilterGroup({
					Filter: Filter,
					apiname: 'LeadsObj',
					$wrapper: this.$('.rules-wrap').eq(0),
					width: 750,
					title: '<span class="del-span j-delete-filter"></span><span style="color:#919eab">' + $t("且（AND）") + '</span>',
					AND_MAX: 10,
					OR_MAX: 10,
					filterType: ["group", 'url', "time", "image", "file_attachment", "percentile", 'signature', "formula", "count", "quote", 'employee_many', 'department_many', 'html_rich_text', 'object_reference_many'],
					filterApiname: ["leads_status", "biz_status", "life_status", "owner_change_time", "resale_count", "transform_time", "assigner_id", "assigned_time", "expire_time", "owner_department", "remaining_time", "out_tenant_id", "last_modified_by", "last_modified_time", "leads_pool_id", "remind_days", 'collected_to', "extend_days", "out_owner"],
					defaultValue: defaultValue,
					formatFields: function (fields) {
						// TODO server 暂不支持计算字段返回 日期、时间、日期时间的类型
						// TODO server 要走isIndex 计算公司中包含特定时间的计算
						let _fields = {};
						_.each(fields || [], function (item) {
							var flag = (item.type == 'formula' && _.contains(['date_time', 'date', 'time'], item.return_type)) || (item.type == 'formula' && !item.is_index);
							if (_.contains(['select_one'], item.type)) {
								var _temp = item.type;
								item.type = 'select_many';
								item.old_type = _temp;
							}
							if (item.api_name == 'out_owner') {
								item.type = 'exemployee';
							}
							!flag && (_fields[item.api_name] = item);
						});
						return _fields;
					},
					helper:helper,

					filterCusOptions: {
						data: data.length ? data : [],
						fieldlist: me.model.get('fieldlist'),
						disabled: me.get('disabled'),
					},
				});
				this.filter.on('change', () => {
					this.hideError(this.$('.filter-box .crm-filter-group'));
				})

				/* this.filter.on('add', function () {
					me.resizedialog();
				}); */
			}

			this.filter.render();
		},

		initPesonnelFilter: function(){
			if (this.personnelFilter || !this.$('.multi-bar-wrapper .filter-group-item').length) return;
			let defaultValue = this.model.get('RuleFilterGroupList')
			this.personnelFilter = new FilterGroup({
				$wrapper: this.$('.multi-bar-wrapper .filter-group-item'),
				title: $t("且(AND)"),
				AND_MAX: 10,
				width: 750,
				addBtnName: $t('新增或关系'),
				apiname: 'PersonnelObj',
				selectone_multiple: true,	//单选变多选
				defaultValue: defaultValue,
				filterApiname: ['is_active', 'is_pause_login', 'out_tenant_id'].concat(CRM.get('isOpenPRM') ? [] : ['partner_id', 'out_owner']),
				helper: helperComp,
				parseCompare(compares, field) {
					if (CRM.get('isOpenPRM') && field.api_name === 'out_owner') {
						compares = compares.filter((item) => {
							return ['IS', 'ISN'].indexOf(item.value1) != -1
						})
					}
					return compares;
				}
			});
			this.personnelFilter.on('change', () => {
				this.hideError(this.$('.multi-bar-wrapper .filter-group-item'));
				this.hideError(this.$('.multi-bar-wrapper .select-bar-item'));
			})
		},

		initRadio: function () {
			var me = this;
			me.IsAllSalesClue = this.model.get('IsAllSalesClue');
			this._rangeRadio = new Radio({
				el: this.$('.radio-range-box'),
				defaultValue: me.IsAllSalesClue,
				displayStyle: 'inline-block',
				options: [{
					text: $t("全部"),
					value: true
				}, {
					text: $t("按筛选条件"),
					value: false
				}]
			});

			this._rangeRadio.on('change', function (value) {
				if (value == true) {
					console.log($t("全部"))
					me.$('.filter-box').addClass('hide');
					me.IsAllSalesClue = true;
					// me.model.set('IsAllSalesClue', true);
					me.filter.resetRender();
				} else if (value == false) {
					console.log($t("按筛选条件"))
					me.$('.filter-box').removeClass('hide');
					me.IsAllSalesClue = false;
					// me.model.set('IsAllSalesClue', false);
				}
			});

			this._rangeRadio.setValue(me.IsAllSalesClue == false ? false : true);
		},

		initOwnerRadio: function () {
			var me = this;
			var poolType = this.model.get('poolType');
			this.AllocatePattern = this.model.get('AllocatePattern');
			let options = [{
				text: $t("仅分配给"),
				value: 1
			}, {
				text: '<span class="text-span " title="' + $t("循环分配") + '">' + $t("循环分配") + '</span><i class="crm-g-remind-ico" data-type="multi-assign"></i>',
				value: 2
			}];
			if (CRM.util.isGrayScale('CRM_POOLWEIGHTALLOCATE') && poolType != 1) {
				options.push({
					text: `<span>${$t('权重分配')}</span><i class="crm-g-remind-ico" data-type="weight-assign"></i>`,
					value: 3,
				})
			}
			this._ownerRadio = new Radio({
				el: this.$('.radio-owner-box'),
				defaultValue: me.AllocatePattern,
				displayStyle: 'block',
				options: options
			});

			this._ownerRadio.on('change', function (value) {
				// var curEl = me._ownerRadio.getCurEl();
				me.AllocatePattern = +value;
				me.$('.assign-owner-panel').hide();
				me.$(`.assign-owner-panel[data-type=${value}]`).show();
				me.allocateModeChangeHandle(me.AllocatePattern, []);
				// if (value == 1) {
				// 	me._initSingleBar([]);
				// 	me.AllocatePattern = 1;
				// 	// me.model.set('AllocatePattern', 1);
				// } else if (value == 2) {
				// 	me._initmultiBar([]);
				// 	me.initPesonnelFilter();
				// 	me.AllocatePattern = 2;
				// 	// me.model.set('AllocatePattern', 2);
				// }
			});

			this._ownerRadio.setValue(me.AllocatePattern || 1, false);
			this.allocateModeChangeHandle(this.AllocatePattern);
			// this.AllocatePattern == 1 ? me._initSingleBar() : me._initmultiBar();
		},

		// 分配方式变更处理
		allocateModeChangeHandle(type, data) {
			const me = this;
			switch(type) {
				case 2:
					if (me.initMultiFlag) return;
					me._initmultiBar(data);
					me.initPesonnelFilter();
					me.initMultiFlag = true;
					break;
				case 3:
					if (me.initWeightFlag) return;
					me._initWeightBar(data);
					me.initWeightFlag = true;
					break;
				default:
					if (me.initSingleFlag) return;
					me._initSingleBar(data)
					me.initSingleFlag = true;
					break;
			}
		},

		_initSingleBar: function (member) {
			const me = this;
			var isOpenPRM = this.model.get('isOpenPRM');
			var poolType = this.model.get('poolType');
			member = member || this.model.get('MemberList');
			// this.$('.multi-bar-wrapper').hide();
			// if (this.$('.single-bar-wrapper').length) {
			// 	this.$('.single-bar-wrapper').show();
			// 	return;
			// }
			// this._ownerRadio.getCurEl().after('<div class="single-bar-wrapper"></div>');
			var $wrapper = this.$('.single-bar-wrapper');
			this.model.get('isOpenPRM') && $wrapper.attr('data-pooltype', this.model.get('poolType'));

			if (poolType != 1) {
				!this.$('.single-bar-in').length && $wrapper.append('<div class="select-bar-item"><div class="select-bar-label">'+ $t("内部") +'</div><div class="select-bar single-bar-in"></div></div>');
				this._getEmployeeDataAsync(function(memberData) {
					me._initMemberSelect(member, memberData);
				})
			}

			if (isOpenPRM) {
				!this.$('.single-bar-ex').length && $wrapper.append('<div class="select-bar-item"><div class="select-bar-label">'+ $t("伙伴") +'</div><div class="select-bar single-bar-ex"></div></div>');
				this._initExMemberSelect(member, 'single');
			}
		},

		_initmultiBar: function (member) {
			const me = this;
			var isOpenPRM = this.model.get('isOpenPRM');
			var poolType = this.model.get('poolType');
			member = member || this.model.get('MemberList');
			// this.$('.single-bar-wrapper').hide();
			// if (this.$('.multi-bar-wrapper').length) {
			// 	this.$('.multi-bar-wrapper').show();
			// 	return;
			// }
			// this._ownerRadio.getCurEl().after('<div class="multi-bar-wrapper"></div>');
			var $wrapper = this.$('.multi-bar-wrapper');
			this.model.get('isOpenPRM') && $wrapper.attr('data-pooltype', this.model.get('poolType'));

			// 线索池类型不为下游的情况
			if (poolType != 1) {
				!this.$('.multi-bar-in').length && $wrapper.append(`
				<div class="allocate-tips">（${$t('crm.allocate.tips')}）</div>
				<div class = "select-and-filter-group">
				<div class="select-bar-item">
					<div class="select-bar-label">${$t("按内部员工")}</div>
					<div class="select-bar multi-bar-in"></div>
				</div>
				<div class="group-row-line">
				<p class="group-line"></p>
				<span class="group-line-text">${$t('或（OR）')}</span>
				</div>
				<div class="filter-bar-item">
					<div class="select-bar-label">${$t("按内部员工指定条件")}</div>
					<div class="filter-group-item"></div>
				</div>
				</div>
				`);
				// this._initMemberSelectM(member);
				this._getEmployeeDataAsync(function(memberData) {
					me._initMemberSelectM(member, memberData);
				})
			}

			if (isOpenPRM) {
				!this.$('.multi-bar-ex').length && $wrapper.append('<div class="select-bar-item"><div class="select-bar-label">'+ $t("按伙伴对接企业") +'</div><div class="select-bar multi-bar-ex"></div></div>');
				this._initExMemberSelect(member, 'multi');
			}
		},

		_initWeightBar(member) {
			const me = this;
			member = member || this.model.get('MemberList');
			this._getEmployeeDataAsync(function(memberData) {
				me.weightView = new WeightMode({
					el: me.$('.weight-bar-wrapper'),
					model: me.model,
					member: member,
					memberData: memberData,
					zIndex: me.get('zIndex') * 1 + 10,
				})
			})
		},

		_initMemberSelect: function(member, memberData){
			var me = this;
			// var memberData = me._getEmployeeData();

			me._singlebar = me._initselectbar({
				el: me.$('.single-bar-in'),
				single: true,
				tabs: [{
					id: 'member',
					title: $t("同事"),
					type: 'sort',
					data: memberData
				}],
				defaultValue: (function () {
					var data = { member: []};
					_.each(member, function (item) {
						if (item.MemberType == 1) {
							data.member.push(item.MemberID)
						}
					});
					return data;
				}())
			});
			me._singlebar.on('change', function (value) {
				if (value && value.member.length){
					me._exSinglebar && me._exSinglebar.clearAll();
				}
			});
		},

		_initMemberSelectM: function(member, memberData) {
			var me = this;
			// var memberData = me._getEmployeeData();

			me._multibar = me._initselectbar({
				el: me.$('.multi-bar-in'),
				tabs: [{
					id: 'member',
					title: $t("同事"),
					type: 'sort',
					data: memberData
				}, {
					id: 'group',
					title: $t("crm.部门"),
					type: 'list',
					data: me.model.get('memberlist')
				}, {
					id: 'usergroup',
					title: $t("crm.用户组"),
					type: 'list',
					data: me.model.get('grouplist')
				}, {
					id: 'role',
					title: $t("角色"),
					type: 'list',
					data: me.model.get('rolelist')
				}],
				defaultValue: (function () {
					var data = {member: [], group: [], usergroup: [], role: []};
					_.each(member, function (item) {
						if (item.MemberType == 1) {
							data.member.push(item.MemberID)
						} else if (item.MemberType == 2) {
							data.group.push(item.MemberID)
						} else if (item.MemberType == 5) {
							data.usergroup.push(item.MemberID)
						} else if (item.MemberType == 6) {
							data.role.push(item.MemberID)
						}
					});
					return data;
				}())
			});
		},

		_initExMemberSelect(member, type){
			var me = this;
			var poolType = me.model.get('poolType');
			var _trees = [];
			poolType == 0 && (_trees= me._getExEmployeeData());
			var config = {
				name: {
					single: '_exSinglebar',
					multi: '_exMultibar'
				},
				options: {
					single: {
						el: me.$('.single-bar-ex'),
						single: true,
					},
					multi: {
						el: me.$('.multi-bar-ex')
					}
				},
				tabs: {
					0: [{
						id: 'outerTenantIds', 
						title: $t('伙伴企业'),
						tabIcon: 'partner',
						icsOptions: {
							itemList: me.model.get('exEnterpriselist') || [],
						},
					}, {
						id: 'outerUids',
						title: $t('伙伴员工'),
						icsOptions: {
							itemList: this.model.get('exEmployeelist') || [],
						},
					}],
					1: [{
						id: 'outerUids',
						title: $t('伙伴员工'),
						icsOptions: {
							itemList: this.model.get('exEmployeelist') || [],
						},
					}],
				}
			}
			me[config.name[type]] = CRM.util.initICSelctor(config.options[type].el, {
                icselProps: {
					propsLine: { label: $t('添加成员') },
					propsInput: {
						tabs: config.tabs[poolType],
						single: config.options[type].single,
					},
					selected: (function () {
						var data = { outerUids: [], outerTenantIds: []};
						_.each(member, function (item) {
							if (item.MemberType == 3) {
								data.outerTenantIds.push(item.MemberID);
							} else if (item.MemberType == 4) {
								data.outerUids.push(item.MemberID);
							}
						});
						return data;
					}()),
				},
                onChange(value) {
					let items = this.$refs.icsel.getSelectedItems();
					items.outerTenantIds.length && CRM.util.setExContactsCache(items.outerTenantIds, 'en');
					items.outerUids.length && CRM.util.setExContactsCache(items.outerUids, 'em');
					if (value && (value.outerUids.length || value.outerTenantIds.length) && type == 'single') {
						me._singlebar && me._singlebar.clearAll();
					}
                },
            })
		},

		__initExMemberSelect: function(member, type){
			var me = this;
			var poolType = me.model.get('poolType');
			var _trees = [];
			poolType == 0 && (_trees= me._getExEmployeeData());
			var config = {
				name: {
					single: '_exSinglebar',
					multi: '_exMultibar'
				},
				options: {
					single: {
						el: me.$('.single-bar-ex'),
						single: true,
					},
					multi: {
						el: me.$('.multi-bar-ex')
					}
				},
				tabs: {
					0: [{
						id: 'exEnterprise',
						type: 'list',
						title: $t('伙伴企业'),
						tabIcon: 'partner',
						data: me.model.get('exEnterpriselist'),
					}, {
						id: 'exEmployee',
						type: 'tree',
						title: $t('伙伴员工'),
						cascade: type == 'multi',
						onlyLeafNode: true,
						onlyLeafNodeClick: type == 'single',
						tabIcon: 'connector',
						data: {
							id: '99999',
							name: $t('SignRecordObj.Config.All'),
							children: _trees
						}
					}],
					1: [{
						id: 'exEmployee',
						type: 'list',
						title: $t('伙伴员工'),
						tabIcon: 'connector',
						data: me.model.get('exEmployeelist')
					}]
				}
			}

			me[config.name[type]] = me._initselectbar(_.extend({
				tabs: config.tabs[poolType],
				defaultValue:(function () {
					var data = { exEmployee: [], exEnterprise: []};
					_.each(member, function (item) {
						if (item.MemberType == 3) {
							data.exEnterprise.push(item.MemberID);
						} else if (item.MemberType == 4) {
							data.exEmployee.push(item.MemberID);
						}
					});
					return data;
				}())
			}, config.options[type]));

			me._exSinglebar && me._exSinglebar.on('change', function (value) {
				if (value) {
					me._singlebar && me._singlebar.clearAll();
				}
			});
		},

		_initselectbar: function (args) {
			var me = this,
				selectbar;

			let sbarEl = args.el || me.el;
			selectbar = new Selector({
				$wrap: me.$(sbarEl),
				parentNode: args.pel,
				zIndex: me.get('zIndex') * 1 + 10,
				tabs: args.tabs || [{
					id: 'member',
					title: $t("同事"),
					type: 'sort',
					searchKeys: ['name', 'nameSpell'],
					data: FS.contacts.sortEmployeesByLetter(util.getAllEmployees())
				}],
				single: args.single || false,
				label: args.label || $t("选择分配范围"),
				defaultSelectedItems: args.defaultValue || []
			});

			selectbar.on('addItem', function () {
				me.$(args.pel || args.el || me.el).closest('.crm-error-wrap').find('.fm-error').remove();
			});

			selectbar.on('change', function () {
				args.changeFn && args.changeFn();
			});

			return selectbar;
		},

		_getEmployeeData: function(){
			var empList = this.model.get('employeelist');
			var memberData = [].concat(empList);
			// 读取部门下的成员
			_.each(this.model.get('memberlist'), function (item) {
				_.each(FS.contacts.getEmployeesByCircleId(item.id) || [], function (eItem) {
					memberData.push(eItem);
				});
			});

			// memberData = _.uniq(memberData, function (item) {
			// 	return item.id * 1
			// });
			return memberData; //FS.contacts.sortEmployeesByLetter(memberData);
		},

		_getEmployeeDataAsync(callback) {
			let memberData = this._getEmployeeData() || [];
			let _gids = _.pluck(this.model.get('grouplist'), 'id');
			let _rids = _.pluck(this.model.get('rolelist'), 'id');
			return Promise.all([util.getGroupsMembers(_gids), util.getRolesMembers(_rids)]).then((values) => {
				_.each(values[0], function(item) {
					memberData = memberData.concat(FS.contacts.getEmployeesByIds(item));
				});
				_.each(values[1], function(item){
					memberData = memberData.concat(FS.contacts.getEmployeesByIds(item));
				})
				memberData = _.uniq(_.compact(memberData), function (item) {
					return item.id * 1
				});
				memberData = FS.contacts.sortEmployeesByLetter(memberData);
				callback(memberData);
			})
		},

		_getExEmployeeData: function() {
			var _exEm = this.model.get('exEmployeelist');
			var _trees = {};
			_.each(_exEm, function(item){
				if (item.type == 'en' && !_trees[item.id]) {
					_trees[item.id] = _.extend(item, {
						children: [],
					});
					return;
				}
				if (!_trees[item.enterpriseId]){
					_trees[item.enterpriseId] = {
						id: item.enterpriseId,
						name: item.enterprise,
						children: [],
					}
				}
				item.enterpriseId && _trees[item.enterpriseId].children.push(item);
			})
			_trees = _.values(_trees);
			return _trees;
		},

		getFilterValue() {
			let data = this.filter.getValue();
            data = JSON.parse(data);
			let $err = this.$('.filter-box .crm-filter-group');
			this.hideError($err);
			if (!this.filter.valid()) {
				this.showError($err, $t('请填写筛选值'));
				return [];
			}
			return data;
		},

		saveHandle: function () {
			let AllocatePattern = this.AllocatePattern;
			let IsAllSalesClue = this.IsAllSalesClue;
			this.datas = [];
			
			// 数据范围为筛选条件时，校验筛选条件
			if (!IsAllSalesClue) {
				let _datas = this.getFilterValue() || [];
				this.datas = _datas;
				if (!this.datas.length) {
					return;
				}
			}
			
			// 校验分配人员
			if(!this.validate()) {
				return;
			}

			this.model.set('RuleFilterList', this.datas);
			this.model.set('MemberList', this.memberlist);
			this.model.set('AllocatePattern', AllocatePattern);
			this.model.set('IsAllSalesClue', IsAllSalesClue);
			// 人员筛选数据
			let personnelFilterData = AllocatePattern == 1 ? [] : (this.personnelFilter ? JSON.parse(this.personnelFilter.getValue()) : []);
			this.model.set('RuleFilterGroupList', personnelFilterData);
			this.trigger('save', this.model.toJSON());
			this.hide();
		},

		parseSaveData: function () {
            return this.filter.getParsedData("clue");
		},

		// 校验分配人员
		validate: function () {
			var flag = true,
				selectFlag = true,
				filterEmptyFlag = true,
				filterErrFlag = true,
				sbarVal = this._singlebar && this._singlebar.getValue('member'),
				exSbarVal = this._exSinglebar && this._exSinglebar.getValue(),
				mbarVal = this._multibar && this._multibar.getValue(),
				exMbarVal = this._exMultibar && this._exMultibar.getValue(),
				weightVal = this.weightView && this.weightView.getValue();

			util.hideErrmsg(this.$el);
			
			if (this.AllocatePattern == 1) {
				// 仅分配
				// 校验选人组件值
				if ((sbarVal && sbarVal.length) || (exSbarVal && (exSbarVal.exEmployee.length || exSbarVal.exEnterprise.length))) {
					var _data = (sbarVal && sbarVal.length) ? sbarVal : (exSbarVal.exEmployee.length ? exSbarVal.exEmployee : exSbarVal.exEnterprise);
					var _type = (sbarVal && sbarVal.length) ? 1 : (exSbarVal.exEmployee.length ? 4 : 3);
					this.memberlist = _.map(_data, function (item) {
						return {
							MemberID: item,
							MemberType: _type
						}
					});

				} else {
					flag = false;
					var $items1 = this.$('.single-bar-wrapper .select-bar-item');
					util.showErrmsg($items1.eq($items1.length-1), $t("请选择分配范围"));
				}

			} else if(this.AllocatePattern == 2) {
				// 循环分配
				// 校验人员筛选值-未填写值
				if (this.personnelFilter && !JSON.parse(this.personnelFilter.getValue()).length){
					filterEmptyFlag = false;
				}
				// 校验人员筛选值-值不完整
				if (this.personnelFilter && !this.personnelFilter.valid()) {
					filterErrFlag = false;
				}
				// 校验选人组件的值
				if ((mbarVal && (mbarVal.member.length || mbarVal.group.length || mbarVal.usergroup.length || mbarVal.role.length)) || (exMbarVal && (exMbarVal.exEmployee.length || exMbarVal.exEnterprise.length))) {
					var _data1 = [], _data2 = [], _data3 = [], _data4 = [], _data5 = [], _data6 = []; 
					if (mbarVal){
						_data1 = _.map(mbarVal.member || [], function (item) {
							return {
								MemberID: item,
								MemberType: 1
							}
						});

						_data2 = _.map(mbarVal.group || [], function (item) {
							return {
								MemberID: item,
								MemberType: 2
							}
						});

						_data5 = _.map(mbarVal.usergroup || [], function (item) {
							return {
								MemberID: item,
								MemberType: 5
							}
						});

						_data6 = _.map(mbarVal.role || [], function (item) {
							return {
								MemberID: item,
								MemberType: 6
							}
						});
					}
					if (exMbarVal) {
						_data3 = _.map(exMbarVal.exEmployee || [], function (item) {
							return {
								MemberID: item,
								MemberType: 4
							}
						});
						_data4 = _.map(exMbarVal.exEnterprise || [], function (item) {
							return {
								MemberID: item,
								MemberType: 3
							}
						});
					}
					this.memberlist = _data1.concat(_data2,_data5, _data6, _data3, _data4);
				} else {
					selectFlag = false;
				}
				
				if (!filterErrFlag) {
					util.showErrmsg(this.$('.multi-bar-wrapper .filter-group-item'), $t("请填写筛选值"));
				} else if (!selectFlag && !filterEmptyFlag) {
					var $items2 = this.$('.multi-bar-wrapper .select-bar-item');
					util.showErrmsg($items2.eq($items2.length-1), $t("请选择分配范围"));
				} 

				flag = (selectFlag || filterEmptyFlag) && filterErrFlag;

			} else {
				flag = this.weightView && this.weightView.validate();

				this.memberlist = weightVal;
			}
			return flag;
		},

		cancelHandle: function (e) {
			this.hide();
		},

		hide: function () {
			this.destroy();
		},

		showError: function (el, msg) {
			el = el;
			msg = msg || $t('请填写筛选值');
			util.showErrmsg(el, msg);
		},

		hideError: function (el) {
			el = el || this.$el;
			util.hideErrmsg(el);
		},
		destroy: function () {
			const me = this;
			_.each(['personnelFilter', 'filter', '_singlebar', '_exSinglebar', '_multibar', '_exMultibar', 'weightView'], (compName) => {
				me[compName] && me[compName].destroy && me[compName].destroy();
			})
			return EditRule.superclass.destroy.apply(this);
		}
	});

	module.exports = EditRule;
});
