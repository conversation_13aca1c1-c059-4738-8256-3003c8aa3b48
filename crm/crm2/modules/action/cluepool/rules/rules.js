/**
 * @description 线索池分配器
 * <AUTHOR>
 */

define(function (require, exports, module) {
	var util = require('crm-modules/common/util'),
		rulesModel = require('./model/model'),
		RulesView = require('crm-components/rulesview/rulesview'),
		EditRule = require('./editrule/editrule'),
		// Parse = require('crm-modules/common/cluepool/cluepool').Parse,
		// FGutil = require('crm-modules/common/filtergroup/util'),
		rulesLayoutTpl = require('./template/rules-layout-html');

	var Model = Backbone.Model.extend({});
	var Rules = Backbone.View.extend({

		options: {
			EditRule: EditRule,
			maxLen: 500,
			parseFn: 'parseField'
		},

		/*绑定事件*/
		events: {
			'click .j-addrule': 'addRuleHandle',
			'click .crm-g-checkbox': 'checkboxHandle'
		},

		/*初始化 */
		initialize: function () {
			this.options = $.extend(true, {}, Rules.prototype.options, this.options);
			let opts = this.options;
			this.setModel(opts.data);

			this.listenTo(this.model, {
				'change:employeelist': this.modelPropChangeHandle,
				'change:memberlist': this.modelPropChangeHandle,
				'change:grouplist': this.modelPropChangeHandle,
				'change:rolelist': this.modelPropChangeHandle,
				'change:exEmployeelist': this.modelPropChangeHandle,
				'change:exEnterpriselist': this.modelPropChangeHandle,
				'change:poolType': this.modelPropChangeHandle,
			});
		},

		/**
		 * @description 此方法中触发model的窗口重置
		 * @return undefined
		 */
		render: function (cb) {
			var me = this;
			this.$el.html(rulesLayoutTpl());
			me.getFieldList(function (fieldlist) {
				me.fieldlist = fieldlist;
				me.getCluePoolList(function () { //?
					cb && cb();
				})
				me.renderRule();
			});
		},

		getRulelist() {
			return this.model.get("isHaveValue") ? this.model.get("rulelist") || {} : this.model.get("rulelist");
		},

		formatRuleModel(ruleModel) {
			ruleModel.set({
				fieldlist: this.fieldlist,
				employeelist: this.model.get('employeelist'),
				memberlist: this.model.get('memberlist'),
				grouplist: this.model.get('grouplist'),
				rolelist: this.model.get('rolelist'),
				exEmployeelist: this.model.get('exEmployeelist'),
				exEnterpriselist: this.model.get('exEnterpriselist'),
				isOpenPRM: this.model.get('isOpenPRM'),
				poolType: this.model.get('poolType'),
			});
				
			return ruleModel;
		},

		formatRuleData(ruleModel) {
			// let opts = this.options;
			// let parse = new Parse();
			// let parsefield = parse[opts.parseFn](this.fieldlist, ruleModel.toJSON());
			// let ruleData = _.extend(ruleModel.toJSON(), parsefield);
			let ruleData = _.extend(ruleModel.toJSON());
			let personelFields = CRM.get(`fields.PersonnelObj`);
			let pData = CRM.util.parseAllocateRule(ruleData, this.fieldlist, personelFields, CRM.get('isOpenPRM'));
			// let datas = ruleData.datas;
			// let RuleFilterGroupList = ruleData.RuleFilterGroupList || [];

			let content1 = $t("全部线索");
			if (!ruleData.IsAllSalesClue) {
				content1 = pData.ruleText;
				// content1 = parse.parseFilterRule(this.fieldlist, ruleData.RuleFilterList, FGutil);
			}
			// if (datas.length > 0) {
			// 	content1 = parse.getRuleText(datas);//this.formatString(datas);
			// }
			let content2 = pData.memberText;
			let label2 = [$t("仅分配给"), $t("循环分配给"), $t('权重分配')][ruleData.AllocatePattern - 1];
			if (ruleData.AllocatePattern == 3) {
				content2 = '';
			}
			// let content2 = '';
			// if (CRM.get('isOpenPRM')) {
			// 	let _c1 = $t('内部：') + ruleData.member.join('、');
			// 	let _c2 = $t('外部：') + ruleData.exContact.join('、');
			// 	if (ruleData.member.length && RuleFilterGroupList.length > 0 && ruleData.exContact.length){
			// 		content2 = _c1 + '<br/>' + this.getScopeHtml(RuleFilterGroupList) + _c2;
			// 	} else if (ruleData.member.length && ruleData.exContact.length){
			// 		content2 = _c1 + '<br/>' + _c2;
			// 	} else if (ruleData.member.length && RuleFilterGroupList.length > 0) {
			// 		content2 = _c1 + '<br/>' + this.getScopeHtml(RuleFilterGroupList);
			// 	} else if (RuleFilterGroupList.length > 0 && ruleData.exContact.length) {
			// 		content2 = this.getScopeHtml(RuleFilterGroupList) + _c2;
			// 	} else if(ruleData.member.length) {
			// 		content2 = _c1;
			// 	} else if (RuleFilterGroupList.length > 0) {
			//  		content2 +=  this.getScopeHtml(RuleFilterGroupList) + '<br/>';
			// 	} else if (ruleData.exContact.length) {
			// 		content2 = _c2;
			// 	}
			// } else {
			// 	content2 += ruleData.member.join('、');
			// 	if (RuleFilterGroupList.length > 0) {
			// 		content2 +=  this.getScopeHtml(RuleFilterGroupList) + '<br/>';
			// 	}
			// }

			return {
				label1: $t("筛选条件"),
				label2: label2,
				content1: content1,
				content2: content2
			}
		},

		// getScopeHtml(fulldata) {
		// 	let data = [];
		// 	if (fulldata.length) {
		// 		let filter_group = fulldata;
		// 		for (let i = 0; i < filter_group.length; i++) {
		// 			let condition = filter_group[i];
		// 			let row = []
		// 			for (let j = 0; j < condition.filters.length; j++) {
		// 				const filter = condition.filters[j];
		// 				let field = CRM.get(`fields.PersonnelObj`)[filter.field_name];
		// 				if (field) {
		// 					let text = FGutil.formatFieldValue(field, filter.field_values, filter.operator);
		// 					row.push(`${field.label} ${filter.operator_name} ${text ? '' + text + '' : ''}`)
		// 				}
		// 			}
		// 			if (i == 0) {
		// 				data.push(`<p style="color:#333;">${row.join(` ${$t('且')} `)}</p>`)
		// 			} else {
		// 				data.push(`<p style="color:#333;">${$t('或')} ${row.join(` ${$t('且')} `)}</p>`)
		// 			}
		// 		}
		// 	}
		// 	let text = data.join(' ') || $t('成员已被全部禁用');
		// 	return `<div class="item-rule">${text}</div>`;
		// },

		// formatString(datas) {
		// 	var html = [];
		// 	_.each(datas, function (data) {
		// 		html.push('"', data.FieldName, '" ', data.Compare, data.FieldValue && ' "', data.FieldValue && data.FieldValue + '" ', ',' + $t("且"));
		// 	});
		// 	var r = new RegExp(',' + $t("且") + '$', "g");
		// 	return html.join('').replace(r, '');
		// },
		editHandle(model, ruleView) {
			const me = this;
			var edit = new EditRule({
				from: model.get('DataType'),
				apiName: this.options.apiName,
			});
			edit.on('save', function (rule) {
				edit.destroy();
				ruleView.render();
				me.onChange();
			});
			model.needDepartmentChildren = this.options.needDepartmentChildren;
			edit.show(model);
		},

		setModel: function (obj) {
			obj = obj || {};
			if (!this.model) {
				this.model = new Model(obj);
				return;
			}
			this.model.set(obj);
		},

		set: function (key, value) {
			this.model.set(key, value);
		},

		get: function (key) {
			return this.model.get(key);
		},

		/**
		 * @description 遍历集合
		 * @return undefined
		 */
		renderRule: function () {
			this.rulesView = new RulesView({
				$el: this.$('.rules-content'),
				groupName: 'j-cluepool-rules',	// 拖拽分组名称
				rules: this,
				rulesModel: rulesModel,
				formatRuleModel: this.formatRuleModel,
				rulelist: this.getRulelist(),
				formatRuleData: this.formatRuleData.bind(this),
				editHandle: this.editHandle.bind(this),
				cloneHandle:this.cloneHandle.bind(this),
				from: this.model.get('from'),
			})

			this.addBtnVisible(this.rulesView.collection.length);
			this.rulesView.on('change', (collection) => {
				this.addBtnVisible(collection.length);
				this.onChange();
			})
			this.rulesView.on('reset', (collection) => {
				this.addBtnVisible(collection.length);
			})
		},
		onChange() {
			this.trigger('change', this.getValue());
		},
		// 新增规则按钮的显示和隐藏
		addBtnVisible(n) {
			let opts = this.options;
			if (n >= opts.maxLen) {
				this.$('.j-addrule').hide();
			} else {
				this.$('.j-addrule').show();
			}
		},

		//属性改变的时候，更新每个model相关属性
		modelPropChangeHandle: function (model) {
			this.rulesView.collection.each(function (child) {
				child.set('memberlist', model.get('memberlist'));
				child.set('employeelist', model.get('employeelist'));
				child.set('grouplist', model.get('grouplist'));
				child.set('rolelist', model.get('rolelist'));
				child.set('exEmployeelist', model.get('exEmployeelist'));
				child.set('exEnterpriselist', model.get('exEnterpriselist'));
				child.set('poolType', model.get('poolType'));
			});
		},

		checkboxHandle: function (e) {
			var target = $(e.target);
			target.toggleClass('state-active');
			this.rulesView.collection.each(function (rule) {
				rule.set('IsIncludePastTime', target.hasClass('state-active'));
			});
			this.onChange();
		},

		/*添加一条规则*/
		addRuleHandle: function (e) {
			let opts = this.options;
			let collection = this.rulesView.collection;
			const me = this;

			let m = new rulesModel.Rule({
				Priority: collection.length + 1,
				fieldlist: this.fieldlist,
				employeelist: this.model.get('employeelist'),
				memberlist: this.model.get('memberlist'),
				grouplist: this.model.get('grouplist'),
				rolelist: this.model.get('rolelist'),
				exEmployeelist: this.model.get('exEmployeelist'),
				exEnterpriselist: this.model.get('exEnterpriselist'),
				isOpenPRM: this.model.get('isOpenPRM'),
				poolType: this.model.get('poolType'),
			});

			this.editrule = new opts.EditRule({
				from: this.model.get('DataType'),
				apiName: this.options.apiName
			});
			m.needDepartmentChildren = this.options.needDepartmentChildren;
			this.editrule.show(m);
			this.editrule.on('save', function (rule, typelist) {
				collection.add(rule);
				me.onChange();
			});

			CRM.util.uploadLog('crmsetting', 's-rule', {
				eventId: 'addDistributionRule',
				eventType: 'cl'
			});
		},
		// 复制
		cloneHandle:function(model){
			let opts = this.options;
			let collection = this.rulesView.collection;
			const me = this;

			let m = new rulesModel.Rule();
			// 要删除原本的id不然保存不上
			m.set(Object.assign(_.extend({}, model.attributes)))
			// 优先级加一，初始化数据
			m.set('Priority',  model.get('Priority') + 1);
			// 要删除原本的id不然保存不上
			m.unset ('SalesCluePoolAllocateRuleID');
			// 渲染实例

			this.editrule = new opts.EditRule({
				from: model.get('DataType'),
				apiName: this.options.apiName
			});
			this.editrule.show(m);
			
			this.editrule.on('save', function (rule, typelist) {
				// 重新排优先级并添加
				collection.each(function(modelItem) {
					var currentPriority = modelItem.get('Priority');
					if (currentPriority >= model.attributes.Priority + 1) {
						modelItem.set('Priority', currentPriority + 1);
					}
				});
				collection.add(rule);
				me.onChange();
			});

			CRM.util.uploadLog('crmsetting', 's-rule', {
				eventId: 'addDistributionRule',
				eventType: 'cl'
			});
		},

		/*=============================================分配规则替换接口=============================================*/

		/*请求字段列表 不能缓存字段描述信息，对选类型字段options会实时更新，需要通过接口获取最新数据*/ 
		getFieldList: function(callback) {
			var me = this;
			$.when(CRM.util.getFieldsByApiName(me.options.apiName), CRM.util.getFieldsByApiName('PersonnelObj')).then((data1, data2) => {
				var list = me.parseFieldData(data1);
				callback && callback(list);
			})
			// CRM.util.fetchDescribe(me.options.apiName, {
			// 	"include_detail_describe": false,
			// 	"include_layout": false,
			// 	"apiname": me.options.apiName,
			// 	"layout_type": "add",
			// 	"recordType_apiName": "record_sKbe4__c"
			// }, function(res) {
			// 	var list = me.parseFieldData(res.objectDescribe.fields);
			// 	callback && callback(list);
			// }, true);
		},


		parseFieldData: function(data){
			var key, temp = [];
			for (key in data){
				if(data[key] && data[key] instanceof Object){
					temp.push(data[key])
				}
			}
			return temp
		},

		/*==========================================================================================*/
		//无需替换
		getCluePoolList: function (callback) {
			var me = this;
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/LeadsPoolObj/controller/List',
				data: {
					object_describe_api_name: "LeadsPoolObj",
					ignore_scene_record_type: false,
					search_query_info: '{"limit":500,"offset":0,"filters":[],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}',
					get_data_only: true
				},
				success: function (res) {
					if (res.Result.StatusCode == 0) {
						var temp = []
						let newParm = []
						let dataList = res.Value.dataList;
						for (let i = 0; i < dataList.length; i++) {
							let data = dataList[i]
							newParm.push(CRM.util.parsePropFromKebabToCamel(data))
						}
						for (var i = 0; i < res.Value.dataList.length; i++) {
							// res.Value.dataList[i].name = res.Value.dataList[i].name
							res.Value.dataList[i].value = res.Value.dataList[i]._id
						}
						me.cluepoolList = res.Value.dataList
						callback && callback()
						return;
					}
					util.alert(res.Result.FailureMessage);
				}
			}, {
					errorAlertModel: 1
				})
		},

		/*获取值*/
		getValue: function () {
			return this.rulesView.collection.collect();
		},

		resetRules: function() {
			this.rulesView.resetRules();
			this.onChange();
		},

		destroy: function () {
			this.remove();
			this.rulesView && this.rulesView.destroy();
			this.fieldlist = [];
			this.undelegateEvents();
			this.stopListening();
		}
	});

	module.exports = Rules;
});
