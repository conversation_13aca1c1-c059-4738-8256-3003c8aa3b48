define(function(require, exports, module){
    let FilterGroup = require('crm-modules/common/filtergroup/filtergroup');
    let helper = require('crm-modules/setting/ownership/ownership').helper;

    function ruleDialog(param) {
        return FxUI.create({
            template: `<fx-dialog
                         class="ownerrule-dialog"
                         :visible.sync="dialogVisible"
                         width="960px"
                         ref="ruleDialog"
                         :append-to-body="true"
                         title="${$t("负责人规则设置")}"
                         max-height="500px"
                        >
                        <div class="dialog-content">
                            <div class="crm-intro">
                                <h3>{{$t("注意")}}</h3>
                                <ol>
                                    <li>{{$t("新进入的线索满足条件时不会清空负责人")}}</li>
                                </ol>
                            </div>
                            <div class="select-wrapper"></div>
                        </div>
                        <div slot="footer" class="dialog-footer">
                            <fx-button size="small" @click="hide">{{$t("取 消")}}</fx-button>
                            <fx-button ref="confirmBtn" size="small" type="primary" @click="onSave">{{$t("确 定")}}</fx-button>
                        </div>
                        </fx-dialog>`,
            data() {
                return {
                    dialogVisible: true,
                }
            },
            mounted() {
                this.$nextTick(this.initFilter);
            },
            methods: {
                initFilter() {
                    var me = this;
                        helper = _.extend({}, helper, {
                            changeDepartmentConfig(child,options,compare) {
                              if((compare.value == 13 || compare.value == 14) && options.apiname == "LeadsObj" && param.needDepartmentChildren){
                                // 默认加上包含子部门的选项
                                child.groupIncludeChildrenStatus = 1;
                                child.needDepartmentChildren = true;
                              }
                              return child;
                            }
                        });
					var filterApiname = ['completion_rate', 'life_status', 'leads_status', 'transform_time', 'assigner_id', 'assigned_time', 'assigned_time', 'expire_time', 'remaining_time', 'last_modified_by', 'remind_days', 'resale_count', 'returned_time', 'owner_department', 'last_modified_time', 'account_id', 'contact_id', 'new_opportunity_id', 'opportunity_id', 'out_tenant_id', 'leads_pool_id', 'extend_days', 'out_owner'].concat(CRM.get('isOpenPRM') ? [] : ['partner_id']);
                    me.filter = new FilterGroup({
                        $wrapper: $('.select-wrapper', me.$el),
                        title: $t("且(AND)"),
                        width: 750,
				        AND_MAX: 10,
                        addBtnName: $t('新增或关系'),
                        apiname: param.apiname,
                        selectone_multiple: true,	//单选变多选
                        needDepartmentChildren: param.needDepartmentChildren || false,
                        defaultValue: param.rule,
						filterType: ['url', 'time', 'image', 'file_attachment', 'percentile', 'count', 'quote', 'formula', 'signature', 'employee_many', 'department_many', 'html_rich_text', 'object_reference_many'],
                        filterApiname: filterApiname,
                        helper,
						formatFields(fields) {
							fields.out_owner && (fields.out_owner.type = 'exemployee');
							return fields;
						},
						// parseCompare(compares, field) {
						//     if (CRM.get('isOpenPRM') && field.api_name === 'out_owner') {
						//         compares = compares.filter((item) => {
						//             return ['IS', 'ISN'].indexOf(item.value1) != -1
						//         })
						//     }
						//     return compares;
						// }
                    });
                },
                getValue() {
                    let result = [];
                    let data = this.filter.getValue();
                    data = JSON.parse(data);

                    let wheres = data;
                    let connector = data.type;

					wheres = wheres.map((condition) => {
                        let filters = condition.filters.map((filter) => {
                            return {
                                operator: filter.operator,
                                operator_name: filter.operator_name,
                                field_name: filter.field_name,
                                field_values: filter.field_values,
                                isIndex: false,
                                fieldNum: 0,
                                connector: 'AND',
                                isObjectReferencea: false
                            }
                        })
                        return {
                            filters: filters,
                            connector: 'OR'
                        }
                    })

					this.hideError();
                    if (!this.filter.valid()) {
                        this.showError();
                        return null;
                    }

					return wheres;
                },
                showError() {
                    CRM.util.showErrmsg($('.select-wrapper', this.$el), $t('请填写筛选值!'));
                },
                hideError() {
                    CRM.util.hideErrmsg($('.select-wrapper', this.$el));
                },
                onSave(){
                   var value = this.getValue();
                   if (!value) return;
                   this.$emit('save', value);
                   this.hide();
                },
                show() {
                    this.dialogVisible = true;
                },
                hide() {
                    this.dialogVisible = false;
                },
                destroy() {
                    this.filter && this.filter.destroy();
                    this.$destroy();
                }
            }
        })
    }
    return ruleDialog;
})
