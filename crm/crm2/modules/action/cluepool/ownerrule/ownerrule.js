define(function(require, exports, module){
    // let FGutil = require('crm-modules/common/filtergroup/util');
    // var Parse = require('crm-modules/common/cluepool/cluepool').Parse;
    var ruleDialog = require('./ruledialog');
    var ruleTpl = require('./ownerrule-html');
    var OwnerRule = Backbone.View.extend({
        initialize: function(options) {
            const me = this;
            // this.parse = this.getCommonParse();
            this.options = options;
            this.apiname = options.apiName;
            this.fieldlist = options.fieldlist;
            this.needDepartmentChildren = options.needDepartmentChildren || false;
            this.rule = options.data || (options.poolData && options.poolData.PoolOwnerRule && options.poolData.PoolOwnerRule.length ? options.poolData.PoolOwnerRule : null);
            if (me.fieldlist) {
                me.render(me.rule);
            } else {
                CRM.util.fetchDescribe(me.apiname, {
                    apiname: me.apiname,
                    include_detail_describe: false,
                    include_layout: false,
                    layout_type: "add",
                    recordType_apiName: "default__c"
                }, function(res) {
                    me.fieldlist = res.objectDescribe.fields;
                    me.render(me.rule);
                }, true);
            }
        },
        render: function(data) {
            var ruleContent = CRM.util.parseFiltersRule(data, this.fieldlist);
            this.$el.html(ruleTpl({
                content: ruleContent,
            }));
        },
        events: {
            'click .ownerrule-add': 'operateRule',
            'click .ownerrule-edit': 'operateRule', 
            'click .ownerrule-del' : 'delRule',
        },
        delRule: function() {
            this.rule = null;
            this.$('.ownerrule-container').attr('data-type', 'add');
            this.$('.ownerrule-content').html('');
            this.dialog && this.dialog.destroy();
            this.dialog = null;
            this.trigger('change', this.getValue());
        },
        operateRule: function() {
            var me = this;
            if (!this.dialog) {
                this.dialog = ruleDialog({
                    rule: this.rule,
                    apiname: this.apiname,
                    needDepartmentChildren: this.needDepartmentChildren,
                });
                this.dialog.$on('save', function(data) {
                    if (data.length) {
                        me.rule = data;
                        me.$('.ownerrule-container').attr('data-type', 'edit');
                        var ruleContent = CRM.util.parseFiltersRule(data, me.fieldlist);
                        me.$('.ownerrule-content').html(ruleContent);
                        me.trigger('change', me.getValue());
                    }
                })
            }
            this.dialog.show();
        },
        getValue: function() {
            return this.rule;
        },
        // getCommonParse: function () {
		// 	var me = this;
		// 	if (!this._parse) {
		// 		this._parse = new Parse();
		// 	}
		// 	return this._parse;
		// },
        destroy: function() {
            this.dialog && this.dialog.destroy();
            this.dialog = null;
        },
    });
    module.exports = OwnerRule;
})