define(function (require, exports, module){
	var util = CRM.util,
        View = require('./view/view'),
        InvoiceView = require('crm-modules/action/field/field').View,
        SelectInvoice = require('./selectinvoice/selectinvoice'),
        Common = require('crm-modules/action/common/common');

	var InvoiceApplicationObj = Common.extend({
        add: function(obj) {
            var me = this;
            obj.data = _.extend(window.PAAS?.app?.isCross?.() ? {} :{
                owner: [CRM.curEmpId + '']
            }, obj.data || {});
            require.async('crm-modules/action/field/field', function(field) {
                field.add(_.extend({
                    View: View,
                    success: function(data) {
                        me.trigger('refresh', 'add', data, data && data._id);
                        obj && !data.__isContinue && obj.showDetail && me.showDetail({
							id: data._id,
							apiname: obj.apiname,
							showMask: obj.showMask,
                        });
                    }
                }, obj))
            })

        },

        edit: function(obj){
            var me = this;
            require.async('crm-modules/action/field/field', function(field) {
                field.edit(_.extend({
                    View: View,
                    success: function(data) {
                        me.trigger('refresh', 'edit', data);
                    }
                }, obj))
            })

		},

		/**
         * @desc 新建发票信息
         */
        addInvoice: function(obj) {
            var me = this;
            require.async('crm-modules/action/field/field', function(field) {
                field.add(_.extend({
                    apiname: 'AccountFinInfoObj',
                    title: $t("新建财务信息"),
                    nonEditable: true,
                    View: InvoiceView,
                    success: function(data) {
                        me.trigger('refresh', 'addInvoice', data);
                    }
                }, obj))
			})
        },

		/**
         * @desc 编辑发票信息
         */
        editInvoice: function(obj) {
            var me = this;
            require.async('crm-modules/action/field/field', function(field) {
                field.edit(_.extend({
                    apiname: 'AccountFinInfoObj',
                    show_type: 'dialog',
                    title: $t("编辑财务信息"),
                    nonEditable: true,
                    View: InvoiceView,
                    success: function(data) {
                        me.trigger('refresh', 'editInvoice', data);
                    }
                }, obj));
			})
        },

        /**
         * @desc 选择发票信息
         */
        selectInvoice: function(obj) {
            var me = this;
            me.select = new SelectInvoice({
				account_id: obj.account_id,
                account_id__r: obj.account_id__r
            });
            me.select.on('selected', function(data) {
                me.trigger('refresh', 'selectInvoice', data);
            });
            me.select.show();
        },

		destroy: function(){
            this.select && this.select.destroy();
            this.select = null;
		}
    });
    InvoiceApplicationObj.View = View;
    module.exports = InvoiceApplicationObj;
})
