.crm-a-spuobj {
	overflow-x: hidden !important;

	.crm-a-product-category {
		width:0;
		.crm-a-category-hand {
			width: 100%;
			padding: 5px 10px;
			height: 34px;
			box-sizing: border-box;
			line-height: 22px;

		}
	}

	.f-item-title {
		font-size: 20px;
		padding-top: 40px;
		padding-bottom: 32px;
		color: #191c21;
	}

	.f-g-item.f-g-item-issepc,.f-g-item.f-g-item-ismulti {
		display: block !important;
		width: 100% !important;
		height: 34px;

		.f-g-item-tit,
		.f-item-wrap {
			display: inline-block;
			float: left;
		}

		.f-g-item-tit {
			width: 112px;
			display: inline-block;
		}
	}

	.f-g-item.multiunit {
		display: block;
		width: 100%;

		.f-item-wrap {
			width: 100%;
			max-width: 100%;
		}
		.add-unit {
			width: 100%;
			height: 22px;

			span {
				float: right;
				font-size: 14px;
				line-height: 22px;
				color: var(--color-info06);
				cursor: pointer;
			}
		}

		.multiunit-table-wrapper {
			// padding-left: 68px;

			.tr-disabled {
				background-color: rgba(238, 238, 238, 0.5);
				pointer-events: none;
			}
		}

		.check-wrapper {
			width: 100%;
			height: 100%;
			text-align: center;

			.check-radio-btn {
				width: 16px;
				height: 16px;
				display: block;
				content: '';
				border: 1px solid #ccc;
				border-radius: 50%;
				box-sizing: border-box;

				&.checked {
					border: 4px solid var(--color-info06);
				}

				&.disabled {
					background: #dee1e6;

					&.checked {
						background: var(--color-neutrals07);
						border-color: #dee1e6;
					}
				}
			}
		}
		.table-wrapper {
			.dt-main {
				border: 1px solid #dee1e6;

				table.tb {
					width: 100% !important;
				}

				.tb-del,
				.tb-enable {
					text-decoration: none;
					display: inline-block;
				}

				.tb-enable {
					margin-left: 10px;
				}
			}
		}
	}

	.fm-error {
		color: #F46458;
		padding-top: 4px;
	}

	.f-g-item.f-g-item-spu {
		width: 100% !important;
		.spec-skuview-wrap {
			width: 100%;
			box-sizing: border-box;

			.crm-comp-dketable {
				.f-group-label {
					border-left: 4px solid var(--color-primary06);
					font-size: 16px;
					padding: 0 16px 0 8px;
					// margin-left: 68px;
					height: 18px;
					line-height: 18px;
				}
				.cp-options .cpopt-wrapper,.cp-selects .cpopt-wrapper{
					.cpopt-select-wrapper{
						.spec-label{
							width: 128px;
							margin:0 8px 0 0;
							text-align: left;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

					}
					.cpopt-options{
						padding-left: 136px;
					}
					&.cpopt-edit {
						.cpopt-select-wrapper ,.cpopt-options{
							display: inline-block;
						}
						.cpopt-select-wrapper{
							width:128px;
							padding-left: 0;

						}
						.spec-label{
							margin: 0;
						}
						.cpopt-options {
             				 padding-left:0;
						}
					}
				}
				.add-spec-wrapper{
					padding-left: 138px;
				}
				.dke-table-layout{
					// margin-left: 68px;
				}
				.f-group-label{
					.full-table{
						margin-right: 78px;
					}
				}
			}
		}
	}
	.f-item-line-wrap[data-apiname="skunospec"]{
		// padding-left:68px;
		.crm-action-nfield {
			overflow: inherit;
		}
	}

	.f-item-line-wrap {
		.crm-action-nfield {
			padding: 0;
			overflow-x: hidden;
		}
	}

	&.crm-action-nfield-mini {
		.f-g-item.f-g-item-spu {
			padding-left: 0;
			.spec-skuview-wrap {
				.crm-comp-dketable{
					.f-group-label,.dke-table-layout{
						margin-left: 0;
					}
					.cp-options,.cp-selects {
						.cpopt-wrapper {
							.cpopt-select-wrapper {
								height: 32px;
								.spec-label {
									max-width: 128px;
									min-width: 96px;
									font-size: 12px;
									text-align: left;
								}
								.cpopt-group-wrapper {
									.crm-w-select .select-tit {
										height: 24px !important;
										min-height: 24px;

										input {
											height: 24px !important;
										}
									}
								}
							}
							&.cpopt-edit {
								.cpopt-select-wrapper {
									padding:0;
									width: auto;
									margin-right: 8px;
								}
								.cpopt-options {
									padding-left: 0;
									margin-left: 0;
								}
							}
						}
					}
				}

			}
		}
		.f-item-line-wrap[data-apiname="skunospec"]{
			padding-left: 24px;
			.f-g-item{
				padding-bottom:10px;
			}
		}
		.f-g-item.multiunit {
			.multiunit-table-wrapper{
				padding-left: 24px;
			}
		}
		.crm-a-product-category {
			.crm-a-category-hand {
				height: 24px;
				line-height: 24px;
				padding:0 0 0 6px;

			}
		}
	}
}
