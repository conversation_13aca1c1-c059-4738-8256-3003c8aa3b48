define(function (require, exports, module) {
	const Base = require('crm-modules/action/field/field').components.Base;
	module.exports = Base.extend({
		dataEvents: {
			'change.used_object_api_name': 'onApiNameChange'
		},
		initialize: function (options) {
			Base.prototype.initialize.call(this, options);
			this.initDom();
		},
		onApiNameChange: async function() {
			let objectApiname = this.get('data').used_object_api_name || 'AccountObj'
			const objectDescribe = await CRM.util.getDescribeLayout({
				apiname:  objectApiname,
				include_layout: false,
				include_detail_describe: false
			}).then((res) => {
				return res.objectDescribe;
			})
			let fields = objectDescribe?.fields || {};
			if(objectApiname == 'NewOpportunityObj') {
				let _fields = {};
				const supportFields = [
					'close_date',
					'owner_department',
					'opp_lines_sum',
					'create_time',
					'probability',
					'created_by',
					'data_own_department',
					'opp_discount',
					'parent_id',
					'name',
					'sales_process_id',
					'promised_probability',
					'sales_status',
					'leads_id',
					'sales_stage',
					'partner_id',
					'amount',
					'life_status',
					'account_id'
				]
				Object.keys(fields).forEach(key => {
					let item = fields[key];
					if(item.define_type == 'custom' || supportFields.find(field => field == item.api_name)) {
						_fields[key] = item;
					}
				})
				fields = _fields;
			}
			this.filterRule.filter.options.apiname = objectApiname;
			this.filterRule.filter.options.objects = [
				{
					api_name: objectApiname,
					fields,
					label: objectDescribe?.display_name_r || objectDescribe?.display_name
				}
			];
			this.filterRule.filter.apiname = objectApiname;
			this.filterRule.filterTip = $t('sfa.ai.interaction.stragey.add.condition.tip1',{objectName: objectDescribe.display_name || objectDescribe.display_name_r})
			this.filterRule.filter.render();
		},
		initDom: async function () {
			const me = this;
			this.addStyle();
			let defaultVal = me.get('data').condition;
			console.log(defaultVal, 'matching_rules');
			if(defaultVal) {
				defaultVal = JSON.parse(defaultVal).value;
			}
			const used_object_api_name = me.get('data').used_object_api_name || 'AccountObj';
			// 获取对象描述，用于显示对象名称
			const objectDescribe = await CRM.util.getDescribeLayout({
				apiname: used_object_api_name,
				include_layout: false,
				include_detail_describe: false
			}).then((res) => {
				return res.objectDescribe;
			})
			require.async('vcrm/sdk', sdk => {
				sdk.getComponent('RelationTemplateFilterRule').then(Comp => {
					const comp = new Vue({
						el: this.$el[0],
						render: (h) => h(
							Comp.default,
							{
								props: {
									data: me.data,
									value: defaultVal ? JSON.parse(defaultVal) : [],
									// filterTip: $t('sfa.ai.interaction.stragey.add.condition.tip1',{objectName: objectDescribe.display_name || objectDescribe.display_name_r}),	
									filterTip: $t('sfa.ai.interaction.stragey.add.condition.tip'),						
									apiName: used_object_api_name,
									objectName: 'InteractionStrategyObj'
								},
								on: {
									
								}
							}
						),
					});
					me.filterRule = comp.$children[0];
				});
			});
		},
		addStyle: function () {
			const parentEl = this.$el.parents('.f-g-item');
			parentEl.css('width', '100%').css('margin-left', '68px');
			parentEl.find('.f-g-item-tit').remove();
			parentEl.find('.f-item-wrap').css('max-width', 'none');
		},
		getValue() {
			return this?.filterRule?.getValue();
		},
	});
});
