/**
 * @description: 
 * @author: lishsh9516
 * @Date: 2023-12-07 10:33:19
 * @LastEditors: lishsh9516
 * @LastEditTime: 2024-01-30 18:13:48
 */
define(function (require, exports, module) {
    const Base = require('crm-modules/action/field/field').C.Base;
    module.exports = Base.extend({
        initialize() {
            Base.prototype.initialize.apply(this, arguments);
            var isEdit = this.get('isEdit');
            var isCopy = this.get('isCopy');
            this.defaultData = isEdit || isCopy ? this.getData('attribute_json') : null;
            $(this.$el[0]).parents('.f-g-item').find('.f-g-item-tit').hide();
            // $(this.$el[0]).parents('.f-g-item').find('.f-item-wrap').css({'margin-left': '80px'});
        },
        async render() {
            let me = this;
            require.async('vcrm/sdk',function(sdk){
                sdk.getComponent('priceQuoterDesigner').then((Comp) => {
                    const $options = {
                        el: me.$el[0],
                        render: h => h(Comp.default, {
                            props: {
                                defaultData: JSON.parse(me.defaultData)
                            }
                        }),
                        mounted() {
                            let _this = this;
                            let onResize = new ResizeObserver(_.debounce(function(entries) {
                                for (let entry of entries) {
                                    const target = entry.target;
                                    _this.getViewWidth($(target));
                                  }
                            }, 300));
                            onResize.observe($('.crm-action-nfield')[0]);
                            this.onResize = onResize;
                        },
                        methods: {
                            getViewWidth($target) {
                                let isMini = $target.hasClass('crm-action-nfield-mini')
                                let viewW = $target.width();
                                // let left = isMini ? 40 : 80;
                                $(this.$el).width(viewW - (isMini ? 20 : 0));
                                // $(this.$el).parents('.f-g-item').find('.f-item-wrap').css({'margin-left': `${left}px`});
                            },
                        },
                        beforeDestroy() {
                            this.onResize.disconnect();
                        }
                    };
                    let vm = new Vue($options).$mount(me.$el[0]);
                    me.priceQuoterDesigner = vm.$children[0];
                });
            });
        },
        async asyncValidateData() {
            let me = this;
            let valid = await me.priceQuoterDesigner.validateForm();
            return !valid;
        },
        destroy() {
            this.priceQuoterDesigner?.$destroy();
        }
    })
})