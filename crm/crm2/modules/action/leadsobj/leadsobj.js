/**
 * 关于线索的一些公用操作
 * <AUTHOR>
 */
define(function(require, exports, module) {

    var util = require('crm-modules/common/util'),
        Common = require('crm-modules/action/common/common'),
        Model = require('./model/model'),
        View = require('./view/view');

    /**
     * @desc 所有操作成功之后派发success事件 失败派发error事件
     *       事件类型(1已联系继续跟进 2线索无效 3转客户 4删除 5分配 6收回 7转移 8新建 9编辑 10领取 11作废 12更换负责人 13退回 14转联系人 15转商机)
     * @example var clue = new Clue(); clue.on('success',function(type,data){})
     *       clue.follow();操作成功之后 type值为1 data为数据
     */
    var Clue = Common.extend({
        ownerType: 1, //自定义字段类型
        source: 8, //附件类型
        objectType: 1, //批量操作类型
        _PL: $t("批量"),
        rootUrl: '/EM1HCRM/SalesClue/',
        ajax: {},
        apiname: 'LeadsObj',

        /**
         *@desc 提供一个可以访问底层View的入口。方便调用和重写
         */
        getView: function() {
            return View;
        },

        /**
         *@desc 添加线索
         *@param {{Object}}
         */
        add: function(obj) {
            var me = this;
            CRM.util.getConfigValue('is_show_creator_auto_add_team').then(status => {
                // 兼容字符串和布尔值
                const isTrue = status === true || status === 'true';
                if (!isTrue) {
                    // 清除缓存
                    CRM.removeLocal('IsAddCreatorToTeamMember');
                    me._add(obj, null);
                } else {
                    me._add(obj, [{
                        key: 'IsAddCreatorToTeamMember',
                        label: $t("创建人自动加为相关团队"),
                        storage: true,
                    }]);
                }
            },()=>{
                me._add(obj, [{
                    key: 'IsAddCreatorToTeamMember',
                    label: $t("创建人自动加为相关团队"),
                    storage: true,
                }]);
            })
        },
        _add:function(obj,gconfigs){
            var me = this;
            require.async('crm-modules/action/field/field', function(field) {
                field.add(_.extend({
                    View: View,
                    Model: Model,
                    isDuplicate: true,
                    cleanOwner: CRM.getLocal('skipCheckCleanOwner'),
                    apiname: me.apiname,
                    gconfigs: gconfigs,
                    show_type: 'full',
                    success: function(data, modelData, res) {
                        me.trigger('refresh', 'add', data);
                        me.trigger('success', 'add', data);
                        if(res.Value && res.Value.errorCode == 999999){
                            util.alert(res.Value.message || $t("销售线索重复，已生成行为记录，不再生成销售线索！"), function() {
                                !data.__isContinue && obj.showDetail && me.showDetail({
                                    id: data._id,
                                    apiname: me.apiname,
                                    showMask: obj.showMask,
                                });
                            });
                            return;
                        }
                        !data.__isContinue && obj.showDetail && me.showDetail({
                            id: data._id,
							apiname: me.apiname,
							showMask: obj.showMask,
                        });
                    }
                }, obj))
            });
        },

        /**
         *@desc 编辑线索 可传入完整的线索数据 {tableData:,fieldData:,fieldList:}
         *@param {{Object}} || {{String}}
         */
        edit: function(obj) {
            var me = this;
            me._edit(obj)
        },

        _edit: function(data) {
            var me = this;
            require.async('crm-modules/action/field/field', function(field) {
                field.edit(_.extend({
                    apiname: me.apiname,
                    View: View,
                    isDuplicate: true,
                    show_type: 'full',
                    success: function(data) {
                        me.trigger('refresh', 'edit', data);
                        me.trigger('success', 'edit', data);
                    }
                }, data, data.extendOptions));
            });
        },

        getDetailById: function(id, cb, content) {
            var me = this;
            var obj = {
                dataId: id,
                source: 8,
                url: '/EM1HCRM/SalesClue/GetSalesClueByID',
                param: {
                    SalesClueID: id,
                    Type: 1
                },
                success: function(res) {
                    if (!res.Value || !res.Value.Item) {
                        util.alert($t("该线索不存在"));
                        me.hide()
                        return;
                    }
                    cb && cb.call(content || me, res.Value);
                }

            }
            util.proxyGetFieldDetail(obj, this);
        },

        sendSaleLog: function(obj) {
            // util.api({
            //     url: '/Event/AddCrmEvent',
            //     type: 'post',
            //     data: {
            //         "externalResources": '[{\"ResourcesId\":\"' + obj.SalesClueID + '\",\"Source\":101,\"Data\":\"{\\\"name\\\":\\\"' + obj.SalesClueName + '\\\",\\\"content\\\":\\\"\\\"}\"}]',
            //         "tagId": "---",
            //         "feedContent": obj.DealResult,
            //         "fileInfos": [],
            //         "circleIDs": [],
            //         "employeeIDs": [util.getCurrentEmployee().employeeID],
            //         "contactIDs": [],
            //         "ncontactIDs": [],
            //         "customerIDs": [],
            //         "ncustomerIDs": [],
            //         "workOrders": []
            //     },
            //     success: function(res) {
            //         res.statusCode !== 0 && util.alert($t("销售记录未能发送成功请稍后重试"));
            //     }
            // })
        },

        /*
         *@desc 领取线索 支持批量
         *@param {{Object}} || {{Array}}
         **/
        receive: function(ids, obj) {
            // var me = this,
            //     isBatch,
            //     title,
            //     msg;

            // ids = ids || [];
            // isBatch = ids.length > 1;
            // title = isBatch ? $t("批量领取") : $t("领取");
            // msg = isBatch ? $t("确定领取这些线索吗") : $t("确定领取这个线索吗");

            // var confirm = util.confirm(msg, title, function() {
            //     me.ajax.receiveXhr = util.FHHApi({
            //         // url: me.rootUrl + 'ChooseSaleClues',
            //         url: '/EM1HNCRM/API/v1/object/LeadsObj/action/Choose',
            //         data: {
            //             objectIDs: ids
            //             // SaleClueIDList: ids
            //         },
            //         success: function(res) {
            //             if (res.Result.StatusCode === 0) {
            //                 confirm.hide();

            //                 if(res.Value.errorList.length){
            //                     util.alert(res.Value.errorList[0]);
            //                     return
            //                 }
            //                 if(res.Value.failedList.length){
            //                     util.alert(res.Value.failedList[0]);
            //                     return
            //                 }
            //                 me.showRemind(isBatch, res.Value, $t("领取成功"));
            //                 me.trigger('refresh');
            //                 me.trigger('success', 10, ids, res.Value);
            //                 return;
            //             }
            //             confirm.hide();
            //             util.alert(res.Result.FailureMessage);
            //             me.trigger('error', 10);
            //         }
            //     }, {
            //         submitSelector: this.element.find('.b-g-btn'),
            //         errorAlertModel: 1
            //     })
            // });
        },

        /*
         *@desc 作废线索线索 支持批量作废
         *@param {{String}} || {{Array}}
         **/
        invalid: function(ids) {
            // var me = this;
            // me.crmObjectInvalid(ids, function() {
            //     me.trigger('success', 11, ids);
            //     me.trigger('refresh', 11);
            // }, $t("crm.销售线索"));
        },

        /*
         *@desc 更换负责人 支持批量
         *@param {{Object}} || {{Array}}
         **/
        changeOwner: function(idsObj, obj) {
     //        var me = this,
     //            ids = idsObj.objectIds,
     //            poolId = obj[0].SalesCluePoolID || obj[0].leads_pool_id;

     //        me.fetchCluepoolEmployee(poolId, function(employee) {
     //            Common.prototype.changeOwner.apply(me, [{
     //                objType: me.objectType,
     //                originList: employee,
     //                excludeIds: obj.owner || (obj[0] && obj[0].owner) || [],
     //                objectIds: ids,
					// apiname: idsObj.apiname || obj.apiname
     //            }, obj, function() {
     //                me.trigger('refresh', 12, ids);
     //                me.trigger('success', 12, null);
     //            }]);
     //        });
        },

        /**
         * 添加相关团队成员
         */
        addSalesTeamM: function(obj) {

		},

        /*
         *@desc 获取线索池成员
         *@param {{String}} 线索池id
         **/
        fetchCluepoolEmployee: function(cluePoolId, cb) {
            var me = this;
            if (!cluePoolId) {
                cb && cb.call(me, null);
                return;
            }

            util.FHHApi({
                url: me.rootUrl + 'GetSaleCluePoolMemberList',
                data: {
                    SaleCluePoolID: cluePoolId
                },
                success: function(res) {
                    var employee = []
                    if (res.Result.StatusCode === 0) {
                        _.each(res.Value.SalesCluePoolMemberInfos, function(item) {
                            var obj;
                            if (item.Type === 1) {
                                obj = util.getEmployeeById(item.DataID);
                                obj && employee.push(obj);
                            } else {
                                [].push.apply(employee, util.getEmployeesByCircleId(item.DataID))
                            }
                        })
                        employee = _.uniq(employee, false, function(item) {
                            return item.id;
                        });
                        employee = _.sortBy(employee, 'spell');
                        cb && cb.call(me, employee[0] && employee);
                        return;
                    }
                    util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            })
        },

        /*
         *@desc 退回线索 支持批量
         *@param {{Object}} || {{Array}}
         **/
        back: function(ids, obj) {
            // var me = this, isBatch, poolId;

            // ids = ids || [];
            // isBatch = ids.length > 1;
            // poolId = obj && obj[0].leads_pool_id;

            // if (!me.backCluepool) {
            //     me.backCluepool = new ReturnBack();
            // }
            // me.backCluepool.off('submit');
            // me.backCluepool.on('submit', function(obj) {
            //     var salesCluePoolID = obj.poolId;
            //     me.ajax.backClueXhr = util.FHHApi({
            //         url: me.rootUrl + 'ReturnSalesClues',
            //         data: {
            //             SalesClueIDs: ids,
            //             Type: 2,
            //             SalesCluePoolID: salesCluePoolID,
            //             BackReason: obj.reason || ''
            //         },
            //         success: function(res) {
            //             me.backCluepool.hide();
            //             if (res.Result.StatusCode === 0) {
            //                 me.showRemind(isBatch, res.Value, $t("退回成功"));
            //                 me.trigger('refresh');
            //                 me.trigger('success', 13, salesCluePoolID);
            //                 return;
            //             }
            //             me.trigger('error', 13, salesCluePoolID);
            //             util.alert(res.Result.FailureMessage);
            //         }
            //     }, {
            //         errorAlertModel: 1,
            //         submitSelector: obj.$btn
            //     })
            // });
            // me.backCluepool.show({
            //     showPool: isBatch || !poolId,
            //     poolId: poolId
            // });
        },

        /**
         *@desc 跟进线索
         *@param {{Object}} 线索基本信息 必须包含SalesClueID 和 DealResult 属性
         *@param {{Number}}处理类型 1 跟进 2无效
         */
        follow: function(obj, type) {
            // var me = this;
            // type || (type = 1)

            // if (!me.handle) {
            //     me.handle = new Handle();
            //     me.handle.on('success', function(data) {
            //         me.trigger('refresh');
            //         me.trigger('success', type, data);
            //         data.isSendLog && me.sendSaleLog(data);
            //     })
            // }
            // me.handle.open(obj, type);
        },

        /**
         *@desc 线索无效
         *@param {{Object}} 线索基本信息 必须包含SalesClueID 和 DealResult 属性
         */
        useless: function(ids, obj) {
            // this.follow(obj, 2);
        },

        /**
         *@desc 转换线索
         *@param {{Object}} 线索基本信息 必须包含_id属性
         */
        convert: function(ids, obj) {
            // var me = this;
            // me._covert = convert({
            //     id: obj._id,
            //     data: obj,
            //     success: function(data) {
            //         me.trigger('success', 14, data);
            //         me.trigger('refresh');
            //     }
            // });
        },

        /**
         *@desc 删除线索
         *@param {{Object}} 线索基本信息 必须包含SalesClueID属性
         */
        del: function(ids) {
            // var me = this;
            // me.crmObjectDel(ids, function() {
            //     me.trigger('refresh');
            //     me.trigger('success', 4, ids);
            // }, $t("crm.销售线索"))
        },

        /**
         *@desc 分配线索
         *@param {{Object}} 线索基本信息 必须包含SalesClueID 和 OwnerName(负责人名称 可为空) 属性
         */
        assign: function(ids, obj) {
            // var me = this;
            // var poolId = obj.leads_pool_id || obj[0].leads_pool_id;
            // var isBatch = ids.length > 1;

            // if (!poolId || !obj || !ids || !ids[0]) return;

            // me.assinSelect && me.assinSelect.destroy();

            // me.assinSelect = new EmployeeSelect({
            //     title: $t("分配")
            // });

            // me.assinSelect.off('submit').on('submit', function(ownerId) {
            //     ownerId && util.FHHApi({
            //         url: '/EM1HNCRM/API/v1/object/LeadsObj/action/Allocate',
            //         data: {
            //             objectIDs: ids,
            //             ownerId: ownerId
            //         },
            //         success: function(res) {
            //             if (res.Result.StatusCode === 0) { //执行成功之后更新一些线索的属性并作为事件数据返回
            //                 var time = $.now(),
            //                     owner = util.getEmployeeById(ownerId),
            //                     assigner = util.getCurrentEmployee(),
            //                     t = {
            //                         OwnerID: ownerId,
            //                         OwnerName: owner ? owner.name : '',
            //                         UpdateTime: time,
            //                         Status: 2,
            //                         IsOverTime: 0,
            //                         AssignTime: time,
            //                         AssignerName: assigner ? assigner.name : ''
            //                     };
            //                 me.showRemind(isBatch, res.Value, $t("分配成功"));
            //                 me.trigger('refresh');
            //                 me.trigger('success', 5, t);
            //                 return;
            //             }
            //             util.alert(res.Result.FailureMessage);
            //             me.trigger('error', 5);
            //         }
            //     }, {
            //         errorAlertModel: 1
            //     });
            // });

            // me.fetchCluepoolEmployee(poolId, function(employee) {
            //     me.assinSelect.open({
            //         employee: employee,
            //         //fieldName: (isBatch ? me._PL : '') + '分配给',
            //         fieldName: $t("{{isBatch}}分配给",{
            //             isBatch:(isBatch ? me._PL : '')
            //         }),
            //         fieldWidth: isBatch ? 80 : 60
            //     })
            // })
        },

        /**
         *@desc 收回线索
         *@param {{Object}} 无论是传入单个对象 或是对象数组均需包含SalesClueID 属性
         */
        revoke: function(ids, obj) {
            // var me = this,
            //     isBatch = ids.length > 1,
            //     html = $t("确定收回这{{text}}线索吗",{
            //         text: (isBatch ? $t("些") : $t("个"))
            //     }),
            //     confirm = util.confirm(html, $t("收回"), function() {
            //         if (!ids.length) {
            //             confirm.hide();
            //             me.trigger('success', 6);
            //             return;
            //         }
            //         util.FHHApi({
            //             url: '/EM1HCRM/SalesClue/ReturnSalesClues',
            //             data: {
            //                 SalesClueIDs: ids,
            //                 Type: 1 //由于取消了员工退回功能,所以该值总为1
            //             },
            //             success: function(res) {
            //                 confirm.hide();
            //                 if (res.Result.StatusCode === 0) {
            //                     var t = {
            //                         OwnerName: '',
            //                         OwnerID: '',
            //                         UpdateTime: $.now(),
            //                         Status: 1,
            //                         AssignTime: 0,
            //                         IsOverTime: 0
            //                     };
            //                     me.showRemind(isBatch, res.Value, $t("收回成功"));
            //                     me.trigger('refresh');
            //                     me.trigger('success', 6, t);
            //                     return;
            //                 }
            //                 util.alert(res.Result.FailureMessage);
            //                 me.trigger('error', 6);
            //             },
            //             error: function() {
            //                 confirm.hide();
            //                 me.trigger('error', 6);
            //             }
            //         }, {
            //             submitSelector: confirm.$('.b-g-btn'),
            //             errorAlertModel: 1
            //         });
            //     });
        },

        /**
         *@desc 转移线索
         *@param {{Object}} 无论是传入单个对象 或是对象数组均需包含SalesClueID SalesCluePoolID 属性
         */
        shift: function(ids, poolId) {
            // var me = this,
            //     isBatch = ids.length > 1;

            // function shiftToCluepool(poolId) {
            //     util.FHHApi({
            //         url: '/EM1HCRM/SalesClue/MoveSalesClueToOtherPool',
            //         data: {
            //             SalesClueIDs: ids,
            //             SalesCluePoolID: poolId
            //         },
            //         success: function(res) {
            //             if (res.Result.StatusCode === 0) {
            //                 me.showRemind(isBatch, res.Value, $t("转移成功"));
            //                 me.trigger('success', 7, poolId);
            //                 me.trigger('refresh');
            //                 return;
            //             }
            //             util.alert(res.Result.FailureMessage);
            //             me.trigger('error', 7, poolId);
            //         }
            //     }, {
            //         errorAlertModel: 1
            //     });
            // }

            // if (!me.shiftCluepool) {
            //     me.shiftCluepool = new CluePoolList({
            //         title: $t("转移")
            //     })
            // }
            // me.shiftCluepool.off('submit').on('submit', function(obj) {
            //     obj && shiftToCluepool(obj.id);
            // });
            // me.shiftCluepool.show({
            //     fieldName: (isBatch ? me._PL : '') + $t("转移到"),
            //     fieldWidth: isBatch ? 80 : 60,
            //     poolId: poolId
            // });
        },

        destroy: function() {
            var me = this;
            me.stopListening();
            _.each(['shiftCluepool', 'assinSelect', 'handle', 'toCus', 'connectHandle', 'employeeSelect', 'backCluepool', '_contact', '_opportunity', '_covert', 'addHandle', 'editHandle', 'salesTeam'], function(name) {
                me[name] && (me[name].destroy(), me[name] = null);
            });
            _.each(me.ajax, function(ajax) {
                ajax && ajax.abort && ajax.abort();
            });
        },

        /**
         * @desc 为各类操作添加事件
         * @param {{Object}}
         * @param {{String}} 事件类型
         */
        _addEvent: function(handle, type) {
            this.listenTo(handle, 'success', function(data) {
                this.trigger('success', type, data)
            });
            this.listenTo(handle, 'error', function() {
                this.trigger('error', type);
            });
        },

        _getIds: function(obj) {
            return _.isArray(obj) ? _.pluck(obj, 'SalesClueID') : [obj.SalesClueID];
        }
    });

    Clue.View = View;

    module.exports = Clue;
});
