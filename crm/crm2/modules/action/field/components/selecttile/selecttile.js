
/**
 *@desc 平铺型选择组件/非标准表单组件
 *<AUTHOR>
 */
define(function(require, exports, module) {
    return Backbone.View.extend({
        events: {
            'click .radio ': '_radioHandle',
            'click .el-check' : '_checkHandle'
        },

        template: require('./tpl-html'),

        initialize: function(opts) {
            this._options = this._parseOptions(opts.options);
            this._isSingle = !opts.multiple;
            this.otherLabel = opts.otherLabel;
            this.defalutValue = opts.defalutValue;
            this.otherIsRequired = opts.otherIsRequired;
            this.emptyRender = opts.emptyRender;
            this.needResize = opts.needResize;
            this.render();
        },

        render: function() {
            if(!this._options || !this._options.length) {
                this.$el.html(this.emptyRender ? this.emptyRender() : '');
            } else {
                this._isSingle ? this.renderSingle() : this.renderMany();
                this.$input = this.$('input');
            }

            this.resize();
        },

        resize() {
            if(CRM.util.getUserAttribute('crmOtherItemNoResize')) return;
            
            if(!this.needResize) return;
            if(!this.__$fullline) {
                this.__$fullline = this.$el.closest('.f-full-line');
            }
            if(this.__$fullline.length) {
                this.__$fullline.removeClass('f-full-line');
                let $otherItem = this.$('.other-item');
                 $otherItem.width(0);
                setTimeout(() => {
                    let pl = parseInt( $otherItem.css('padding-left') || 8);
                    $otherItem.width(this.$('>.crm-field-selecttile').width() - pl);
                    this.__$fullline.addClass('f-full-line');
                })
            }
        },

        _parseOptions: function(options) {
            return _.filter(options, function(a) {
                return !(a.value === '' || a.value === void 0);
            })
        },

        renderSingle: function() {
            this._radioItem = null;
            var vv = this.defalutValue;
            var isNull = this.dataIsNull(vv);
            this.$el.html(this.template({
                value: vv,
                options: this._options,
                disabled:  vv !== 'other',
                single: this._isSingle,
                dataIsNull: isNull,
                otherLabel: this.otherLabel || ''
            }))

            if(!isNull) {
                this._radioItem = this.$('.checked');
                this._radioItem.length || (this.defalutValue = null); //传入的默认值没有对应的选项，清空默认值。
            }
        },

        _radioHandle: function(e, noTriggerChange) {
            if(noTriggerChange === void 0 && (this.__disable || this.options.isStop())) return;

            if($(e.target).hasClass('b-g-ipt')) return;
            e.preventDefault();

            var $target = $(e.currentTarget);
            $target.toggleClass('checked');
            this.defalutValue = $target.hasClass('checked') ? this._options[$target.index()].value : null;
            this.defalutValue === 'other' ? this.$input.removeAttr('disabled').attr('placeholder', $t('请输入') + (this.otherIsRequired ? '(' + $t('必填') + ')' : '')) : this.$input.attr({disabled: 'disabled', placeholder: ''});
            noTriggerChange || this.trigger('change', this.defalutValue);
            if(this._radioItem && this._radioItem[0] === e.currentTarget) return;
            this._radioItem && this._radioItem.removeClass('checked');
            this._radioItem = $target;
        },

        renderMany: function() {
            var vv = this.defalutValue;
            var isNull = !vv || !vv.length;
            this.$el.html(this.template({
                value: vv,
                options: this._options,
                disabled: !(vv && _.contains(vv, 'other')),
                single: false,
                dataIsNull: isNull,
                otherLabel: this.otherLabel || ''
            }))
        },

        _checkHandle: function(e) {
            if(this.__disable || this.options.isStop()) return;
            if($(e.target).hasClass('b-g-ipt')) return;
            e.preventDefault();

            var $target = $(e.currentTarget);
            $target.toggleClass('checked');
            var vv = this._options[$target.index()].value;
            this.defalutValue = $target.hasClass('checked') ? (this.defalutValue ? _.union([vv], this.defalutValue) : [vv]) : _.without(this.defalutValue, vv);
            _.contains(this.defalutValue || [], 'other') ? this.$input.removeAttr('disabled').attr('placeholder', $t('请输入') + (this.otherIsRequired ? '(' + $t('必填') + ')' : '')) : this.$input.attr({disabled: 'disabled', placeholder: ''});
            this.trigger('change', this.defalutValue);
        },

        getValue: function() {
            return this._isSingle ? this.defalutValue : this.defalutValue && this.defalutValue.slice(0);
        },

        getOtherLabel: function() {
            var vv = this.defalutValue;
            return (this._isSingle ? vv === 'other' : vv && _.contains(vv, 'other')) ? CRM.util.trim(this.$input.val()) : '';
        },

        dataIsNull: function(val) {
            return val === void 0 || val === '' || val === null || (_.isObject(val) && _.isEmpty(val));
        },

        _clean: function() {
            this._radioItem && (this._radioItem.removeClass('checked'), this._radioItem = null);
            this.defalutValue = null;
            this.otherLabel = '';
            this.$input && this.$input.val('').attr('disabled', 'disabled');
        },

        setValue: function(val, otherLabel, isTriggerChage) {
            let __disable = this.__disable;
            this.__disable = null;
            this.$input && this.$input.val(this.otherLabel = otherLabel || '');
            if(this._isSingle) {
                if(this.defalutValue == val) {
                    this.__disable = __disable;
                    return;
                };
                var index;
                this.dataIsNull(val) || _.find(this._options, function(a, i) {
                    if(a.value == val) {
                        index = i + '';
                        return true;
                    }
                })
                index ? this.$('.item:eq(' + (index * 1) + ')').trigger('click', !isTriggerChage) : this._clean();
            } else {
                val = _.filter(val || [], code => _.findWhere(this._options, {value: code}));
                this.defalutValue = val;
                var $items = this.$('.item').removeClass('checked');
                if(val && val.length) {
                    var options = this._options;
                    $items.each(function(index) {
                        _.contains(val, options[index].value) && $(this).addClass('checked');
                    })
                    _.contains(val, 'other') && this.$input && this.$input.removeAttr('disabled').attr('placeholder', $t('请输入') + (this.otherIsRequired ? '(' + $t('必填') + ')' : ''));
                } else {
                    this.$input && this.$input.attr({disabled: 'disabled', placeholder: ''});
                }
            }

            this.__disable = __disable;
        },

        resetOptions: function(options) {
            this._options = this._parseOptions(options);
            this.render();
        },

        validOther: function() {
            return true;
        },

        setPlaceHolder: function() {},

        disable: function() {
            this.__disable = true;
            this.$el.addClass('crm-selecttitle__disabled');
            this.$input && (this._isSingle ? this.defalutValue === 'other' : _.contains(this.defalutValue || [], 'other')) && this.$input.attr('disabled', 'disabled');
        },
        enable: function() {
            this.__disable = null;
            this.$el.removeClass('crm-selecttitle__disabled');
            this.$input && (this._isSingle ? this.defalutValue === 'other' : _.contains(this.defalutValue || [], 'other')) && this.$input.removeAttr('disabled');
        },

        destroy: function() {
            this._options = this.events = this.$input = this._radioItem = null;
            this.off(), this.stopListening();
            this.events = null;
        }
    });
});