.crm-field-selecttile {
	margin-left: -16px;
	margin-top: -3px;
	.item {
		display: inline-flex;
		margin-left: 16px;
		margin-top: 8px;
	}
	.icon_s {
		position: relative;
		width: 16px;
	    height: 16px;
	    background: var(--color-neutrals01);
	    margin-right: 8px;
		margin-top: 2px;
	    box-sizing: border-box;
	    border: 1px solid #c1c5ce;
		border-radius: 2px;
	    cursor: pointer;
		&:hover {
			border-color: var(--color-primary06);
		}
	    &:after {
	    	content: ' ';
	    	position: absolute;
	    }
	}
	.checked .icon_s {
		border-color: #ff8000;
		border-color: var(--color-primary06);
		background-color: #ff8000;
		background-color: var(--color-primary06);
	}
	.el-radion-icon {
		border-radius: 100%;
	    &:after {
	    	left: 50%;
		    top: 50%;
	    	width: 8px;
	    	height: 8px;
	    	border-radius: 100%;
		    background-color: var(--color-neutrals01);
		    transform: translate(-50%,-50%) scale(0);
		    transition: transform .15s ease-in;
	    }
	}
	.checked .el-radion-icon {
		&:after {
			transform: translate(-50%,-50%) scale(1);
		}
	}
	.el-label {
		cursor: pointer;
		flex: 1;
		line-height: 20px;
	}
	.other-item {
		display: flex;
		margin-left: 0;
		padding-left: 16px;
		box-sizing: border-box;
		align-items: center;
		min-width: 200px;
		.el-label {
			flex: none;
			margin-top: 0;
		}
		input {
			margin-left: 4px;
			flex: 1;
		}
		input[disabled] {
			background-color: var(--color-neutrals03, #f2f3f5);
			border: 1px solid #c1c5ce !important;
		}
	}

	.el-check-icon {
		border-radius: 2px;
	    &:after {
	    	left: 5px;
		    top: 2px;
		    height: 7px;
		    width: 3px;
	    	box-sizing: content-box;
		    border: 1px solid var(--color-neutrals01);
		    border-left: 0;
		    border-top: 0;
		    transform: rotate(45deg) scaleY(0);
		    transition: transform .15s ease-in .05s;
		    transform-origin: center;
	    }
	}

	.checked .el-check-icon {
		&:after {
			transform: rotate(45deg) scaleY(1);
		}
	}
}

.crm-selecttitle__disabled {
	max-width: 544px;
	.icon_s {
		background-color: #f5f7fa;
		cursor: not-allowed;
		&:hover {
			border-color: #c1c5ce;
		}
	}
	.checked .icon_s {
		border-color: var(--color-primary03);
		background-color: var(--color-primary03);
	}

	.f-disable {
		display: none;
	}
}

.crm-action-nfield-mini {
	.icon_s {
		height: 14px;
		width: 14px;
	}
	.s_muti .icon_s {
		&::after {
			left: 4px;
			top: 1px;
		}
	}
	.el-label {
		line-height: 18px;
	}
	.crm-field-selecttile {
		margin-left: -8px;

		.item {
			margin-left: 8px;
			margin-top: 4px;
		}
		.other-item {
			padding-left: 8px;
			margin-left:0;
		}
	}
}
