/**
 * 多选
 */
define(function(require, exports, module) {

    var Base = require('../base');
    var Select = require('crm-widget/select/select');
    var SelectTile = require('../selecttile/selecttile');

   return Base.extend({
        events: {
            'blur .f-ipt': '_onBlurHandle',
            'focus .f-ipt': '_onFocusHandle'
        },
        initialize: function() {
            Base.prototype.initialize.apply(this, arguments);
            this.model.on('resetOptionsByFilterOptions.' + this.fieldAttr.api_name, this.handleResetOptionsByFilterOptions, this);
            this.pn = this.fieldAttr.cascade_parent_api_name;
            _.each(this.fieldAttr.options, function(a) {
                a.name = a.label;
            })
        },

        getEmptyStr: function() {
            var pn = this.fieldAttr.cascade_parent_api_name;
            var str;
            var style = '';
            if (pn && this.model.assertFieldInLayouts(pn) && this.model.dataIsNull(this.getData(pn))) {//父没有数据
                var attr = this.getAttr(pn);
                str = $t("请先选择{{label}}", {label: attr ? _.escape(attr.label) : ''});
                style = ' style="color:#bbb"';
            } else {
                str = $t('无可选项');
                style = ' style="color:#bbb"';
            }

            return '<input' + style + ' disabled="disabled" class="b-g-ipt f-ipt" value="' + str + '"/>';
        },

        render: function() {
            var me = this;
            var options = me.getOptions();
            var values = me.parseValues(me.getData(), options);
            
            if(values && values.slice) {
                values = values.slice(0);
            }
            
            if(this.fieldAttr.is_tiled) {
                me._isTile = true;
                me.select = new SelectTile({
                    el: me.$el,
                    options: options,
                    defalutValue: values,
                    multiple: true,
                    otherIsRequired: this.otherIsRequired(),
                    otherLabel: _.contains(values, 'other') && this.getData(this.apiname + '__o'),
                    needResize: !!this.fieldAttr.full_line,
                    emptyRender: function() {
                        return me.getEmptyStr();
                    },
                    isStop() {
                        return me.model.masterIsCalculating();
                    }
                })
                me.select.on('change', function(value) {
                    me.selectChange(value && value.slice(0));
                })
            } else  {
                me.select = new Select({
                    $wrap: me.$el,
                    appendBody: true,
                    zIndex: 10000,
                    placeHolder: $t("请选择"),
                    options: options,
                    defaultVal: values,
                    multiple: true
                });

                me.select.on('change', function() {
                    me.hideError();
                })

                me.select.on('debounce.change', function(value) {
                    me.selectChange(value && value.slice(0));
                });

                //强制隐藏掉more-btn 应该是底层组件的bug
                me.$('.select-tit .more-btn.crm-hide').height(0);
            }

            me.toggleTips(options, values);
            me.setStatus();
        },

        dataEvents: function() {
            var pn = this.fieldAttr.cascade_parent_api_name;
            if (!pn || !this.model.assertFieldInLayouts(pn)) return;
            var obj = this.getAttr(pn);
            if (!obj) return;
            var _events = {};
            _events['change.' + obj.api_name] = 'parentChangeHandle';
            return _events;
        },

        /**
         *@desc 级联数据处理
         */
        parentChangeHandle: function() {
            var options = this.getOptions();
            this.select.resetOptions(options, true);
            var values = this.parseValues(this.getData(), options);
            this.setValue(values);
            this.toggleTips(options, values);
        },

        getOptions: function() {
            var options = [];
            var pn = this.pn;
            if (!pn || !this.model.assertFieldInLayouts(pn)) { // 没有依赖的父不做任何变化
                options = options.concat(this.fieldAttr.options);
            } else {
                var attr = this.getAttr(pn);
                if (attr && attr.api_name) {
                    var pv = this.getData(attr.api_name);
                    var po = _.find(attr.options, function(a) {
                        return a.value == pv;
                    })
                    if (po) {
                        var _dependOptions = po.child_options && po.child_options[0] && po.child_options[0][this.fieldAttr.arias_name || this.apiname];
                        if (!_dependOptions || !_dependOptions.length) {
                            options = [];
                        } else {
                            _.each(this.fieldAttr.options, function(a) {
                                _.contains(_dependOptions, a.value) && options.push(a);
                            }) 
                        }
                    }
                } else {
                    options = options.concat(this.fieldAttr.options);
                }
            }

            return this.parseOptions(options);
        },

        parseOptions: function(options) {
            var arr = this.getData();
            var label = arr && _.contains(arr, 'other') ? (this.getData(this.apiname + '__o') || '') : '';
            _.find(options, function(a) {
                if(a.value === 'other') {
                    a.customValue = label;
                    a.isCustom = true;
                    a.customRequired = a.is_required;
                    return true;
                }
            })

            return this._parseByFilterOptions(options || []);
        },

        _parseByFilterOptions: function(options) {
            var filterOptions = this.fieldAttr.filterOptions;
            if(filterOptions && filterOptions.length) {
                return _.filter(options, function(a) {
                    return !_.contains(filterOptions, a.value);
                })
            }
            return options;
        },


        parseValues: function(values, options) {
            if(!values) {
                values = [];
            }
            var pn = this.pn;
            if(!pn || !this.model.assertFieldInLayouts(pn)) return values;
            return _.filter(values, function(a) {
                return !!_.findWhere(options, {value: a});
            })
        },

        toggleTips: function(options, values) {
            if(!options.length && this.pn) {
                if(this.getData(this.pn)) {
                    this._isTile || this.select.setPlaceHolder($t('请选择'));
                    this.isReadonly() ? this.__disable() : this.__enable();
                } else {
                    var attr = this.getAttr(this.pn);
                    this._isTile || this.select.setPlaceHolder($t("请先选择{{label}}", {label: attr.label}));
                    this.__disable();
                }
            } else {
                values.length || (!this._isTile && this.select.setPlaceHolder($t('请选择')));
                this.isReadonly() ? this.__disable() : this.__enable();
            }
        },

        __disable: function() {
            this._optionsReadonly = true;
            this.disable();
        },

        __enable: function() {
            this._optionsReadonly = false;
            this.enable();
        },

        selectChange: function(value) {
            var me = this;
            var ov = this.getData() || [];
            var ta = _.union(ov, value).length;
            var ol = ov.length;
            var vl = value.length;

            let apiname_r = this.apiname + '__o';
            let otherLabel = _.contains(value, 'other') ? this.getOtherLabel() : '';
            let oldOtherLabel = this.getData(apiname_r);
            this.setData(otherLabel, apiname_r, true);
            this.setParam(apiname_r, otherLabel);

            if(ol !== vl || (ol && ta > ol) || otherLabel !== oldOtherLabel) {
                var noValue = value === '' || (value.length === 1 && value[0] === '');
                noValue && me.isRequired() ? me.showError() : me.hideError();
                this.setData(value, null, null, true);
            }
        },

        showError: function(error) {
            this.super.showError.apply(this, [null, error || $t("请至少选择一项")])
        },


        getOtherLabel: function() {
            if(this._isTile) return this.select.getOtherLabel();

            var obj = _.findWhere(this.fieldAttr.options, {
                value: 'other'
            })

            return (obj && obj.customValue) || '';
        },

        otherIsRequired: function() {
            var obj = _.findWhere(this.fieldAttr.options, {
                value: 'other'
            })
            return obj && obj.is_required;
        },

        getValue: function() {
            var me = this;
            if (!me.select) return [];
            var arr = me.select.getValue() || [];
            arr = _.filter(arr, function(a) {
                return a !== '';
            })

            if(!me.select.validOther()) {
                me.showError('   ');
                setTimeout(function() {
                    me.hideError();
                })

                return arr;
            }
            
            if (!arr.length) {
                me.isRequired() && me.showError();
            }

            var label = me.getOtherLabel();
            if(me.otherIsRequired() && _.contains(arr, 'other') && !label) {
                me.showError($t("请填写其他选项的备注信息"));
                return arr;
            }

            me.setParam(me.apiname + '__o', label);

            return arr;
        },

        setData: function(value, apiname, noTrigger, isBlur) {
            this.model.setData({
                apiname: apiname || this.apiname,
                value: value
            }, noTrigger, isBlur);
        },

        setValue: function(values, noTrigger, isBlur) {
            this.hideError();
            noTrigger || this.setData(values, null, null, isBlur);
            if(this._isTile) {
                this.select.setValue(values && values.slice ? values.slice() : [], this.getData(this.apiname + '__o') || '');
                return;
            }
            if(_.contains(values, 'other')) {
                this.select && this.select.destroy();
                this.render();
            } else {
                if(noTrigger) {
                    values = _.filter(values, v => this.select.getOption(v));
                }
                this.select && (this.select.setValue(values && values.slice ? values.slice() : []), this.select._setOneLineStyle && this.select._setOneLineStyle());
            }
        },

        resize: function() {
            if(this._isTile) {
                this.select && this.select.resize && this.select.resize();
                return;
            };

            this.setData(this.getOtherLabel(), this.apiname + '__o', true);
            this.select && this.select.destroy();
            this.render();
        },

        disable: function() {
            this.select && this.select.disable();
            this.super.disable.apply(this, arguments);
        },

        enable: function() {
            //TO DO 960删除此灰度
            if(!CRM.util.getUserAttribute('crmFieldOptionsReadonly') && (this._elIsReadonly || this._optionsReadonly)) return;//_elIsReadonly布局只读，_optionsReadonly父选项未选时只读

            this.select && this.select.enable();
            this.super.enable.apply(this, arguments);
        },

        _onBlurHandle: function(e) {
            var vv = this.select.getOtherLabel();
            !vv && this.otherIsRequired() ? this.showError($t('请填写其他选项的备注信息')) : this.hideError();
            var data = this.get('data');
            data[this.apiname] = null;
            data[this.apiname + '__o'] = vv;
            this.setData(this.select.getValue(), null, null, true);
        },

        _onFocusHandle: function(e) {
            this.hideError();
        },

        handleResetOptionsByFilterOptions: function() {
            var filterOptions = this.fieldAttr.filterOptions;
            if(filterOptions) {
                var options = this.getOptions();
                this.select.resetOptions(options, null, null, true);
                this.setValue(this.getData(), true);
            }
        },

        destroy: function() {
            this.select && (this.select.destroy(), (this.select = null));
            this.super.destroy.apply(this, arguments);
        }
    })
});
