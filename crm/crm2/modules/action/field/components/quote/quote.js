/**
 *@desc 引用
 *<AUTHOR>
 */
define(function(require, exports, module) {
    var Base = require('../base');
    var Attach = require('../attach/attach');
    var format = require('../../format/format');
    return Base.extend({
        events: {
            'click .img-item': 'previewImage'
        },

        render: function() {
            let qt = this.fieldAttr.quote_field_type;
            this.isQuoteImage = qt === 'image';
            this.isQuoteFile = _.contains(['file_attachment', 'big_file_attachment'], qt);
            this.setValue(this.getData());
        },

        quotechangeHandle: function(obj) {},

        disable: function() {},

        setValue: function(val) {
            var html = '';
            var qt = this.fieldAttr.quote_field_type;

            if(this.isQuoteFile) {
                this.renderQuoteFile(val);
                return
            };

            let _v = this.getData(this.apiname + '__encrypt') ? this.getData(this.apiname + '__s') || '********' : '';
            if(_v) {
                this.$el.html('<span  style="padding-top:4px;display:inline-block">' + _v + '</span>');
                return;
            }

            if(this.isQuoteImage) {
                if(val && val.length) {
                    var width = this.$el.width() / 5;
                    this._imageList = format({type: 'image'}, val);
                    html = '<div class="crm-quote-img-wrap">' + _.map(this._imageList, (a) => {
                        return this.getImageHtml(a, width);
                    }).join('') + '</div>';
                    this.$el.attr('data-type', '');
                } else {
                    this._imageList = [];
                    this.$el.attr('data-type', 'quote');
                }

                this.$el.html(html);

                return;
            }

            if(!this.dataIsNull(val)) {
                html = '<p>' + (qt === 'select_one' || qt === 'select_many' ? _.escape(val.replace(/\|/g, ',')) : format({...this.fieldAttr, disableRegionFormat: true}, val, qt === 'currency' ? {} : this.get('data'))) + '</p>';
            }

            this.$el.html(html);
        },

        renderQuoteFile(dList) {
            this.$el.parent().addClass('crm-quote-file_attachment');
            this._quoteAttach && this._quoteAttach.destroy();

            if(!dList || !dList.length) return;

            this._quoteAttach = new Attach({
                el: this.$el,
                model: this.model,
            })
            this._quoteAttach.getData = () => {
                return dList;
            }

            this._quoteAttach.fieldAttr = {...this.fieldAttr, type: this.fieldAttr.quote_field_type, is_readonly: true};

            this._quoteAttach.render();
        },

        getImageHtml(item, width) {
            return '<div style="height:' + width + 'px" class="img-item"><img src="' + item.smallUrl + '"/></div>';
        },

        previewImage: function(e) {
            if(this.useNewPreview) {
                CRM.api.preview_image({
                    isNew: true,
                    list: this._imageList,
                    index: $(e.target).index()
                })
            } else {
                CRM.api.preview_image({
                    datas: this._imageList,
                    index: $(e.target).index()
                })
            }
        },

        resize: function() {
            if(this.isQuoteImage) {
                var val = this.getData();
                val && val.length && this.setValue(val);
            }
        }
    })
})
