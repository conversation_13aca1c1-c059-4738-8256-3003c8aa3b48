/**
 *@desc 网址
 *<AUTHOR>
 */
define(function(require, exports, module) {
    var Input = require('../input/input');
    var isGrayUrlReg = !CRM.util.getUserAttribute('isGrayUrlReg');
    var regs = '';
    if(isGrayUrlReg) {
        regs = /^(((http[s]?|ftp):\/\/|www\.)[a-z0-9\.\-]+\.([a-z]{2,4})|((http[s]?|ftp):\/\/)?(([01]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))(\.(([01]?[\d]{1,2})|(2[0-4][\d])|(25[0-5]))){3})(:\d+)?(\/[a-z0-9\$\^\*\+\?\(\)\{\}\.\-_~!@#%&:;\/=<>]*)?/;
    } else {
        regs = /^(((http[s]?|ftp):\/\/|[a-zA-Z0-9]+\.)[a-z0-9.-]+\.[a-z0-9-]{2,}|((http[s]?|ftp):\/\/)?($([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|(([01]?\d{1,2}|2[0-4]\d|25[0-5])\.){3}([01]?\d{1,2}|2[0-4]\d|25[0-5])))(:\d+)?(\/[\w\$\^\*\+\?\(\)\{\}._~!@#%&:;\/=<>-]*)?/i;
    }
    module.exports = Input.extend({
    	options: {
            regs: regs,
            tips: $t("请输入正确的网址如www")
        },

        initialize: function() {
        	Input.prototype.initialize.apply(this,arguments);
        	this._onInputHandle = function() {}
        },

        _onFocusHandle: function() {
            Input.prototype._onFocusHandle.apply(this, arguments);
            this.hideError();
        }
    })
})
