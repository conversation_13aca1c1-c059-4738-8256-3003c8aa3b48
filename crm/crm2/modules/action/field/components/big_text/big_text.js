/**
 *@desc 长文本
 *<AUTHOR>
 */
 define(function(require, exports, module) {
    var Base = require('../base');
    return Base.extend({
        render() {
            let me = this;
            let {placeholder, empty_prompt, help_text, max_length, autoFocus} = me.fieldAttr;
            me.instance = FxUI.create({
            	wrapper: me.$el[0],
                template: `
                    <fx-input
                        type="textarea"
                        show-word-limit
                        clearable
                        v-model="dValue"
                        :rows="dSize.minRows"
                        :autosize="dSize"
                        :disabled="dDisable" 
                        :maxlength="dMaxlength"
                        :placeholder="dPlaceholder"
                        @blur="blurHandle" 
                        @focus="focusHandle"
                    />
                `,
                data() {
                    return {
                    	dMaxlength: max_length,
                        dPlaceholder: placeholder || empty_prompt || help_text || $t('请输入'),
                        dDisable: me.isReadonly(),
                        dSize: {
                            minRows: 10,
                            maxRows: 20
                        },
                        dValue: ''
                    }
                },
                mounted: function() {
                    this.$nextTick(() => {
                        this.dValue = me.getData();
                		autoFocus && me.$('textarea').focus();
                        me.renderAITool && me.renderAITool();
                    })
                },
                methods: {
                    blurHandle() {
                        let value = CRM.util.trim(this.dValue || '');
                        let preData = me.getData() || '';

                        me.toggleLimitError(value);

                        if(value !== preData) {
                            me.setData(value, null, null, true);
                        }
                    },
                    setValue(value) {
                        this.dValue = value;
                        this.blurHandle();
                    },
                    focusHandle() {
                        me.hideError();
                    }
                }
            });
        },

        toggleLimitError(value) {
            value || (value = '');

            let minLength = this.fieldAttr.min_length || 0;
            let length = value.length;
            if(value && length < minLength) {
                this.showError('', $t('请至少输入{{num}}个有效字符', {num: minLength}));
            } else if(length > this.fieldAttr.max_length) {
                this.showError('', $t('paas.crm.input.max', {num: this.fieldAttr.max_length}));
            } else {
                this.hideError();
            }
        },

        setData(value) {
            let data = this.get('data');
            data[this.fieldAttr.api_name + '__o'] = (value || '').slice(0, 100);
            Base.prototype.setData.apply(this, arguments);
        },

        setValue(value, noTrigger) {
            value = CRM.util.trim(value || '');

            this.instance && (this.instance.dValue = value || '');

            this.setData(value, '' , noTrigger);

            this.hideError();
        },

        getValue: function() {
            let value = CRM.util.trim(this.getData() || '');

            if(!value && this.isRequired()) {//验证必填
                this.showError();
                return;
            }

            this.toggleLimitError(value);

            let fieldName = this.fieldAttr.api_name;
			let tt = {type: 'area'};
			tt[fieldName] = value;
			tt[fieldName + '__o'] = this.getData(fieldName + '__o') || value.slice(0, 100);

            console.log(tt);

            return tt;
        },

        //根据AI返回的数据设置值
        setDataByAi(data) {
            this.instance && this.instance.setValue(data ? data.slice(0, this.fieldAttr.max_length || 2000) : '');
        },

        destroy() {
            this.instance && (this.instance.destroy(), this.instance = null);
            Base.prototype.destroy.apply(this, arguments);
        }
    })
})