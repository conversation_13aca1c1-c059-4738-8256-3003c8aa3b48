/*
 *@desc 下游负责组件 下游是不能用通用通讯录组件的，因为下游没数据，只能通过特殊接口获取
 *
 **/
 define(function (require, exports, module) {
	var Base = require('../base');

	return Base.extend({
        render() {
            this.queryUpstreamEmployee().then(upstreamEmployee => {
                this.employeeDatas = _.map(upstreamEmployee, a => ({id: a.employeeId + '', name: a.employeeName}));
      
                let tt = this._supEmployeeDatas();

                this.getWheres() ? this.renderFake(tt) : this.renderSelector();
            })
        },

        //补齐不存在的人员数据，只为能正常显示
        _supEmployeeDatas() {
            let vv = this.getData();
            let r = this.getData(this.fieldAttr.api_name + '__r');
            let tt;
            if(vv && vv.length) {//有默认值
                if(!_.find(this.employeeDatas, a => a.id == vv[0])) {//不能找到相关的值
                    this.employeeDatas.push(tt = {
                        id: vv[0],
                        name: r && r[0] ? r[0].name : '******',
                        isFake: true
                    })
                }
            }

            return tt && [tt];
        },

        getWheres() {
            let wheres = this.fieldAttr.wheres;
            return wheres && wheres.length ? wheres : null;
        },

        queryUpstreamEmployee() {
            let me = this;
            return new Promise(resolve => {
                if(me._upstreamEmployeeCache) return resolve(me._upstreamEmployeeCache);
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/PublicEmployeeObj/service/queryUpstreamPublicEmployeesWithSimpleInfo',
                    data: {
                        apiName: this.get('apiname')
                    },
                    success(res) {
                        if(res.Result.StatusCode === 0) {//成功获取数据
                            me.afterFetch(res.Value.data).then(upstreamEmployee => {
                                resolve(me._upstreamEmployeeCache = upstreamEmployee);
                            })
                        } else {
                            FxUI.Message({
                                isMiddler: true,
                                duration: 2000,
                                message: res.Result.FailureMessage,
                                type: 'error'
                            })
                            resolve();
                        }
                    },
                    error() {
                        resolve();
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        queryWheresEmployee() {
            let me = this;
            return new Promise(resolve => {
                let wheres = this.getWheres();
                if(!wheres) return resolve();

                let apiname = 'PersonnelObj';
                let allEmployees = [], offset = 0;
                CRM.util.waiting('', true);
                function _fetch() {
                    CRM.util.FHHApi({
                        url: `/EM1HNCRM/API/v1/object/${apiname}/controller/RelatedList`,
                        data: {
                            object_data: me.get('data'),
                            associated_object_describe_api_name: apiname,
                            relatedFieldName: me.fieldAttr.api_name,
                            field_projection: ['user_id'], //user_id || dept_id
                            search_query_info: JSON.stringify({
                                limit: 1000,
                                offset,
                                wheres
                            }),
                            include_associated: true,
                            include_describe: false,
                            include_layout: false,
                            trigger_info: me.model.getTriggerInfo()
                        },
                        success(res) {
                            if (res.Result.StatusCode !== 0) {
                                CRM.util.waiting(false);
                                resolve([]);
                                FxUI.Message({
                                    isMiddler: true,
                                    duration: 2000,
                                    message: res.Result.FailureMessage,
                                    type: 'error'
                                })
                                return;
                            }

                            let dataList = res.Value.dataList ;
                            Array.prototype.push.apply(allEmployees, dataList)
                            if (dataList.length > 999) {
                                offset = offset + 1000;
                                _fetch();
                            } else {
                                CRM.util.waiting(false);
                                resolve(allEmployees);
                            }
                        },
                        error() {
                            CRM.util.waiting(false);
                            resolve([]);
                        }
                    }, {
                        errorAlertModel: 1
                    })
                }

                _fetch();
            })
        },

        //渲染一个模拟的选择框
		renderFake(value) {
            let me = this;
			if (!me._fakeComp) {
				me._fakeComp = FxUI.create({
					wrapper: me.$el[0],
					template: '<fx-selector-input-label-v2 ref="fakeLabel" v-show="dVisiable" @clickSelectorInputLabel="handleClick" @deleteTag="handleClean" @clear="handleCleanAll" :addBtnLabel="dLabel"  :size="dsize" v-model="dValue"/>',
					data: () => {
						return {
							dValue: value || [],
							dsize: me.getSelectorSize(),
							dLabel: $t('请选择人员'),
							dVisiable: true
						}
					},
                    mounted() {
                        this.$nextTick(() => this.setValue(me.getData()));
                    },
					methods: {
						handleClick() {
                            if(this._rendering) return;

                            let id = 'selector_' + _.uniqueId();
                            this._rendering = true;
                            me.renderSelector({showPanel: true, id}).then(selector => {
                                this._rendering = false;
                                if(!selector) return;
                                this.dVisiable = false;
                                setTimeout(() => {
                                    selector.on('change', (ee) => {
                                        me.destroySelector();
                                        this.dVisiable = true;
                                        this.setValue(ee.member);
                                    })
                                    selector.on('hide', (tool) => {
                                        if (tool && tool.id === id) {
                                            this.dVisiable = true;
                                            me.destroySelector();
                                        }
                                    })
                                }, 200)
                            })
						},
						handleCleanAll() {
							me.setData([], null, null, true);
						},
						handleClean(tag) {
							me.setData(_.without(me.getData(), tag.id + ''), null, null, true);
						},
						setSize(size) {
							this.$refs.fakeLabel.setSize(size);
						},
                        setValue(arr) {
                            this.dValue = me.getAriasData(arr && arr[0]);
                        }
					}
				})
			}

			me.setStatus();
		},

        getAriasData(id) {
            return id ? [_.findWhere(this.employeeDatas, {id: id + ''}) || {id, name: '******'}] : []
        },

        renderSelector(opts) {
            return new Promise(resolve => {
                this.queryWheresEmployee().then(wheresEmployees => {
                    require.async('crm-widget/selector/selector', Selector => {
                        let param = _.extend({
                            $wrap: this.$el,
                            parentNode: this.fieldAttr.appendBody ? $('body') : this.$el,
                            zIndex: 10000,
                            label: $t('请选择人员'),
                            single: true,
                            tabs: [{
                                id: 'member',
                                type: 'list',
                                selectAll: false,
                                data: wheresEmployees ? _.filter(this.employeeDatas, a => {
                                    return !!_.find(wheresEmployees, b => b.user_id == a.id);
                                }) : this.employeeDatas.slice(0)
                            }],
                            defaultSelectedItems: {
                                member: this.getData() || []
                            },
                            v2Size: this.getSelectorSize()
                        }, opts)

                        let selector = new Selector(param);
                        selector.on('change', (ee) => this.selectChangeHandle(ee && ee.member || []));
                        resolve(this.selector = selector);
                    })
                })
            })
        },
		
		selectChangeHandle(value) {
            this.setData(value, null, null, true);
		},

		getValue() {
            let vv = this.getData() || [];
            if(this.isRequired() && !vv.length) {
                this.showError()
            } else {
                this.hideError()
            }

            return vv;
		},

		setValue() {
            if(!this.__setValue) {
                this.__setValue = _.debounce(() => {
                    if(!this.employeeDatas) return;
                    let tt = this._supEmployeeDatas();
                    if(this._fakeComp) {
                        this._fakeComp.setValue(this.getData());
                        return;
                    }

                    if(!this.selector) return;
                    if(tt) {
                        this.selector.updateTabData('member', this.employeeDatas.slice(0));
                        
                    }

                    this.selector.setValue('member', this.getData() || []);

                }, 100)
            }

            this.__setValue();
		},

		getSelectorSize() {
			return this.model.isMiniView() ? 'micro' : 'small';
		},

		resize() {
			var size = this.getSelectorSize();
			this.select && this.select.setSize && this.select.setSize(size);
			this._fakeComp && this._fakeComp.setSize && this._fakeComp.setSize(size);
		},

        destroySelector() {
            this.selector && this.selector.destroy && (this.selector.destroy(), this.select = null);
        },

		destroy() {
            this.destroySelector();
			this._fakeComp && this._fakeComp.destroy && (this._fakeComp.destroy(), this._fakeComp = null);
			this.super.destroy.call(this);
		},

        //*****************以下为公海特殊逻辑**********************/
        // 暂时去掉公海特殊逻辑，走通用的处理
        afterFetch(upStreamEmployees) {
            return Promise.resolve(upStreamEmployees);
            // return new Promise((resolve) => {
            //     if (!this.isHighSeasObj()) return resolve(upStreamEmployees);

            //     this.getHighSeasEmployeeList().then((res) => {
            //         let highSeasEx = res.exEmployee;
            //         let _upStreamEmployees = _.filter(upStreamEmployees, (item) => {
            //             return _.find(highSeasEx, b => b.id == item.employeeId);
            //         })
            //         resolve(_upStreamEmployees);
            //     })
            // })
        },

        isHighSeasObj() {
            return this.model.get('pageApiname') === 'HighSeasObj';
        },

        getHighSeasEmployeeList:function(){
            // 不使用缓存数据
            return CRM.util.getPoolMembersByID({
                apiName: 'AccountObj',
                poolIds: [this.model.get('hsID')]
            })
        }
	});
});
