/*
 *@desc 通讯录组件
 *
 **/
define(function (require, exports, module) {

	var util = require('crm-modules/common/util'),
		Base = require('../base');
	var crmOrgByLonginer = FS.util.getUserAttribute('crmOrgByLonginer');
	var crmSelectorStop = FS.util.getUserAttribute('crmSelectorStop');
	return Base.extend({
		//开启另一种交互 主要覆盖有筛选范围的 没法回填数据的 属性是只读的
		isFake: function () { //针对筛选
			return this.isReadonly() || this.hasWheres() || this.hasOrg() || this._hasEmptyData() || !this.isSingle() || crmOrgByLonginer;
		},

		//是否依赖多组织，目前仅只有负责人和归属部门特殊支持
		hasOrg: function () {
			var key = 'data_own_organization';
			var fieldName = this.fieldAttr.api_name;
			return _.contains(['owner', 'data_own_department'], fieldName) && ((this.get('fields') || {})[key] || _.has(this.get('data'), key));
		},

		//获取归属组织数据
		getOrgaValue: function () {
			var orga = this.getData('data_own_organization');
			return orga && (_.isArray(orga) ? orga[0] : orga);
		},

		hasWheres: function () {
			return this.fieldAttr.wheres && this.fieldAttr.wheres.length;
		},

		//主要解决编辑历史数据时，如果当前人员数据无法在通讯录找到导致回填不了，提交时会提交空值把数据清空。为了解决此类问题需要进行renderFake, 把显示和选数据分开
		_hasEmptyData: function () {
			if (util.isConnectApp()) return true; //下游

			var data = this.getSelectorDatas();
			if (!data.length) return;

			var isEmp = this.isEmployee();
			return !!_.find(data, function (id) { //可能一些历史数据停用员工，回填不了数据
				return !util[isEmp ? 'getEmployeeById' : 'getCircleById'](id);
			});
		},

		render: function () {
			var me = this;
			me.isFake() ? me.renderFake() : me.renderSelector(me.getSelectorDatas()).then(function (select) {
				select.on('change', function (ee) {
					me.selectChangeHandle(ee);
				});

				me.setStatus();
			})
		},

		getLabelTip: function () {
			return $t('请选择');
		},

		//渲染一个模拟的选择框
		renderFake: function () {
			var me = this;
			me.select && (me.select.destroy(), me.select = null);
			var vv = me.parseDatas() || [];
			if (!me._fakeComp) {
				me._fakeComp = FxUI.create({
					wrapper: me.$el[0],
					template: '<fx-selector-input-label-v2 ref="fakeLabel" v-show="dVisiable" @clickSelectorInputLabel="handleClick" @deleteTag="handleClean" @clear="handleCleanAll" :addBtnLabel="dLabel"  :size="dsize" v-model="dValue"/>',
					data: () => {
						return {
							dValue: vv,
							dsize: me.getSelectorSize(),
							dLabel: me.getLabelTip(),
							dVisiable: true
						}
					},
					methods: {
						handleClick: function () {
							me.fetchRenderParam().then(function (param) {
								var id = 'selector_' + _.uniqueId();
								param.id = id;
								param.showPanel = true;
								me.renderSelector(me.getSelectorDatas(), param).then(function (select) {
									me._fakeComp.dVisiable = false;
									var cee;
									setTimeout(function () {
										select.on('change', function (ee) {
											me._remberStopData(select);
											if (me.isSingle()) {
												me.selectChangeHandle(ee);
												me.renderFake();
											} else {
												cee = ee;
											}
										})
										select.on('hide', function (tool) {
											if (tool && tool.id === id) {
												cee && me.selectChangeHandle(cee);
												me.select && me.renderFake();
											}
										})
									}, 200)
								})
							})
						},
						handleCleanAll: function () {
							me.setData([], null, null, true);
							me.isSingle() || me.hideError();
						},
						handleClean: function (tag) {
							me.setData(_.without(me.getData(), tag.id + ''), null, null, true);
							me.isSingle() || me.hideError();
						},
						setSize: function (size) {
							this.$refs.fakeLabel.setSize(size);
						}
					}
				})
			} else {
				me._fakeComp.dVisiable = true;
				me._fakeComp.dValue = vv;
			}

			me.hideError();
			me.setStatus();
		},

		_remberStopData(select) {
			try {
				_.each(select.getSelectedItems().stopDepartment, a => {
					(this._stopDatas || (this._stopDatas = {}))[a.id] = {...a};
				})
			} catch(e) {

			}
		},

		getSelectorDatas: function () {
			var data = this.getData();
			return _.isArray(data) ? data : data ? [data] : [];
		},

		parseDatas: function () {
			var datas = this.getSelectorDatas();
			if (!datas.length) return;
			var __l = {};
			_.each(this.getData(this.fieldAttr.api_name + '__l') || [], function (a) {
				__l[a.id || a.deptId] = {
					id: a.id || a.deptId,
					name: a.name || a.deptName
				};
			})
			var result = [];
			let isEmployee = this.isEmployee();
			var fn = isEmployee ? 'getEmployeeById' : 'getCircleById';
			var _stopDatas = fn === 'getCircleById' && crmSelectorStop ? this._stopDatas || {} : {};
			_.each(datas, function (id) {
				var obj = _stopDatas[id] || __l[id] || util[fn](id);

				if(obj) {
					if(isEmployee) {
						if(!obj.profileImage) {
							let employee = util.getEmployeeById(id);
							obj.profileImage = employee ? employee.profileImage : util.getAvatarPath();
						}
					} else if(!obj.recordType) {
						obj = {...obj, recordType: 'default__c'};
					}
				} else {
					obj = {name: '--', id: id, __isDel: true}
				}

				result.push(obj);
			})

			return result;
		},

		_diffEmpDatas: function (ld, data) {
			if (ld.length !== data.length) return;

			return !_.find(ld, function (a) {
				var _id = a.id || a.deptId;
				return _id && !_.find(data, function (id) {
					return _id == id
				})
			})
		},

		isEmployee: function () {
			var type = this.fieldAttr.type;
			return type === 'employee' || type === 'employee_many';
		},

		isSingle: function () {
			return this.fieldAttr.is_single === false ? false : true;
		},

		renderSelector: function (defaulData, opts) {
			var me = this;
			opts || (opts = {});
			return new Promise(function (resolve) {
				var isEmp = me.isEmployee();
				require.async('crm-widget/selector/selector', function (Selector) {
					me.select && me.select.destroy();
					var param = {
						id: opts.id,
						$wrap: me.$el,
						parentNode: me.fieldAttr.appendBody ? $('body') : me.$el,
						zIndex: 10000,
						label: me.getLabelTip(),
						single: me.isSingle(),
						action_from: me.get('action_from') //历史遗留参数，不知道是什么作用
					}

					if(CRM.util.isZsyGrayControl() && _.contains(['owner', 'data_own_department'], me.fieldAttr.api_name)) {
						param.baseOnCurUserDepIdsApiNames = 'ObjectAdd';
					}

					if (me.model && me.model.isMiniView()) {
						param.v2Size = 'micro';
					}

					if (!param.single && me.fieldAttr.max_num) {
						param.maxNum = me.fieldAttr.max_num;
						param.maxNumBlock = true;
					}

					opts.showPanel && (param.showPanel = true);

					if (isEmp) {
						defaulData && (param.defaultSelectedItems = {
							member: defaulData.slice(0)
						})

						// if (!opts.departmentId && me.hasWheres() && FS.contacts.getAllEmployees().length > 500) {
						// 	param.tabs = [{
						// 		id: 'member',
						// 		type: 'list',
						// 		selectAll: !me.isSingle(),
						// 		data: me._getEmpList(opts.ids)
						// 	}]
						// } else {
						// 	param.member = {
						// 		departmentId: opts.departmentId,
						// 		group: false
						// 	}
						// 	opts.excluedeIds && (param.excludeItems = {
						// 		member: opts.excluedeIds
						// 	})
						// 	param.selectAll = true;
						// }
						param.member = {
							departmentId: opts.departmentId,
							group: false,
							selectAll: !me.isSingle()
						}
						opts.excluedeIds && (param.excludeItems = {
							member: opts.excluedeIds
						})
					} else {
						defaulData && (param.defaultSelectedItems = {
							group: defaulData.slice(0)
						})

						if (me.hasWheres()) { //带筛选条件的部门只能用list的交互 目前底层支持不了
							param.tabs = [{
								id: 'group',
								type: 'list',
								tabIcon: 'circle',
								selectAll: !me.isSingle(),
								data: me._getListData(opts.ids, opts.departmentId)
							}]
						} else {
							let isDeptOrg = me.fieldAttr.optional_type === 'dept_org';//可同时选部门组织
							let isOrg = me.fieldAttr.optional_type === 'organization';//只能选组织
							let st = isDeptOrg ? '' : isOrg ? 'organization' : 'department';
							param.group = {
								company: true,
								chooseType: st,
								departmentId: opts.departmentId,
								canSelectRoot: isDeptOrg || isOrg,
							}
							opts.excluedeIds && (param.excludeItems = {
								group: opts.excluedeIds
							})

							if(crmSelectorStop) {
								param.stopDepartment = st ? {
									chooseType: st
								} : true
							}
						}

					}

					let inner = function() {
						if(me.parseParam) {
							param = me.parseParam(param);
						}
	
						resolve(me.select = new Selector(param));
					}

					let stopIds = param.stopDepartment && _.filter(defaulData, id => !util.getCircleById(id));
					if(stopIds && stopIds.length) {//有停用的部门数据
						util.waiting($t('数据加载中') + '...', true);
						FS.contacts.getCirclesByIdsAsync(stopIds).then(stopDatas => {
							util.waiting(false, true);
							if(!me.options) return;

							param.stopDepartment = {
								chooseType: param.group.chooseType,
								data: stopDatas || []
							}

							param.defaultSelectedItems.stopDepartment = stopIds;
							param.defaultSelectedItems.group = _.filter(defaulData, id => !_.find(stopIds, sid => sid == id));

							inner();
						})
					} else {
						inner();
					}
				})
			})
		},

		_getEmpList: function (ids) {
			var list = [];
			_.each(ids, function (id) {
				var item = util.getEmployeeById(id);
				item && list.push(item);
			})

			return list;
		},

		_getListData: function (ids, departmentId) {
			var list = [];
			_.each(ids, function (id) {
				var item = util.getCircleById(id);
				item && list.push(item);
			})

			if(crmOrgByLonginer && departmentId) {
				let dd = FS.contacts.getAllCircles({departmentId: departmentId * 1});
                list = _.filter(list, a => a.id == departmentId || !!_.find(dd, b => b.id == a.id));
			}

			return list;
		},

		_num2String: function (list) {
			return _.map(list, function (id) {
				return id + ''
			})
		},

		selectChangeHandle: function (ee) {
			var data = this._num2String(this.isEmployee() ? ee.member : !crmSelectorStop ? ee.group : [...ee.group || [], ...ee.stopDepartment || []]);
			this.__log(data, 1);
			if (this.isOverNum(data.length)) {
				util.alert($t('{{label}}数量不能超过{{num}}个', {
					label: this.fieldAttr.label,
					num: this.fieldAttr.max_num
				}));
				data.length = this.fieldAttr.max_num;
			}
			this.hideError();

			this._diffData(data) && this.setData(data, null, null, true);
		},

		_diffData: function (nd) {
			var od = this.getData() || [];
			nd || (nd = []);
			if (!od.length && !nd.length) return;
			if (od.length !== nd.length) return true;

			return _.union(od, nd).length !== od.length;
		},

		fetchRenderParam: function () {
			var me = this;
			return new Promise(function (resolve) {
				var param = {};
				if (me.hasOrg()) {
					param.departmentId = me.getOrgaValue();
				}

				if(!param.departmentId && crmOrgByLonginer) {//根据当前登录人显示归属组织及数据
					param.departmentId = FS.contacts.getMainOrganizationIdByEmpId(CRM.curEmpId) || '';
				}

				me.fetchExcludeDatas().then(function (excluedeIds, ids) {
					param.excluedeIds = excluedeIds;
					param.ids = ids;
					resolve(param);
				})
			})
		},

		fetchExcludeDatas: function () {
			var me = this;
			return {
				then: function (resolve) {
					var attr = me.fieldAttr;
					if (!me.hasWheres()) return resolve();
					util.waiting($t('数据加载中') + '...');
					var isEmp = me.isEmployee();
					var apiName = isEmp ? 'PersonnelObj' : 'DepartmentObj';
					var dataType = isEmp ? 'user_id' : 'dept_id';
					var ids = [];
					var offset = 0;
					var excluedeIds = [];

					var isDeptOrg = attr.optional_type === 'dept_org';//可同时选部门组织
					var isOrg = attr.optional_type === 'organization';//只能选组织

					function _fetch() {
						util.FHHApi({
							url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/RelatedList',
							data: me.beforeRequest({
								object_data: me.get('data'),
								associated_object_describe_api_name: apiName,
								relatedFieldName: attr.api_name,
								field_projection: [dataType], //user_id || dept_id
								include_associated: true,
								search_query_info: JSON.stringify({
									limit: 1000,
									offset: offset,
									filters: isEmp ? void 0 : [{
										field_name: 'record_type',
										field_values: me.getFieldValue ? me.getFieldValue() : isDeptOrg ? ['organization__c', 'default__c'] :  [!isOrg ? 'default__c' : 'organization__c'],
										operator: isDeptOrg ? 'HASANYOF' : 'EQ'
									}],
									wheres: attr.wheres
								}),
								include_describe: false,
								include_layout: false,
								trigger_info: me.model.getTriggerInfo()
							}),
							success: function (res) {
								if (res.Result.StatusCode !== 0) {
									util.waiting(false);
									util.alert(res.Result.FailureMessage);
									return;
								}

								ids = ids.concat(_.pluck(res.Value.dataList, dataType));

								if (res.Value && res.Value.dataList && res.Value.dataList.length > 999) {
									offset = offset + 1000;
									_fetch();
								} else {
									util.waiting(false);
									var datas = FS.contacts[isEmp ? 'getAllEmployees' : 'getAllCircles']();
									_.each(datas, function (a) {
										_.contains(ids, a.id + '') || excluedeIds.push(a.id + '');
									})

									resolve(excluedeIds, ids);
								}
							}
						}, {
							errorAlertModel: 1
						})
					}

					_fetch();
				}
			}
		},

		isOverNum: function (num) {
			return !this.isSingle() && num > this.fieldAttr.max_num;
		},

		getValue: function () {
			var ids = [];
			if (this.select) {
				_.each(this.select.getValue(), function (arr) {
					[].push.apply(ids, arr);
				})
			} else {
				var v = this.getData();
				//增加获取到的值为非数组情况的容错处理
				ids = v ? (_.isArray(v) ? v : [v]) : [];
			}

			if (this.isRequired() && !ids.length) {
				this.showError();
			} else if (this.isOverNum(ids.length)) {
				this.showError(null, $t('{{label}}数量不能超过{{num}}个', {
					label: this.fieldAttr.label,
					num: this.fieldAttr.max_num
				}));
			} else {
				this.hideError();
			}

			this.__log(ids, 2);

			return this._num2String(ids);
		},

		__log: function (ids, type) {
			try {
				if (this.fieldAttr.is_single && ids.length > 1) {
					if (this.select && this.select._selector && this.select._selector.selectorInput) {
						try {
							var v1 = this.select.getValue() || {};
							// var v2 = this.select._selector.getValue() || {};
							var v3 = this.select._selector.selectorInput.getValue() || {};
							var v4 = this.select._selector.selectorInput.selectedVal || {};
							var v5 = this.select._selector.selectorInput.selectedItems || [];
							var v6 = this.select._selector.selectorInput.selectedItemsMap || {};
              var v7 = this.select._selector.selectorInput.defaultSelectedItems
							CRM.util.uploadLog('form', 'field', {
								eventId: 'nselectorerror',
								eventData: {
									type: type,
									label: this.fieldAttr.label,
                  ids: JSON.stringify(ids),
									v1: JSON.stringify(v1),
									// v2: JSON.stringify(v2),
									v3: JSON.stringify(v3),
									v4: JSON.stringify(v4),
									v5: JSON.stringify(v5),
									v6: JSON.stringify(v6),
									v7: JSON.stringify(v7),
									single: this.select._selector.selectorInput.single,
                  'FS.theme': FS.theme
								}
							});
						} catch (error) {
							CRM.util.uploadLog('form', 'field', {
								eventId: 'nselectorerror',
								eventData: {
									type: type,
									label: this.fieldAttr.label
								}
							});
						}
					} else {
						CRM.util.uploadLog('form', 'field', {
							eventId: 'nfakeselectorerror',
							eventData: {
								type: type,
								label: this.fieldAttr.label,
								member: ids
							}
						});
					}
				}
			} catch (e) {}
		},

		setValue: function (arr) {
			this.hideError(this.$el);
			if (this.isFake()) {
				this.renderFake();
			} else {
				this.select && this.select.setValue(this.isEmployee() ? 'member' : 'group', arr);
			}
		},

		getSelectorSize: function () {
			return this.model.isMiniView() ? 'micro' : 'small';
		},

		resize: function () {
			var size = this.getSelectorSize();
			this.select && this.select.setSize && this.select.setSize(size);
			this._fakeComp && this._fakeComp.setSize && this._fakeComp.setSize(size);
		},

		beforeRequest(param) {
			let beforeRequest = this.get('beforeSelectorRequest');
			return beforeRequest ? beforeRequest(param) : param;
		},

		destroy: function () {
			this.select && this.select.destroy && (this.select.destroy(), this.select = null);
			this._fakeComp && this._fakeComp.destroy && (this._fakeComp.destroy(), this._fakeComp = null);
			this._stopDatas = null;
			this.super.destroy.call(this);
		}
	});
});
