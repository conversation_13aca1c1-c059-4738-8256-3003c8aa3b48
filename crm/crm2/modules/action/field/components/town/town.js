/**
 *@desc 乡镇组件
 *<AUTHOR>
 */
define(function(require, exports, module) {

    var Select = require('../selectone/selectone');

    return Select.extend({
        render: function() {
            var me = this;
            var vv = me.getData();
            Select.prototype.render.apply(me, arguments);
            var pv = me.getData(me.fieldAttr.cascade_parent_api_name);
            pv && me._resetTownOptions(pv, function() {
                vv && me.select.setValue(vv);
            })
        },

        _resetTownOptions: function(code, cb) {
            var me = this;
            me.model.asyncGetTownOptions(code).then(function(res) {
                me.select.resetOptions(res || []);
                cb && cb();
            })
        },

        parentChangeHandle: function(obj) {
            var me = this;
            me._optionsReadonly = false;
            if(obj && obj.value) {
                me._resetTownOptions(obj.value, function() {
                    me.enable();
                    me.setValue('');
                });
            } else {
                Select.prototype.parentChangeHandle.apply(me, arguments);
            }
        }
    });
});
