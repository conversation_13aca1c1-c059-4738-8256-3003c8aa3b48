define(function (require, exports, module) {
	const shortkey = require('./shortkey');
	return Backbone.View.extend({
		options: {
			batchButtons: [],
			singleButtons: [],
			title: '',
			total: 0,
			showRemoveBtn: true,
			isEdit: true,
			outNum: 4,
			eventKey: '',
			showFullBtn: true
		},

		initialize: function (opts) {
			this.listenTo(this.model, 'action:' + opts.eventKey, this.actionHandle);
			let outNum = this.options.singleButtons && this.options.singleButtons.outNum;
			outNum && (this.options.outNum = outNum);
			this.render();
		},

		render: function () {
			var me = this;
			var opts = this.options;
			me.titComp = FxUI.create({
				wrapper: this.$el[0],
				template: '<div class="crm-md20-tit">' +
					'<div :class="cLeftCls">' +
					'<div v-if="!checkNum" class="left-uncheck">' +
					'<span v-if="title" class="h-tit">{{title}}</span>' +
					'<span v-if="cTotal" class="h-num">{{cTotal}}</span>' +
					'<span class="h-error">{{cErrorNum}}</span>' +
					'</div>' +
					'<div v-if="checkNum" class="left-check">' +
					'<span class="h-tit">{{cCheckNum}}</span>' +
					'<span v-show="cLeftBatchBtns.length" class="split-line left-btn-wrap">' +
					'<span @click="handleBtn(btn, $event)" v-for="btn in cLeftBatchBtns" :class="getCls(btn)">{{btn.label}}</span>' +
					'</span>' +
					'</div>' +
					'</div>' +
					'<div class="h-mid" ref="mid"></div>' +
					'<div class="h-right">' +
					'<div class="single-wrap">' +
					'<template v-for="btn in cSingleButtons">' +
					'<fx-dropdown trigger="click" @command="handleCommand" v-if="btn._more">' +
					'<fx-button v-show="btn._more.length" size="micro"><span class="fx-icon-more"></span></fx-button>' +
					'<fx-dropdown-menu class="md20-dropdown__xxx" slot="dropdown">' +
					'<fx-dropdown-item :key="mbtn.action + index" :command="mbtn" v-for="(mbtn, index) in btn._more">{{mbtn.label}}</fx-dropdown-item>' +
					'</fx-dropdown-menu>' +
					'</fx-dropdown>' +
					'<span @click="handleBtn(btn, $event)" :data-action="btn.action" v-else :class="getCls(btn)">{{btn.label}}</span>' +
					'</template>' +
					'</div>' +
					'<div class="t-icon-wrap">' +
					'<span></span>' +
					'<span @click="handleViewShortKey" v-if="showShortKeyBtn" data-title="' + $t('查看{{title}}', {title: $t('快捷键')}) + '" data-pos="top" class="fx-icon-shortcuts crm-ui-title"></span>' +
					'<span @click="handleFull(1, $event)" v-if="showFullBtn" :data-title="fullBtnLabel" data-pos="top" :class="fullBtnClass" class="crm-ui-title"></span>' +
					'<span v-show="showRemoveBtn && isEdit && cCloseStatus" @click="closeHandle" class="h-del fx-icon-close"></span>' +
					'</div>' +
					'</div>' +
					'</div>',
				data: function () {
					return {
						batchButtons: me.parseBatchButtons(opts.batchButtons),
						singleButtons: me.parseSingleButtons(opts.singleButtons, opts.outNum),
						showRemoveBtn: opts.showRemoveBtn && _.findWhere(opts.batchButtons || [], {action: 'delSelectedHandle'}),
						isEdit: opts.isEdit,
						title: opts.title,
						total: opts.total,
						errorNum: '',
						checkNum: '',
						outNum: opts.outNum,
						dFilterAction: [],
						showShortKeyBtn: opts.showShortKeyBtn,
						showFullBtn: opts.showFullBtn,
						fullBtnClass: 'fx-icon-fullscreen',
						fullBtnLabel: $t('全屏显示')
					}
				},
				mounted() {
					this.$nextTick(() => {
						me.slotInstance = opts.titSlot && opts.titSlot(this.$refs.mid);
					})
				},
				methods: {
					getCls: function (item) {
						return 'hand-btn' + (item.class ? ' ' + item.class : '');
					},
					handleCommand: function (btn) {
						me.triggerEvent(btn);
					},
					handleBtn: function (btn, $event) {
						me.triggerEvent(btn, $event);
					},
					closeHandle: function () {
						me.triggerEvent({action: 'removeRecordTypeHandle'});
					},
					handleFull(btn, $event) {
						me.triggerEvent({action: '_fullHandle'}, $event);
					},
					handleViewShortKey() {
						shortkey();
					}
				},
				computed: {
					cLeftCls: function() {
						return 'h-left' + (this.title ? '' : ' hide-title');
					},
					cTotal: function () {
						return (this.total || !this.title) ? $t('共{{num}}条', {num: this.total || 0}) : '';
					},
					cErrorNum: function () {
						return this.errorNum ? $t('paas.crm.md.error', {num: this.errorNum}) : '';
					},
					cCheckNum: function () {
						return this.checkNum ? $t('已选数据:{{num}}条', {num: this.checkNum}) : '';
					},
					cLeftBatchBtns: function() {
						return _.filter(this.batchButtons, (a) => {
							return !_.contains(this.dFilterAction, a.action);
						})
					},
					cSingleButtons: function() {
						let buttons = _.filter(this.singleButtons, a => {
							return !(_.contains(this.dFilterAction, a.action) || (a.data && _.contains(this.dFilterAction, a.data.lookup_field_name)));
						})
						if(buttons.length > this.outNum) {
							buttons.push({
								label: '',
								action: '',
								_more: buttons.splice(this.outNum)
							})
						}
						return buttons;
					},
					cCloseStatus: function() {
						return !_.contains(this.dFilterAction, 'delSelectedHandle');
					}
				}
			})
		},

		triggerEvent: function (btn, $evnet) {
			let allButtons = [];
			_.each(this.titComp.cSingleButtons, a => {
				if(a._more) {
					_.each(a._more, b => {
						if(b.action === 'singleAddHandle' || b.action === 'importExcelHandle') {
							allButtons.push(b);
						} else if(b.action === 'batchAddHandle') {
							allButtons.push(b.data);
						}
					})
				} else {
					allButtons.push(a)
				}
			})
			let data = _.extend({
				allButtons
			}, btn.data);

			this.model.trigger('action:' + this.options.eventKey, btn.action, data, $evnet);
		},

		parseBatchButtons: function (buttons) {
			buttons = _.map(buttons, function (a) {
				return {action: a.action, label: a.label, data: a.data, isShow: true}
			});
			buttons.push({
				action: 'cancelSelectedHandle',
				label: $t('取消'),
				class: buttons.length ? 'split-line' : ''
			})

			return buttons;
		},

		parseSingleButtons: function (buttons, outNum) {
			return _.map(buttons, function (a) {
				return {action: a.action, label: a.label, data: a.data}
			});
		},

		numChangeHandle: function (num) {
			this.titComp.total = num;
		},

		updateErrorHandle: function (errorNum) {
			this.titComp.errorNum = errorNum;
		},

		actionHandle: function (action, data) {
			this[action] && this[action](data);
		},

		checkHandle: function (data) {
			this.titComp.checkNum = data && data.length;
		},

		fullStatusChangelHandle: function (isFull) {
			// var tt = _.findWhere(this.titComp.cSingleButtons, {action: '_fullHandle'});
			// if (!tt) return;

			if(!this.options.showFullBtn) return;
		
			this.titComp.fullBtnClass = isFull ? 'fx-icon-fullscreen-exit' : 'fx-icon-fullscreen';
			this.titComp.fullBtnLabel = isFull ? $t('退出全屏') : $t('全屏显示');
		},

		toggleTitCloseHandle: function (status) {
			//this.titComp.showRemoveBtn = !!status;
		},

		// {
	  	//   batchEdit: false|true  tableBatchEditHandle
	  	//   batchDel: false|true,  delSelectedHandle 
	  	//   batchCopy: false|true, copySelectedHandle
	  	//   addOne: false|true     singleAddHandle
	  	//}
		toggleTitButtonsHandle: function(status) {
			_.each(status, (v, k) => {
				if(!v) {
					this.titComp.dFilterAction.push(k);
				} else {
					this.titComp.dFilterAction = _.filter(this.titComp.dFilterAction, a => {return a !== k});
				}
			})
		},

		getSingleButtonsHandle: function(opts) {
			let btns = [];
			_.map(this.titComp.cSingleButtons, a => {
				if(a.action === 'singleAddHandle') {
					btns.push(a);
					return;
				}
				if(a.data && a.data.lookup_field_name) {
					a.callBack || btns.push(a);
					return;
				}
				_.each(a._more, b => {
					if(b.action === 'singleAddHandle') {
						btns.push(b);
						return;
					}
					if(b.data && b.data.lookup_field_name) {
						b.callBack || btns.push(b);
					}
				})
			})

			opts.callback(btns);
		},

		assertHasSingleAddRight(opts) {
			let tt = _.find(this.titComp ? this.titComp.cSingleButtons : this.options.singleButtons, a => {
				return a.action === 'singleAddHandle' || _.findWhere(a._more, {action: 'singleAddHandle'});
			})
			opts.callback(!!tt);
		},

		destroy: function () {
			this.stopListening(), this.off();
			this.titComp && this.titComp.destroy();

			try {
				this.slotInstance && this.slotInstance.destroy && this.slotInstance.destroy();
			} catch(e) {};

			this.$el.html('');
			this.titComp = this.options = null;
		}
	})
})
