define(function (require, exports, module) {
	var Tit = require('./tit');
	var NavBar = require('./navbar');
	var Table = require('./table');
	var LayoutItem = require('./layoutitem');
	return Backbone.View.extend({
		initialize: function (opts) {
			this.listenTo(this.model, 'form.data.update.complete', () => {
				_.each(this.slots, slot => slot && slot.update && slot.update());
			});
		},
		render: function () {
			var me = this;
			me._layoutItems = {};
			var opts = me.options;
			var tabs = [];
			var more2 = opts.detailObjects.length > 1;
			var ars = {};
			me.$el.html('<div class="crm-md20__wrapper"><div class="md20-nav__wrapper"></div><div class="md20-layout__wrapper"></div><div class="md20-add__wrapper"></div></div');

			me.$itemWrapper = me.$('.md20-layout__wrapper');
			_.each(opts.detailObjects, function (a, index) {
				tabs.push({
					value: a.apiName,
					label: a.label,
					active: a.active
				})
				ars[a.apiName] = [];
				a.headerSlot && me.renderSlot(a.headerSlot, a.apiName, index > 0);
				if(a.layoutSlot) {
					me.renderLayoutSlot(a);
				} else {
					let showRTLabel = a.components.length > 1;
					_.each(a.components, function (b, cindex) {
						me.renderLayoutItem({
							apiName: a.apiName,
							recordType: b.recordType,
							objectDescribe: a.objectDescribe,
							layout: b.layout,
							batchButtons: b.batchButtons,
							singleButtons: b.singleButtons,
							operateButtons: b.operateButtons,
							isEdit: b.isEdit,
							title: more2 ? (showRTLabel ? b.label : '') : (
								showRTLabel ? a.label + '-' + b.label : a.label
							),
							Table: b.Table || a.Table || Table,
							ActionTask: b.ActionTask,
							Tit: b.Tit || a.Tit || Tit,
							LayoutItem: b.LayoutItem || a.LayoutItem || LayoutItem,
							isShow: !index,
							model: me.model,
							datas: b.datas,
							showFullBtn: !more2,
							showRemoveBtn: a.components.length > 1,
							fieldRenders: b.fieldRenders,
							$eventBus: opts.$eventBus,
							isFirst: !cindex
						});
						ars[a.apiName].push({
							label: b.label,
							value: b.recordType,
							isShow: false
						})
					})
				}

				a.footerSlot && me.renderSlot(a.footerSlot, a.apiName, index > 0);
			})

			me.renderNav(tabs);
			me.renderAdd(ars, tabs[0].value);
		},

		renderSlot(slots, apiName, isHide) {
			var $wrapper = $(`<div ${isHide ? 'style="display:none"' : ''} class="slot-wrapper crm-clearfix clearfix slot__${apiName}"></div>`);
			this.$itemWrapper.append($wrapper);
			this.slots || (this.slots = []);
			try {
				_.each(slots, slot => this.slots.push(slot($wrapper[0])));
			} catch(e) {console.error(e)}
		},

		renderAdd: function (ars, apiName) {
			var me = this;
			me.addRecordTypeComp = FxUI.create({
				wrapper: this.$('.md20-add__wrapper')[0],
				template: '<div v-if="cRtDatas.length" class="rt-dropdown">' +
					'<fx-dropdown trigger="click" @command="handleCommand">' +
					'<span class="el-dropdown-link">+' + $t("添加业务类型") + '</span>' +
					'<fx-dropdown-menu slot="dropdown">' +
					'<fx-dropdown-item v-for="item in cRtDatas" :key="item.command" :command="item">{{item.label}}</fx-dropdown-item>' +
					'</fx-dropdown-menu>' +
					'</fx-dropdown>' +
					'</div>',
				data: function () {
					return {
						apiName: apiName,
						rtDatas: ars,
						filterRts: []
					}
				},
				methods: {
					handleCommand: function (item) {
						var tt = _.findWhere(this.rtDatas[this.apiName], {value: item.command});
						tt.isShow = false;
						// this.rtDatas = _.filter(this.rtDatas, function (a) {
						// 	return a.command !== item.command;
						// })
						me.__removeForceHide(this.apiName, item.command);
					},
					showRt: function (apiName, rt) {
						var tt = _.findWhere(this.rtDatas[apiName], {value: rt});
						tt.isShow = true;
					}
				},

				computed: {
					cRtDatas() {
						var rts = [];
						_.each(this.rtDatas[this.apiName],  (a) => {
							if(_.contains(this.filterRts, this.apiName + a.value)) return;
							a.isShow && rts.push({
								label: a.label,
								command: a.value
							})
						})
						return rts;
					}
				}
			})
		},

		renderNav: function (tabs) {
			var me = this;
			var navBar = new NavBar({
				el: me.$('.md20-nav__wrapper'),
				tabs: tabs
			})
			navBar.render();

			navBar.on('tabClick', function (apiName) {
				me.showLayoutItem(apiName);
				me.addRecordTypeComp.apiName = apiName;
				_.each(me._layoutItems, function (item, k) {
					k !== apiName && me.hideLayoutItem(k);
				})
				me.model.trigger('mdnav:change', apiName);
			});
			navBar.on('fullClick', function () {
				me.fullHandle();
			});

			me.navBar = navBar;
		},

		//隐藏或显示指定的从对象
		//param: {sdfsaf__cc: {hidden: true}, aa__c: {hidden: false}}
		toggleMDStatus: function (param) {
			var me = this;
			me.__mdStatus || (me.__mdStatus = {});
			_.each(param, function (obj, apiName) {
				me.__mdStatus[apiName] = !!obj.hidden;
				me.navBar[obj.hidden ? 'hideItem' : 'showItem'](apiName);
			})

			me.navBar.setActiveItem();
		},

		//隐藏或显示指定的从对象业务类型
		//param: {sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
		toggleRecordTypeStatus: function(param) {
			var tt = this._layoutItems;
			var rintance = this.addRecordTypeComp;
			var activeApiName = this.navBar.getCurActive().value;
			if(!this.__recordTypsStaus) {
				this.__recordTypsStaus = param || {};
			}

			_.each(param, (obj, apiName) => {
				var tmp = this.__recordTypsStaus[apiName] || (this.__recordTypsStaus[apiName] = {});
				_.each(obj, (item, rt) => {
					var tc = _.findWhere(tt[apiName], {__recordType: rt});
					if(tc) {
						tmp[rt] = item;
						var ft = apiName + rt;
						if(item.hidden) {
							activeApiName === apiName && tc.hide();
							rintance.filterRts.push(ft);
						} else {
							if(activeApiName === apiName) {
								this._forceWrapper && this._forceWrapper[apiName] && this._forceWrapper[apiName][rt] ? rintance.handleCommand({command: rt}) : tc.show();
							}
							rintance.filterRts = _.filter(rintance.filterRts, function(a) {
								return a !== ft;
							})
						}
					}
				})
			})
		},

		switchMDItem: function (apiName) {
			this.navBar.setActiveItem(apiName);
		},

		showLayoutItem: function (apiName) {
			var tt = this.__recordTypsStaus;
			_.each(this._layoutItems[apiName], function (a) {
				if(tt && tt[apiName] && tt[apiName][a.__recordType] && tt[apiName][a.__recordType].hidden) return;
				a.show();
			})
			this.$('.slot-wrapper').hide();
			this.$('.slot__' + apiName).show();
		},

		hideLayoutItem: function (apiName) {
			_.each(this._layoutItems[apiName], function (a) {
				a.hide();
			})
		},

		renderLayoutSlot(opts) {
			let me = this;
			let {layoutSlot, apiName, components} = opts;
			let $wrapper = $('<div class="md20-layoutitem__wrapper"></div>');
			this.$itemWrapper.append($wrapper);

			_.each(components, b => {
				let recordType = b.recordType;
				this.listenTo(this.model,  this.model.getMDActionEventKey(apiName, recordType), (action, arg1, arg2) => {
					let fn = {
						mdupdateHandle: 'update',
						setFieldAttrHandle: 'setFieldAttr',
						setRowFieldAttrHandle: 'setRowFieldAttr',
						toggleTitButtonsHandle: 'toggleTitButtons',
						setTableBtnsHandle: 'setTableBtns',
						toggleCheckedHandle: 'toggleChecked'
					}[action]
					layoutItem[fn] && layoutItem[fn](arg1, arg2, {apiName, recordType});
				});
				this.listenTo(this.model, 'getCheckedDatasHandle', cb => {
					cb(layoutItem.getCheckedDatas());
				})
			})
			opts.onCheckChange = () => {
				_.each(this.slots, slot => slot.checkChange && slot.checkChange());
			}
			let _slotItem = layoutSlot($wrapper, opts);
			let layoutItem = {
				hide() {
					$wrapper.hide();
				},
				show() {
					$wrapper.show();
				},
				showError: function () {
					if (this._fmerror) return;
					this._fmerror = $('<span style="height:1px;" class="fm-error"></span>'); //class必须为fm-error方便上层验证错误 高度为1方便滚动到头部
					$wrapper.prepend(this._fmerror);
				},
				hideError: function () {
					if (this._fmerror) {
						this._fmerror.remove();
						this._fmerror = null;
					}
				},

				getCheckedDatas() {
					return [];
				},

				//希望业务实现的方法
				validate() {},
				setFieldAttr() {},
				setRowFieldAttr() {},
				toggleTitButtons(){},
				setTableBtns() {},
				destroy() {},
				..._slotItem,

				//获取数据先验证
				getValue() {
					this.validate() ? this.showError() : this.hideError();
					return _slotItem.getValue();
				},
				update(datas, a, opts) {
					_slotItem.update && _slotItem.update(datas);
					if(_slotItem.getValue) {//数据同步
						let dataList = _slotItem.getValue();
						let tt = {};
						_.each(dataList, a => {
							let ss = (tt[a.object_describe_api_name] || (tt[a.object_describe_api_name] = {}));
							(ss[a.record_type] || (ss[a.record_type] = [])).push(a);
						})
						_.each(tt, (item, objApiName) => {
							_.each(item, (dataList, recordType) => {
								me.model.newUpdateMDData(dataList, objApiName, recordType);
							})
						})
					} else {
						me.model.updateMDData(datas, opts.apiName, opts.recordType);
					}
				},
				toggleChecked(rowIds, isChecked) {
					_slotItem.checkDetailsRow && _slotItem.checkDetailsRow(rowIds, isChecked);
				}
			}

			if (!this._layoutItems[apiName]) {
				this._layoutItems[apiName] = [];
			}

			this._layoutItems[apiName].push(layoutItem);
		},

		renderLayoutItem: function (opts) {
			var me = this;
			var $wrapper = $('<div class="md20-layoutitem__wrapper"></div>');
			me.$itemWrapper.append($wrapper);

			opts.el = $wrapper;
			var layoutItem = new opts.LayoutItem(opts);
			layoutItem.on('remove', function () {
				me.__forceHide(opts.apiName, opts.recordType, layoutItem, $wrapper);
			})
			layoutItem.on('full', function () {
				me.fullHandle();
			})
			layoutItem.on('updateerror', function (errorNum) {
				me.__updateErrorHandle(opts.apiName, opts.recordType, errorNum);

			})

			if (!me._layoutItems[opts.apiName]) {
				me._layoutItems[opts.apiName] = [];
			}

			layoutItem.__recordType = opts.recordType;
			layoutItem.render();

			me._layoutItems[opts.apiName].push(layoutItem);
		},


		__forceHide: function (apiName, rt, layoutItem, $wrapper) {
			var list = this._layoutItems[apiName];

			list.splice(_.indexOf(list, layoutItem), 1);
			list.push(layoutItem);

			this.$itemWrapper.append($wrapper);//追加到最后
			$wrapper.addClass('force__hide');//强制隐藏掉

			var obj = this._forceWrapper || (this._forceWrapper = {});
			(this._forceWrapper[apiName] || (this._forceWrapper[apiName] = {}))[rt] = $wrapper;

			var me = this;
			if (_.keys(obj).length === list.length - 1) {
				_.each(list, function (a) {
					me.model.triggerMDActionEvent(apiName, a.__recordType, 'toggleTitCloseHandle', false);
				})
			}

			this.addRecordTypeComp.showRt(apiName, rt);
		},

		__removeForceHide: function (apiName, rt) {
			var obj = this._forceWrapper[apiName];
			obj[rt].removeClass('force__hide');
			var me = this;
			_.each(this._layoutItems[apiName], function (a) {
				me.model.triggerMDActionEvent(apiName, a.__recordType, 'toggleTitCloseHandle', true);
			})
			delete obj[rt];
		},

		__updateErrorHandle: function (apiName, recordType, errorNum) {
			this.navBar && this.navBar.toggleErrorStatus && this.navBar.toggleErrorStatus(apiName, !!errorNum);
			this._errorStatus || (this._errorStatus = {});
			(this._errorStatus[apiName] || (this._errorStatus[apiName] = {}))[recordType] = !!errorNum;
			_.find(this._errorStatus, function (obj) {
				return _.find(obj, function (v) {
					return !!v
				});
			}) ? this._showError() : this._hideError();

			errorNum && this.__showMDHideError(apiName, recordType);
			

			this.options.$eventBus && this.options.$eventBus.trigger('__updateError', apiName, recordType, errorNum);
		},

		__showMDHideError(apiName, recordType) {
			var tt = this.__recordTypsStaus;
			var mdStatus = this.__mdStatus;
			if(tt && tt[apiName] && tt[apiName][recordType] && tt[apiName][recordType].hidden || (mdStatus && mdStatus[apiName])) {
				this.model.showMDHideError(apiName, recordType);
			}
		},

		fullHandle: function () {
			var me = this;
			var flag = me._isFull = !me._isFull;
			var $scroll;
			me.model.trigger('getScroll', (scroll) => {
				if(scroll && scroll.length) {
					$scroll = scroll;
				}
			})

			if(me.isNormalFull === void 0) {
				me.isNormalFull = me.model.get('isNormalDialog') || me.$el.closest('.crm-c-dialog').length;
			}

			if(me.isNormalFull) {
				if(flag && $scroll && $scroll.scrollTop) {
					me._scrollTop = $scroll.scrollTop();
				}

				me.$el[flag ? 'addClass' : 'removeClass']('md20-layout__full');

				if(!flag && $scroll && $scroll.scrollTop) {
					$scroll.scrollTop(me._scrollTop);
					delete me._scrollTop;
				}
			} else {
				me.$parent || (me.$parent = me.$el.parent());

				if(flag) {//全屏
					$scroll && (me._scrollTop = $scroll.scrollTop());
					let zIndex = me.model.get('zIndex');
					let nZIndex =  _.max([zIndex || 0, 1000]);
					me._oldZIndex = zIndex;
					me.model.set('zIndex', nZIndex);
	
					me.fulldialog = CRM.util.fullDialog({
						el: me.$el,
						zIndex: nZIndex,
						css: {
							width: 'auto',
							padding: '0 8px',
							overflow: 'auto'
						}
					})
				} else {
					me.$el.appendTo(me.$parent);
					me.fulldialog.destroy();
					me.fulldialog = null;
					me.model.set('zIndex', me._oldZIndex);
	
					if ($scroll) {
						$scroll.scrollTop(me._scrollTop);
						delete me._scrollTop;
					}
				}
			}
			
			_.each(this.options.detailObjects, function (a) {//通知其它组件更新
				_.each(a.components, function (b) {
					me.model.triggerMDActionEvent(a.apiName, b.recordType, 'fullStatusChangelHandle', me._isFull);
				})
			})

			me.navBar.setFullStatus(me._isFull);
		},

		getValue: function () {
			var details = {};
			_.each(this._layoutItems, function (a, apiName) {
				var arr = [];
				_.each(a, function (b) {
					[].push.apply(arr, b.getValue() || []);
				})
				details[apiName] = arr;
			})

			return details;
		},

		_showError: function () {
			if (this._fmerror) return;
			this._fmerror = $('<span style="height:1px;" class="fm-error"></span>'); //class必须为fm-error方便上层验证错误 高度为1方便滚动到头部
			this.$el.prepend(this._fmerror);
		},

		_hideError: function () {
			if (this._fmerror) {
				this._fmerror.remove();
				this._fmerror = null;
			}
		},

		destroy: function () {
			this.stopListening(), this.off();
			this.options = null;
			_.each(this._layoutItems, function (arr) {
				_.each(arr, function (a) {
					a.destroy && a.destroy();
					a = null;
				})
			})
			_.each(this.slots, function(slot) {
				try {
					slot && slot.destroy && slot.destroy();
				} catch(e) {}
			})
			this._fmerror && this._fmerror.remove();
			this._layoutItems = this._forceWrapper = this.slots = null;
		}
	})
})
