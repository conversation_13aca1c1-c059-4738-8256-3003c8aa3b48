/**
 *@desc 760版本之后，从对象可以分布到表单的任意位置，不再是一个md,可能会有多个md
 * 鉴于此 md20提供了一个伪md组件，去统一管理分布在各处的md组件，对于上层表单来说，虽然md被分散在各处，但可以提供统一入口进行维护
 *<AUTHOR>
 *@date 2021/03/23
 */
//!!!!!!!!!! 严禁修改本文件，如需改动请知会作者 !!!!!!!!!!//
//!!!!!!!!!! 严禁修改本文件，如需改动请知会作者 !!!!!!!!!!//
//!!!!!!!!!! 严禁修改本文件，如需改动请知会作者 !!!!!!!!!!//
define(function(require, exports, module) {
	var Base = require('../base');
	var Table = require('./table');
	var TreeTable = require('./treetable');
	var Layout = require('./layout');
	var ActionTask = require('./task/actiontask');

	const ACTION = require('./layout_v2/action');
	const LayoutV2 = require('./layout_v2/layout_v2');
	const HANDLE = require('./layout_v2/handle');
	return Base.extend({
		isMd20: true,
		modelEvents: {
			//主对象数据计算时，计算了从对象的数据，监听该事件更新从对象数据
			'mdData:change': 'mdDataChange',
			'mdnav:change': 'handleMDNavChange',
			'mdTableRenderComplete': 'handleMDTableComplete',
			'mdSingleEdit': 'handleSingleEdit',
			'scrollToDetailsByRowId': 'scrollToDetailsByRowId'
		},

		handleMDNavChange(objApiName) {
			let mdSwitchCallBack = this.model.get('mdSwitchCallBack');
			mdSwitchCallBack && mdSwitchCallBack(objApiName);
		},

		_createEventBus() {
			return {
				on: (actions, objectApiName, recordType) => {
					_.each(actions, (handle, action) => {
						let _evs = (this._evs || (this._evs = {}));
						let eventKey = action + objectApiName + recordType;
						(_evs[eventKey] || (_evs[eventKey] = [])).push(handle);
					})
				},
				trigger: (action, objectApiName, recordType, data, data1) => {
					this._handleAction(action, objectApiName, recordType, data, data1);
				}
			}
		},

		//统一处理从对象上面的所有action
		_handleAction(action, objectApiName, recordType, data, data1) {
			if(this.model.masterIsCalculating()) return;
			let selfAction = (this.actionCallBacks[objectApiName + recordType] || {})[action];
			if(this.__editing && (selfAction || _.contains([ACTION.CELLEDITBEFORE, ACTION.SINGLEADD, ACTION.IMPORTEXCEL, ACTION.BATCHADD, ACTION.SINGLE_EDIT, ACTION.CLONE, ACTION.DELETE, ACTION.CLEAN_VALUE, ACTION.BATCHDELETE, ACTION.BATCHDELETE, ACTION.READONLY_CELLEDIT], action))) return;

			if(selfAction) {
				selfAction(data.data, {objectApiName, objApiName: objectApiName, recordType, $event: data.$event, buttons: data.allButtons});
				return;
			}

			let eventKey = action + objectApiName + recordType;
			_.each(this._evs[eventKey], handle => {
				handle(data, objectApiName, recordType, data1);
			})
		},

		...HANDLE,

		_bindEvents(objectApiName, recordType) {
			//兼容老的事件，后期去掉
			this.listenTo(this.model, this.model.getMDActionEventKey(objectApiName, recordType), (action, param1, param2) => {
				let aris = {
					toggleTitButtonsHandle: ACTION.TOGGLE_COMMON_BUTTONS,
					setTableBtnsHandle: ACTION.TOGGLE_SINGLE_BUTTONS,
					setRowFieldAttrHandle: ACTION.TOGGLE_SINGLE_FIELD_STATUS,
					setFieldAttrHandle: ACTION.TOGGLE_COMMON_FIELD_STATUS,
					mdupdateHandle: ACTION.UPDATE,
					numChangeHandle: ACTION.TOTALCHANGE,
					cancelSelectedHandle: ACTION.CANCELSELECTED,
					removeRecordTypeHandle : ACTION.REMOVERECORDTYPE,
					srcollToHandle: ACTION.SCROLL_TO_TR,
					toggleCheckedHandle: 'toggleCheckedHandle',
					twinkleTrHandle: 'twinkleTrHandle'
				}[action]
				aris && this.$eventBus.trigger(aris, objectApiName, recordType, param1, param2);
			});

			this.$eventBus.on({
				//某个从表格渲染完成
				[ACTION.RENDERCOMPLETE]: (...args) => this.handleMDTableComplete(...args),
				[ACTION.SINGLEADD]: (...args) => this.handleSingleAdd(...args),
				[ACTION.IMPORTEXCEL]: (...args) => this.handleImportExcel(...args),
				[ACTION.DELETE]: (...args) => this.handleDelete(...args),
				[ACTION.BATCHDELETE]: (...args) => this.handleBatchDelete(...args),
				[ACTION.CLONE]: (...args) => this.handleClone(...args),
				[ACTION.BATCHCLONE]: (...args) => this.handleBatchClone(...args),
				[ACTION.BATCHEDIT]: (...args) => this.handleBatchEdit(...args),
				[ACTION.BATCHVIEWIMAGE]: (...args) => this.handleBatchViewImage(...args),
				[ACTION.DELETEALL]: (...args) => this.handleDeleteAll(...args),
				[ACTION.CELLEDITBEFORE]: (...args) => this.handleEditBefore(...args),
				[ACTION.CELLEDITAFTER]: (...args) => this.handleEditAfter(...args),
				[ACTION.BATCHADD]: (...args) => this.handleBatchAdd(...args),
				[ACTION.BATCH_EDIT_BEFORE]: (...args) => this.handleBatchEditBefore(...args),
				[ACTION.BATCH_EDIT_AFTER]: (...args) => this.handleBatchEditAfter(...args),
				[ACTION.CLEAN_VALUE]: (...args) => this.handleCleanValue(...args),
				[ACTION.SINGLE_EDIT]: (...args) => this.handleSingleEdit(...args),
				[ACTION.READONLY_CELLEDIT]: (...args) => this.handleReadOnlyCellEdit(...args),
				[ACTION.UPDATE_ERRORNUM]: (...args) => this._emitDataError(...args)
			}, objectApiName, recordType);
		},

		render() {
			let me = this, initConfig = [];

			me.$eventBus = me._createEventBus();
			me.actionCallBacks = {};

			if (me.model.get('isFormLayout')) { //走新的从对象混排布局
				me.options.$mdMixWrap && me.options.$mdMixWrap.each(function () {
					initConfig.push({
						el: $(this),
						apiNames: $(this).attr('data-apinames').split('|')
					})
				})
			} else { //还走老的
				initConfig.push({
					el: me.$el,
					apiNames: me.model.getMDApiNames()
				})
			}

			me.model._mdRendering = true;
			
			me.fetchConfigs(initConfig).then((configs) => {
				me.model.runMDRenderBeforeService().then((rst = {}) => {
					if(!me.model) return;
					
					me._handleMDRenderBeforeResult(rst, initConfig)
					me.renderLayout(configs);
					_.each(rst, a => a.detailRecordType && me.toggleRecordTypeStatus(a.detailRecordType));
				})
			})
		},

		fetchFieldMapping(initConfig) {
			let apiNames = [];
			_.each(initConfig, a => {
				_.each(a.apiNames, describeApiName => {
					apiNames.push({describeApiName})
				})
			})
			return new Promise(resolve => {
				CRM.util.findRecordFieldMapping(apiNames).then((fiedMapping) => {
					this.model.set('mdFieldMapping', fiedMapping);
				}).finally(() => {
					resolve();
				})
			})
		},

		fetchConfigs(initConfig) {
			let me = this;
			return new Promise(resolve => {
				let $wait = this.model.commonWaiting($t('数据加载中') + '...');
				Promise.all([this.model.fetchAllMDDatas(), this.model.fetchAllTDWidths(), this.fetchFieldMapping(initConfig)], this.model.checkAiUserLicense()).then(res => {
					$wait.remove();
					let widthConfigs = res[1] || {};
					let datas = res[0] || {};
					let ttt = {};
					_.each(initConfig, (a) => {
						a.detailObjects = _.map(a.apiNames, (apiName) => {
							let dob = me.getDetailObject(apiName);
							let components = [];
							let tList = datas[apiName];
							
							_.each(dob.layoutList, (b) => {
								if (b.not_match && !(tList && _.findWhere(tList, {record_type: b.record_type}))) {//不匹配也没有数据的
									return;
								}

								components.push({
									recordType: b.record_type,
									label: _.findWhere(dob.objectDescribe.fields.record_type.options, {value: b.record_type}).label,
									batchButtons: me.getBatchButtons(apiName, b, dob.related_list_name),
									singleButtons: me.getSingleButtons(b, dob.objectDescribe.fields),
									operateButtons: me.getOperateButtons(apiName, b),
									Table: Table,
									layout: b.detail_layout,
									widthConfig: widthConfigs && widthConfigs[apiName],
									isEdit: !b.not_match
								})
							})
							ttt[apiName] = _.pluck(components, 'recordType');

							return {
								apiName: apiName,
								label: dob.related_list_label || dob.objectDescribe.display_name,
								objectDescribe: dob.objectDescribe,
								components: components
							}
						})
					})

					let allTablesData = this.model.initMDData(datas, ttt);
					_.each(initConfig, a => {
						_.each(a.detailObjects, b => {
							_.each(b.components, c => {
								c.datas = (allTablesData[b.apiName] || {})[c.recordType] || [];
							})
						})
					})

					resolve(initConfig);
				})
			})
		},

		_handleMDRenderBeforeResult(rst = {}, initConfig) {
			let editUUID = 'edit_' + _.uniqueId();
			_.each(initConfig, config => {
				let {detailObjects} = config;
				if(!detailObjects || !detailObjects.length) return;
				_.each(detailObjects, a => {
					let {fields} = a.objectDescribe;
					let objectApiName = a.apiName;
					let ra = rst[a.apiName] || {};
					let crm2kColumnRenders = (this.getColumnRenders() || {})[a.apiName];

					//布局头部 底部自定义组件插槽
					a.headerSlot = ra.headerSlot;
		 			a.footerSlot = ra.footerSlot;
					a.layoutSlot = rst[a.apiName] && rst[a.apiName].layoutSlot;


					this._cacheDatasErrorCallBack(a.apiName, ra.datasErrorCallBack);

					_.each(a.components, b => {
						let actionCallBacks = {}, tt = ra.buttons || {};
						if(tt[b.recordType]) {
							tt = {...tt,...tt[b.recordType]}
						}

						tt.del && (b.singleButtons = _.filter(b.singleButtons, c => {
							return !_.find(tt.del, d => {
								return d === c.name || (c.data && d === c.data.lookup_field_name);
							})
						}))

						tt.retain && (b.singleButtons = _.filter(b.singleButtons, c => {
							return _.find(tt.retain, d => {
								return d === c.name || (c.data && d === c.data.lookup_field_name);
							})
						}))

						tt.add && _.each(tt.add, btn => {
							b.singleButtons.unshift(btn);
							if(!btn.action) btn.action = 'md_action_' + CRM.util.getUUId();
							actionCallBacks[btn.action] = btn.callBack;
						})

						tt.reset && _.each(tt.reset, (btn, action) => {
							let tss =_.find(b.singleButtons, c => {
								return action === c.name || (c.data && action === c.data.lookup_field_name);
							})
							if(tss) {
								_.extend(tss, btn);
								btn.callBack && (actionCallBacks[tss.action] = btn.callBack);
							}
						})

						b.datas = (b.datas || []).slice(0);

						tt = ra.batchButtons || {};
						tt.del && (b.batchButtons = _.filter(b.batchButtons, c => {
							return !_.contains(tt.del, c.action);
						}))
						tt.retain && (b.batchButtons = _.filter(b.batchButtons, c => {
							return _.contains(tt.retain, c.action);
						}))
						tt.add && _.each(tt.add, btn => {
							b.batchButtons.unshift(btn);
							if(!btn.action) btn.action = 'md_action_' + CRM.util.getUUId();
							actionCallBacks[btn.action] = btn.callBack;
						})

						_.each((rst[a.apiName + b.recordType] && rst[a.apiName + b.recordType].fakeFields) || ra.fakeFields, fakeField => {
							_.find(b.layout.components, comp => {
								return _.find(comp.field_section, section => {
									return _.find(section.form_fields, (ff, index) => {
										if(ff.field_name === fakeField.field_name) {
											let tfields  = _.map(fakeField.fields, fitem => {
												let type = fitem.type || 'text';
												let fieldName = fitem.api_name;
												if(fieldName) {
													let hfd = fields[fieldName] || {};
													fields[fieldName] = _.extend(hfd, {
														...fitem,
														type: type,
														api_name: fieldName,
														is_fake: true,
														is_fixed: fitem.fixed || hfd.is_fixed,
														titleHtml: fitem.label_html || hfd.label_html,
														actionCallBacks: null,
														render: (cellValue, cellType, trData) => {
															return this._excCacheFieldRender(objectApiName, fieldName, cellValue, cellType, trData);
														}
													})

													this._cacheFieldRender(objectApiName, fieldName, fitem.render);
												}

												_.extend(actionCallBacks, fitem.actionCallBacks);

												return {
													render_type: type,
													is_readonly: fitem.is_readonly,
													is_required: fitem.is_required,
													field_name: fieldName,
													__reRender: !!fitem.render
												}
											})

											tfields = _.filter(tfields, a => !_.findWhere(section.form_fields, {field_name: a.field_name}));
											if(tfields.length) {
												tfields.unshift(fakeField.pos === 'before' ? index : index + 1, 0);
												Array.prototype.splice.apply(section.form_fields, tfields);
											}
											
											return true;
										}
									})
								})
							})
						})

						if(ra.hideFields) {
							let hideFields = _.union(ra.hideFields.common || [], ra.hideFields[b.recordType] || []);
							if(hideFields.length) {
								_.each(b.layout.components, comp => {
									_.each(comp.field_section, section => {
										section.form_fields = _.filter(section.form_fields, ff => !_.contains(hideFields, ff.field_name));
									})
								})
							}
						}

						if(ra.resetFields) {
							_.each(b.layout.components, comp => {
								_.each(comp.field_section, section => {
									_.each(section.form_fields, a => {
										let att = ra.resetFields[a.field_name];
										if(att) {
											att.type && (a.render_type = att.type);
											att.is_readonly !== void 0 && (a.is_readonly = att.is_readonly);
											att.is_required !== void 0 && (a.is_required = att.is_required);
											_.extend(fields[a.field_name], att);
										}
									})
								})
							})
						}

						let fconfig = ra.fieldsAttr;
						fconfig && (fconfig = _.extend({}, fconfig, fconfig[b.recordType]));
						fconfig && _.each(b.layout.components, comp => {
							_.each(comp.field_section, section => {
								_.each(section.form_fields, ff => {
									if(fconfig[ff.field_name]) {
										_.extend(ff, fconfig[ff.field_name]);
									}
								})
							})
						})

						let operateBtns = (rst[a.apiName + b.recordType] && rst[a.apiName + b.recordType].operateBtns) || ra.operateBtns;
						let operateBtnsDepends = (rst[a.apiName + b.recordType] && rst[a.apiName + b.recordType].operateBtnsDepends) || ra.operateBtnsDepends;
						b.fieldRenders = {
							beforeOperate(btns, trData) {
								btns || (btns =  b.operateButtons)
								if(!operateBtns) return btns;
								let retains, dels = [], adds = [];
								_.each(operateBtns, fn => {
									let btnConfig = fn(trData) || {};
									btnConfig.retain !== void 0 && (retains = btnConfig.retain);
									[].push.apply(adds, _.filter(btnConfig.add || [], addBtn => !_.findWhere(adds, {action: addBtn.action})));

									_.each(btnConfig.reset, (attr, action) => {
										_.extend(_.findWhere(btns, {action}) || {}, attr);
									})
									
									dels = _.union(dels, btnConfig.del || []);
								})
								if(retains !== void 0) {
									return retains ? _.filter(btns, btn => _.contains(retains, btn.action)) : [];
								};
								adds = _.filter(adds, btn => !(_.contains(dels, btn.action) || _.findWhere(btns, {action: btn.action})));
								btns = _.filter(btns, btn => !_.contains(dels, btn.action));

								_.each(adds, btn => {
									if(!btn.action) btn.action = 'md_action_' + CRM.util.getUUId();
									actionCallBacks[btn.action] = btn.callBack;
								})

								return btns.concat(adds);
							},
							parseData: ra.parseData ? (list) => {
								_.each(ra.parseData, fn => {
									list = fn(list);
								})
								return list;
							} : void 0,
							editUUID,
							filterBatchEditFields: ra.filterBatchEditFields,
							titSlot: ra.titSlot && ra.titSlot[b.recordType],
							tableFooterSlot: ra.tableFooterSlot && (ra.tableFooterSlot[b.recordType] || ra.tableFooterSlot.common),
							tableLeftSlot: ra.tableLeftSlot,
							columnRenders: ra.columnRenders && ({...ra.columnRenders.common,...ra.columnRenders[b.recordType]}),
							tableOptions: ra.tableOptions,
							columnWidthConfig: {...b.widthConfig, ...ra.columnWidthConfig},
							operateBtnsDepends: operateBtnsDepends,

							crm2kColumnRenders,

							beforeMDUpdate: ra.beforeMDUpdate,

							cardConfig: ra.cardConfig
						}
						b.ActionTask = ActionTask.extend({
							beforeActionHandle(action, param = {}, $event) {
								if(actionCallBacks[action]) {
									actionCallBacks[action](param.data, {
										objectApiName: a.apiName,
										objApiName: a.apiName,
										recordType: b.recordType,
										buttons: param.allButtons,
										$event
									});
									return false;
								}
							}
						})

						b.treeConfig = ra.treeConfig;

						ra.treeConfig && (b.Table = TreeTable.extend({getConfig: function() {return ra.treeConfig}}));

						this.actionCallBacks[a.apiName + b.recordType] = actionCallBacks;
					})
				})
			})
		},

		renderLayout(initConfig) {
			this.totalComps = 1;
			this.__renderStartTime = new Date().getTime();
			let MyLayout = CRM.util.getUserAttribute('crmFieldLayoutV2') ? LayoutV2 : Layout;

			this.layouts = _.map(initConfig, config => {
				config.model = this.model;
				config.$eventBus = this.$eventBus;
				_.each(config.detailObjects, a => {
					_.each(a.components, b => {
						a.layoutSlot || this.totalComps++;
						this._bindEvents(a.apiName, b.recordType);
					})
				})

				let layout = new MyLayout(config);
				layout.render();
				return layout;
			})

			this.handleMDTableComplete();
		},

		handleMDTableComplete() {
			if(--this.totalComps) return;
			this.model.runMDRenderAftereService().then(() => {
				this.model.set('_mdRenderCostTime', new Date().getTime() - this.__renderStartTime);
				this.model.trigger('mdrendercomplete');
				this.model._mdRendering = false;
				this.model._clearMasterEndParams();
			})
		},

		getDetailObject(apiname) {
			return _.find(this.model.get('detailObjectList'), a => a.objectDescribe.api_name === apiname);
		},

		getSingleButtons(dl, fields) {
			if (dl.not_match) return [];
			var buttons;
			if (this.model.get('isFormLayout')) {
				let aris = {Single_Add: 'singleAddHandle', Import_Excel: 'importExcelHandle'}
				buttons = _.map(dl.detail_layout.buttons, function (d) {
					return {
						name: d.action,
						action: aris[d.action] || 'batchAddHandle',
						label: d.label,
						data: d
					}
				})
			} else {
				buttons = [{
					name: 'Single_Add',
					label: $t('添加一行'),
					action: 'singleAddHandle'
				}]

				_.each(dl.detail_layout.components, function (a) {
					_.each(a.field_section, function (b) {
						_.each(b.form_fields, function (c) {
							if (c.render_type === 'object_reference' && !c.is_readonly) {
								var t = fields[c.field_name];
								buttons.push({
									label: $t('从{{label}}批量添加', {label: t.label}),
									action: 'batchAddHandle',
									data: {
										lookup_field_name: c.field_name
									}
								})
							}
						})
					})
				})

				if (buttons.length > 1 && dl.support_manual_add === false) {
					buttons.shift();
				}

				if(FS.crmUtil.getUserAttribute('isGrayExcelCopy') && buttons.length) {
					buttons.unshift({
						name: 'Import_Excel',
						action: 'importExcelHandle',
						label: $t('从本地Excel导入')
					})
				}
			}

			return buttons;
		},

		getBatchButtons(apiName, b, related_list_name) {
			var buttons = [];
			if (!b.not_match) {
				if(this.model.get('isFormLayout')) {
					_.each(b.batchButtons, function(a) {
						var action = {Delete: 'delSelectedHandle', 'Batch_Edit': 'tableBatchEditHandle', 'Clone': 'copySelectedHandle'}[a.action];
						action && buttons.push({
							action: action,
							label: a.label
						})
					});
				} else {
					if(this.model.getMDHandleRight('batchEdit', apiName, b.record_type, related_list_name) !== false) {
						buttons.push({
							action: 'tableBatchEditHandle',
							label: $t('批量编辑')
						})
					}
					if (this.model.getMDHandleRight('del', apiName, b.record_type, related_list_name) !== false) {
						buttons.push({
							action: 'delSelectedHandle',
							label: $t('删除')
						})
					}
					if (this.model.getMDHandleRight('copy', apiName, b.record_type, related_list_name) !== false) {
						buttons.push({
							action: 'copySelectedHandle',
							label: $t('复制')
						})
					}
				}
				
			}

			if (CRM.ea === 'cqdzt888' && _.contains(['object_dUMoS__c', 'object_r4lh5__c', 'object_KUD8s__c', 'object_8cKxu__c'], apiName)) { //德庄特殊需求 批量预览从对象图片 2020/06/28  wangj
				buttons.push({
					action: 'batchViewImageHandle',
					label: $t('el.upload.preview', null, '查看图片')
				})
			} 

			if (CRM.ea === 'dzjt6688' && apiName === 'object_wSWPD__') { //德庄特殊需求 批量预览从对象图片 2022/01/14  wangj
				buttons.push({
					action: 'batchViewImageHandle',
					label: $t('el.upload.preview', null, '查看图片')
				})
			} 

			return buttons;
		},

		getOperateButtons(apiName, b) {
			var buttons = [];
			if (!b.not_match) {
				if(this.model.get('isFormLayout')) {
					_.each(b.singleButtons, function(a) {
						var action = {Delete: 'delRowHandle', Clone: 'copyRowHandle', Tile: ACTION.SINGLE_EDIT, Insert: 'insertHandle'}[a.action];
						if(a.action === 'Insert') a.label = '';
						action && buttons.push({
							action: action,
							label: a.label
						})
					});
				} else {
					if (this.model.getMDHandleRight('del', apiName, b.record_type) !== false) {
						buttons.push({
							action: 'delRowHandle',
							label: $t('删除')
						})
					}
					if (this.model.getMDHandleRight('copy', apiName, b.record_type) !== false) {
						buttons.push({
							action: 'copyRowHandle',
							label: $t('复制')
						})
					}
				}
			}

			return buttons;
		},

		//为crm2k留的钩子，因时间原因暂且提供这样一个方法，不建议覆盖使用
		getColumnRenders() {
			// return {
			// 	object_c1YBm__c: {
			// 		field_ufYgj__c: {
			// 			render(formatVal, trData) {
			// 				return `<span style="color:red">${formatVal}</span>`;
			// 			}
			// 		},
			// 		field_v10hw__c: {
			// 			depend_fields: ['field_ufYgj__c'],
			// 			render(formatVal, trData) {
			// 				if(trData.field_ufYgj__c > 100) {
			// 					return `<span style="color:green">${formatVal}</span>`;
			// 				} else {
			// 					return `<span style="color:blue">${formatVal}</span>`;
			// 				}
			// 			}
			// 		}
			// 	}
			// }
		},

		_cacheFieldRender(objectApiName, fieldName, fn) {
			let t = (this.__cacheFieldRenders || (this.__cacheFieldRenders = {}));
			(t[objectApiName + fieldName] || (t[objectApiName + fieldName] = [])).push(fn);
		},

		_excCacheFieldRender(objectApiName, fieldName, cellValue, cellType, trData) {
			_.each((this.__cacheFieldRenders || {})[objectApiName + fieldName], fn => {
				cellValue = fn(cellValue, cellType, trData);
			})
			return cellValue;
		},

		_cacheDatasErrorCallBack(objectApiName, fn) {
			if(!fn) return;
			(this.__cacheDatasErrorCallBack || (this.__cacheDatasErrorCallBack = {}))[objectApiName] = fn;
		},

		_emitDataError(errorNum, objectApiName, recordType) {
			let t = this._datasError || (this._datasError = {});
			(t[objectApiName] || (t[objectApiName] = {}))[recordType] = errorNum || 0;

			if(!this.__emitDataError) {
				this.__emitDataError = _.debounce(() => {
					_.each(this.__cacheDatasErrorCallBack, (callBack, k) => {
						callBack(this._datasError[k] || {});
					})
				}, 100)
			}

			this.__emitDataError();
		},

		//兼容老地方调用
		resize() {},
		switchMDByIndex() {},
		td2ddm() {
			return this.model.getDDM();
		},
		
		//左侧导航切换从对象
		switchMDByApiName(apiName) {
			_.each(this.layouts, layout => layout.switchMDItem(apiName));
		},

		scrollToDetailsByRowId(rowId) {
			let item = this.model.getMDDataByRowId(rowId);
			if(!item) return;

			this.switchMDByApiName(item.object_describe_api_name);
			this.model.triggerMDActionEvent(item.object_describe_api_name, item.record_type, 'twinkleTrHandle', rowId);
		},

		getValue() {
			let details = {};
			_.each(this.layouts, layout => _.extend(details, layout.getValue()));
			//需要过滤掉仅用于辅助显示的数据
			return this.model.filterFakeDetails(details);
		},

		//根据UI函数的返回结果更新从对象
		updateByUIEventData(res, opts) {
			this.model.hackUpdateByUIEventData(res, opts && opts._indexToRowId);
		},

		//待老对象替换完成之后优化
		mdDataChange(res, serverRes) {
			this.model.hackMdDataChange(res, serverRes && serverRes._indexToRowId);
		},

		//隐藏或显示指定的从对象
		//param: {sdfsaf__cc: {hidden: true}, aa__c: {hidden: false}}
		toggleMDStatus(param) {
			_.each(this.layouts, layout => layout.toggleMDStatus(param));
			this.model.trigger('togglemdstatus', param);
		},

		//隐藏或显示指定的从对象业务类型
		//param: {sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
		toggleRecordTypeStatus(param) {
			let attrs = {};
			_.each(param, (item, key) => {
				if(item.all) {
					delete param[key];
					attrs[key] = item.all;
				}
			})
			_.isEmpty(attrs) || this.toggleMDStatus(attrs);

			_.each(this.layouts, layout => layout.toggleRecordTypeStatus(param));
		},

		destroy() {
			_.each(this.layouts, layout => {
				layout.destroy && layout.destroy();
				layout = null;
			})
			this.layouts = this._evs = this.__cacheFieldRenders = this.__cacheDatasErrorCallBack = null;

			try {
				this.singleEditComp && (this.singleEditComp.destroy(), this.singleEditComp = null);
			} catch(e) {}

			Base.prototype.destroy.apply(this, arguments);
		}
	})
})