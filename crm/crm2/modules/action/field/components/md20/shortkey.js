define(function (require, exports, module) {
    return function () {
        let strs = '在填写数据的过程中，可以通过方向键快速切换。#左右切换当前仅支持输入型字段，对于不支持的字段会跳过。#切换到上一行#切换到下一行，如果当前是最后一行则自动新增一行#切换到左边的单元格#切换到右边单元格';
        let tips = $t('crm.field.md.newshortkey', null, strs).split('#')
        let dialog = FxUI.create({
            template: `
                <fx-dialog @close="handleClose" zIndex="10000" style="top:0px;right:0px" width="374px" :modal="false" :visible="dVisible" sliderPanel modal appendToBody title="${$t('快捷键')}">
                    <h3 style="font-size:14px;color:var(--color-neutrals19, #333);font-weight:500;line-height:20px;">${$t('paas.cursor.move')}</h3>
                    <p style="font-size:12px;color:var(--color-neutrals11, #bbb);font-weight:400;line-height:18px;margin-bottom:12px;">${tips[1]}</p>
                    <div style="display:flex;justify-content:space-between;gap:32px;margin-top:8px;" v-for="item in dTips" :key="item.icon">
                        <span style="font-size:16px;padding:5px 8px; height:26px;box-sizing:border-box;border-radius:6px;background:var(--color-special01,#f2f4fb)" :class="item.icon"></span>
                        <span style="color:var(--color-neutrals15);font-weight:400;font-size:14px;text-align:right;line-height:20px;">{{item.tip}}</span>
                    </div>
                </fx-dialog>
            `,
            data() {
                return {
                    dVisible: true,
                    dTips: [{
                        tip: tips[2],
                        icon: 'el-icon-top'
                    }, {
                        tip: tips[3],
                        icon: 'el-icon-bottom'
                    },{
                        tip: tips[4],
                        icon: 'el-icon-back'
                    },{
                        tip: tips[5],
                        icon: 'el-icon-right'
                    }]
                }
            },
            methods: {
                handleClose() {
                    this.dVisible = false;
                    dialog.destroy();
                }
            }
        })
    }
})