define(function(require) {
    const ACTION = require('./action');
    const ParseExcelTask = require('../task/parseexceltask');
    return {
        handleSingleAdd(opts, objApiName, recordType) {
            this.model.runMDAddOneService({
                objApiName,
                recordType,
                finallyCallback(error) {
                    !error && opts.callback && opts.callback();
                },
                insertedRowId: opts.insertedRowId
            })
        },

        handleSingleEdit(opts, objApiName, recordType) {
            let superRoot = this;
            let ps = superRoot.model.getPluginService({
                dataIndex: [opts.data.rowId],
                objApiName,
                recordType
            });
            
            ps.run('md.tile.before').then((rst = {}) => {
                if(rst.StatusCode) return;

                let dob = superRoot.getDetailObject(objApiName);

                let {hideClose, pluginFields, imageSize, hideFields, viewRenderBefore = () => Promise.resolve()} = rst.Value || {};

                superRoot.singleEditComp = FxUI.create({
                    template: `
                        <fx-dialog
                            :z-index="${superRoot.model.get('zIndex') || 2000}"
                            class="crm-md-singleedit"
                            ref="dialog1"
                            title="${$t('编辑') + ' ' + dob.related_list_label}"
                            :width="dWidth + 'px'"
                            :visible="dialogVisible1"
                            appendToBody
                            sliderPanel
                            
                            :close-on-click-outside="false"
                            :close-on-click-modal="false"
                            :close-on-press-escape="false"
                            :show-close="!dHideClose"
                            @close="handleClose"
                        >
                            <div style="position:absolute;top:2px;right:48px;height:auto" class="action-field-wrap">
                                <div style="border:none;padding:0"  class="field-wrap-tit">
                                    <div class="view-switch j-view-switch" style="display: flex;">
                                        <div title="${$t('舒适')}" :class="dType === 'single' ? 'active' : ''" class="si s-single" @click="handleSwitch('single')"><span></span></div>
                                        <div title="${$t('舒适')}" :class="dType === 'large' ? 'active' : ''" class="si s-large" @click="handleSwitch('large')"><span></span><span></span></div>
                                        <div title="${$t('紧凑')}" :class="dType === 'mid' ? 'active' : ''" class="si s-mid" @click="handleSwitch('mid')"><span></span><span></span><span></span></div>
                                        <div title="${$t('紧凑')}" :class="dType === 'mini' ? 'active' : ''" class="si s-mini " @click="handleSwitch('mini')"><span></span><span></span><span></span><span></span></div>
                                    </div>
                                </div>
                            </div>
                            <div class="md-edit-wrapper md-a">
                                <div ref="viewContent" class="view-content"/>
                                <div class="footer">
                                    <fx-button class="b-s" data-action="pre" v-if="!maskTip" @click="handlePre($event)" size="small">${$t('crm.md.save.pre')}</fx-button>
                                    <fx-button class="b-s" data-action="next" v-if="!maskTip" @click="handleNext($event)" size="small">${$t('crm.md.save.next')}</fx-button>
                                    <span style="flex:1"/>
                                    <fx-button type="primary" @click="handleConfirm($event)" size="small">${hideClose ? $t('保存并关闭') : $t('保存')}</fx-button>
                                    <fx-button v-if="!dHideClose" @click="handleClose" size="small">${$t('取消')}</fx-button>
                                </div>
                            </div>
                            <div v-if="maskTip" class="md-edit-mask md-a">{{maskTip}}</div>
                        </fx-dialog>
                    `,
                    data() {
                        return {
                            maskTip: '',
                            dWidth: this.calcWidth(),
                            dialogVisible1: true,
                            dHideClose: hideClose,
                            dType: ''
                        }
                    },
    
                    mounted() {
                        this.dType = this.getViewType();
                        this.renderView(opts.data);
                    },
    
                    methods: {
                        handleSwitch(type) {
                            this.typeChanged = true;
                            CRM.setLocal('crmMDTitleViewColumn', type);
                            this.viewInstance.switchView(this.dType = type);
                        },
                        renderView(data) {
                            viewRenderBefore(data).then((rst = {}) => {
                                if(rst.hideFields) {
                                    hideFields = rst.hideFields;
                                }
                                if(rst.imageSize) {
                                    imageSize = rst.imageSize;
                                }
                                this._renderView(data);
                            })
                        },
                        _renderView(data) {
                            let masterData = {...superRoot.get('data')};
                            let rowId = data.rowId;
                            let details = {};
                            _.each(superRoot.model.getDetails(), (arr, apiname) => {
                                details[apiname] = _.map(arr, (a) => {
                                    return _.extend({}, a);
                                })
                            })
    
                            let RESULT = [];
                            let vueInstance = this;
                            require.async('crm-modules/action/field/field', field => {
                                let onlyShowRequired;
                                if(this.viewInstance) {
                                    onlyShowRequired = this.viewInstance.get('onlyShowRequired');
                                    this.viewInstance.destroy();
                                }
                                let MyView = field.View.extend({
                                    mycomponents: {
                                        select_one: field.C.select_one.extend({
                                            render() {
                                                let usePlugin = pluginFields && _.contains(pluginFields, this.fieldAttr.api_name);
                                                if(usePlugin) {
                                                    this.fieldAttr.is_tiled = false;
                                                }

                                                field.C.select_one.prototype.render.apply(this, arguments);

                                                usePlugin && this._addEditMask();
                                            },

                                            _addEditMask() {
                                                if(!this.select) return;

                                                this._editMask = $('<div style="position:absolute;right:0;left:0;top:0;height:34px;z-index:10">');
                                                this.$el.append(this._editMask);
                                                this._editMask.on('click', (e) => {
                                                    if(this.isReadonly()) return;
    
                                                    let ps = superRoot.model.getPluginService({
                                                        masterData,
                                                        details,
                                                        options: _.map(this.fieldAttr.options || [], a => ({...a})),
                                                        operateType: 'mdEdit',
                                                        fieldName: this.fieldAttr.api_name,
                                                        dataIndex: [rowId],
                                                        objApiName,
                                                        recordType
                                                    });


                                                    let filterOptions = this.fieldAttr.filterOptions || [];

                                                    ps.run('md.edit.before').then((rst = {}) => {
                                                        if(rst.StatusCode) return ps.end(true);
                                                        if(rst.Value && rst.Value.options) {
                                                            this.fieldAttr.filterOptions = filterOptions.concat(_.difference(_.pluck(this.fieldAttr.options, 'value'), _.pluck(rst.Value.options, 'value')));
                                                            this.handleResetOptionsByFilterOptions();
                                                        }

                                                        this._editMask.hide();

                                                        setTimeout(() => {
                                                            this.select.show();
                                                            this.select.once('hide', () => {
                                                                this.fieldAttr.filterOptions = filterOptions
                                                            })
                                                        });
                                                    })
                                                })

                                                this.select.on('hide', () => this._editMask.show());
                                            },

                                            destroy() {
                                                this._editMask && (this._editMask.remove(), this._editMask = null);
                                                field.C.select_one.prototype.destroy.apply(this, arguments);
                                            }
                                        }),

                                        object_reference: field.C.object_reference.extend({
                                            render() {
                                                field.C.object_reference.prototype.render.apply(this, arguments);
                                                this._addEditMask();
                                            },
                                            _addEditMask() {
                                                //加一个蒙层 好触发md.edit.before
                                                let $input = this.$('input').attr('disabled', 'disabled');;
    
                                                this._editMask = $('<div style="position:absolute;right:0;left:0;top:0;bottom:0;z-index:10">');
                                                this.$el.append(this._editMask);
    
                                                this._editMask.on('click', (e) => {
                                                    if(this.isReadonly()) return;
    
                                                    let ps = superRoot.model.getPluginService({
                                                        masterData,
                                                        details,
                                                        operateType: 'mdEdit',
                                                        fieldName: this.fieldAttr.api_name,
                                                        dataIndex: [rowId],
                                                        objApiName,
                                                        recordType
                                                    });
                                                    ps.run('md.edit.before').then((rst = {}) => {
                                                        if(rst.StatusCode) return ps.end(true);
    
                                                        this.__pluginConfig = rst.Value;
    
                                                        if(rst.Value && rst.Value.skipSearch) {
                                                            this._pickHandle();
                                                        } else {    
                                                            (this._editMask.remove(), this._editMask = null);
                                                            $input.removeAttr('disabled');
                                                            //判断是直接打开还是输入框得到焦点
                                                             e.clientX < $input.width() + $input.offset().left ? $input.focus() : this._pickHandle();
                                                        }
                                                    })
                                                })
                                            },
    
                                            _pickSuccess(data) {
                                                this.model.__togglePluginLookupData(this.fieldAttr.api_name, data);
                                                //this.model
                                                field.C.object_reference.prototype._pickSuccess.apply(this, arguments);
                                            },
    
                                            getExtraPickParam() {
                                                let field = this.fieldAttr;
                                                let item = {...this.__pluginConfig};
                                                if(field.wheres && field.wheres.length) {
                                                    let masterFields = superRoot.model.get('fields') || {};
                                                    let hideAdd, formFillData = {};
                                                    _.find(field.wheres, a => {
                                                        _.find(a.filters, b => {
                                                            if(b.value_type == 8) { //五角关系屏蔽新建按钮
                                                                hideAdd = true;
                                                                return hideAdd;
                                                            }
                                                            let fv = b.field_values[0];
                                                            let arr = fv && fv.split && fv.split('__r.');
                                                            if(!arr || !arr[1]) return;
                                                            let tf = arr[1].replace('$', '');
                                                            if(masterFields[tf]) { //是四角关系，带入值
                                                                formFillData[b.field_name] = masterData[tf];
                                                                formFillData[b.field_name + '__r'] = masterData[tf + '__r'];
                                                            }
                                                        })
    
                                                        return hideAdd;
                                                    })
    
                                                    item.formFillData = _.isEmpty(formFillData) ? null : formFillData;
                                                    item.hideAdd = hideAdd;
                                                }
    
                                                delete item.beforeRequest;
    
                                                return item;
                                            },
                                            getLookupData() {
                                                let data = field.C.object_reference.prototype.getLookupData.apply(this, arguments);
                                                //叠加上主对象的数据
                                                return _.extend({}, superRoot.model.getLookupData(), data);
                                            },
                                            beforeRequest(rq) {
                                                rq.master_data = {...masterData};
                                                // rq.object_data = _.extend({}, superRoot.model.getLookupData(), rq.object_data);
                                                rq.details = superRoot.getDetailsLookup(objApiName, details, rq.object_data);
                                                rq.trigger_info = superRoot.model.getTriggerInfo();
                                                rq.maskFieldApiNames = superRoot.model._getCalculateEncryptFields();
    
                                                if(this.__pluginConfig && this.__pluginConfig.beforeRequest) {
                                                    rq = this.__pluginConfig.beforeRequest(rq);
                                                }
    
                                                return rq; 
                                            },
                                            destroy() {
                                                this._editMask && (this._editMask.remove(), this._editMask = null);
                                                field.components.object_reference.prototype.destroy.apply(this, arguments);
                                            }
                                        }),

                                        object_reference_many: field.C.object_reference_many.extend({
                                            beforeRequest(rq) {
                                                rq.master_data = {...masterData};
                                                rq.details = superRoot.getDetailsLookup(objApiName, details, rq.object_data);
                                                rq.trigger_info = superRoot.model.getTriggerInfo();
                                                rq.maskFieldApiNames = superRoot.model._getCalculateEncryptFields();
                                                return rq;
                                            }
                                        }),
                                        out_employee : field.C.out_employee.extend({
                                            beforeRequest(rq) {
                                                rq.master_data = {...masterData};
                                                rq.details = superRoot.getDetailsLookup(objApiName, details, rq.object_data);
                                                rq.trigger_info = superRoot.model.getTriggerInfo();
                                                return rq
                                            }
                                        }),
                                        out_department : field.C.out_department.extend({
                                            beforeRequest(rq) {
                                                rq.master_data = {...masterData};
                                                rq.details = superRoot.getDetailsLookup(objApiName, details, rq.object_data);
                                                rq.trigger_info = superRoot.model.getTriggerInfo();
                                                return rq;
                                            }
                                        }),
                                        image: field.C.image.extend({
                                            getImageSize() {
                                                return imageSize;
                                            }
                                        }),
                                        quote: field.C.quote.extend({
                                            getImageHtml(item) {
                                                if(imageSize) {
                                                    return `<div style="height:${imageSize.height || imageSize.width}px;width:${imageSize.width}px" class="img-item"><img src="${item.originUrl || item.smallUrl}"/></div>`
                                                } else {
                                                    return field.C.quote.prototype.getImageHtml.apply(this, arguments);
                                                }
                                            }
                                        }),
                                    }
                                })
                                this.viewInstance = new MyView({
                                    el: this.$refs.viewContent,
                                    apiname: objApiName,
                                    record_type: recordType,
                                    isWM: true,
                                    noPreCalculate: true,
                                    supportRequiredCheck: true,
                                    showScroll: true,
                                    onlyShowRequired,
                                    hideFields: hideFields,
                                    viewType: this.dType,
                                    beforeSelectorRequest(param) {
                                        param.master_data = masterData;
                                        param.details = superRoot.getDetailsLookup(objApiName, details, param.object_data);
                                        param.trigger_info = superRoot.model.getTriggerInfo();
                                        return param;
                                    },
                                    Model: field.Model.extend({
                                        fetch() {
                                            let objectDescribe = JSON.parse(JSON.stringify(dob.objectDescribe));
                                            let fields = objectDescribe.fields;
                                            let child_options = (_.findWhere(fields.record_type.options, {value: recordType}) || {}).child_options;
                                            child_options && _.each(fields, a => {
                                                if(a.type === 'select_one' || a.type === 'select_many') {
                                                    _.find(child_options, item => {
                                                        let co = item[a.api_name];
                                                        if(co) {
                                                            a.options = _.filter(a.options, b => !b.not_usable && _.contains(co, b.value));
                                                            return true;
                                                        }
                                                    })
                                                }
                                            });

                                            let fieldMapping = superRoot.model.get('mdFieldMapping');
                                            fieldMapping = fieldMapping && fieldMapping[objApiName] && fieldMapping[objApiName][recordType];
                                            if(fieldMapping && fieldMapping.fields) {
                                                _.each(fieldMapping.fields, (a, fieldName) => {
                                                    let tt = fields[fieldName];
                                                    tt && (tt.label = a.label);
                                                })
                                            }


                                            this.parseDescribe({
                                                objectData: JSON.parse(JSON.stringify(data)),
                                                layout: JSON.parse(JSON.stringify(_.findWhere(dob.layoutList, {record_type: recordType}).detail_layout)),
                                                objectDescribe
                                            });
                                        },
    
                                        //执行UI事件对字段的修改
                                        triggerOnLoadEvent() {
                                            let show_field = [], hide_field = [], force_readonly_field = [], no_readonly_field = [], required_field = [], no_required_field = [];
                                            let aa = {};
                                            _.each(this.get('data')._cellStatus, (c, f) => {
                                                if(c.readonly) {
                                                    force_readonly_field.push(f);
                                                }
                                                if(c.notreadonly) {
                                                    no_readonly_field.push(f)
                                                } 
                                                if(c.hide) {
                                                    hide_field.push(f);
                                                } 
                                                if(c.show) {
                                                    show_field.push(f);
                                                }
                                                 if(c.require) {
                                                    required_field.push(f);
                                                    aa[f] = {
                                                        required: true
                                                    }
                                                } 
                                                if(c.notrequire) {
                                                    no_required_field.push(f)
                                                }
                                            })

                                            _.isEmpty(aa) || this._hackAreaUIResult(aa);
    
                                            this.trigger('rule', {show_field, hide_field, force_readonly_field, no_readonly_field, required_field, no_required_field});
                                        },
    
                                        setData(data, noTrigger, isBlur, noCalculate) {
                                            let value = data.value;
                                            let key = data.apiname;
                                            let modelData = this.get('data');
                                            let oldValue = modelData[key];
                                            if(oldValue === value || (this.dataIsNull(oldValue) && this.dataIsNull(value))) return;
    
                                            this._setHisData(oldValue, key);
                                            modelData[key] = value;
                                            this.triggerRule({[key]: value});
                                            this.cascadeChangeTips();
    
                                            if(noTrigger) return;
        
                                            this.excuteFns(data);
    
                                            noCalculate || this.triggerCalculate(data.apiname, isBlur, modelData);
                                        },
    
                                        _handleCalResult(res, valueFields, blurKey) {
                                            let changeData = res.mdUpdate[rowId];
                                            delete res.mdUpdate[rowId];
                                            if(changeData) {
                                                //TO DO 960删除
                                                if(CRM.util.getUserAttribute('crmBackMDTileHandle')) {
                                                    let t = this._differenceData(changeData);
                                                    try {
                                                        this.triggerRule(t);
                                                    } catch(e) {}
                                                    
                                                    _.extend(this.get('data'), t);
                                                    _.each(t, (v, k) => {
                                                        if (v === void 0) return;
                                                        try {
                                                            this.excuteFns({
                                                                apiname: k,
                                                                value: v,
                                                                valueFields,
                                                                isCalculate: true
                                                            }, true, true)
                                                        } catch(e) {}
                                                    })
                                                } else {
                                                    this.newBatchUpdate(changeData, true, true);
                                                }
                                            }
                                            
                                            //更新主对象的数据
                                            _.extend(masterData, res.masterUpdate);
    
                                            if(res.mdMove && res.mdMove.length) {
                                                let _moveIds = [];
                                                _.each(res.mdMove, a => {
                                                    _.each(a.datas, b => _moveIds.push(b.rowId));
                                                })
                                                _.each(res.mdInsert, a => {
                                                    a.datas = _.filter(a.datas, b => !_.contains(_moveIds, b.rowId));
                                                })
    
                                                _.each(res.mdMove, function(item) {
                                                    //先删除
                                                    (res.mdDel || (res.mdDel = [])).push(...item.datas);
                                                    let _datas = [...item.datas];
                                                    _.each(item.datas, a => {
                                                        res.mdInsert = _.filter(res.mdInsert, b => {
                                                            if(b.insertRow.rowId === a.rowId) {
                                                                _.find(_datas, (c, index) => {
                                                                    if(c.rowId === a.rowId) {
                                                                        _datas.splice(b.isBefore ? index : index + 1, 0, ...b.datas);
                                                                        return true;
                                                                    }
                                                                })
                                                                return;
                                                            }
    
                                                            return true;
                                                        })
                                                    })
    
                                                    item.datas = _datas;
                                    
                                                    //再插入
                                                    (res.mdInsert || (res.mdInsert = [])).push(item);
                                                })
                                            }
    
                                            if(res.mdDel && res.mdDel.length) {
                                                let dRowIds = _.pluck(res.mdDel, 'rowId');
                                                let tmp = {};
                                                _.each(details, (arr, k) => {
                                                    let _arr = _.filter(arr, a => !_.contains(dRowIds, a.rowId));
                                                    if(_arr.length !== arr.length) {
                                                        tmp[k] = _arr;
                                                    }
                                                })
                                                _.extend(details, tmp);
                                            }
    
                                            _.each(res.mdInsert, a => {
                                                _.find(details, (arr) => {
                                                    return _.find(arr, (b, index) => {
                                                        if(b.rowId === a.insertRow.rowId) {
                                                            let _datas = _.map(a.datas, aa => ({...aa, __isInsert: true}))
                                                            arr.splice(a.isBefore ? index : index + 1, 0, ..._datas);
                                                            return true;
                                                        }
                                                    })
                                                })
                                            })
    
                                            _.each(res.mdAdd, item => {
                                                let oa = item.object_describe_api_name;
                                                (details[oa] || (details[oa] = [])).push(item);
                                            })
                                            _.each(res.mdUpdate, (obj, rowId) => {
                                                _.find(details, list => {
                                                    return _.find(list, a => {
                                                        if(a.rowId == rowId) {
                                                            _.extend(a, obj);
                                                            return true;
                                                        }
                                                    })
                                                })
                                            })
    
                                            //当前数据已被删除
                                            _.findWhere(details[objApiName], {rowId}) || (vueInstance.maskTip = $t('crm.field.md.deltip'));
                                            
                                            let {objectAttribute, detailRecordType, detailFieldAttribute, optionAttribute, detailRowIdFieldAttribute} = res;
                                            //res.objectAttribute 控制从对象显示隐藏
                                            if(objectAttribute && objectAttribute[objApiName]) {
                                                vueInstance.maskTip = objectAttribute[objApiName].hidden ? $t('crm.field.md.hidetip') : '';
                                            }
    
                                            //res.detailRecordType 控制业务类型显示
                                            //{sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
                                            if(detailRecordType && detailRecordType[objApiName] && detailRecordType[objApiName][recordType]) {
                                                vueInstance.maskTip = detailRecordType[objApiName][recordType].hidden ? $t('crm.field.md.hidetip') : '';
                                            }
    
                                            //res.detailFieldAttribute 控制从对象字段隐藏 和 只读
                                            //fieldAttr: {object__cc: {name: {hidden: true, readOnly: true}}}
                                            let fieldAttr = (detailFieldAttribute && detailFieldAttribute[objApiName]) || {};
                                            _.each(fieldAttr[rowId], (attr, type) => {
                                                _.each(attr, (v, k) => {
                                                    fieldAttr[k] = _.extend(fieldAttr[k] || {}, {[type]: v})
                                                })
                                            })
                                            //res.detailRowIdFieldAttribute 控制从对象单条只读显示
                                            //{rowId: {readOnly: {name: true, sex: false}, forceReadOnly: {ss__c: true}}}
                                            if(detailRowIdFieldAttribute && detailRowIdFieldAttribute[rowId]) {
                                                _.each(detailRowIdFieldAttribute[rowId].readOnly, (v, k) => {
                                                    fieldAttr[k] = _.extend(fieldAttr[k] || {}, {readOnly: v}) ;
                                                })
                                            }
                                           
                                            _.isEmpty(fieldAttr) || this._setFieldAttribute(fieldAttr);
    
                                            //res.optionAttribute 控制从对象字段选项
                                            //{object__c: {field_y210t__c: {options1: {hidden: true|false}}}}
                                            if(optionAttribute && optionAttribute[objApiName]) {
                                                this._toggleFieldOptions({[objApiName]: optionAttribute[objApiName]});
                                            }
    
                                            //res.remind 事件提示信息
                                            this._toggleFieldRemind(res.remind, blurKey);
    
                                            //res.detailButton  控制从对象按钮
                                            //res.fieldAttribute 控制主对象字段属性
                                            RESULT.push(res);
                                        },
    
                                        __togglePluginLookupData(fieldName, lookupData) {
                                            let _data = this.get('__pluginLookupData');
                                            if(!_data) {
                                                this.set('__pluginLookupData', _data = {});
                                            }
    
                                            if(lookupData) {
                                                _data[fieldName] = lookupData;
                                            }
    
                                            return _data[fieldName];
                                        },
    
                                        triggerCalculate(key, isBlur, modelData) {
                                            if(!this.__calculate) {
                                                this.__calculate = _.debounce(() => {
                                                    let blurKey = this.__calculate.blurField;
                                                    let ps = superRoot.model.getPluginService({
                                                        masterData,
                                                        details,
                                                        operateType: 'mdEdit',
                                                        changeData: {[rowId]: this.__calculate.__calculateData},
                                                        lookupData: this.__togglePluginLookupData(blurKey),
    
                                                        // changeFields: _.keys(this.__calculate.__calculateData),
                                                        dataIndex: [rowId],
                                                        fieldName: blurKey,
                                                        triggerField: blurKey,
                                                        objApiName,
                                                        recordType
                                                    });
                                                    
                                                    this.__calculate.blurField = this.__calculate.__calculateData = void 0;
                                                    this._analysisAjax = true;
    
                                                    ps.run('md.edit.after').then(rst => {
                                                        let changeFields;
                                                        _.each(ps.changeData, (a, rowId) => {
                                                            ps.dataUpdater.updateData('', rowId, a);
                                                            changeFields || (changeFields = _.keys(a));
                                                            ps.dataUpdater.__setFieldCheckCalculationConfig({changeFields: {[rowId]: changeFields}});
                                                        })
                                                        ps.changeFields = changeFields;
    
                                                        if(ps.filterFields) {
                                                            if(ps.filterFields[ps.objApiName]) {
                                                                ps.filterFields[ps.objApiName] = ps.filterFields[ps.objApiName].concat(changeFields);
                                                            } else {
                                                                ps.filterFields[ps.objApiName] = changeFields;
                                                            }
                                                        } else {
                                                            ps.filterFields = {[ps.objApiName]: changeFields};
                                                        }

                                                        ps.commonAction(rst && rst.StatusCode, {beforeEndPlugin: 'md.edit.end'});
                                                    })
    
                                                    ps.end = (error) => {
                                                        this._analysisAjax = false;
                                                        if(error) {//回滚相关数据
                                                            let __hisData = this.get('__hisData');
                                                            __hisData && this.newBatchUpdate(__hisData, true);
                                                        } else {
                                                            this._handleCalResult(ps.collectChange(), ps.changeFields, blurKey);
                                                        }
    
                                                        this.set('__hisData', null);

                                                        this.__mergePluginFieldCheck(ps.__fieldCheckCalculationConfig);
                                                    }
                                                }, 150)
                                            }
    
                                            (this.__calculate.__calculateData || (this.__calculate.__calculateData = {}))[key] = modelData[key];
                                            isBlur && (this.__calculate.blurField = key);
                                            this.__calculate();
                                        },
    
                                        _mergeSingleEditResult(object_data) {
                                            let objectAttribute = {}, detailRecordType = {}, detailFieldAttribute = {}, detailRowIdFieldAttribute = {}, optionAttribute = {}, detailButton = {}, fieldAttribute = {}, masterUpdate = {};
                                            let mdAdd = [], mdDel = [], mdUpdate = {};
                                            _.each(RESULT, res => {
                                                _.extend(objectAttribute, res.objectAttribute);
                                                _.each(res.detailRecordType, (c, k) => {
                                                    detailRecordType[k] = _.extend(detailRecordType[k] || {}, c);
                                                })
                                                _.each(res.detailFieldAttribute, (c, k) => {
                                                    let tt = detailFieldAttribute[k] || {};
                                                    _.each(c, (d, f) => {
                                                        let ttf = tt[f] || (tt[f] = {});
                                                        if(_.isObject(d.hidden || d.readOnly || d.required)) {
                                                            d.hidden && (ttf.hidden = _.extend(ttf.hidden || {}, d.hidden));
                                                            d.readOnly && (ttf.readOnly = _.extend(ttf.readOnly || {}, d.readOnly));
                                                            d.required && (ttf.required = _.extend(ttf.required || {}, d.required));
                                                        } else {
                                                            ttf = _.extend(ttf, d);
                                                        }
                                                    })
                                                    detailFieldAttribute[k] = tt;
                                                })
                                                //res.detailRowIdFieldAttribute 控制从对象单条只读显示
                                                //{rowId: {readOnly: {name: true, sex: false}, forceReadOnly: {ss__c: true}}}
                                                _.each(res.detailRowIdFieldAttribute, (c, k) => {
                                                    let tt = detailRowIdFieldAttribute[k] || {};
                                                    _.each(c, (d, f) => {
                                                        tt[f] = _.extend(tt[f] || {}, d);
                                                    })
                                                    detailRowIdFieldAttribute[k] = tt;
                                                })
    
                                                _.each(res.optionAttribute, (c, k) => {
                                                    let tt = optionAttribute[k] || {};
                                                    _.each(c, (d, f) => {
                                                        tt[f] = _.extend(tt[f] || {}, d);
                                                    })
                                                    optionAttribute[k] = tt;
                                                })
                                                _.each(res.fieldAttribute, (a, k) => {
                                                    fieldAttribute[k] = _.extend(fieldAttribute[k] || {}, a);
                                                })
                                                _.each(res.detailButton, (c, k) => {
                                                    let tt = detailButton[k] || [];
                                                    detailButton[k] = tt;
                                                    _.each(c, d => {
                                                        let tc = _.findWhere(tt, {record_type: d.record_type})
                                                        if(!tc) {
                                                            tt.push(tc = {
                                                                buttons: [],
                                                                record_type: d.record_type
                                                            })
                                                        }
                                                        let tb = {};
                                                        _.each(d.buttons, e => {
                                                            if(e.buttonType === 'single') {
                                                                if(tb[e.action]) {
                                                                    if(e.hidden) {
                                                                        tb[e.action].aas = _.filter(tb[e.action].aas, a => _.contains(e.rowIds, a));
                                                                        tb[e.action].ah.push(...e.rowIds);
                                                                    } else {
                                                                        tb[e.action].ah = _.filter(tb[e.action].ah, a => _.contains(e.rowIds, a));
                                                                        tb[e.action].ass.push(...e.rowIds);
                                                                    }
                                                                } else {
                                                                    tb[e.action] = {
                                                                        button: e,
                                                                        ah: e.hidden ? e.rowIds : [],
                                                                        aas: !e.hidden ? e.rowIds : []
                                                                    }
                                                                }
                                                            } else {
                                                                _.find(tc.buttons, ee => {
                                                                    if(ee.buttonType === e.buttonType && ee.action === e.action) {
                                                                        ee.hidden = e.hidden;
                                                                        return true;
                                                                    }
                                                                }) || tc.buttons.push(e);
                                                            }
                                                        })
    
                                                        _.each(tb, a => {
                                                            let a1 = _.union(a.ah);
                                                            a1.length && tc.buttons.push({
                                                                ...a.button,
                                                                hidden: true,
                                                                rowIds: a1
                                                            })
                                                            a1 = _.union(a.aas);
                                                            a1.length && tc.buttons.push({
                                                                ...a.button,
                                                                hidden: false,
                                                                rowIds: a1
                                                            })
                                                        })
    
                                                    })
                                                })
                                     
                                                _.each(res.mdUpdate, (item, rowId) => {
                                                    mdUpdate[rowId] = _.extend(mdUpdate[rowId] || {}, item)
                                                })
                                                _.extend(masterUpdate, res.masterUpdate);
                                            })
    
                                            mdUpdate[rowId] = _.extend(object_data, this.get('data'));
    
                                            let oldDetails = superRoot.model.getDetails();
                                            _.each(details, (arr, k) => {
                                                let arr1 = oldDetails[k];
                                                _.each(arr, item => {
                                                    _.findWhere(arr1, {rowId: item.rowId}) || mdAdd.push(item);
                                                })
                                            })
                                            _.each(oldDetails, (arr, k) => {
                                                let arr1 = details[k];
                                                _.each(arr, item => {
                                                    _.findWhere(arr1, {rowId: item.rowId}) || mdDel.push(item);
                                                })
                                            })
                                            _.each(mdAdd, item => {
                                                if(mdUpdate[item.rowId]) {
                                                    _.extend(mdAdd, mdUpdate[item.rowId]);
                                                    delete mdUpdate[item.rowId]
                                                }
                                            })
                                            _.each(mdDel, item => {
                                                if(mdUpdate[item.rowId]) {
                                                    delete mdUpdate[item.rowId]
                                                }
                                            })
    
                                            let mdInsert = [], insertRowIds = [];
                                            _.each(details, arr => {
                                                let ti = [];
                                                _.each(arr, a => {
                                                    if(a.__isInsert) {
                                                        ti.push(a);
                                                        delete a.__isInsert;
                                                    } else if(ti.length) {
                                                        mdInsert.push({
                                                            insertRow: a,
                                                            isBefore: true,
                                                            datas: _.map(ti, c => {
                                                                insertRowIds.push(c.rowId);
                                                                return c;
                                                            })
                                                        })
                                                        ti = [];
                                                    }
                                                })
                                            })
    
                                            if(insertRowIds.length) {
                                                mdAdd = _.filter(mdAdd, a => !_.contains(insertRowIds, a.rowId));
                                            }
    
                                            return {
                                                objectAttribute: _.isEmpty(objectAttribute) ? void 0 : objectAttribute,
                                                detailRecordType: _.isEmpty(detailRecordType) ? void 0 : detailRecordType,
                                                detailFieldAttribute: _.isEmpty(detailFieldAttribute) ? void 0 : detailFieldAttribute,
                                                optionAttribute: _.isEmpty(optionAttribute) ? void 0 : optionAttribute,
                                                detailButton: _.isEmpty(detailButton) ? void 0 : detailButton,
                                                fieldAttribute: _.isEmpty(fieldAttribute) ? void 0 : fieldAttribute,
                                                detailRowIdFieldAttribute: _.isEmpty(detailRowIdFieldAttribute) ? void 0 : detailRowIdFieldAttribute,
                                                masterUpdate,
                                                mdAdd,
                                                mdDel,
                                                mdUpdate,
                                                mdInsert
                                            }
                                        },
    
                                        create(param, $target) {
                                            _.each(this.get('data'), (v, k) => {
                                                if(/__(?:r|l|s|encrypt)$/.test(k)) {
                                                    param.object_data[k] = v;
                                                }
                                            })
                                            superRoot.model.caltaskSuccessHandle(this._mergeSingleEditResult(param.object_data));
                                            superRoot.model.__mergePluginFieldCheck(this.get('fieldCheckCalculationConfig'));
                                            
    
                                            let action = $target.data('action');
                                            if(!action) {
                                                action = $target.parent().data('action');
                                            }
                                            if(action === 'next') {
                                                superRoot.$eventBus.trigger(ACTION.GET_DATA_BY_OFFSETPOS, objApiName, recordType, {
                                                    rowId,
                                                    offsetPos: 1,
                                                    isTile: true,
                                                    callback(data) {
                                                        if(!data) {
                                                            vueInstance.dialogVisible1 = false;
                                                            return;
                                                        };
                                                        if(data.rowId === rowId) {
                                                            FxUI.Message({
                                                                isMiddler: true,
                                                                duration: 1500,
                                                                message: $t('crm.field.md.lastone'),
                                                                type: 'warning'
                                                            })
                                                        }
                                                        vueInstance.renderView(data);
                                                    }
                                                })
                                            } else if(action === 'pre') {
                                                superRoot.$eventBus.trigger(ACTION.GET_DATA_BY_OFFSETPOS, objApiName, recordType, {
                                                    rowId,
                                                    offsetPos: -1,
                                                    isTile: true,
                                                    callback(data) {
                                                        if(!data) {
                                                            vueInstance.dialogVisible1 = false;
                                                            return;
                                                        };
                                                        if(data.rowId === rowId) {
                                                            FxUI.Message({
                                                                isMiddler: true,
                                                                duration: 1500,
                                                                message: $t('crm.field.md.firstone'),
                                                                type: 'warning'
                                                            })
                                                        }
                                                        vueInstance.renderView(data);
                                                    }
                                                })
                                            } else {
                                                vueInstance.dialogVisible1 = false;
                                            }                            
                                        }
                                    })
                                })
                                $(window).on('resize', this.handleResize)
                                this.viewInstance.on('rendercomplete', () => {
                                    //this.handleResize();
                                    _.find(details, list => {
                                        return _.find(list, (a, index) => {
                                            if(a.rowId == rowId) {
                                                //做下数据同步
                                                list[index] = this.viewInstance.get('data');
                                                return true;
                                            }
                                        })
                                    })
                                });
                                this.viewInstance.render();
                            })
                        },
                        handleResize() {
                            this.dWidth = this.calcWidth();
                            this.viewInstance.switchView(this.dType = this.getViewType());
                        },
                        getViewType(flag) {
                            let viewType;
                            if(!this.typeChanged && rst.Value && rst.Value.viewColumns) {
                                viewType =  ['', 'single', 'large', 'mid', 'mini'][rst.Value.viewColumns];
                            }

                            if(!viewType) {
                                viewType = CRM.getLocal('crmMDTitleViewColumn') || (this.dWidth > 1200 ? 'mini' : this.dWidth > 1000 ? 'mid' : '');
                            }

                            return viewType || 'large';
                        },
                        calcWidth() {
                            let dw = $('body').width() - 420;
                            return dw < 890 ? 890 : dw;
                        },
                        handleClose() {
                            this.destroy();
                        },
                        handleNext(e) {
                            this.viewInstance.submit(e, !!this.maskTip);
                        },
                        handlePre(e) {
                            this.viewInstance.submit(e, !!this.maskTip);
                        },
                        handleConfirm(e) {
                            this.viewInstance.submit(e, !!this.maskTip);
                        }
                    },
    
                    beforeDestroy() {
                        $(window).off('resize', this.handleResize);
                        this.viewInstance && (this.viewInstance.destroy(), this.viewInstance = null);
                        superRoot.singleEditComp = null;
                    }
                })
            })
        },

        handleBatchAdd(opts, objApiName, recordType) {
            let dob = this.getDetailObject(objApiName);
            let lookupField = dob.objectDescribe.fields[opts.data.lookup_field_name];
            this.model.runBatchAddService({
                objApiName,
                recordType,
                lookupField,
                insertedRowId: opts.insertedRowId,
                pickData: (pluginContext) => {
                    return {
                        then: resolve => {
                            var tc = !pluginContext.skipReadOnlyValidate && this.model.getMDFieldAttrStatus(objApiName, recordType);
                            if(tc && tc[lookupField] && tc[lookupField].readOnly) {
                                CRM.util.alert($t('该字段已被设为只读，不支持此操作'));
                                return;
                            }
                            let masterApiName = this.model.get('apiname');
                            let pitems = (lookupField.new_cascade_parent_api_name || {})[masterApiName];
                            let masterData = pluginContext.dataGetter.getMasterData();
                            let strs = [];
                            _.each(pitems, (fieldName) => {
                                if(masterData[fieldName]) return;
                                let tmp = pluginContext.dataGetter.getFieldAttr(fieldName, masterApiName);
                                tmp && strs.push('【' + tmp.label + '】');
                            })

                            if(strs.length) {
                                FS.crmUtil.alert($t('父对象字段必填提示', {tip: this.model.get('display_name') +  '.' + strs.join('&')}));
                                resolve();
                                return;
                            }

                            let hideAdd, formFillData = {};
                            if(lookupField.new_cascade_parent_api_name && lookupField.wheres) {
                                _.find(lookupField.wheres, (a) => {
                                    _.find(a.filters, (b) => {
                                        if(b.value_type == 8) { //五角关系屏蔽新建按钮
                                            hideAdd = true;
                                            return hideAdd;
                                        }
                                        let fv = b.field_values[0];
                                        let arr = fv && fv.split && fv.split('__r.');
                                        if(!arr || !arr[1]) return;
                                        let tf = arr[1].replace('$', '');
                                        if(pluginContext.dataGetter.getFieldAttr(tf, masterApiName)) { //是四角关系，带入值
                                            formFillData[b.field_name] = masterData[tf];
                                            formFillData[b.field_name + '__r'] = masterData[tf + '__r'];
                                        }
                                    })
    
                                    return hideAdd;
                                })
                            }

                            let object_data = {...this.model.getLookupData(), ...this.model.getRowBasicData(objApiName, recordType)};
                            
                            let pickConfig = pluginContext.pickConfig || {};

                            let treeSelectConfig;
                            if(lookupField.is_support_tree_view && lookupField.describe_api_name) {
                                treeSelectConfig = {
                                    objectApiName: lookupField.target_api_name,
                                    fieldName: lookupField.api_name,
                                    descrebeApiName: lookupField.describe_api_name,
                                    isNew: lookupField.tree_type === 'bigData',
                                    searchName: lookupField.is_open_display_name ? 'display_name' : 'name'
                                }
                            }

                            CRM.api.pick_data({
                                treeSelectConfig,
                                apiName: lookupField.target_api_name,
                                target_related_list_name: lookupField.target_related_list_name,
                                single: false,
                                zIndex: (this.model.get('zIndex') || 1000) + 1,
                                pluginContext,
                                object_data,
                                methods: {
                                    select(res) {
                                        resolve({
                                            addDatas: _.map(res.selected, a => {
                                                var tt = {};
                                                let langFieldName = lookupField.is_open_display_name ? 'display_name' : 'name';
                                                tt[lookupField.api_name] = a._id;
                                                tt[lookupField.api_name + '__r'] = a[langFieldName + '__r'] || (lookupField.is_open_display_name ? a.display_name || '--' : a.name);
                                                return tt;
                                            }),
                                            lookupData: res.selected
                                        })
                                    }
                                },
                                formFillData: _.isEmpty(formFillData) ? null : formFillData,
                                hideAdd,
                                filters: pickConfig.filters,
                                extendParam: {
                                    ...pickConfig.extendParam,
                                    trigger_info: this.model.getTriggerInfo(),
                                    isSupportCount: true,
                                    url: pickConfig.url
                                },
                                beforeRequest: (rq) => {
                                    var sq = JSON.parse(rq.search_query_info);
                                    sq.wheres = lookupField.wheres;
                                    rq.search_query_info = JSON.stringify(sq);

                                    rq.master_data = masterData;
                                    rq.object_data = _.extend({}, object_data, rq.object_data);
                                    rq.details = this.getDetailsLookup(objApiName, pluginContext.dataGetter.getDetails(), object_data);
                                    if(pickConfig.beforeRequest) {
                                        return pickConfig.beforeRequest(rq);
                                    }

                                    rq.maskFieldApiNames = this.model._getCalculateEncryptFields();

                                    return rq;
                                }
                            })

                        }
                    }
                }
            })
        },

        handleImportExcel(opts, objApiName, recordType) {
            let me = this;
            let dob = me.getDetailObject(objApiName);
            me.model.runImportExcelSevice({
                objApiName,
                recordType,
                importFunc(importConfig) {
                    return {
                        then(resolve) {
                            let $input = $('<input style="display:none" accept=".xlsx,.xls" type="file"/>');
                            $('body').append($input);
                            $input.trigger('click').on('change', evt => {
                                $input.remove();
                                let fileReader = new FileReader();
                                fileReader.readAsBinaryString(evt.target.files[0]);
                                fileReader.onload = (ev) => {
                                    try {
                                        CRM.api.XLSX({data: ev.target.result, success: workbook => {
                                            if(!workbook || workbook.length < 2) {
                                                FS.util.alert($t('请确保所选文件数据大于1条'));
                                                return;
                                            }
                                            let fields = dob.objectDescribe.fields, columns = [], trDatas = [], excelDatas = workbook.slice(1);
                                            let forceImportFields = importConfig && importConfig.forceImportFields || [];
                                            let layouts = [], flag;
                                            _.each(forceImportFields, b => {
                                                if(b && b.field_name) {
                                                    if(!flag) {
                                                        fields = _.extend({}, fields);
                                                        flag = true;
                                                    }
                                                    b = {...b, api_name: b.field_name, render_type: b.type, data: b.field_name, title: b.label}
                                                    fields[b.field_name] = b;
                                                    layouts.push(b);
                                                    columns.push(b);
                                                }
                                            })
                                            me.$eventBus.trigger(ACTION.GET_ALL_COLUMNS, objApiName, recordType, {callback(importColumns) {
                                                _.each(importColumns, a => {
                                                    if(a.start_time_field || a.end_time_field) return;
                                                    if(_.contains(forceImportFields, a.data)) {
                                                        columns.push(a);
                                                    } else {
                                                        a.isEdit && fields[a.data] && columns.push(a);
                                                    }
                                                });
                                            }})

                                            let dl = _.findWhere(dob.layoutList, {record_type: recordType});
                                            _.each(dl.detail_layout.components, a => {
                                                _.each(a.field_section, b => {
                                                    _.each(b.form_fields, c => layouts.push(c));
                                                })
                                            })

                                            if(importConfig && importConfig.filterExcelDatas) {
                                                excelDatas = importConfig.filterExcelDatas(excelDatas);
                                            }
                                
                                            _.each(excelDatas, () => trDatas.push(_.extend({}, me.model.getRowBasicData(objApiName, recordType))));

                                            new ParseExcelTask({
                                                apiName: objApiName,
                                                recordType
                                            }).start({
                                                fields,
                                                columns,
                                                excelDatas,
                                                layouts,
                                                header: workbook[0],
                                                type: 'import',
                                                masterData: me.model.get('data'),
                                                trDatas,
                                                importConfig,
                                                workbook,
                                                event: ev,
                                                areaDatas2Label: _.bind(me.model.location2AreaCodeByLocal, me.model)
                                            }).then(formatDatas => resolve(false, formatDatas, evt)).catch(() => resolve(true))
                                        }});
                                    } catch(e) {
                                        FS.util.alert($t('导入失败'));
                                        resolve(true);
                                    }
                                }
                                fileReader.onerror = () => {
                                    FS.util.alert($t('文件上传失败！'));
                                    resolve(true);
                                }
                            });
                        }
                    }
                }
            })
        },

        handleExcelDatas(opts, objApiName, recordType) {
            let me = this;
            me.model.runExcelCopyService({
                objApiName,
                recordType,
                startPasteDataIndex: opts.rowId,
                excelCopyFunc(pasteConfig) {
                    let {field, excelDatas, rowId} = opts;
                    return me.__excelCopyFunc(rowId, field.api_name, excelDatas, objApiName, recordType, pasteConfig);
                }
            })
        },

        __excelCopyFunc(rowId, fieldName, excelDatas, objApiName, recordType, pasteConfig = {}) {
            let me = this;
            return new Promise(resolve => {
                let datas;
                me.$eventBus.trigger(ACTION.GET_DATAS_BY_START, objApiName, recordType, {
                    start: rowId,
                    length: excelDatas.length,
                    callback(res) {
                        datas = res;
                    }
                })

                let dob = me.getDetailObject(objApiName);
                let fields = dob.objectDescribe.fields;
                let fieldNames;
                me.$eventBus.trigger(ACTION.GET_COLUMNS_BY_START, objApiName, recordType, {
                    start: fieldName,
                    length: excelDatas[0].length,
                    callback(res) {
                        fieldNames = res;
                    }
                })

                let dl = _.findWhere(dob.layoutList, {record_type: recordType});
                let layouts = [];
                _.each(dl.detail_layout.components, a => {
                    _.each(a.field_section, b => {
                        _.each(b.form_fields, c => layouts.push(c));
                    })
                })

                if(pasteConfig.filtersMappingFields) {
                    fieldNames = _.pluck(pasteConfig.filtersMappingFields(_.map(fieldNames, a => _.extend({id: a}, fields[a]))), 'api_name');
                }
                if(pasteConfig.filterExcelDatas) {
                    excelDatas = pasteConfig.filterExcelDatas(excelDatas);
                }
    
                if(!excelDatas.length || !fieldNames.length) return resolve();
                
                let trDatas = _.map(datas, item => ({...item}));
                let trDataLength = trDatas.length;
                _.each(excelDatas, (i, index) => index < trDataLength || trDatas.push({...me.model.getRowBasicData(objApiName, recordType)}));

                new ParseExcelTask({
                    apiName: objApiName,
                    recordType
                }).start({
                    fields, 
                    layouts,
                    fieldNames,
                    excelDatas: excelDatas,
                    masterData: me.model.get('data'),
        
                    trDatas,
                    importConfig: pasteConfig
                }).then(formatDatas => {
                    resolve({
                        changeData: _.map(datas, item => _.extend(_.pick(item, ['record_type', 'object_describe_api_name', 'rowId']), formatDatas.shift())),
                        addDatas:  _.map(formatDatas, item => _.extend({}, me.model.getRowBasicData(objApiName, recordType), item))
                    })
                }).catch(() => {
                    resolve();
                })
            })
        },

        //只读字段的excel复制粘贴 仅响应插件里支持的字段
        handleReadOnlyCellEdit(opts, objApiName, recordType) {
            let me = this, rowId = opts.rowId, fieldName = opts.fieldName;
            me.model.runExcelCopyService({
                fieldIsReadOnly: true,
                fieldName,
                objApiName,
                recordType,
                startPasteDataIndex: rowId,
                excelCopyFunc(pasteConfig) {
                    return new Promise(resolve => {
                        let pos = opts.$target.offset();
                        let $input = $(`<input type="text" class="b-g-ipt" style="position:fixed;top:${pos.top}px;left:${pos.left}px;z-index:20000"/>`);
                        let excelDatas;
                        let __pasteHandles = function(e) {
                            let clipboardData, $doc, $trs;
                            clipboardData = e.clipboardData || window.clipboardData;
                            $doc = new DOMParser().parseFromString(clipboardData.getData('text/html'),'text/html');
                            $trs = Array.from($doc.querySelectorAll('table tr'));

                            e.stopPropagation();
                            e.preventDefault();

                            if(!$trs || !$trs.length) return resolve();

                            excelDatas = [];
                            $trs.forEach(function($tr) {
                                let arr = [];
                                Array.from($tr.querySelectorAll('td')).forEach(function($td) {
                                    arr.push(CRM.util.trim($td.innerText));
                                })
                                excelDatas.push(arr);
                            })

                            $input.blur();
                        }
                        
                        document.addEventListener('paste', __pasteHandles);
                        $('body').append($input);
                        $input.focus();
                        $input.on('blur', _.debounce(e => {
                            document.removeEventListener('paste', __pasteHandles);
                            $input.remove();

                            if(!excelDatas) return resolve();

                            console.log(excelDatas);

                            me.__excelCopyFunc(rowId, fieldName, excelDatas, objApiName, recordType, pasteConfig).then(resolve);
                        }, 200));
                    })
                }
            })
        },

        handleDelete(opts, objApiName, recordType) {
            this.model.runMDDelService({
                objApiName,
                recordType,
                delDatas: _.isArray(opts.data) ? opts.data : [opts.data]
            })
        },

        handleBatchDelete(opts, objApiName, recordType) {
            let checkDatas;
            this.$eventBus.trigger(ACTION.GET_SELECTED_DATA, objApiName, recordType, {
                callback(res) {
                    checkDatas = res;
                }
            })
            this.model.runMDDelService({
                objApiName,
                recordType,
                delDatas: _.map(checkDatas, a => ({...a})),
                finallyCallback: (error) => {
                    error || this.$eventBus.trigger(ACTION.CANCELSELECTED, objApiName, recordType);
                }
            })
        },

        handleClone(opts, objApiName, recordType) {
            this.model.runMDCoypeService({
                objApiName,
                recordType,
                copyRowIds: _.isArray(opts.data) ? _.pluck(opts.data, 'rowId') : [opts.data.rowId],
                changeData: opts.changeData
            })
        },

        handleBatchClone(opts, objApiName, recordType) {
            let checkDatas;
            this.$eventBus.trigger(ACTION.GET_SELECTED_DATA, objApiName, recordType, {
                callback(res) {
                    checkDatas = res;
                }
            })

            this.model.runMDCoypeService({
                objApiName,
                recordType,
                copyRowIds: _.pluck(checkDatas, 'rowId'),
                finallyCallback: (error) => {
                    error || this.$eventBus.trigger(ACTION.CANCELSELECTED, objApiName, recordType);
                }
            })
        },

        handleBatchViewImage(opts, objApiName, recordType) {
            this.$eventBus.trigger(ACTION.GET_SELECTED_DATA, objApiName, recordType, {
                callback: (dataList) => {
                    let dob = this.getDetailObject(objApiName);
                    CRM.api.list_batchbtn_operate({
                        button_api_name: 'thirdapp_image_preview__c',
                        describe: dob.objectDescribe,
                        field_list: this.model.getObjectLayoutFields(objApiName, recordType),
                        dataList
                    })
                }
            })
        },

        handleDeleteAll(opts, objApiName, recordType) {
            let datas = [];
            _.each(this.model.getDetails()[objApiName], a => {
                a.record_type === recordType && datas.push({...a});
            })
            if(!datas.length) return this.$eventBus.trigger(ACTION.REMOVERECORDTYPE, objApiName, recordType);

            this.model.runMDDelService({
                objApiName,
                recordType,
                delDatas: datas,
                finallyCallback: (error) => {
                   error || this.$eventBus.trigger(ACTION.REMOVERECORDTYPE, objApiName, recordType);
                }
            })
        },

        getExtraPickParam(field, masterData) {
            if(!field.new_cascade_parent_api_name || !field.wheres) return {};
            let fields = this.get('fields'), noAdd, formData = {};

            _.find(field.wheres, (a) => {
                _.find(a.filters, (b) => {
                    if(b.value_type == 8) { //五角关系屏蔽新建按钮
                        noAdd = true;
                        return noAdd;
                    }
                    let fv = b.field_values[0];
                    let arr = fv && fv.split && fv.split('__r.');
                    if(!arr || !arr[1]) return;
                    let tf = arr[1].replace('$', '');
                    if(fields[tf]) { //是四角关系，带入值
                        formData[b.field_name] = masterData[tf];
                        formData[b.field_name + '__r'] = masterData[tf + '__r'];
                    }
                })

                return noAdd;
            })

            return {
                formFillData: _.isEmpty(formData) ? null : formData,
                hideAdd: noAdd
            }
        },

        getDetailsLookup(objApiName, details, objectData) {
            let dt = {};
            _.each(details, (arr, k) => {
                let fieldNames = this.model.getALLMDLookupKey()[k].slice(0);
                fieldNames.push('rowId');
                let narr = _.map(arr, a => _.pick(a, fieldNames));
                if(k === objApiName) {
                    _.find(narr, (a, index) => {
                        if(a.rowId === objectData.rowId) {
                            narr.splice(index, 1);
                            narr.unshift(objectData);
                            return true
                        }
                    }) || narr.unshift(objectData)
                }

                dt[k] = narr;
            })

            return dt;
        },

        assertCascadeIsPass(field, objectApiName, data, masterData) {
            //判断四角五角关系
            let np = (field.new_cascade_parent_api_name || {})[this.model.get('apiname')];
            let fields = this.model.get('fields'), strs = [];
            _.each(np, fieldName => {
                if(masterData[fieldName] || !fields[fieldName]) return;
                strs.push(`【${fields[fieldName].label}】`);
            })
            if(strs.length) {
                FS.crmUtil.alert($t('父对象字段必填提示', {tip: `${this.model.get('display_name') || ''}.${strs.join('&')}`}));
                return false;
            }

            //判断三角关系
            if(field.wheres && field.wheres.length > 1) return true; //或关系
            let cs = field.cascade_parent_api_name
            if (!cs) return true;

            fields = (this.model.getDescribeByObjApiName(objectApiName) || {}).fields;
            if(!fields) return true;

            //合并字段信息
            let fieldMapping = this.model.get('mdFieldMapping');
            fieldMapping = fieldMapping && fieldMapping[objectApiName] && fieldMapping[objectApiName][data.record_type];
            let fieldArias = (fieldMapping && fieldMapping.fields) || {};
            _.each(_.isString(cs) ? [cs] : cs, fieldName => {
                let tmp = fields[fieldName];
                if (!data[fieldName] && tmp) {
                    strs.push(fieldArias[fieldName] ? fieldArias[fieldName].label : tmp.label);
                }
            })

            if (!strs.length) return true;

            FS.crmUtil.alert($t('父对象字段必填提示', {tip: strs.join(',')}));
        },

        getEditField(field, objectApiName, recordType, fieldOptions) {
            let fields = (this.model.getDescribeByObjApiName(objectApiName) || {}).fields;

            field = _.extend({}, fields[field.api_name]);
            fieldOptions && (field.options = fieldOptions);

            let arr = [];
            if(field.type !== 'object_reference' && field.cascade_all_fields && field.cascade_all_fields.length > 1) {
                _.each(field.cascade_all_fields, (k) => {
                    if(k === field.api_name) {
                        arr.push(field) 
                    } else {
                        k !== 'record_type' && fields[k] && arr.push({...fields[k]});
                    }
                })
            } else {
                arr.push(field);
            }

            if(field.type === 'select_one' || field.type === 'select_many') {
                let rt = _.findWhere(fields.record_type.options, {value: recordType});
                rt && rt.child_options && _.each(arr, a => {
                    if(a.cascade_parent_api_name === 'record_type') { //存在业务类型的依赖关系
                        let co;
                        _.find(rt.child_options, (item) => {
                            if(item[a.api_name]) {
                                co = item[a.api_name];
                                return true;
                            }
                        })
                        a.options = co && co.length ? _.filter(a.options, b => !b.not_usable && _.contains(co, b.value)) : []
                    }
                })
            }

            //合并字段信息
            let fieldMapping = this.model.get('mdFieldMapping');
            fieldMapping = fieldMapping && fieldMapping[objectApiName] && fieldMapping[objectApiName][recordType];
            let fieldArias = (fieldMapping && fieldMapping.fields) || {};
            _.each(arr, a => {
                let tt = fieldArias[a.api_name];
                if(tt) {
                    a.label = tt.label;
                }
            })
            _.each(arr, a => {
                a.is_tile_help_text = false;
                a.enable_multi_lang = false;
            });

            return arr.length > 1 ? arr : arr[0];
        },

        handleEditBefore(opts, objApiName, recordType) {
            let me = this;
            let {field, rowId} = opts;
            me.model.runMDEditService({
                objApiName,
                recordType,
                fieldName: field.api_name,
                dataIndex: [rowId],
                options: _.map(field.options, a => ({...a})),
                fieldEditTask(ps) {
                    if(opts.actionCallBack) {
                        opts.next(false);
                        return new Promise(resolve => {
                            opts.actionCallBack({
                                column: opts.field,
                                $target: opts.$target,
                                $event: opts.e,
                                data: ps.dataGetter.getData(objApiName, rowId),
                                next(changeData) {
                                    resolve(changeData && {[rowId]: changeData});
                                }
                            });
                        })
                    }
                    
                    me.__editing = true;

                    if(ps.pickConfig && ps.pickConfig.fieldEditCallBack) {
                        return new Promise(resolve => {
                            ps.finallyCallback = function() {
                                me.__editing = false;
                                opts.next(false);
                            }
                            ps.pickConfig.fieldEditCallBack({
                                column: opts.field,
                                $target: opts.$target,
                                $event: opts.e
                            }).then(changeData => {
                                resolve(changeData && {[rowId]: changeData});
                            })
                        })
                    }

                    return {
                        then(resolve) {
                            let isPick = field.type === 'object_reference';
                            let extParam, masterData =  ps.dataGetter.getMasterData();
                            let objectData = ps.dataGetter.getData(objApiName, rowId);
                            if(isPick && !me.assertCascadeIsPass(field, objApiName, objectData, masterData)) {
                                me.__editing = false;
                                return opts.next(false)
                            };
                            if(isPick || _.contains(['employee', 'employee_many', 'department', 'department_many', 'object_reference_many', 'out_employee', 'out_department'], field.type)) {
                                let beforeRequest = ps.pickConfig && ps.pickConfig.beforeRequest;
                                if(beforeRequest) {
                                    delete ps.pickConfig.beforeRequest;
                                }
                                extParam = {
                                    skipSearch: ps.skipSearch,
                                    trigger_info: me.model.getTriggerInfo(),
                                    extraPickParam: _.extend(me.getExtraPickParam(field, masterData), ps.pickConfig),
                                    beforeRequest(rq) {
                                        rq.master_data = masterData;
                                        rq.object_data = _.extend({}, me.model.getLookupData(), objectData);
                                        rq.details = me.getDetailsLookup(objApiName, ps.dataGetter.getDetails(), objectData);
                                        if(beforeRequest) {
                                            rq = beforeRequest(rq);
                                        }
                                        rq.maskFieldApiNames = me.model._getCalculateEncryptFields();
                                        return rq;
                                    },
                                    getLookupData() {
                                        return _.extend({}, me.model.getLookupData(), objectData);
                                    }
                                }
                            }


                            opts.next(me.getEditField(field, objApiName, recordType, ps.fieldOptions), extParam).then(res => {
                                let {changeData, next} = res;
                                let {excelDatas, lookupData} = res.exOpts;



                                ps.finallyCallback = function() {
                                    me.__editing = false;
                                    next && next();
                                }

                                if(field.type === 'location' && changeData) {
                                    return me.model.fillCPCTByLocation({
                                        locationText: changeData[field.api_name], 
                                        fieldName:field.api_name, 
                                        data: objectData, 
                                        objApiName, 
                                        recordType, 
                                        exOpts: res.exOpts
                                    }).then(aa => {
                                        resolve({[rowId]: {...changeData, ...aa}})
                                    })
                                }

                                if(excelDatas) {//excel复制粘贴
                                    me.handleExcelDatas({
                                        excelDatas,
                                        rowId,
                                        field
                                    }, objApiName, recordType);

                                    resolve();
                                } else {
                                    if(isPick) {
                                        _.each(field.cus_children, fieldName => {
                                            if(objectData[fieldName]) {
                                                changeData[fieldName] = null;
                                                changeData[fieldName] = '';
                                            }
                                        })
                                    }
                                    resolve(changeData && {[rowId]: changeData}, lookupData);
                                }
                            })
                        }
                    }
                }
            })
        },

        handleBatchEditBefore(opts, objectApiName, recordType) {
            opts.next({
                fields: this.getEditField(opts.field, objectApiName, recordType),
                zIndex: this.model.get('zIndex'),
                masterData: this.model.get('data')
            });
        },

        handleBatchEditAfter(opts, objApiName, recordType) {
            let dataIndex = _.keys(opts.changeData);
            this.model.runMDEditService({
                objApiName,
                recordType,
                fieldName: opts.editField[0],
                dataIndex: dataIndex,
                batchEditDataIndex: dataIndex,
                fieldEditTask(ps) {
                    return {
                        then: resolve => resolve(opts.changeData)
                    }
                },
                finallyCallback: (error) => {
                    !error && opts.isContinue && this.handleBatchEdit({}, objApiName, recordType);
                }
            })
        },

        handleBatchEdit(opts, objApiName, recordType) {
            let me = this;
            let fields = me.getDetailObject(objApiName).objectDescribe.fields;
            let columns;
            let childOptions = (_.findWhere(fields.record_type.options, {value: recordType}) || {}).child_options;

            me.$eventBus.trigger(ACTION.GET_ALL_COLUMNS, objApiName, recordType, {
                callback(allColumns) {
                    columns = [];
                    _.each(allColumns, a => {
                        if(!a.isEdit || a.noSupportBatchEdit) return;
                        if(a.cascade_parent_api_name === 'record_type' && childOptions) {
                            a = {...a};
                            let co;
                            _.find(childOptions, (item) => {
                                if(item[a.api_name]) {
                                    co = item[a.api_name];
                                    return true;
                                }
                            })
                            a.options = co && co.length ? _.filter(a.options, b => !b.not_usable && _.contains(co, b.value)) : [];
                        }

                        let tt = (me.model.getMDFieldAttrStatus(objApiName, recordType) || {})[a.data];
                        if(tt && (tt.hidden || tt.readOnly || tt.forceReadOnly)) {
                            a = {...a, is_readonly: true};
                        }

                        if(a.label !== a.title) {
                            a.label = a.title;
                        }
    
                        columns.push(a);
                    });
                }
            })

            me.$eventBus.trigger(ACTION.START_BATCHEDIT, objApiName, recordType, {
                fields: columns,
                zIndex: me.model.get('zIndex'),
                showContinue: true
            });
        },

        handleCleanValue(opts, objApiName, recordType) {
            let {fieldName, rowId, changeData} = opts;
            this.model.runMDEditService({
                objApiName,
                recordType,
                fieldName,
                dataIndex: [rowId],
                isClean: true,
                fieldEditTask(ps) {
                    return {
                        then: resolve => resolve({[rowId]: changeData})
                    }
                }
            })
        }
    }
});
