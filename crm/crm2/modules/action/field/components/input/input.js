/**
 *@desc 输入框
 */
define(function(require, exports, module) {

    var Base = require('../base');


    module.exports = Base.extend({

        options: {
            regs: null,
            tips: $t("输入的值不合法")
        },

        template: require('./tpl-html'),

        events: {
            'input .j-f-ipt': '_onInputHandle',
            'blur .j-f-ipt': '_onBlurHandle',
			'focus .j-f-ipt': '_onFocusHandle',
			'paste .j-f-ipt': '_onPasteHandle',
            'click .j-ai-text': '_useAiCreateText'
        },

        initialize: function() {
            Base.prototype.initialize.apply(this, arguments);
            if(this.fieldAttr.input_mode === 'scan_only') {
                this.fieldAttr.placeholder = $t('仅支持移动端填写');
            }
        },

        render: function() {
            this.$el.html(this.template({
                obj: this.fieldAttr,
                isReadOnly: this.isReadonly(),
                data: this.format(this.getData())
            }))
            this.$ipt = this.$('.j-f-ipt');
            this._renderMask();

            this.renderAITool();

            this.reSetIptTitle();
        },

        //内容提示
        reSetIptTitle(flag) {
            //针对单行文本和详细地址加title提示
            if(this.fieldAttr && /^(text|address)$/.test(this.fieldAttr.type)) {
                this.$ipt && this.$ipt.attr && this.$ipt.attr('title',  flag ? '' : this.$ipt.val());
            }
        },

        _validateMask() {
            return this.fieldAttr.mask_field_encrypt && this.getData(this.apiname + '__encrypt');
        },

        //渲染掩码
        _renderMask() {
            if(this.fieldAttr.mask_field_encrypt) {
                let _v = this.getData(this.apiname + '__s');
                let style = 'position:absolute;top:1px;left:1px;right:1px;bottom:1px;display:flex;align-items:center;box-sizing:border-box;padding: 4px 0 0 4px;';
                if(this.fieldAttr.display_style === 'step') {
                    style += 'justify-content:center;';
                }
                if(!_v) {
                    style += 'display:none;';
                }
                style += 'background:' + (this.isReadonly() ? '#f6f6f6' : '#fff');

                this._$maskDiv = $(`<div style="${style}">${_v}</div>`);
                this._$maskDiv.on('click', e => {
                    if(this.isReadonly()) return;
                    if(this.$ipt && this.$ipt.attr('disabled') === 'disabled') return;
                    
                    this.$ipt && this.$ipt.focus();
                })
                if(this.$ipt) {
                    this.$ipt.on('focus', e => {
                        this._$maskDiv.html('').css('display', 'none');
                    });
                    this.$ipt.on('blur', e => {
                        if(this.__encchange) {
                            this._clanMask();
                            this.__encchange = false;
                        } else {
                            this._setMaskValue();
                        }
                    });
                    this.$ipt.on('input', e => {
                        this.setData('', this.apiname + '__encrypt', true, null, true);
                        this.setData('', this.apiname + '__s', true, null, true);
                        this.__encchange = true;
                    });
                }
                this.$el.append(this._$maskDiv);
            }
        },

        //清除掩码
        _clanMask() {
            if(!this.fieldAttr.mask_field_encrypt) return;
            
            this.setData('', this.apiname + '__encrypt', true, null, true);
            this.setData('', this.apiname + '__s', true, null, true);
            this._$maskDiv.html('').css('display', 'none');
        },

        setDataByAi(data) {
            let me = this;
            data = data.slice(0, me.fieldAttr.max_length || 500);

            me.setData(data, null, null, true);
            me.setCompValue ? me.setCompValue(data) : me.$ipt.val(data);
            me.hideError();
        },

        //设置掩码的值
        _setMaskValue() {
            if(!this.fieldAttr.mask_field_encrypt) return;
            setTimeout(() => {
                let _v = this.getData(this.apiname + '__encrypt') ? this.getData(this.apiname + '__s') || '********' : '';
                this._$maskDiv.html(_v);

                this._$maskDiv.css('display', _v ? 'flex' : 'none');
            })
        },

        isReadonly: function() {
            var attr = this.fieldAttr;
            return attr && (attr.is_readonly || attr.input_mode === 'scan_only');
        },

        _onInputHandle: function(e) {
			if (this._pasteing) {
				this._pasteing = null;
				e.target.value = CRM.util.trim(e.target.value);
			}
            this.validate();
        },

        /**
         * @desc 输入框失去焦点时 显示或隐藏错误信息
         */
        _onBlurHandle: function(e) {
			this._pasteing = null;
            this.setData(this.validate() ? this.getIptValue() : '', null, null, true);
            this.reSetIptTitle();
        },

        /**
         * @desc
         */
        _onFocusHandle: function(e) {
            var vv = this.getIptValue();
            vv && this.validate(vv);
            
            this.reSetIptTitle(true);
        },

		_onPasteHandle: function () {
			this._pasteing = true;
		},

        showError: function(el, msg) {
            // this.$ipt.addClass('b-g-ipt-error');
            var error = msg || (this.$ipt.val() ? this.options.tips : '');
            this.super.showError.apply(this, [el, error]);
        },

        // hideError: function() {
        //     this.super.hideError.call(this);
        //     // this.$ipt.removeClass('b-g-ipt-error');
        // },

        /**
         *@ desc 验证输入的值是否合法
         */
        validate: function(value) {
            if(this._validateMask()) return true;

            var val = value || this.getIptValue();
            var obj = this.fieldAttr;

            var flag = true;
            var reg = this.options.regs;
            var msg;

            if (!val) {
                flag = !this.isRequired() //必填时验证失败
            } else if (reg) {
                flag = reg.test(val);
            }else if (this.customValidate) {
                var _obj = this.customValidate(value);
                flag = _obj.flag;
                msg = _obj.msg;
            }

            flag ? this.hideError() : this.showError(null, msg);

            return flag;
        },

        getIptValue: function() {
            return CRM.util.trim(this.$ipt.val())
        },

        /**
         *@desc
         *@return
         */
        getValue: function(noValid) {
            var val = this.getIptValue();
            if(noValid) return val;

            if (!this.validate()) return '';
            return val;
        },

        format: function(val) {
            return val;
        },

        setValue: function(value, noTrigger) {
            if(!this.$ipt) return;
            
            this.$ipt.val(this.format(value));

            this.setData(value, '' , noTrigger, null, noTrigger);

            this.hideError();

            this._setMaskValue();

            this.reSetIptTitle();
        },

        disable: function() {
            this.$ipt && this.$ipt.attr('disabled', 'disabled').addClass('ipt-disabled');
            this._$maskDiv && this._$maskDiv.css('background', '#f6f6f6');
        },

        enable: function() {
            this.$ipt && this.$ipt.removeAttr('disabled').removeClass('ipt-disabled');
            this._$maskDiv && this._$maskDiv.css('background', '#fff');
        },

        destroy: function() {
            this.$ipt && (this.$ipt.off(), this.$ipt = null);
            this._$maskDiv && (this._$maskDiv.remove(), this._$maskDiv = null);
            Base.prototype.destroy.call(this);
        }
    });
});
