/**
 *@desc 单选组件
 *<AUTHOR>
 */
define(function(require, exports, module) {

    var Base = require('../base');
    var Select = require('crm-widget/select/select');
    var SelectTile = require('../selecttile/selecttile');

    return Base.extend({

        template: require('./tpl-html'),

        events: {
            'blur .f-ipt': '_onBlurHandle',
            'focus .f-ipt': '_onFocusHandle'
        },

        initialize: function() {
            Base.prototype.initialize.apply(this, arguments);
            this.model.on('resetOptionsByFilterOptions.' + this.fieldAttr.api_name, this.handleResetOptionsByFilterOptions, this);
            _.each(this.fieldAttr.options, function(a) {
                a.name = a.label;
            })
        },

        dataEvents: function() {
            var pn = this.fieldAttr.cascade_parent_api_name;
            if (!pn || !this.model.assertFieldInLayouts(pn)) return;
            var obj = this.getAttr(pn);
            if (!obj) return;
            var _events = {};
            _events['change.' + obj.api_name] = 'parentChangeHandle';
            return _events;
        },

        /**
         *@desc 渲染
         */
        render: function() {
            var value = this.getData();
            var options = this._parseOptions();
            var ao = value && _.find(options, function(a) {
                return a.value === value;
            })
			if (value && !ao) { //选项不存在清空
				this.setData('', null, true, null, true);
			}
			var isOther = ao && value === 'other';

			this.$el.html(this.renderHtml({
				isOther: isOther
			}));

            this.$ipt = this.$('.f-ipt');
            this.$wrap = this.$('.crm-a-field-selectone');

            this.createSelect(ao ? value : '', options);
			this.setDisable();
		},

		setDisable() {
			var options = this._parseOptions();
			if (options[0]._disable || this.isReadonly()) {
                this._optionsReadonly = true;
				this.disable()
			}
		},

		renderHtml(obj) {
			return this.template({
				isOther: obj.isOther,
				value: obj.isOther ? this.getData(this.apiname + '__o') : '',
				isRequired: this.otherIsRequired()
			});
		},

        _parseByFilterOptions: function(options) {
            var filterOptions = this.fieldAttr.filterOptions;
            if(filterOptions && filterOptions.length) {
                return _.filter(options, function(a) {
                    return !_.contains(filterOptions, a.value);
                })
            }
            return options;
        },

        _parseOptions: function(parentValue) {
            var options = [{
                value: '',
                name: $t("请选择"),
                isShowPlaceHolder: true
            }];
            var pn = this.fieldAttr.cascade_parent_api_name;
            if (!pn || !this.model.assertFieldInLayouts(pn)) { // 没有依赖的父不做任何变化
                return this._parseByFilterOptions(options.concat(this.fieldAttr.options));
            }

            var attr = this.getAttr(pn);
            if (attr && attr.api_name) {
                var pv = parentValue || this.getData(attr.api_name);
                var po = _.find(attr.options, function(a) {
                    return a.value == pv;
                })
                if (!po) {
                    return [{
                        _disable: true,
                        value: '',
                        name: $t("请先选择{{label}}", {label: attr.label}),
                        isShowPlaceHolder: true
                    }]
                }

                var _dependOptions = po.child_options && po.child_options[0] && po.child_options[0][this.fieldAttr.arias_name || this.apiname];
                if (!_dependOptions || !_dependOptions.length) {
                    return this._parseByFilterOptions(options);
                }

                _.each(this.fieldAttr.options, function(a) {
                    _.contains(_dependOptions, a.value) && options.push(a);
                })
            } else {
                options = options.concat(this.fieldAttr.options);
            }

            return this._parseByFilterOptions(options);
        },

        getEmptyStr: function() {
            var pn = this.fieldAttr.cascade_parent_api_name;
            var str;
            var style = '';
            if (pn && this.model.assertFieldInLayouts(pn) && this.model.dataIsNull(this.getData(pn))) {//父没有数据
                var attr = this.getAttr(pn);
                str = $t("请先选择{{label}}", {label: attr ? _.escape(attr.label) : ''});
                style = ' style="color:#bbb"';
            } else {
                str = $t('无可选项');
                style = ' style="color:#bbb"';
            }

            return '<input' + style + ' disabled="disabled" class="b-g-ipt f-ipt" value="' + str + '"/>';
        },

        createSelect: function(value, options) {
            var me = this;
            if (me.select) return;
            if(this.fieldAttr.is_tiled) {
                me._isTile = true;
                me.select = new SelectTile({
                    el: me.$('.select-wrap'),
                    options: options,
                    defalutValue: value,
                    otherIsRequired: this.otherIsRequired(),
                    otherLabel: value === 'other' && this.getData(this.apiname + '__o'),
                    needResize: !!this.fieldAttr.full_line,
                    emptyRender: function() {
                        return me.getEmptyStr();
                    },
                    isStop() {
                        return me.model.masterIsCalculating()
                    }
                })
                me.$wrap.removeClass('other-selected');
            } else {
                me.select = new Select({
                    $wrap: me.$('.select-wrap'),
                    appendBody: !!me.fieldAttr.appendBody,
                    zIndex: me.fieldAttr.appendBody ? 10000 : 1000,
                    options: options,
                    defaultVal: value || ''
                })
            }

			me.select.on('change', function(value) {
                me.selectChange(value);
            });

            value || setTimeout(function() {
                me._setDefaultValue(options);
            }, 20);
        },

		//出于某些原因，在正式回填值并派发相应事件之前做一些拦截
		//返回false 回到之前的值，不派发事件
		//默认都能回填
		intercept: function(value, cb) {
            let fieldChangeBeforeCallBack = this.getFieldChangeBeforeCallBack();
            if(fieldChangeBeforeCallBack) {
                fieldChangeBeforeCallBack({[this.apiname]: value}, {[this.apiname]: this.getData()}).then(() => cb && cb.call(this, true)).catch(() => {cb && cb.call(this, false)});
            } else {
                cb && cb.call(this, true);
            }
		},

        selectChange: function(value, noBlur) {
        	var me = this;
            value === '' && this.isRequired() ? this.showError() : this.hideError();

			this._isTile || this.$wrap[value === 'other' ? 'addClass' : 'removeClass']('other-selected');

			this.intercept(value,function (flag) {
				value = flag ? value : me.getData();
				if(!flag) me.setValue(value, true);
				me.setData(value, null, null, !noBlur);
				me.hideError();
                me.afterIntercept && me.afterIntercept(value);
			});
        },

        otherIsRequired: function() {
            var obj = _.findWhere(this.fieldAttr.options, {
                value: 'other'
            })
            return obj && obj.is_required;
        },

        /**
         *@desc 获取下拉框的值
         */
        getValue: function() {
            if (!this.select) return '';
            var val = this.select.getValue();
            if (this.model.dataIsNull(val)) {
                this.isRequired() && this.showError();
                return '';
            }
            if (val === 'other') {
                var ov = this.getIptValue();
                !ov && this.otherIsRequired() ? this.showError() : this.setParam(this.apiname + '__o', ov);
            } else {
                this.setParam(this.apiname + '__o', '');
            }

            return val;
        },

        /**
         *@desc 设置下拉框的值
         */
        setValue: function(value, noTrigger, noFill, noChange) {
            this.hideError();
            if(!noFill) {
                if(this._isTile) {
                    this.select && this.select.setValue(value || '', this.getData(this.apiname + '__o') || '');
                } else {
                    if(this.select) {
                        if(value && !this.select.getOption(value)) {//清空
                            value = '';
                        }
                        this.select.setValue(value || '');
                    }
                    
                    var isOther = value === 'other';
                    this.$wrap[isOther ? 'addClass' : 'removeClass']('other-selected');
                    isOther && this.$ipt.val(this.getData(this.apiname + '__o') || '');
                }
            }

            noTrigger || this.setData(value, null, noChange);
        },

        /**
         *@desc 级联数据处理
         */
        parentChangeHandle: function(obj) {
            var options = this._parseOptions(obj && obj.value);
            this.select.resetOptions(options, true);

            if (options[0]._disable) {
                this._optionsReadonly = true;
                this.disable();
                this.setValue('');
                return;
            }

            this._optionsReadonly = false;

            this.enable();
            var vv = this.getData();
            var ao = vv && _.find(options, function(a) {
                return a.value === vv;
            })

            if(!ao && this._setDefaultValue(options)) {
                return;
			}

            this.setValue(ao ? vv : '', !!ao);
        },

        getIptValue: function() {
            return this._isTile ? this.select.getOtherLabel() : CRM.util.trim(this.$ipt.val())
        },

        getSelectedName: function() {
            return this.select.getSelectedName();
        },

        _onBlurHandle: function(e) {
            var vv = this.getIptValue();
			!vv && this.otherIsRequired() ? this.showError() : this.hideError();
            if(vv !== this.getData(this.apiname + '__o')) {
                var data = this.get('data');
                data[this.apiname] = null;
                data[this.apiname + '__o'] = vv;
                this.setParam(this.apiname + '__o', vv);
                this.setData('other', null, null, true);
            }
        },

        _onFocusHandle: function(e) {
            this.hideError();
        },

        //667需求，当只有一个选项且该字段必填的时候默认填上
        _setDefaultValue: function(options) {
            if(this.isRequired() && options.length === 2) {
                this.setValue(options[1].value);
                return true;
			}
        },

        enable: function() {
            //TO DO 960删除此灰度
            if(!CRM.util.getUserAttribute('crmFieldOptionsReadonly') && (this._elIsReadonly || this._optionsReadonly)) return;//_elIsReadonly布局只读，_optionsReadonly父选项未选时只读
            if(this.isReadonly()) return;
            
            this._isTile || this.super.enable.apply(this, arguments);
            this.select && this.select.enable && this.select.enable();
        },

        disable: function() {
            this._isTile || this.super.disable.apply(this, arguments);
            this.select && this.select.disable && this.select.disable();
        },

        handleResetOptionsByFilterOptions: function() {
            var filterOptions = this.fieldAttr.filterOptions;
            if(filterOptions) {
                var options = this._parseOptions(null, 'uiEventTrigger');  // sfa商机销售流程字段 Ui事件 特殊处理逻辑
                var vv = this.getData();
                var flag = !!_.findWhere(options, {value: vv});//当前值还在选项里不用清除
                this.select.resetOptions(options, flag, null, true);
            }
        },

        resize: function() {
            if(this._isTile) {
                this.select && this.select.resize && this.select.resize();
            }
        },

        destroy: function() {
            this.select && this.select.destroy && this.select.destroy();
            this.select = this.tips = this.$ipt = this.$wrap = null;
            this.super.destroy.call(this);
        }
    });
});
