/**
 * @desc crm实时编辑
 * <AUTHOR>
 * @date 2019/12/09
 */
 define(function(require, exports, module) {
	var View = require('../view/view');
	var Model = require('../model/model');
	var util = FS.crmUtil;

	var MyModel = Model.extend({
		fetch: function() {
			var me = this;
			var fields = me.get('editFields');
			var areaItem = _.findWhere(fields, {type: 'country'});

			if(areaItem) {
				let isNewArea = util.getUserAttribute('crmNewArea') || util.getUserAttribute('crmAreaV3');
				if(isNewArea) {
					let fieldLabels = [];
					let require_field;
					let fieldsMap = {}
					_.each(['country', 'province', 'city', 'district', 'town', 'village'], a => {
						let aa = _.findWhere(fields, {type: a});
						if(!aa) return true;
						if(aa.is_required) {
							require_field = a;
						}
						fieldsMap['area_' + a] = aa.api_name;
						fieldLabels.push(aa.label);
					}) 
					me.parse({
						[areaItem.api_name]: {
							api_name: areaItem.api_name,
							label: fieldLabels.join('/'),
							fieldsMap,
							is_required: !!require_field,
							require_field,
							fieldLabels,
							auto: true,
							type: 'area'
						}
					})
				} else {
					util.getCountryAreaOptions().then(function(obj) {
						_.each(fields, function(item) {
							var tmp = obj[item.type];
							if(tmp) {
								item.options = tmp.options;
								item.arias_name = item.type;
								item.type = 'select_one';
							}
						})
						me.parse(fields);
					})
				}
			} else {
				me._filterOptions(fields);
				me.parse(fields);
			}
		},
		parse: function(fields) {
			this.set({
				layout: [{
					label: '',
					api_name: 'basic',
					components: fields
				}],
				fields: fields,
				__trigger_info: {
					trigger_page: 'Edit'
				}
			})
		},

		update: function(param, btn) {
            var me = this;
            me.trigger('updateajax.start');
            param.object_data = me._parseSubmitData(param.object_data);
            param.object_data.version = void 0;// 实时编辑特殊去掉version
            me.updateAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + me.get('apiname') + '/action/Edit',
                data: param,
                success: function(res) {
                   me.trigger('updateajax.complete');
                   if(res.Value && res.Value.triggerApproval) {
                   		res.noRemind = true;
                   }
                   res.Result.StatusCode === 0 ? me.submitSuccess(res, param, btn) : me.submitError(res, param, btn);
                },
                complete: function() {
                    me.updateAjax = null;
                }
            }, {
                errorAlertModel: 1,
                hideZhong: true
            })
        },

        _parseSubmitData: function(data, flag) {
        	var apiname = this.get('apiname');
        	var arr = [];
        	var fields = this.get('fields');
        	_.each(fields, function(a) { //添加上计算字段，主要是因为掩码字段不再布局里下发，但有可能是计算出来的
        		if(a.calculate_relation && !flag) {
        			var tmp = a.calculate_relation.calculate_fields[apiname];
        			tmp && [].push.apply(arr, tmp);
        		}
				// 添加上数据多语设置
				if (a.enable_multi_lang) {
					arr.push(a.api_name + '__lang');
				}
        	})
        	var keys = arr.concat(_.keys(fields));
        	return _.extend(_.pick(this.get('data'), keys), data);
        }
	})
	var MyView = View.extend({
		submit: function(e, selfSubmit) {
            var me = this;
            if(me._submiting || !me.model) return;
            var layout = me.get('layout');
            if(!layout || !layout.length) return;

            me._submiting = true;
            me.checkDataBySelf();

            _.delay(function() {   // 整体做了 50秒延迟， 目的 最后一次失去焦点 修改默认值
                var data = me.collect();

                if (!me.validate()) {
                    me._submiting = null;
                    return;
                }

                if(me._checkCalculate()) { //计算字段还没有完成延迟两秒再提交
                    _.delay(function() {
                        me._submiting = null;
                        me.submit(e, selfSubmit);
                    }, 100)
                } else {
                    me._submiting = null;
					me.model.asyncvalidateFormData(data).then(noPass => {
						if(noPass) return;
						if(!me.model) return;

						selfSubmit ? selfSubmit(me.model._parseSubmitData(me.model.formatParam(data).object_data), true) : me.model.submit(data);
					});
                }
            }, 200);
        }
	})
	var SingleEdit = Backbone.View.extend({
		events: {
			'click': '_viewClick',
			'click  .tit>.j-btn'  : 'handleBtnClick'
		},
		initialize: function() {
			this._setElement(this.options);
			var me = this;
			$(window).on('hashchange.crmsingleedit', function () {
				me.destroy();
			});
		},

		_setElement: function() {
            var el = $('<div class="crm-field-singleedit"></div>');
            $('body').append(el);
            this.setElement(el);
		},

		_viewClick: function(e) {
			var $target = $(e.target);
			if(!$target.width()) return;
			if(!$target.closest('.edit-innder').length) {
				setTimeout(() => {
					let $main = this.$('.tit .main:first');
					if($main.length && this.view && this.view.model && this.view.model.get('formDataChange')) {
						$main.click();
					} else {
						this.destroy();
					}
				}, 50)
			}
		},

		render: function() {
			var opts = this.options;
			var pos = opts.$target.offset();
			var width = opts.$target.width();
			var $h = opts.$target.height();
			var maxWidth = _.contains(['image', 'file_attachment', 'big_file_attachment', 'long_text'], opts.fieldType) ? 550 : 400;
			var w =  _.max([width + 104, maxWidth]);
			var left = pos.left - 104;
			var bw = this.$el.width();
			var zIndex;
			if(!opts.zIndex) {
				zIndex = util.getzIndex(500);
				zIndex < 0 && (zIndex = 500);
			} else {
				zIndex = opts.zIndex;
			}
			this.$el.css('z-index', zIndex);
			if(w + left > bw) {
				var sy = w + left - bw;
				if(w > maxWidth) {
					w - sy > maxWidth - 1 ? (w = w - sy, sy = 0) : (sy -= w - maxWidth, w = maxWidth);
				}
				left = left - sy;
			}
			var $top = 8;
			var posTop = pos.top;
			if($h < 32) {
				posTop += parseInt(opts.$target.css('padding-top'));
				$top = (32 - $h) / 2 + 8;
			}

			var style = 'left:' +  left + 'px;' + 'top:' + (posTop - $top) + 'px;' + 'width:' + w + 'px';
			var btns = [{
				iconClass: 'fx-icon-close',
				title: '',
				callback: (view, e) => {
					this.$el.hide();
					this.destroy();
				}
			}, {
				iconClass: 'fx-icon-ok-2 main',
				title: $t('保存'),
				callback: (view, e) => {
					if(opts.saveCallback) {
						view.submit(e, (data) => {
							opts.saveCallback(data, (serverReturnData = {}) => {
								view.trigger('success', serverReturnData)
							});
						})
					} else {
						view.submit();
					}
				}
			}]
			if(opts.otherBtns && !opts.saveCallback) {//流程没有保存按钮
				btns.pop();
			}
			_.each(opts.otherBtns, btn => {
				btns.push({
					iconClass: btn.iconClass || 'fx-icon-ok-2 main',
					title: btn.title,
					callback: (view, e) => {
						view.submit(e, (data) => {
							btn.callback(data, (serverReturnData = {}) => {
								view.trigger('success', serverReturnData)
							});
						})
					}
				})
			})

			let isNewArea = _.findWhere(opts.editFields, {type: 'country'}) && (util.getUserAttribute('crmNewArea') || util.getUserAttribute('crmAreaV3'));

			this.btns = btns.concat(_.map(opts.c));
			this.$el.html(`<div style="${style}" class="edit-innder${!isNewArea && _.keys(opts.editFields).length > 1 ? ' edit_more' : ''}"><div class="view-wrap"></div><div class="tit">${_.map(this.btns, btn => `<span data-pos="top" data-title="${btn.title}" class="crm-ui-title j-btn ${btn.iconClass}"></span>`).join('')}</div></div>`);
			this.renderView(opts);
		},

		renderView: function(opts) {
			var view = new MyView({
				el: this.$('.view-wrap'),
				Model: MyModel,
				isWM: true,
				editFields: opts.editFields,
				data: opts.data,
				dataId: opts.data._id,
				version: opts.data.version,
				record_type: opts.data.record_type,
				apiname: opts.apiname,
				isEdit: true,
				zIndex: opts.zIndex
			})

			var me = this;
			view.on('rendercomplete', function() {
				setTimeout(function() {
					var $ipt = view.getElementByApiname(opts.fieldName).find('.b-g-ipt');
					var field = view.get('fields')[opts.fieldName] || {};
					if(field.cascade_parent_api_name && field.cascade_parent_api_name.length) return;
					if($ipt.length) {
						$ipt.focus();
						setTimeout(function(){
						    $ipt[0].setSelectionRange && $ipt[0].setSelectionRange(-1, -1);
						}, 20);
					}
					if(opts.fieldType === 'location') {
						view.$el.find('>.crm-action-nfield').css({overflow: 'visible'});
					}
				}, 100)
			})
			view.render();
			view.on('success', function(serverReturnData, modelData, res) {
				var editFields;
				var data;
				if(res && res.Value.triggerApproval) {
					FxUI.Message({
			          isMiddler: true,
			          duration: 2000,
			          message: $t('paas.crm.field.trigger_approval_tip', null, '此次修改触发了审批，需要审核通过方可生效'),
			          type: 'warning'
			        })
			        editFields = ['version'];
			        data = {
			        	version: serverReturnData.version
			        }
				} else {
					_.each(opts.editFields, function(a) {
						var vv = serverReturnData[a.api_name];
						if (_.contains(['employee', 'department', 'employee_many', 'department_many'], a.type)) {
							if(vv) {
								var fn = 'getEmployeeById';
								if (a.type === 'department' || a.type === 'department_many') {
									fn = 'getCircleById'
								}
								serverReturnData[a.api_name + '__l'] = _.map(vv, function(id) {
									let name = (util[fn](id) || {}).name;
									return {
										name: name,
										deptName: name,
										id: id,
										deptId: id
									}
								})
							} else {
								serverReturnData[a.api_name + '__l'] = [];
							}
						} else if(a.type === 'town' || _.contains(['country', 'province', 'city', 'district'], a.arias_name)) {//补充label
							var comp = view.forms && view.forms[a.api_name];
							if(comp) {
								serverReturnData[a.api_name + '__r'] = comp.getSelectedName ? comp.getSelectedName() : '';
							}
						} else if(a.type === 'quote') {
							serverReturnData[a.api_name] = modelData && modelData.data[a.api_name];
						}
					})
					editFields = _.keys(opts.editFields).concat(['last_modified_by', 'last_modified_time', 'version']);
					data = serverReturnData;
				}

				me.options.success && me.options.success({
					editFields: editFields,
					data: data
				});
				me.destroy();
			})
			view.model.on('updateajax.start', function() {
				util.waiting($t('保存中') + '...');
			})
			view.model.on('updateajax.complete', function() {
				util.waiting(false);
			})

			view.$el.on('keydown', 'input.j-f-ipt', function(e) {
				if(e.keyCode === 13) {
					e.target.blur();
					me.$('.tit .j-btn').eq(1).click();
				}
			})

			me.view = view;
		},

		handleBtnClick: function(e) {
			var btn = this.btns[$(e.target).index()];
			btn.callback(this.view, e);
			return false;
		},

		destroy: function() {
			if (this.destroyed) return;

			$(window).off('hashchange.crmsingleedit');

			this.view && this.view.destroy();
			this.remove();
			this.fullDialog && this.fullDialog.destroy();
			this.options = this.events = this.model = this.fullDialog = this.view = null;
			this.destroyed = true;
		}
	})


	function fetchRichTextDatail(opts) {
		return new Promise(function (resolve) {
			if (opts.data[opts.fieldName]) {
				return resolve({});
			}
			util.waiting();
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + opts.apiname + '/controller/WebDetail',
				data: {
					objectDataId: opts.data._id,
					objectDescribeApiName: opts.apiname,
					includeLayout: false,
					includeDescribe: false
				},
				success: function (res) {
					util.waiting(false);
					if (res.Result.StatusCode === 0) {
						resolve(res.Value.data);
					} else {
						util.alert(res.Result.FailureMessage);
					}
				},
				error() {
					util.waiting(false);
				}
			}, {
				errorAlertModel: 1
			})
		})
	}


	function renderDialogComp(opts) {
		let field = opts.editFields[opts.fieldName], zIndex = opts.zIndex || util.getzIndex(900);
		opts.editFields[opts.fieldName] = _.extend({}, field, {full_line: true});
		opts.zIndex = zIndex;

		let dialogComp = FxUI.create({
			template: `
				<fx-dialog
					class="crm-single-edit__dialog"
					size="big" 
					title="${_.escape(field.label)}"
					append-to-body 
					draggable
					:z-index="${zIndex + 5}"
					:visible.sync="dVisiable"
					:close-on-click-outside="false"
					:close-on-click-modal="false"
					:close-on-press-escape="false"
					@close="closeHandle"
					
				>
					<div class="wrapper" ref="wrapper"></div>
					<span slot="footer" class="dialog-footer">
						<fx-button type="primary" @click="submitHandle($event)" size="small">${$t('确 定')}</fx-button>
						<fx-button @click="closeHandle" size="small">${$t('取 消')}</fx-button>
					</span>
				</fx-dialog>
			`,
			data() {
				return {
					dVisiable: true
				}
			},
			mounted() {
				this.renderComp()
			},
			methods: {
				renderComp() {
					let view = new MyView({
						el: this.$refs.wrapper,
						Model: MyModel,
						isWM: true,
						editFields: opts.editFields,
						data: opts.data,
						dataId: opts.data._id,
						version: opts.data.version,
						record_type: opts.data.record_type,
						apiname: opts.apiname,
						isEdit: true,
						zIndex: opts.zIndex + 6
					})
					view.render();
					view.on('success', (serverReturnData, modelData, res) => {
						let editFields, data;
						if (res && res.Value.triggerApproval) {
							FxUI.Message({
								isMiddler: true,
								duration: 2000,
								message: $t('paas.crm.field.trigger_approval_tip', null, '此次修改触发了审批，需要审核通过方可生效'),
								type: 'warning'
							})
							editFields = ['version'];
							data = {
								version: serverReturnData.version
							}
						} else {
							editFields = _.keys(opts.editFields).concat(['last_modified_by', 'last_modified_time', 'version']);
							data = serverReturnData;
						}

						opts.success && opts.success({
							editFields: editFields,
							data: data
						});

						this.closeHandle();
					})

					view.model.on('updateajax.start', () => util.waiting($t('保存中') + '...'));
					view.model.on('updateajax.complete', () => util.waiting(false))

					this.view = view;
				},

				submitHandle(e) {
					this.view.submit(e);
				},

				closeHandle() {
					this.view && (this.view.destroy(), this.view = null);
					dialogComp && dialogComp.destroy && (dialogComp.destroy(), dialogComp = null);
					$(window).off('hashchange.crmsingleedit');
				}
			}
		})

		$(window).on('hashchange.crmsingleedit', function () {
			dialogComp && dialogComp.closeHandle();
		});
	}

	return function(opts) {
		var ft = opts.editFields[opts.fieldName] || {};
		opts.fieldType = ft.type;
		['html_rich_text','rich_text', 'big_text'].indexOf(ft.type) >=0  ? fetchRichTextDatail(opts).then(function (data) {
			opts.data = _.extend({}, opts.data, data), renderDialogComp(opts)
		}) : new SingleEdit(opts).render();
	}
})
